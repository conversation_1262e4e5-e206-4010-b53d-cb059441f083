using System;
using System.IO;
using OcrLiteLib;
using System.Linq;
using System.Diagnostics;
using System.Threading.Tasks;

namespace OcrConsoleApp
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== 优化OCR服务测试 ===");
            Console.WriteLine("请选择测试模式:");
            Console.WriteLine("1. 批量图片处理（串行）");
            Console.WriteLine("2. 批量图片处理（并行）★推荐");
            Console.WriteLine("3. 高性能并行测试（提高CPU使用率）🔥");
            Console.WriteLine("4. 并行vs串行对比测试");
            Console.WriteLine("5. 简单OCR测试");
            Console.WriteLine("6. 性能测试");
            Console.WriteLine("7. 内存压力测试");
            Console.WriteLine("8. 快速测试");
            Console.WriteLine("9. 完整功能测试");
            Console.WriteLine("10. 方法重载测试（调试用）");
            Console.Write("请输入选择 (1-10): ");

            string choice = Console.ReadLine();

            switch (choice)
            {
                case "1":
                    RunBatchImageProcessing();
                    break;
                case "2":
                    RunParallelBatchProcessing().Wait();
                    break;
                case "3":
                    HighPerformanceTest.RunHighPerformanceTest().Wait();
                    break;
                case "4":
                    ParallelDemo.RunComparisonTest().Wait();
                    break;
                case "5":
                    SimpleOcrTest.RunTest();
                    break;
                case "6":
                    SimpleOcrTest.RunPerformanceTest();
                    break;
                case "7":
                    SimpleOcrTest.RunMemoryStressTest();
                    break;
                case "8":
                    QuickTest.Run();
                    break;
                case "9":
                    TestRunner.RunAllTests();
                    break;
                case "10":
                    SimpleTest.TestMethodOverloads();
                    SimpleTest.TestUnmanagedMemoryManager();
                    SimpleTest.TestOcrUtils();
                    break;
                default:
                    Console.WriteLine("无效选择，运行并行批量处理...");
                    RunParallelBatchProcessing().Wait();
                    break;
            }
        }

        static void RunBatchImageProcessing()
        {
            Console.WriteLine("\n=== 批量图片处理 ===");

            // 1. 初始化内存优化设置
            InitializeMemoryOptimization();

            // 2. 开始内存监控
            MemoryMonitor.StartMonitoring();

            // 模型文件夹和文件名与WinForm预设保持一致
            string modelsDir = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrOnnxForm\\models";
            string detPath = Path.Combine(modelsDir, "ch_PP-OCRv4_det_infer.onnx");
            string clsPath = Path.Combine(modelsDir, "ch_ppocr_mobile_v2.0_cls_infer.onnx");
            string recPath = Path.Combine(modelsDir, "ch_PP-OCRv4_rec_infer.onnx");
            string keysPath = Path.Combine(modelsDir, "ppocr_keys_v1.txt");
            int numThread = 1;
            int padding = 50;
            int imgResize = 1536;
            float boxScoreThresh = 0.6f;
            float boxThresh = 0.3f;
            float unClipRatio = 2.0f;
            bool doAngle = true;
            bool mostAngle = true;

            string imagesDir = Path.Combine("D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrConsoleApp\\", "images");
            if (!Directory.Exists(imagesDir))
            {
                Console.WriteLine($"Images folder not found: {imagesDir}");
                return;
            }

            string[] exts = new[] { ".jpg", ".jpeg", ".png", ".bmp", ".gif" };
            var files = Directory.GetFiles(imagesDir)
                .Where(f => exts.Contains(Path.GetExtension(f).ToLower())).ToArray();
            if (files.Length == 0)
            {
                Console.WriteLine($"No images found in: {imagesDir}");
                return;
            }

            Console.WriteLine($"找到 {files.Length} 个图像文件");

            // 3. 创建优化的OCR服务
            using (var ocrService = CreateOptimizedOcrService())
            {
                try
                {
                    // 4. 优化的模型初始化
                    InitializeModelsOptimized(ocrService, detPath, clsPath, recPath, keysPath, numThread);
                    MemoryMonitor.Checkpoint("模型初始化完成");

                    var stopwatch = Stopwatch.StartNew();
                    int processedCount = 0;

                    foreach (var imagePath in files)
                    {
                        Console.WriteLine($"\n==== 处理: {Path.GetFileName(imagePath)} ====");

                        try
                        {
                            // 使用带性能统计的检测方法
                            OcrServiceOptimized.PerformanceStats stats;
                            var textBlocks = ocrService.DetectTextBlocks(imagePath, padding, imgResize,
                                                                        boxScoreThresh, boxThresh, unClipRatio,
                                                                        doAngle, mostAngle, out stats);

                            processedCount++;

                            // 显示详细的性能统计和内存信息
                            var memInfo = ocrService.GetDetailedMemoryInfo();
                            Console.WriteLine($"识别结果 ({textBlocks.Count} 个文本块):");
                            Console.WriteLine($"  总耗时: {stats.TotalDetectionTime}ms");
                            Console.WriteLine($"  - 预处理: {stats.PreprocessTime}ms");
                            Console.WriteLine($"  - 文本检测: {stats.DbNetTime}ms");
                            Console.WriteLine($"  - 角度分类: {stats.AngleNetTime}ms");
                            Console.WriteLine($"  - 文字识别: {stats.CrnnNetTime}ms");
                            Console.WriteLine($"  - 后处理: {stats.PostprocessTime}ms");
                            Console.WriteLine($"  内存信息: {memInfo}");

                            foreach (var block in textBlocks)
                            {
                                Console.WriteLine($"  文本: {block.Text}");
                                Console.WriteLine($"  位置: [{block.BoxPoints[0].X},{block.BoxPoints[0].Y}] -> [{block.BoxPoints[2].X},{block.BoxPoints[2].Y}]");
                                Console.WriteLine($"  置信度: {block.BoxScore:F3}");
                                Console.WriteLine();
                            }

                            // 使用智能内存管理（减少频率）
                            if (processedCount % 10 == 0)
                            {
                                UnmanagedMemoryManager.SmartMemoryCleanup(1500); // 1.5GB阈值
                            }

                            // 每处理5张图片检查一次内存
                            if (processedCount % 5 == 0)
                            {
                                MemoryMonitor.Checkpoint($"处理完成 {processedCount} 张图片");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"处理图片出错 {imagePath}: {ex.Message}");
                        }
                    }

                    stopwatch.Stop();

                    Console.WriteLine($"\n=== 处理完成 ===");
                    Console.WriteLine($"总共处理: {processedCount} 张图片");
                    Console.WriteLine($"总耗时: {stopwatch.ElapsedMilliseconds}ms");
                    Console.WriteLine($"平均耗时: {stopwatch.ElapsedMilliseconds / Math.Max(processedCount, 1)}ms/张");

                    // 最终内存检查
                    MemoryMonitor.CheckpointWithGC("所有图片处理完成");
                    Console.WriteLine($"最终内存使用: {FormatBytes(ocrService.GetMemoryUsage())}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine("初始化或处理过程中出错: " + ex.Message + "\n" + ex.StackTrace);
                }
            }

            // 显示内存报告
            Console.WriteLine("\n" + MemoryMonitor.GetDetailedReport());

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 并行批量图片处理
        /// </summary>
        static async Task RunParallelBatchProcessing()
        {
            Console.WriteLine("\n=== 并行批量图片处理 ===");

            // 1. CPU性能优化
            CpuOptimizer.OptimizeForHighPerformance();
            CpuOptimizer.DisplaySystemInfo();

            // 2. 启动CPU监控
            CpuOptimizer.StartCpuMonitoring();

            // 3. 初始化内存优化设置
            InitializeMemoryOptimization();

            // 4. 开始内存监控
            MemoryMonitor.StartMonitoring();

            // 模型文件夹和文件名与WinForm预设保持一致
            string modelsDir = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrOnnxForm\\models";
            //string detPath = Path.Combine(modelsDir, "ch_PP-OCRv4_det_infer.onnx");
            string detPath = Path.Combine(modelsDir, "ch_PP-OCRv5_mobile_det.onnx");
            string clsPath = Path.Combine(modelsDir, "ch_ppocr_mobile_v2.0_cls_infer.onnx");
            string recPath = Path.Combine(modelsDir, "ch_PP-OCRv4_rec_infer.onnx");
            string keysPath = Path.Combine(modelsDir, "ppocr_keys_v1.txt");
            int numThread = 1;
            int padding = 50;
            int imgResize = 1536;
            float boxScoreThresh = 0.6f;
            float boxThresh = 0.3f;
            float unClipRatio = 2.0f;
            bool doAngle = true;
            bool mostAngle = true;

            string imagesDir = Path.Combine("D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrConsoleApp\\", "images");
            if (!Directory.Exists(imagesDir))
            {
                Console.WriteLine($"Images folder not found: {imagesDir}");
                return;
            }

            // 询问并发数
            Console.Write($"请输入并发数 (1-{Environment.ProcessorCount}, 默认为 {Environment.ProcessorCount - 1}): ");
            string concurrencyInput = Console.ReadLine();
            int maxConcurrency = Environment.ProcessorCount - 1;
            if (!string.IsNullOrEmpty(concurrencyInput) && int.TryParse(concurrencyInput, out int inputConcurrency))
            {
                maxConcurrency = Math.Max(1, Math.Min(inputConcurrency, Environment.ProcessorCount));
            }

            Console.WriteLine($"使用并发数: {maxConcurrency}");

            try
            {
                // 3. 创建并行批量处理器
                using (var processor = new ParallelBatchProcessor(detPath, clsPath, recPath, keysPath, numThread, maxConcurrency))
                {
                    Console.WriteLine("预热OCR服务池...");
                    await processor.WarmupAsync();
                    MemoryMonitor.Checkpoint("服务池预热完成");

                    // 4. 设置进度报告
                    var progress = new Progress<ProcessProgress>(p =>
                    {
                        if (p.CompletedCount % 5 == 0 || p.IsCompleted)
                        {
                            Console.WriteLine($"进度更新: {p}");
                        }
                    });

                    // 5. 开始并行处理
                    var stopwatch = Stopwatch.StartNew();
                    var results = await processor.ProcessDirectoryAsync(
                        imagesDir,
                        new[] { ".jpg", ".jpeg", ".png", ".bmp", ".gif" },
                        padding, imgResize, boxScoreThresh, boxThresh, unClipRatio, doAngle, mostAngle,
                        progress);

                    stopwatch.Stop();

                    // 6. 显示详细结果
                    Console.WriteLine($"\n=== 并行处理结果详情 ===");
                    var successResults = results.Where(r => r.IsSuccess).ToList();
                    var failureResults = results.Where(r => !r.IsSuccess).ToList();

                    Console.WriteLine($"成功处理: {successResults.Count} 张");
                    Console.WriteLine($"处理失败: {failureResults.Count} 张");
                    Console.WriteLine($"总耗时: {stopwatch.ElapsedMilliseconds}ms ({stopwatch.ElapsedMilliseconds / 1000.0:F1}s)");

                    if (successResults.Any())
                    {
                        var avgProcessingTime = successResults.Average(r => r.ProcessingTimeMs);
                        var minProcessingTime = successResults.Min(r => r.ProcessingTimeMs);
                        var maxProcessingTime = successResults.Max(r => r.ProcessingTimeMs);
                        var totalTextBlocks = successResults.Sum(r => r.TextBlocks.Count);

                        Console.WriteLine($"平均处理时间: {avgProcessingTime:F1}ms/张");
                        Console.WriteLine($"最快处理时间: {minProcessingTime}ms");
                        Console.WriteLine($"最慢处理时间: {maxProcessingTime}ms");
                        Console.WriteLine($"总识别文本块: {totalTextBlocks} 个");
                        Console.WriteLine($"吞吐量: {successResults.Count * 1000.0 / stopwatch.ElapsedMilliseconds:F1} 张/秒");

                        // 显示前几个成功结果的详细信息
                        Console.WriteLine($"\n前5个成功结果:");
                        foreach (var result in successResults.Take(5))
                        {
                            Console.WriteLine($"  {Path.GetFileName(result.ImagePath)}:");
                            Console.WriteLine($"    处理时间: {result.ProcessingTimeMs}ms");
                            Console.WriteLine($"    文本块数: {result.TextBlocks.Count}");
                            Console.WriteLine($"    线程ID: {result.ThreadId}");

                            if (result.TextBlocks.Any())
                            {
                                Console.WriteLine($"    识别文本: {string.Join(", ", result.TextBlocks.Take(3).Select(tb => $"\"{tb.Text}\""))}");
                                if (result.TextBlocks.Count > 3)
                                {
                                    Console.WriteLine($"    ... 还有 {result.TextBlocks.Count - 3} 个文本块");
                                }
                            }
                            Console.WriteLine();
                        }
                    }

                    if (failureResults.Any())
                    {
                        Console.WriteLine($"\n失败结果:");
                        foreach (var result in failureResults)
                        {
                            Console.WriteLine($"  {Path.GetFileName(result.ImagePath)}: {result.Error?.Message}");
                        }
                    }

                    // 7. 内存统计
                    MemoryMonitor.CheckpointWithGC("并行处理完成");
                    Console.WriteLine("\n" + MemoryMonitor.GetDetailedReport());
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"并行处理过程中出错: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 初始化内存优化设置
        /// </summary>
        static void InitializeMemoryOptimization()
        {
            Console.WriteLine("=== 初始化内存优化设置 ===");

            try
            {
                // 设置垃圾回收模式
                Console.WriteLine("设置垃圾回收模式...");
                System.Runtime.GCSettings.LatencyMode = System.Runtime.GCLatencyMode.Batch;

                // 设置大对象堆压缩模式
                Console.WriteLine("设置大对象堆压缩模式...");
                System.Runtime.GCSettings.LargeObjectHeapCompactionMode = System.Runtime.GCLargeObjectHeapCompactionMode.CompactOnce;

                // 预先进行一次完整的垃圾回收
                Console.WriteLine("执行初始垃圾回收...");
                long beforeGC = GC.GetTotalMemory(false);
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                long afterGC = GC.GetTotalMemory(false);
                Console.WriteLine($"初始GC完成，释放内存: {FormatBytes(beforeGC - afterGC)}");

                Console.WriteLine($"内存优化设置完成，当前内存: {FormatBytes(GC.GetTotalMemory(false))}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"内存优化设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建优化的OCR服务实例
        /// </summary>
        static OcrServiceOptimized CreateOptimizedOcrService()
        {
            Console.WriteLine("\n=== 创建优化OCR服务 ===");

            // 在创建服务前再次清理内存
            long beforeCreate = GC.GetTotalMemory(false);
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            long afterClean = GC.GetTotalMemory(false);

            Console.WriteLine($"创建前内存清理，释放: {FormatBytes(beforeCreate - afterClean)}");
            Console.WriteLine($"开始创建OCR服务实例...");

            var ocrService = new OcrServiceOptimized();

            long afterCreate = GC.GetTotalMemory(false);
            Console.WriteLine($"OCR服务创建完成，内存增长: {FormatBytes(afterCreate - afterClean)}");

            return ocrService;
        }

        /// <summary>
        /// 优化的模型初始化
        /// </summary>
        static void InitializeModelsOptimized(OcrServiceOptimized ocrService,
            string detPath, string clsPath, string recPath, string keysPath, int numThread)
        {
            Console.WriteLine("\n=== 优化模型初始化 ===");

            long beforeInit = GC.GetTotalMemory(false);
            Console.WriteLine($"初始化前内存: {FormatBytes(beforeInit)}");

            try
            {
                // 初始化模型
                ocrService.InitModels(detPath, clsPath, recPath, keysPath, numThread);

                long afterInit = GC.GetTotalMemory(false);
                Console.WriteLine($"模型初始化完成，总内存增长: {FormatBytes(afterInit - beforeInit)}");

                // 初始化后的内存优化
                Console.WriteLine("执行初始化后内存优化...");
                System.Runtime.GCSettings.LargeObjectHeapCompactionMode = System.Runtime.GCLargeObjectHeapCompactionMode.CompactOnce;
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                long afterOptim = GC.GetTotalMemory(false);
                Console.WriteLine($"优化后内存: {FormatBytes(afterOptim)}");
                Console.WriteLine($"优化释放内存: {FormatBytes(afterInit - afterOptim)}");

                // 恢复正常的GC模式
                System.Runtime.GCSettings.LatencyMode = System.Runtime.GCLatencyMode.Interactive;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"模型初始化失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 运行时内存监控和优化
        /// </summary>
        static void RuntimeMemoryOptimization(int currentCount, int thresholdMB = 1000)
        {
            // 每10次处理检查一次内存
            if (currentCount % 10 == 0)
            {
                long currentMemory = GC.GetTotalMemory(false);
                long thresholdBytes = (long)thresholdMB * 1024 * 1024;

                if (currentMemory > thresholdBytes)
                {
                    Console.WriteLine($"内存使用过高 ({FormatBytes(currentMemory)})，执行强制清理...");

                    long beforeCleanup = currentMemory;

                    // 强制垃圾回收
                    System.Runtime.GCSettings.LargeObjectHeapCompactionMode = System.Runtime.GCLargeObjectHeapCompactionMode.CompactOnce;
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();

                    long afterCleanup = GC.GetTotalMemory(false);
                    Console.WriteLine($"强制清理完成，释放: {FormatBytes(beforeCleanup - afterCleanup)}，当前: {FormatBytes(afterCleanup)}");
                }
            }
        }

        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;

            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }

            return $"{number:n1} {suffixes[counter]}";
        }
    }
}
