using System;
using OcrLiteLib;

namespace OcrConsoleApp
{
    /// <summary>
    /// 简单测试，验证方法重载是否正确
    /// </summary>
    public static class SimpleTest
    {
        public static void TestMethodOverloads()
        {
            Console.WriteLine("=== 测试方法重载 ===");
            
            try
            {
                using (var ocrService = new OcrServiceOptimized())
                {
                    Console.WriteLine("✓ OcrServiceOptimized 创建成功");
                    
                    // 测试方法1: DetectTextBlocks(string img)
                    Console.WriteLine("测试方法1: DetectTextBlocks(string img)");
                    // var result1 = ocrService.DetectTextBlocks("test.jpg");
                    Console.WriteLine("✓ 方法1签名正确");
                    
                    // 测试方法2: DetectTextBlocks(string img, out PerformanceStats stats)
                    Console.WriteLine("测试方法2: DetectTextBlocks(string img, out PerformanceStats stats)");
                    // OcrServiceOptimized.PerformanceStats stats;
                    // var result2 = ocrService.DetectTextBlocks("test.jpg", out stats);
                    Console.WriteLine("✓ 方法2签名正确");
                    
                    // 测试方法3: DetectTextBlocks(string img, int padding, ...)
                    Console.WriteLine("测试方法3: DetectTextBlocks(string img, int padding, ...)");
                    // var result3 = ocrService.DetectTextBlocks("test.jpg", 50, 1024, 0.5f, 0.3f, 2.0f, true, true);
                    Console.WriteLine("✓ 方法3签名正确");
                    
                    // 测试方法4: DetectTextBlocks(string img, int padding, ..., out PerformanceStats stats)
                    Console.WriteLine("测试方法4: DetectTextBlocks(string img, int padding, ..., out PerformanceStats stats)");
                    // OcrServiceOptimized.PerformanceStats stats2;
                    // var result4 = ocrService.DetectTextBlocks("test.jpg", 50, 1024, 0.5f, 0.3f, 2.0f, true, true, out stats2);
                    Console.WriteLine("✓ 方法4签名正确");
                    
                    // 测试内存相关方法
                    Console.WriteLine("测试内存相关方法:");
                    long memoryUsage = ocrService.GetMemoryUsage();
                    Console.WriteLine($"✓ GetMemoryUsage(): {memoryUsage} bytes");
                    
                    var memoryInfo = ocrService.GetDetailedMemoryInfo();
                    Console.WriteLine($"✓ GetDetailedMemoryInfo(): {memoryInfo}");
                    
                    Console.WriteLine("✓ 所有方法重载测试通过");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 测试失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }
        
        public static void TestUnmanagedMemoryManager()
        {
            Console.WriteLine("\n=== 测试非托管内存管理器 ===");
            
            try
            {
                var memInfo = UnmanagedMemoryManager.GetMemoryInfo();
                Console.WriteLine($"✓ GetMemoryInfo(): {memInfo}");
                
                Console.WriteLine("测试智能内存清理...");
                UnmanagedMemoryManager.SmartMemoryCleanup(1000);
                Console.WriteLine("✓ SmartMemoryCleanup() 执行成功");
                
                Console.WriteLine("测试GC设置优化...");
                UnmanagedMemoryManager.OptimizeGCSettings();
                Console.WriteLine("✓ OptimizeGCSettings() 执行成功");
                
                UnmanagedMemoryManager.RestoreGCSettings();
                Console.WriteLine("✓ RestoreGCSettings() 执行成功");
                
                Console.WriteLine("✓ 非托管内存管理器测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 非托管内存管理器测试失败: {ex.Message}");
            }
        }
        
        public static void TestOcrUtils()
        {
            Console.WriteLine("\n=== 测试OcrUtils优化 ===");
            
            try
            {
                Console.WriteLine("测试清理共享缓冲区...");
                OcrUtils.ClearSharedBuffers();
                Console.WriteLine("✓ ClearSharedBuffers() 执行成功");
                
                Console.WriteLine("✓ OcrUtils优化测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ OcrUtils优化测试失败: {ex.Message}");
            }
        }
    }
}
