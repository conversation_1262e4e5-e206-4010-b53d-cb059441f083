﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.ReaderWriter</name>
  </assembly>
  <members>
    <member name="T:System.Xml.ConformanceLevel">
      <summary>指定 <see cref="T:System.Xml.XmlReader" /> 和 <see cref="T:System.Xml.XmlWriter" /> 物件所執行的輸入或輸出檢查數量。</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Auto">
      <summary>
        <see cref="T:System.Xml.XmlReader" /> 或 <see cref="T:System.Xml.XmlWriter" /> 物件會自動偵測是否應執行文件或片段檢查，並進行適當的檢查。如果您包裝其他 <see cref="T:System.Xml.XmlReader" /> 或 <see cref="T:System.Xml.XmlWriter" /> 物件，則外部物件不會執行任何其他的一致性檢查。必須由基礎物件來進行一致性檢查。請參閱 <see cref="P:System.Xml.XmlReaderSettings.ConformanceLevel" /> 和 <see cref="P:System.Xml.XmlWriterSettings.ConformanceLevel" /> 屬性，以取得如何判定符合性層級的詳細資料。</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Document">
      <summary>XML 資料使用格式正確的 XML 1.0 文件 編譯，如 W3C 所定義。</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Fragment">
      <summary>XML 資料是格式正確的 XML 片段，如 W3C 所定義。</summary>
    </member>
    <member name="T:System.Xml.DtdProcessing">
      <summary>指定處理 DTD 的選項。<see cref="T:System.Xml.DtdProcessing" /> 列舉型別是由 <see cref="T:System.Xml.XmlReaderSettings" /> 類別所使用。</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Ignore">
      <summary>導致 DOCTYPE 項目受到忽略。不會發生 DTD 處理。</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Prohibit">
      <summary>指定在遇到 DTD 時擲回 <see cref="T:System.Xml.XmlException" /> 並顯示訊息，說明禁止使用 DTD。這是預設行為。</summary>
    </member>
    <member name="T:System.Xml.IXmlLineInfo">
      <summary>提供讓類別能夠傳回行和位置資訊的介面。</summary>
    </member>
    <member name="M:System.Xml.IXmlLineInfo.HasLineInfo">
      <summary>取得值，這個值指出類別是否可以傳回行資訊。</summary>
      <returns>如果可以提供 <see cref="P:System.Xml.IXmlLineInfo.LineNumber" /> 和 <see cref="P:System.Xml.IXmlLineInfo.LinePosition" />，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LineNumber">
      <summary>取得目前的行號。</summary>
      <returns>目前的行號，如果沒有可用的行資訊 (例如 <see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" /> 傳回 false)，則為 0。</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LinePosition">
      <summary>取得的目前行位置。</summary>
      <returns>目前的行位置，如果沒有可用的行資訊 (例如 <see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" /> 傳回 false)，則為 0。</returns>
    </member>
    <member name="T:System.Xml.IXmlNamespaceResolver">
      <summary>提供對一組前置詞和命名空間 (Namespace) 對應的唯讀存取。</summary>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>取得定義之前置詞/命名空間對應的集合，目前位於範圍中。</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" />，包含目前範圍內的命名空間。</returns>
      <param name="scope">
        <see cref="T:System.Xml.XmlNamespaceScope" /> 值，指定要傳回之命名空間節點的型別。</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupNamespace(System.String)">
      <summary>取得命名空間 URI，對應至指定的前置詞。</summary>
      <returns>對應至前置詞的命名空間 URI，如果前置詞未對應至命名空間 URI，則為 null。</returns>
      <param name="prefix">您要尋找其命名空間 URI 的前置詞。</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupPrefix(System.String)">
      <summary>取得前置詞，對應至指定的命名空間 URI。</summary>
      <returns>對應至命名空間 URI 的前置詞，如果命名空間 URI 未對應至前置詞，則為 null。</returns>
      <param name="namespaceName">您要尋找其前置詞的命名空間 URI。</param>
    </member>
    <member name="T:System.Xml.NamespaceHandling">
      <summary>指定是否要移除 <see cref="T:System.Xml.XmlWriter" /> 中的重複命名空間宣告。</summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.Default">
      <summary>指定不要移除重複的命名空間宣告。</summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.OmitDuplicates">
      <summary>指定要移除重複的命名空間宣告。若要移除重複的命名空間，前置詞和命名空間必須相符。</summary>
    </member>
    <member name="T:System.Xml.NameTable">
      <summary>實作單一執行緒的 <see cref="T:System.Xml.XmlNameTable" />。</summary>
    </member>
    <member name="M:System.Xml.NameTable.#ctor">
      <summary>初始化 NameTable 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>將指定的字串原子化，並將其加入至 NameTable。</summary>
      <returns>原子化後的字串，如果已經存在於 NameTable 中，則為現有的字串。如果 <paramref name="len" /> 為零，則會傳回 String.Empty。</returns>
      <param name="key">包含要加入之字串的字元陣列。</param>
      <param name="start">陣列中以零起始的索引，指定字串的第一個字元。</param>
      <param name="len">字串中的字元數。</param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />-或-<paramref name="start" /> &gt;= <paramref name="key" />.Length-或-<paramref name="len" /> &gt;= <paramref name="key" />.Length如果 <paramref name="len" /> =0，上述條件就不會造成例外狀況擲回。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0。</exception>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.String)">
      <summary>將指定的字串原子化，並將其加入至 NameTable。</summary>
      <returns>原子化後的字串，如果已經存在於 NameTable 中，則為現有的字串。</returns>
      <param name="key">要加入的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 為 null。</exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>取得包含與指定陣列中指定字元範圍內的字元相同的字串。</summary>
      <returns>原子化字串，如果字串尚未原子化，則為 null。如果 <paramref name="len" /> 為零，則會傳回 String.Empty。</returns>
      <param name="key">包含要尋找之名稱的字元陣列。</param>
      <param name="start">陣列中以零起始的索引，指定名稱的第一個字元。</param>
      <param name="len">名稱中字元的數目。</param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />-或-<paramref name="start" /> &gt;= <paramref name="key" />.Length-或-<paramref name="len" /> &gt;= <paramref name="key" />.Length如果 <paramref name="len" /> =0，上述條件就不會造成例外狀況擲回。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0。</exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.String)">
      <summary>取得具有指定值的原子化字串。</summary>
      <returns>原子化字串物件；如果字串尚未原子化，則為 null。</returns>
      <param name="value">要尋找的名稱。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 為 null。</exception>
    </member>
    <member name="T:System.Xml.NewLineHandling">
      <summary>指定如何處理分行符號。</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Entitize">
      <summary>實體化換行字元。當正規化 <see cref="T:System.Xml.XmlReader" /> 來讀取輸出時，這個設定會保留所有字元。</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.None">
      <summary>換行字元未變更。輸出與輸入相同。</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Replace">
      <summary>取代換行字元，使其與 <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" /> 屬性中指定的字元相符。</summary>
    </member>
    <member name="T:System.Xml.ReadState">
      <summary>指定讀取器 (Reader) 的狀態。</summary>
    </member>
    <member name="F:System.Xml.ReadState.Closed">
      <summary>已經呼叫 <see cref="M:System.Xml.XmlReader.Close" /> 方法。</summary>
    </member>
    <member name="F:System.Xml.ReadState.EndOfFile">
      <summary>已經順利到達檔案結尾。</summary>
    </member>
    <member name="F:System.Xml.ReadState.Error">
      <summary>發生錯誤，造成讀取作業無法繼續。</summary>
    </member>
    <member name="F:System.Xml.ReadState.Initial">
      <summary>尚未呼叫 Read 方法。</summary>
    </member>
    <member name="F:System.Xml.ReadState.Interactive">
      <summary>已經呼叫 Read 方法。讀取器可能呼叫其他方法。</summary>
    </member>
    <member name="T:System.Xml.WriteState">
      <summary>指定 <see cref="T:System.Xml.XmlWriter" /> 的狀態。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Attribute">
      <summary>指出正在寫入屬性值。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Closed">
      <summary>指出已呼叫 <see cref="M:System.Xml.XmlWriter.Close" /> 方法。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Content">
      <summary>指出正在寫入項目內容。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Element">
      <summary>指出正在寫入項目開始標記。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Error">
      <summary>已經擲回例外狀況，<see cref="T:System.Xml.XmlWriter" /> 因此處於無效狀態。您可以呼叫 <see cref="M:System.Xml.XmlWriter.Close" /> 方法，將 <see cref="T:System.Xml.XmlWriter" /> 置於 <see cref="F:System.Xml.WriteState.Closed" /> 狀態下。任何其他 <see cref="T:System.Xml.XmlWriter" /> 方法呼叫會導致 <see cref="T:System.InvalidOperationException" />。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Prolog">
      <summary>指出正在寫入初構 (Prolog)。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Start">
      <summary>指出尚未呼叫 Write 方法。</summary>
    </member>
    <member name="T:System.Xml.XmlConvert">
      <summary>編碼和解碼 XML 名稱，並且提供在 Common Language Runtime 類型和 XML 結構描述定義語言 (XSD) 類型之間轉換的方法。轉換資料類型時，傳回的值與地區設定無關。</summary>
    </member>
    <member name="M:System.Xml.XmlConvert.DecodeName(System.String)">
      <summary>將名稱解碼。這個方法反向執行 <see cref="M:System.Xml.XmlConvert.EncodeName(System.String)" /> 和 <see cref="M:System.Xml.XmlConvert.EncodeLocalName(System.String)" /> 方法。</summary>
      <returns>解碼的名稱。</returns>
      <param name="name">要轉換的名稱。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeLocalName(System.String)">
      <summary>將名稱轉換為有效的 XML 區域名稱。</summary>
      <returns>編碼的名稱。</returns>
      <param name="name">要編碼的名稱。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeName(System.String)">
      <summary>將名稱轉換為有效的 XML 名稱。</summary>
      <returns>傳回以逸出字元取代任何無效字元的名稱。</returns>
      <param name="name">要轉譯的名稱。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeNmToken(System.String)">
      <summary>根據 XML 規格驗證確定名稱有效。</summary>
      <returns>編碼的名稱。</returns>
      <param name="name">要編碼的名稱。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToBoolean(System.String)">
      <summary>將 <see cref="T:System.String" /> 轉換成對等的 <see cref="T:System.Boolean" />。</summary>
      <returns>Boolean 值，為 true 或 false。</returns>
      <param name="s">要轉換的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> does not represent a Boolean value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToByte(System.String)">
      <summary>將 <see cref="T:System.String" /> 轉換成對等的 <see cref="T:System.Byte" />。</summary>
      <returns>字串的對等 Byte。</returns>
      <param name="s">要轉換的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToChar(System.String)">
      <summary>將 <see cref="T:System.String" /> 轉換成對等的 <see cref="T:System.Char" />。</summary>
      <returns>Char，表示單一字元。</returns>
      <param name="s">字串，含有要轉換的單一字元。</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="s" /> parameter is null. </exception>
      <exception cref="T:System.FormatException">The <paramref name="s" /> parameter contains more than one character. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTime(System.String,System.Xml.XmlDateTimeSerializationMode)">
      <summary>使用指定的 <see cref="T:System.Xml.XmlDateTimeSerializationMode" />，將 <see cref="T:System.String" /> 轉換為 <see cref="T:System.DateTime" /></summary>
      <returns>
        <see cref="T:System.DateTime" /> 的對等 <see cref="T:System.String" />。</returns>
      <param name="s">要進行轉換的 <see cref="T:System.String" /> 值。</param>
      <param name="dateTimeOption">其中一個 <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> 值，可指定應將日期轉換為當地時間，或保留為國際標準時間 (UTC) (如果它是 UTC 日期)。</param>
      <exception cref="T:System.NullReferenceException">
        <paramref name="s" /> is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="dateTimeOption" /> value is null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is an empty string or is not in a valid format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String)">
      <summary>將提供的 <see cref="T:System.String" /> 轉換成 <see cref="T:System.DateTimeOffset" /> 對等用法。</summary>
      <returns>所提供之字串的 <see cref="T:System.DateTimeOffset" /> 對應項。</returns>
      <param name="s">要轉換的字串。注意   字串必須符合 XML dateTime 型別的 W3C Recommendation 子集。如需詳細資訊，請參閱 http://www.w3.org/TR/xmlschema-2/#dateTime。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The argument passed to this method is outside the range of allowable values.For information about allowable values, see <see cref="T:System.DateTimeOffset" />.</exception>
      <exception cref="T:System.FormatException">The argument passed to this method does not conform to a subset of the W3C Recommendations for the XML dateTime type.For more information see http://www.w3.org/TR/xmlschema-2/#dateTime.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String)">
      <summary>將提供的 <see cref="T:System.String" /> 轉換成 <see cref="T:System.DateTimeOffset" /> 對等用法。</summary>
      <returns>所提供之字串的 <see cref="T:System.DateTimeOffset" /> 對應項。</returns>
      <param name="s">要轉換的字串。</param>
      <param name="format">轉換 <paramref name="s" /> 的來源格式。格式參數可以是 XML dateTime 型別之 W3C Recommendation 的任何子集(如需詳細資訊，請參閱 http://www.w3.org/TR/xmlschema-2/#dateTime)。 字串 <paramref name="s" /> 會針對這個格式進行驗證。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> or <paramref name="format" /> is an empty string or is not in the specified format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String[])">
      <summary>將提供的 <see cref="T:System.String" /> 轉換成 <see cref="T:System.DateTimeOffset" /> 對等用法。</summary>
      <returns>所提供之字串的 <see cref="T:System.DateTimeOffset" /> 對應項。</returns>
      <param name="s">要轉換的字串。</param>
      <param name="formats">轉換 <paramref name="s" /> 之來源格式的陣列。<paramref name="formats" /> 中的每個格式，可以是 XML dateTime 型別的 W3C Recommendation 子集(如需詳細資訊，請參閱 http://www.w3.org/TR/xmlschema-2/#dateTime)。 字串 <paramref name="s" /> 會針對其中一種格式進行驗證。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDecimal(System.String)">
      <summary>將 <see cref="T:System.String" /> 轉換成對等的 <see cref="T:System.Decimal" />。</summary>
      <returns>字串的對等 Decimal。</returns>
      <param name="s">要轉換的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Decimal.MinValue" /> or greater than <see cref="F:System.Decimal.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDouble(System.String)">
      <summary>將 <see cref="T:System.String" /> 轉換成對等的 <see cref="T:System.Double" />。</summary>
      <returns>字串的對等 Double。</returns>
      <param name="s">要轉換的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Double.MinValue" /> or greater than <see cref="F:System.Double.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToGuid(System.String)">
      <summary>將 <see cref="T:System.String" /> 轉換成對等的 <see cref="T:System.Guid" />。</summary>
      <returns>字串的對等 Guid。</returns>
      <param name="s">要轉換的字串。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt16(System.String)">
      <summary>將 <see cref="T:System.String" /> 轉換成對等的 <see cref="T:System.Int16" />。</summary>
      <returns>字串的對等 Int16。</returns>
      <param name="s">要轉換的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int16.MinValue" /> or greater than <see cref="F:System.Int16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt32(System.String)">
      <summary>將 <see cref="T:System.String" /> 轉換成對等的 <see cref="T:System.Int32" />。</summary>
      <returns>字串的對等 Int32。</returns>
      <param name="s">要轉換的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int32.MinValue" /> or greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt64(System.String)">
      <summary>將 <see cref="T:System.String" /> 轉換成對等的 <see cref="T:System.Int64" />。</summary>
      <returns>字串的對等 Int64。</returns>
      <param name="s">要轉換的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int64.MinValue" /> or greater than <see cref="F:System.Int64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSByte(System.String)">
      <summary>將 <see cref="T:System.String" /> 轉換成對等的 <see cref="T:System.SByte" />。</summary>
      <returns>字串的對等 SByte。</returns>
      <param name="s">要轉換的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.SByte.MinValue" /> or greater than <see cref="F:System.SByte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSingle(System.String)">
      <summary>將 <see cref="T:System.String" /> 轉換成對等的 <see cref="T:System.Single" />。</summary>
      <returns>字串的對等 Single。</returns>
      <param name="s">要轉換的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Single.MinValue" /> or greater than <see cref="F:System.Single.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Boolean)">
      <summary>將 <see cref="T:System.Boolean" /> 轉換成 <see cref="T:System.String" />。</summary>
      <returns>Boolean 的字串表示，也就是 "true" 或 "false"。</returns>
      <param name="value">要進行轉換的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Byte)">
      <summary>將 <see cref="T:System.Byte" /> 轉換成 <see cref="T:System.String" />。</summary>
      <returns>Byte 的字串表示。</returns>
      <param name="value">要進行轉換的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Char)">
      <summary>將 <see cref="T:System.Char" /> 轉換成 <see cref="T:System.String" />。</summary>
      <returns>Char 的字串表示。</returns>
      <param name="value">要進行轉換的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTime,System.Xml.XmlDateTimeSerializationMode)">
      <summary>使用指定的 <see cref="T:System.Xml.XmlDateTimeSerializationMode" />，將 <see cref="T:System.DateTime" /> 轉換為 <see cref="T:System.String" />。</summary>
      <returns>
        <see cref="T:System.DateTime" /> 的對等 <see cref="T:System.String" />。</returns>
      <param name="value">要進行轉換的 <see cref="T:System.DateTime" /> 值。</param>
      <param name="dateTimeOption">其中一個 <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> 值，可指定如何處理 <see cref="T:System.DateTime" /> 值。</param>
      <exception cref="T:System.ArgumentException">The <paramref name="dateTimeOption" /> value is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> or <paramref name="dateTimeOption" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset)">
      <summary>將提供的 <see cref="T:System.DateTimeOffset" /> 轉換成 <see cref="T:System.String" />。</summary>
      <returns>所提供之 <see cref="T:System.DateTimeOffset" /> 的 <see cref="T:System.String" /> 表示。</returns>
      <param name="value">要轉換的 <see cref="T:System.DateTimeOffset" />。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset,System.String)">
      <summary>將提供的 <see cref="T:System.DateTimeOffset" /> 轉換成指定格式的 <see cref="T:System.String" />。</summary>
      <returns>以所提供之 <see cref="T:System.DateTimeOffset" /> 指定格式的 <see cref="T:System.String" /> 表示。</returns>
      <param name="value">要轉換的 <see cref="T:System.DateTimeOffset" />。</param>
      <param name="format">
        <paramref name="s" /> 所要轉換成的格式。格式參數可以是 XML dateTime 型別之 W3C Recommendation 的任何子集(如需詳細資訊，請參閱 http://www.w3.org/TR/xmlschema-2/#dateTime)。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Decimal)">
      <summary>將 <see cref="T:System.Decimal" /> 轉換成 <see cref="T:System.String" />。</summary>
      <returns>Decimal 的字串表示。</returns>
      <param name="value">要進行轉換的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Double)">
      <summary>將 <see cref="T:System.Double" /> 轉換成 <see cref="T:System.String" />。</summary>
      <returns>Double 的字串表示。</returns>
      <param name="value">要進行轉換的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Guid)">
      <summary>將 <see cref="T:System.Guid" /> 轉換成 <see cref="T:System.String" />。</summary>
      <returns>Guid 的字串表示。</returns>
      <param name="value">要進行轉換的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int16)">
      <summary>將 <see cref="T:System.Int16" /> 轉換成 <see cref="T:System.String" />。</summary>
      <returns>Int16 的字串表示。</returns>
      <param name="value">要進行轉換的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int32)">
      <summary>將 <see cref="T:System.Int32" /> 轉換成 <see cref="T:System.String" />。</summary>
      <returns>Int32 的字串表示。</returns>
      <param name="value">要進行轉換的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int64)">
      <summary>將 <see cref="T:System.Int64" /> 轉換成 <see cref="T:System.String" />。</summary>
      <returns>Int64 的字串表示。</returns>
      <param name="value">要進行轉換的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.SByte)">
      <summary>將 <see cref="T:System.SByte" /> 轉換成 <see cref="T:System.String" />。</summary>
      <returns>SByte 的字串表示。</returns>
      <param name="value">要進行轉換的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Single)">
      <summary>將 <see cref="T:System.Single" /> 轉換成 <see cref="T:System.String" />。</summary>
      <returns>Single 的字串表示。</returns>
      <param name="value">要進行轉換的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.TimeSpan)">
      <summary>將 <see cref="T:System.TimeSpan" /> 轉換成 <see cref="T:System.String" />。</summary>
      <returns>TimeSpan 的字串表示。</returns>
      <param name="value">要進行轉換的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt16)">
      <summary>將 <see cref="T:System.UInt16" /> 轉換成 <see cref="T:System.String" />。</summary>
      <returns>UInt16 的字串表示。</returns>
      <param name="value">要進行轉換的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt32)">
      <summary>將 <see cref="T:System.UInt32" /> 轉換成 <see cref="T:System.String" />。</summary>
      <returns>UInt32 的字串表示。</returns>
      <param name="value">要進行轉換的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt64)">
      <summary>將 <see cref="T:System.UInt64" /> 轉換成 <see cref="T:System.String" />。</summary>
      <returns>UInt64 的字串表示。</returns>
      <param name="value">要進行轉換的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToTimeSpan(System.String)">
      <summary>將 <see cref="T:System.String" /> 轉換成對等的 <see cref="T:System.TimeSpan" />。</summary>
      <returns>字串的對等 TimeSpan。</returns>
      <param name="s">要轉換的字串。字串格式必須符合＜W3C XML 結構描述第 2 部分：資料型別＞對持續期間的建議。</param>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in correct format to represent a TimeSpan value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt16(System.String)">
      <summary>將 <see cref="T:System.String" /> 轉換成對等的 <see cref="T:System.UInt16" />。</summary>
      <returns>字串的對等 UInt16。</returns>
      <param name="s">要轉換的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt16.MinValue" /> or greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt32(System.String)">
      <summary>將 <see cref="T:System.String" /> 轉換成對等的 <see cref="T:System.UInt32" />。</summary>
      <returns>字串的對等 UInt32。</returns>
      <param name="s">要轉換的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt32.MinValue" /> or greater than <see cref="F:System.UInt32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt64(System.String)">
      <summary>將 <see cref="T:System.String" /> 轉換成對等的 <see cref="T:System.UInt64" />。</summary>
      <returns>字串的對等 UInt64。</returns>
      <param name="s">要轉換的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt64.MinValue" /> or greater than <see cref="F:System.UInt64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyName(System.String)">
      <summary>根據 W3C Extended Markup Language Recommendation，驗證確定名稱是有效的名稱。</summary>
      <returns>名稱 (如果它是有效的 XML 名稱)。</returns>
      <param name="name">要驗證的名稱。</param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid XML name. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNCName(System.String)">
      <summary>根據 W3C Extended Markup Language Recommendation，驗證確定名稱是有效的 NCName。NCName 是不能包含冒號的名稱。</summary>
      <returns>名稱 (如果它是有效的 NCName)。</returns>
      <param name="name">要驗證的名稱。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid non-colon name. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNMTOKEN(System.String)">
      <summary>根據＜W3C XML Schema Part2: Datatypes＞建議，驗證字串是否為有效的 NMTOKEN。</summary>
      <returns>名稱語彙基元 (如果它是有效的 NMTOKEN)。</returns>
      <param name="name">您要驗證的字串。</param>
      <exception cref="T:System.Xml.XmlException">The string is not a valid name token.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyPublicId(System.String)">
      <summary>如果字串引數中的所有字元都是有效的公用 ID 字元，則會傳回傳入的字串執行個體。</summary>
      <returns>如果引數中的所有字元都是有效的公用 ID 字元，則會傳回傳入的字串。</returns>
      <param name="publicId">包含要驗證之 ID 的 <see cref="T:System.String" />。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyWhitespace(System.String)">
      <summary>如果字串引數中的所有字元都是有效的空白字元，則會傳回傳入的字串執行個體。</summary>
      <returns>如果字串引數中的所有字元都是有效的空白字元，則會傳回傳入的字串執行個體；否則傳回 null。</returns>
      <param name="content">要驗證的 <see cref="T:System.String" />。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyXmlChars(System.String)">
      <summary>如果字串引數中的所有字元及 Surrogate 字組字元都是有效的 XML 字元，則傳回傳入的字串，否則擲回 XmlException，並提供遇到的第一個無效字元的相關資訊。</summary>
      <returns>如果字串引數中的所有字元及 Surrogate 字組字元都是有效的 XML 字元，則傳回傳入的字串，否則擲回 XmlException，並提供遇到的第一個無效字元的相關資訊。</returns>
      <param name="content">包含要驗證之字元的 <see cref="T:System.String" />。</param>
    </member>
    <member name="T:System.Xml.XmlDateTimeSerializationMode">
      <summary>指定在字串和 <see cref="T:System.DateTime" /> 之間轉換時如何處理時間值。</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Local">
      <summary>當做當地時間。如果 <see cref="T:System.DateTime" /> 物件表示 Coordinated Universal Time (UTC)，則將它轉換成當地時間。</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.RoundtripKind">
      <summary>時區資訊應在轉換時保存。</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Unspecified">
      <summary>如果要將 <see cref="T:System.DateTime" /> 轉換成字串，則當做當地時間。</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Utc">
      <summary>當做 UTC。如果 <see cref="T:System.DateTime" /> 物件表示當地時間，則將它轉換成 UTC。</summary>
    </member>
    <member name="T:System.Xml.XmlException">
      <summary>傳回有關上次例外狀況的詳細資訊。</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor">
      <summary>初始化 XmlException 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String)">
      <summary>使用指定的錯誤訊息，初始化 XmlException 類別的新執行個體。</summary>
      <param name="message">錯誤描述。</param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception)">
      <summary>初始化 XmlException 類別的新執行個體。</summary>
      <param name="message">錯誤條件的描述。</param>
      <param name="innerException">擲回 XmlException 的 <see cref="T:System.Exception" /> (如果有的話)。這個值可以是 null。</param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception,System.Int32,System.Int32)">
      <summary>使用指定的訊息、內部例外狀況、行號和行位置，初始化 XmlException 類別的新執行個體。</summary>
      <param name="message">錯誤描述。</param>
      <param name="innerException">導致目前例外狀況的例外。這個值可以是 null。</param>
      <param name="lineNumber">指示發生錯誤之位置的行號。</param>
      <param name="linePosition">指示發生錯誤之位置的行位置。</param>
    </member>
    <member name="P:System.Xml.XmlException.LineNumber">
      <summary>取得行號，指出發生錯誤的位置。</summary>
      <returns>指示發生錯誤之位置的行號。</returns>
    </member>
    <member name="P:System.Xml.XmlException.LinePosition">
      <summary>取得行位置，指出發生錯誤的位置。</summary>
      <returns>指示發生錯誤之位置的行位置。</returns>
    </member>
    <member name="P:System.Xml.XmlException.Message">
      <summary>取得描述目前例外狀況的訊息。</summary>
      <returns>解釋例外狀況原因的錯誤訊息。</returns>
    </member>
    <member name="T:System.Xml.XmlNamespaceManager">
      <summary>解析、加入並移除集合的命名空間，並且為這些命名空間提供範圍管理。</summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.#ctor(System.Xml.XmlNameTable)">
      <summary>使用指定的 <see cref="T:System.Xml.XmlNameTable" /> 初始化 <see cref="T:System.Xml.XmlNamespaceManager" /> 類別的新執行個體。</summary>
      <param name="nameTable">要使用的 <see cref="T:System.Xml.XmlNameTable" />。</param>
      <exception cref="T:System.NullReferenceException">null is passed to the constructor </exception>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.AddNamespace(System.String,System.String)">
      <summary>將指定的命名空間加入集合中。</summary>
      <param name="prefix">與要加入的命名空間關聯的前置詞。使用 String.Empty 來加入預設命名空間。附註：如果 <see cref="T:System.Xml.XmlNamespaceManager" /> 將用於解析 XML 路徑語言 (XPath) 運算式中的命名空間，則必須指定前置詞。如果 XPath 運算式不包括前置詞，則會假設命名空間統一資源識別項 (URI) 為空命名空間。如需有關 XPath 運算式以及 <see cref="T:System.Xml.XmlNamespaceManager" /> 的詳細資訊，請參考 <see cref="M:System.Xml.XmlNode.SelectNodes(System.String)" /> 和 <see cref="M:System.Xml.XPath.XPathExpression.SetContext(System.Xml.XmlNamespaceManager)" /> 方法。</param>
      <param name="uri">要加入的命名空間。</param>
      <exception cref="T:System.ArgumentException">The value for <paramref name="prefix" /> is "xml" or "xmlns". </exception>
      <exception cref="T:System.ArgumentNullException">The value for <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.DefaultNamespace">
      <summary>取得預設命名空間的命名空間 URI。</summary>
      <returns>傳回預設命名空間的命名空間 URI，若無預設命名空間，則傳回 String.Empty。</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetEnumerator">
      <summary>傳回用於逐一查看 <see cref="T:System.Xml.XmlNamespaceManager" /> 中命名空間的列舉值。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" />，包含 <see cref="T:System.Xml.XmlNamespaceManager" /> 儲存的前置詞。</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>取得命名空間名稱集合，會根據前置詞索引，可用於列舉目前在範圍中的命名空間。</summary>
      <returns>目前在範圍中的命名空間和前置詞配對集合。</returns>
      <param name="scope">列舉值，指定要傳回之命名空間節點的類型。</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.HasNamespace(System.String)">
      <summary>取得值，表示提供的前置詞是否具有針對目前推送的範圍中定義的命名空間。</summary>
      <returns>如果已經定義命名空間，則為 true，否則為 false。</returns>
      <param name="prefix">您要尋找的命名空間的前置詞。 </param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupNamespace(System.String)">
      <summary>取得指定前置詞的命名空間 URI。</summary>
      <returns>傳回 <paramref name="prefix" /> 的命名空間 URI；如果無對應的命名空間，則傳回 null。已擷取傳回的字串。如需擷取字串的詳細資訊，請參閱 <see cref="T:System.Xml.XmlNameTable" /> 類別。</returns>
      <param name="prefix">您要解析其命名空間 URI 的前置詞。若要符合預設命名空間，請傳送 String.Empty。</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupPrefix(System.String)">
      <summary>尋找為指定命名空間 URI 宣告的前置詞。</summary>
      <returns>符合的前置詞。如果沒有對應的前置詞，此方法會傳回 String.Empty。如果提供了 null 值，則會傳回 null。</returns>
      <param name="uri">用來解析前置詞的命名空間。</param>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.NameTable">
      <summary>取得與這個物件相關的 <see cref="T:System.Xml.XmlNameTable" />。</summary>
      <returns>
        <see cref="T:System.Xml.XmlNameTable" />，由這個物件所使用。</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PopScope">
      <summary>將命名空間範圍自堆疊取出。</summary>
      <returns>如果堆疊上留有命名空間範圍，則為 true，若未取出其他命名空間，則為 false。</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PushScope">
      <summary>將命名空間範圍推送至堆疊。</summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.RemoveNamespace(System.String,System.String)">
      <summary>移除指定前置詞的指定命名空間。</summary>
      <param name="prefix">命名空間的前置詞</param>
      <param name="uri">指定的前置詞中要移除的命名空間。命名空間由目前的命名空間範圍移除。忽略目前範圍以外的命名空間。</param>
      <exception cref="T:System.ArgumentNullException">The value of <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="T:System.Xml.XmlNamespaceScope">
      <summary>定義命名空間範圍。</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.All">
      <summary>目前節點範圍中定義的所有命名空間。這包含 xmlns:xml 命名空間，這個命名空間一定是以隱含方式宣告。尚未定義命名空間傳回的順序。</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.ExcludeXml">
      <summary>目前節點範圍中定義的所有命名空間，但是 xmlns:xml 命名空間 (一定以隱含方式宣告) 除外。尚未定義命名空間傳回的順序。</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.Local">
      <summary>目前節點上區域定義的所有命名空間。</summary>
    </member>
    <member name="T:System.Xml.XmlNameTable">
      <summary>原子化字串物件的資料表。</summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.#ctor">
      <summary>初始化 <see cref="T:System.Xml.XmlNameTable" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>在衍生類別中覆寫時，原子化指定的字串，並將它加入至 XmlNameTable。</summary>
      <returns>新的原子化字串或已經存在的現有原子化字串。如果長度為零，則傳回 String.Empty。</returns>
      <param name="array">字元陣列，包含要加入的名稱。</param>
      <param name="offset">陣列中以零起始的索引，指定名稱的第一個字元。</param>
      <param name="length">名稱中字元的數目。</param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />-或-<paramref name="offset" /> &gt;= <paramref name="array" />.Length-或-<paramref name="length" /> &gt; <paramref name="array" />.Length如果 <paramref name="length" /> =0，上述條件就不會造成例外狀況擲回。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0。</exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.String)">
      <summary>在衍生類別中覆寫時，原子化指定的字串，並將它加入至 XmlNameTable。</summary>
      <returns>新的原子化字串或已經存在的現有原子化字串。</returns>
      <param name="array">要加入的名稱。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>在衍生類別中覆寫時，取得包含相同字元的原子化字串做為指定陣列中的指定字元範圍。</summary>
      <returns>原子化字串，如果字串尚未原子化，則為 null。如果 <paramref name="length" /> 為零，則會傳回 String.Empty。</returns>
      <param name="array">字元陣列，包含要查詢的名稱。</param>
      <param name="offset">陣列中以零起始的索引，指定名稱的第一個字元。</param>
      <param name="length">名稱中字元的數目。</param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />-或-<paramref name="offset" /> &gt;= <paramref name="array" />.Length-或-<paramref name="length" /> &gt; <paramref name="array" />.Length如果 <paramref name="length" /> =0，上述條件就不會造成例外狀況擲回。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0。</exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.String)">
      <summary>在衍生類別中覆寫時，取得包含相同值的原子化字串做為指定的字串。</summary>
      <returns>原子化字串，如果字串尚未原子化，則為 null。</returns>
      <param name="array">要查詢的名稱。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
    </member>
    <member name="T:System.Xml.XmlNodeType">
      <summary>指定節點的類型。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Attribute">
      <summary>屬性 (例如，id='123')。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.CDATA">
      <summary>CDATA 區段 (例如，&lt;![CDATA[my escaped text]]&gt;)。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Comment">
      <summary>註解 (例如，&lt;!-- my comment --&gt;)。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Document">
      <summary>做為文件樹狀結構的根的文件物件可存取整個 XML 文件。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentFragment">
      <summary>文件片段。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentType">
      <summary>文件類型宣告，以下列標記指示 (例如，&lt;!DOCTYPE...&gt;)。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Element">
      <summary>項目 (例如，&lt;item&gt;)。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndElement">
      <summary>結尾項目標記 (例如，&lt;/item&gt;)。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndEntity">
      <summary>當 XmlReader 到達實體 (Entity) 結尾時傳回的資料，取代呼叫 <see cref="M:System.Xml.XmlReader.ResolveEntity" /> 的結果。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Entity">
      <summary>實體宣告 (例如，&lt;!ENTITY...&gt;)。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EntityReference">
      <summary>實體參考 (例如，&amp;num;)。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.None">
      <summary>如果尚未呼叫 Read 方法，則由 <see cref="T:System.Xml.XmlReader" /> 傳回此資料。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Notation">
      <summary>文件類型宣告中的標記法 (例如，&lt;!NOTATION...&gt;)。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.ProcessingInstruction">
      <summary>處理指示 (例如，&lt;?pi test?&gt;)。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.SignificantWhitespace">
      <summary>混合內容模型中標記之間的泛空白字元 (White Space)，或 xml:space="preserve" 範圍 (Scope) 中的泛空白字元。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Text">
      <summary>節點的文字內容。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Whitespace">
      <summary>標記之間的泛空白字元。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.XmlDeclaration">
      <summary>XML 宣告 (例如，&lt;?xml version='1.0'?&gt;)。</summary>
    </member>
    <member name="T:System.Xml.XmlParserContext">
      <summary>提供 <see cref="T:System.Xml.XmlReader" /> 剖析 XML 片段所需的所有內容資訊。</summary>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace)">
      <summary>使用指定的 <see cref="T:System.Xml.XmlNameTable" />、<see cref="T:System.Xml.XmlNamespaceManager" />、基底 URI、xml:lang、xml:space 和文件型別的值，初始化 XmlParserContext 類別的新執行個體。</summary>
      <param name="nt">用來原子化字串的 <see cref="T:System.Xml.XmlNameTable" />。如果這是 null，則改用用來建構 <paramref name="nsMgr" /> 的名稱資料表。如需原子化字串的詳細資訊，請參閱 <see cref="T:System.Xml.XmlNameTable" />。</param>
      <param name="nsMgr">
        <see cref="T:System.Xml.XmlNamespaceManager" />，用來查詢命名空間資訊，或是 null。</param>
      <param name="docTypeName">文件型別宣告的名稱。</param>
      <param name="pubId">公用識別項。</param>
      <param name="sysId">系統識別項。</param>
      <param name="internalSubset">內部 DTD 子集。此 DTD 子集用於實體解析，而非用於文件驗證。</param>
      <param name="baseURI">XML 片段的基底 URI (載入片段的來源位置)。</param>
      <param name="xmlLang">xml:lang 範圍。</param>
      <param name="xmlSpace">
        <see cref="T:System.Xml.XmlSpace" /> 值，指出 xml:space 的範圍。</param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> 與用來建構 <paramref name="nsMgr" /> 的 XmlNameTable 不是同一個。</exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>使用指定的 <see cref="T:System.Xml.XmlNameTable" />、<see cref="T:System.Xml.XmlNamespaceManager" />、基底 URI、xml:lang、xml:space、編碼方式和文件型別的值，初始化 XmlParserContext 類別的新執行個體。</summary>
      <param name="nt">用來原子化字串的 <see cref="T:System.Xml.XmlNameTable" />。如果這是 null，則改用用來建構 <paramref name="nsMgr" /> 的名稱資料表。如需原子化字串的詳細資訊，請參閱 <see cref="T:System.Xml.XmlNameTable" />。</param>
      <param name="nsMgr">
        <see cref="T:System.Xml.XmlNamespaceManager" />，用來查詢命名空間資訊，或是 null。</param>
      <param name="docTypeName">文件型別宣告的名稱。</param>
      <param name="pubId">公用識別項。</param>
      <param name="sysId">系統識別項。</param>
      <param name="internalSubset">內部 DTD 子集。此 DTD 用於實體解析，而非用於文件驗證。</param>
      <param name="baseURI">XML 片段的基底 URI (載入片段的來源位置)。</param>
      <param name="xmlLang">xml:lang 範圍。</param>
      <param name="xmlSpace">
        <see cref="T:System.Xml.XmlSpace" /> 值，指出 xml:space 的範圍。</param>
      <param name="enc">指示編碼設定的 <see cref="T:System.Text.Encoding" /> 物件。</param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> 與用來建構 <paramref name="nsMgr" /> 的 XmlNameTable 不是同一個。</exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace)">
      <summary>使用指定的 <see cref="T:System.Xml.XmlNameTable" />、<see cref="T:System.Xml.XmlNamespaceManager" />、xml:lang 和 xml:space 的值，初始化 XmlParserContext 類別的新執行個體。</summary>
      <param name="nt">用來原子化字串的 <see cref="T:System.Xml.XmlNameTable" />。如果這是 null，則改用用來建構 <paramref name="nsMgr" /> 的名稱資料表。如需原子化字串的詳細資訊，請參閱 <see cref="T:System.Xml.XmlNameTable" />。</param>
      <param name="nsMgr">
        <see cref="T:System.Xml.XmlNamespaceManager" />，用來查詢命名空間資訊，或是 null。</param>
      <param name="xmlLang">xml:lang 範圍。</param>
      <param name="xmlSpace">
        <see cref="T:System.Xml.XmlSpace" /> 值，指出 xml:space 的範圍。</param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> 與用來建構 <paramref name="nsMgr" /> 的 XmlNameTable 不是同一個。</exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>使用指定的 <see cref="T:System.Xml.XmlNameTable" />、<see cref="T:System.Xml.XmlNamespaceManager" />、xml:lang、xml:space 和編碼方式，初始化 XmlParserContext 類別的新執行個體。</summary>
      <param name="nt">用來原子化字串的 <see cref="T:System.Xml.XmlNameTable" />。如果這是 null，則改用用來建構 <paramref name="nsMgr" /> 的名稱資料表。如需原子化字串的詳細資訊，請參閱 <see cref="T:System.Xml.XmlNameTable" />。</param>
      <param name="nsMgr">
        <see cref="T:System.Xml.XmlNamespaceManager" />，用來查詢命名空間資訊，或是 null。</param>
      <param name="xmlLang">xml:lang 範圍。</param>
      <param name="xmlSpace">
        <see cref="T:System.Xml.XmlSpace" /> 值，指出 xml:space 的範圍。</param>
      <param name="enc">指示編碼設定的 <see cref="T:System.Text.Encoding" /> 物件。</param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> 與用來建構 <paramref name="nsMgr" /> 的 XmlNameTable 不是同一個。</exception>
    </member>
    <member name="P:System.Xml.XmlParserContext.BaseURI">
      <summary>取得或設定基底 URI。</summary>
      <returns>用來解析 DTD 檔案的基底 URI。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.DocTypeName">
      <summary>取得或設定文件型別宣告的名稱。</summary>
      <returns>文件型別宣告的名稱。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.Encoding">
      <summary>取得或設定編碼類型。</summary>
      <returns>指示編碼類型的 <see cref="T:System.Text.Encoding" /> 物件。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.InternalSubset">
      <summary>取得或設定內部 DTD 子集。</summary>
      <returns>內部 DTD 子集。例如，這個屬性會傳回介於方括弧 &lt;!DOCTYPE doc [...]&gt; 之間的所有內容。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NamespaceManager">
      <summary>取得或設定 <see cref="T:System.Xml.XmlNamespaceManager" />。</summary>
      <returns>XmlNamespaceManager。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NameTable">
      <summary>取得用來原子化字串的 <see cref="T:System.Xml.XmlNameTable" />。如需原子化字串的詳細資訊，請參閱 <see cref="T:System.Xml.XmlNameTable" />。</summary>
      <returns>XmlNameTable。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.PublicId">
      <summary>取得或設定公用識別項。</summary>
      <returns>公用識別項。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.SystemId">
      <summary>取得或設定系統識別項。</summary>
      <returns>系統識別項。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlLang">
      <summary>取得或設定目前的 xml:lang 範圍。</summary>
      <returns>目前的 xml:lang 範圍。如果範圍內沒有 xml:lang，則會傳回 String.Empty。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlSpace">
      <summary>取得或設定目前的 xml:space 範圍。</summary>
      <returns>
        <see cref="T:System.Xml.XmlSpace" /> 值，指出 xml:space 的範圍。</returns>
    </member>
    <member name="T:System.Xml.XmlQualifiedName">
      <summary>表示 XML 限定名稱 (Qualified Name)。</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor">
      <summary>初始化 <see cref="T:System.Xml.XmlQualifiedName" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String)">
      <summary>使用指定的名稱，初始化 <see cref="T:System.Xml.XmlQualifiedName" /> 類別的新執行個體。</summary>
      <param name="name">做為 <see cref="T:System.Xml.XmlQualifiedName" /> 物件名稱使用的區域名稱。</param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String,System.String)">
      <summary>使用指定的名稱和命名空間，來初始化 <see cref="T:System.Xml.XmlQualifiedName" /> 類別的新執行個體。</summary>
      <param name="name">做為 <see cref="T:System.Xml.XmlQualifiedName" /> 物件名稱使用的區域名稱。</param>
      <param name="ns">
        <see cref="T:System.Xml.XmlQualifiedName" /> 物件的命名空間。</param>
    </member>
    <member name="F:System.Xml.XmlQualifiedName.Empty">
      <summary>提供空白的 <see cref="T:System.Xml.XmlQualifiedName" />。</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Xml.XmlQualifiedName" /> 物件是否等於目前的 <see cref="T:System.Xml.XmlQualifiedName" /> 物件。</summary>
      <returns>如果這兩個是相同的執行個體物件，則為 true，否則為 false。</returns>
      <param name="other">要比較的 <see cref="T:System.Xml.XmlQualifiedName" />。</param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.GetHashCode">
      <summary>傳回 <see cref="T:System.Xml.XmlQualifiedName" /> 的雜湊程式碼。</summary>
      <returns>這個物件的雜湊程式碼。</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.IsEmpty">
      <summary>取得值，指出 <see cref="T:System.Xml.XmlQualifiedName" /> 是否為空白。</summary>
      <returns>如果名稱和命名空間為空白字串，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Name">
      <summary>取得 <see cref="T:System.Xml.XmlQualifiedName" /> 限定名稱的字串表示。</summary>
      <returns>限定名稱的字串表示，如果物件並未定義名稱，則為 String.Empty。</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Namespace">
      <summary>取得 <see cref="T:System.Xml.XmlQualifiedName" /> 命名空間的字串表示。</summary>
      <returns>命名空間的字串表示，如果物件並未定義命名空間，則為 String.Empty。</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Equality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>比較兩個 <see cref="T:System.Xml.XmlQualifiedName" /> 物件。</summary>
      <returns>如果這兩個物件具有相同的名稱和命名空間值，則為 true，否則為 false。</returns>
      <param name="a">要比較的 <see cref="T:System.Xml.XmlQualifiedName" />。</param>
      <param name="b">要比較的 <see cref="T:System.Xml.XmlQualifiedName" />。</param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Inequality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>比較兩個 <see cref="T:System.Xml.XmlQualifiedName" /> 物件。</summary>
      <returns>如果這兩個物件的名稱和命名空間值不同，則為 true，否則為 false。</returns>
      <param name="a">要比較的 <see cref="T:System.Xml.XmlQualifiedName" />。</param>
      <param name="b">要比較的 <see cref="T:System.Xml.XmlQualifiedName" />。</param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString">
      <summary>傳回 <see cref="T:System.Xml.XmlQualifiedName" /> 的字串值。</summary>
      <returns>
        <see cref="T:System.Xml.XmlQualifiedName" /> 的字串值，其格式為 namespace:localname。如果這個物件尚未定義命名空間，則這個方法只傳回區域名稱。</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString(System.String,System.String)">
      <summary>傳回 <see cref="T:System.Xml.XmlQualifiedName" /> 的字串值。</summary>
      <returns>
        <see cref="T:System.Xml.XmlQualifiedName" /> 的字串值，其格式為 namespace:localname。如果這個物件尚未定義命名空間，則這個方法只傳回區域名稱。</returns>
      <param name="name">物件的名稱。</param>
      <param name="ns">物件的命名空間。</param>
    </member>
    <member name="T:System.Xml.XmlReader">
      <summary>表示提供快速、非快取、順向 (Forward-only) 存取 XML 資料的讀取器 (Reader)。若要瀏覽此類型的.NET Framework 原始碼，請參閱參考來源。</summary>
    </member>
    <member name="M:System.Xml.XmlReader.#ctor">
      <summary>初始化 XmlReader 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Xml.XmlReader.AttributeCount">
      <summary>在衍生類別中覆寫時，取得目前節點上的屬性數目。</summary>
      <returns>目前節點的屬性數目。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.BaseURI">
      <summary>在衍生類別中覆寫時，取得目前節點的基底 URI。</summary>
      <returns>目前節點的基底 URI。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadBinaryContent">
      <summary>取得值，這個值表示 <see cref="T:System.Xml.XmlReader" /> 是否會實作二進位內容讀取方法。</summary>
      <returns>如果實作二進位內容讀取方法，則為 true，否則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadValueChunk">
      <summary>取得值，指出 <see cref="T:System.Xml.XmlReader" /> 是否會實作 <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" /> 方法。</summary>
      <returns>true if the <see cref="T:System.Xml.XmlReader" /> implements the <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" /> method; otherwise false.</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanResolveEntity">
      <summary>取得值，指出這個讀取器是否可以剖析和解析實體。</summary>
      <returns>如果讀取器可以剖析和解析實體，則為 true，否則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream)">
      <summary>建立新<see cref="T:System.Xml.XmlReader" />執行個體使用指定的資料流，以預設設定。</summary>
      <returns>用以在資料流中讀取 XML 資料的物件。</returns>
      <param name="input">包含 XML 資料的資料流。<see cref="T:System.Xml.XmlReader" /> 會掃描資料流的前幾個位元組，以尋找位元組順序標記或其他編碼符號。決定編碼後，會使用該編碼繼續讀取資料流，處理流程也會繼續將輸入剖析成 (Unicode) 字元的資料流。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 值為 null。</exception>
      <exception cref="T:System.Security.SecurityException">
        <see cref="T:System.Xml.XmlReader" /> 沒有足夠權限來存取 XML 資料的位置。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings)">
      <summary>建立新<see cref="T:System.Xml.XmlReader" />具有指定的資料流和設定執行個體。</summary>
      <returns>用以在資料流中讀取 XML 資料的物件。</returns>
      <param name="input">包含 XML 資料的資料流。<see cref="T:System.Xml.XmlReader" /> 會掃描資料流的前幾個位元組，以尋找位元組順序標記或其他編碼符號。決定編碼後，會使用該編碼繼續讀取資料流，處理流程也會繼續將輸入剖析成 (Unicode) 字元的資料流。</param>
      <param name="settings">新的設定<see cref="T:System.Xml.XmlReader" />執行個體。這個值可以是 null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 值為 null。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>建立新<see cref="T:System.Xml.XmlReader" />執行個體使用指定的資料流、 設定和內容資訊進行剖析。</summary>
      <returns>用以在資料流中讀取 XML 資料的物件。</returns>
      <param name="input">包含 XML 資料的資料流。<see cref="T:System.Xml.XmlReader" /> 會掃描資料流的前幾個位元組，以尋找位元組順序標記或其他編碼符號。決定編碼後，會使用該編碼繼續讀取資料流，處理流程也會繼續將輸入剖析成 (Unicode) 字元的資料流。</param>
      <param name="settings">新的設定<see cref="T:System.Xml.XmlReader" />執行個體。這個值可以是 null。</param>
      <param name="inputContext">剖析 XML 片段所需的內容資訊。內容資訊可以包含要使用的 <see cref="T:System.Xml.XmlNameTable" />、編碼方式、命名空間範圍、目前的 xml:lang 和 xml:space 範圍、基底 URI，以及文件類型定義。這個值可以是 null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 值為 null。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader)">
      <summary>建立新<see cref="T:System.Xml.XmlReader" />使用指定的文字讀取器的執行個體。</summary>
      <returns>用以在資料流中讀取 XML 資料的物件。</returns>
      <param name="input">要從中讀取 XML 資料的文字閱讀器。因為文字閱讀器會傳回 Unicode 字元的資料流，所以 XML 讀取器不會使用 XML 宣告中所指定的編碼方式，來解碼資料流。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 值為 null。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings)">
      <summary>建立新<see cref="T:System.Xml.XmlReader" />使用指定的文字讀取器和設定的執行個體。</summary>
      <returns>用以在資料流中讀取 XML 資料的物件。</returns>
      <param name="input">要從中讀取 XML 資料的文字閱讀器。因為文字閱讀器會傳回 Unicode 字元的資料流，所以 XML 讀取器不會使用 XML 宣告中所指定的編碼方式，來解碼資料流。</param>
      <param name="settings">新的設定<see cref="T:System.Xml.XmlReader" />。這個值可以是 null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 值為 null。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>建立新<see cref="T:System.Xml.XmlReader" />剖析使用指定的文字讀取器、 設定和內容資訊的執行個體。</summary>
      <returns>用以在資料流中讀取 XML 資料的物件。</returns>
      <param name="input">要從中讀取 XML 資料的文字閱讀器。因為文字閱讀器會傳回 Unicode 字元的資料流，所以 XML 讀取器不會使用 XML 宣告中所指定的編碼方式，來解碼資料流。</param>
      <param name="settings">新的設定<see cref="T:System.Xml.XmlReader" />執行個體。這個值可以是 null。</param>
      <param name="inputContext">剖析 XML 片段所需的內容資訊。內容資訊可以包含要使用的 <see cref="T:System.Xml.XmlNameTable" />、編碼方式、命名空間範圍、目前的 xml:lang 和 xml:space 範圍、基底 URI，以及文件類型定義。這個值可以是 null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 值為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.Xml.XmlReaderSettings.NameTable" /> 和 <see cref="P:System.Xml.XmlParserContext.NameTable" /> 屬性都包含值(這些 NameTable 屬性中只有一個可以設定和使用)。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String)">
      <summary>使用指定的 URI，建立新的 <see cref="T:System.Xml.XmlReader" /> 執行個體。</summary>
      <returns>用以在資料流中讀取 XML 資料的物件。</returns>
      <param name="inputUri">包含 XML 資料之檔案的 URI。<see cref="T:System.Xml.XmlUrlResolver" /> 類別是用來將路徑轉換成正式的資料代表。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputUri" /> 值為 null。</exception>
      <exception cref="T:System.Security.SecurityException">
        <see cref="T:System.Xml.XmlReader" /> 沒有足夠權限來存取 XML 資料的位置。</exception>
      <exception cref="T:System.IO.FileNotFoundException">URI 所識別的檔案不存在。</exception>
      <exception cref="T:System.UriFormatException">在適用於 Windows 市集應用程式的 .NET 或可攜式類別庫中，反而要攔截基底類別例外狀況 <see cref="T:System.FormatException" />。URI 格式不正確。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String,System.Xml.XmlReaderSettings)">
      <summary>建立新<see cref="T:System.Xml.XmlReader" />使用指定的 URI 和設定的執行個體。</summary>
      <returns>用以在資料流中讀取 XML 資料的物件。</returns>
      <param name="inputUri">包含 XML 資料之檔案的 URI。<see cref="T:System.Xml.XmlReaderSettings" /> 物件上的 <see cref="T:System.Xml.XmlResolver" /> 物件是用於將路徑轉換成標準資料表示。如果 <see cref="P:System.Xml.XmlReaderSettings.XmlResolver" /> 為 null，則會使用新的 <see cref="T:System.Xml.XmlUrlResolver" /> 物件。</param>
      <param name="settings">新的設定<see cref="T:System.Xml.XmlReader" />執行個體。這個值可以是 null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputUri" /> 值為 null。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到由 URI 指定的檔案。</exception>
      <exception cref="T:System.UriFormatException">在適用於 Windows 市集應用程式的 .NET 或可攜式類別庫中，反而要攔截基底類別例外狀況 <see cref="T:System.FormatException" />。URI 格式不正確。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.Xml.XmlReader,System.Xml.XmlReaderSettings)">
      <summary>建立新<see cref="T:System.Xml.XmlReader" />使用指定的 XML 讀取器和設定的執行個體。</summary>
      <returns>包裝的物件周圍指定<see cref="T:System.Xml.XmlReader" />物件。</returns>
      <param name="reader">您想要當做基礎 XML 讀取器使用的物件。</param>
      <param name="settings">新的設定<see cref="T:System.Xml.XmlReader" />執行個體。<see cref="T:System.Xml.XmlReaderSettings" /> 物件的一致性層級必須符合基礎讀取器的一致性層級，或是必須設為 <see cref="F:System.Xml.ConformanceLevel.Auto" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> 值為 null。</exception>
      <exception cref="T:System.InvalidOperationException">如果 <see cref="T:System.Xml.XmlReaderSettings" /> 物件指定的一致性層級與基礎讀取器的一致性層級不相符。-或-基礎 <see cref="T:System.Xml.XmlReader" /> 處於 <see cref="F:System.Xml.ReadState.Error" /> 或 <see cref="F:System.Xml.ReadState.Closed" /> 狀態。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Depth">
      <summary>在衍生類別中覆寫時，取得 XML 文件中目前節點的深度。</summary>
      <returns>XML 文件中目前節點的深度。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose">
      <summary>將 <see cref="T:System.Xml.XmlReader" /> 類別目前的執行個體所使用的資源全部釋出。</summary>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose(System.Boolean)">
      <summary>釋放 <see cref="T:System.Xml.XmlReader" /> 所使用的 Unmanaged 資源，並選擇性釋放 Managed 資源。</summary>
      <param name="disposing">true 表示釋放 Managed 和 Unmanaged 資源，false 則表示只釋放 Unmanaged 資源。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.EOF">
      <summary>在衍生類別中覆寫時，取得指出讀取器是否在資料流結尾的值。</summary>
      <returns>如果讀取器定位於資料流結尾，則為 true，否則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.Int32)">
      <summary>在衍生類別中覆寫時，取得具有指定索引的屬性值。</summary>
      <returns>指定的屬性值。這個方法不會移動讀取器。</returns>
      <param name="i">屬性的索引。索引以零為起始。(第一個屬性的索引為 0。)</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="i" /> 超出範圍。它必須是非負值，而且小於屬性集合的大小。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String)">
      <summary>在衍生類別中覆寫時，取得具有指定的 <see cref="P:System.Xml.XmlReader.Name" /> 的屬性值。</summary>
      <returns>指定的屬性值。如果找不到該屬性或其值為 String.Empty，則傳回 null。</returns>
      <param name="name">屬性的限定名稱 (Qualified Name)。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String,System.String)">
      <summary>在衍生類別中覆寫時，取得具有指定的 <see cref="P:System.Xml.XmlReader.LocalName" /> 與 <see cref="P:System.Xml.XmlReader.NamespaceURI" /> 的屬性值。</summary>
      <returns>指定的屬性值。如果找不到該屬性或其值為 String.Empty，則傳回 null。這個方法不會移動讀取器。</returns>
      <param name="name">屬性的本機名稱。</param>
      <param name="namespaceURI">屬性的命名空間 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetValueAsync">
      <summary>非同步取得目前節點的值。</summary>
      <returns>目前節點的值。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.InvalidOperationException">已呼叫 <see cref="T:System.Xml.XmlReader" /> 非同步方法，而未將 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 旗標設定為 true。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和訊息「如果您想要使用非同步方法，將XmlReaderSettings.Async 設為 true」。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasAttributes">
      <summary>取得值，表示目前節點是否具有任何屬性。</summary>
      <returns>如果目前節點擁有屬性，則為 true，否則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasValue">
      <summary>在衍生類別中覆寫時，取得值，指出目前節點是否具有 <see cref="P:System.Xml.XmlReader.Value" />。</summary>
      <returns>如果讀取器目前所在節點具有 Value，則為 true，否則為 false。如果為 false，則節點的值為 String.Empty。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsDefault">
      <summary>在衍生類別中覆寫時，取得值，指出目前節點是否為從 DTD 或結構描述中定義的預設值產生的屬性。</summary>
      <returns>如果目前節點是 DTD 或結構描述中定義的預設值所產生的屬性，則為 true，如果已經明確設定屬性值，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsEmptyElement">
      <summary>在衍生類別中覆寫時，取得值，指出目前節點是否為空項目 (例如，&lt;MyElement/&gt;)。</summary>
      <returns>true if the current node is an element (<see cref="P:System.Xml.XmlReader.NodeType" /> equals XmlNodeType.Element) that ends with /&gt;; otherwise, false.</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsName(System.String)">
      <summary>傳回值，指出字串引數是否為有效的 XML 名稱。</summary>
      <returns>如果名稱有效，則為 true，否則為 false。</returns>
      <param name="str">要驗證的名稱。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="str" /> 值為 null。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsNameToken(System.String)">
      <summary>傳回值，指出字串引數是否為有效的 XML 名稱語彙基元。</summary>
      <returns>如果它是有效的名稱語彙基元，則為 true，否則為 false。</returns>
      <param name="str">要驗證的名稱語彙基元。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="str" /> 值為 null。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement">
      <summary>呼叫 <see cref="M:System.Xml.XmlReader.MoveToContent" /> 並測試目前的內容節點為開頭標記或空項目標記。</summary>
      <returns>如果 <see cref="M:System.Xml.XmlReader.MoveToContent" /> 找到開頭標記或空項目標記，則為 true，如果找到的節點型別並非 XmlNodeType.Element，則為 false。</returns>
      <exception cref="T:System.Xml.XmlException">在輸入資料流中遇到錯誤的 XML。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String)">
      <summary>呼叫 <see cref="M:System.Xml.XmlReader.MoveToContent" /> 並測試目前的內容節點為開頭標記或空項目標記，以及所找到項目的 <see cref="P:System.Xml.XmlReader.Name" /> 屬性是否符合指定的引數。</summary>
      <returns>如果產生的節點是項目，並且 Name 屬性符合指定的字串，則為 true。如果找到的節點型別並非 XmlNodeType.Element 或項目 Name 屬性不符合指定字串，則為 false。</returns>
      <param name="name">字串符合所找到項目的 Name 屬性。</param>
      <exception cref="T:System.Xml.XmlException">在輸入資料流中遇到錯誤的 XML。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String,System.String)">
      <summary>呼叫 <see cref="M:System.Xml.XmlReader.MoveToContent" /> 並測試目前的內容節點為開頭標記或空項目標記，以及所找到項目的 <see cref="P:System.Xml.XmlReader.LocalName" /> 與 <see cref="P:System.Xml.XmlReader.NamespaceURI" /> 屬性是否符合指定的引數。</summary>
      <returns>如果產生的節點是項目，則為 true。如果找到的節點型別並非 XmlNodeType.Element 或項目的 LocalName 和 NamespaceURI 屬性不符合指定字串，則為 false。</returns>
      <param name="localname">字串符合所找到項目的 LocalName 屬性。</param>
      <param name="ns">字串符合所找到項目的 NamespaceURI 屬性。</param>
      <exception cref="T:System.Xml.XmlException">在輸入資料流中遇到錯誤的 XML。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.Int32)">
      <summary>在衍生類別中覆寫時，取得具有指定索引的屬性值。</summary>
      <returns>指定的屬性值。</returns>
      <param name="i">屬性的索引。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String)">
      <summary>在衍生類別中覆寫時，取得具有指定的 <see cref="P:System.Xml.XmlReader.Name" /> 的屬性值。</summary>
      <returns>指定的屬性值。如果找不到屬性，會傳回 null。</returns>
      <param name="name">屬性的限定名稱 (Qualified Name)。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String,System.String)">
      <summary>在衍生類別中覆寫時，取得具有指定的 <see cref="P:System.Xml.XmlReader.LocalName" /> 與 <see cref="P:System.Xml.XmlReader.NamespaceURI" /> 的屬性值。</summary>
      <returns>指定的屬性值。如果找不到屬性，會傳回 null。</returns>
      <param name="name">屬性的本機名稱。</param>
      <param name="namespaceURI">屬性的命名空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.LocalName">
      <summary>在衍生類別中覆寫時，取得目前節點的區域名稱。</summary>
      <returns>目前節點名稱的前置詞被移除。例如，對 &lt;bk:book&gt; 項目而言，LocalName 為 book。對於沒有名稱的節點型別 (如 Text、Comment 等)，這個屬性會傳回 String.Empty。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.LookupNamespace(System.String)">
      <summary>在衍生類別中覆寫時，解析目前項目範圍內的命名空間前置詞。</summary>
      <returns>前置詞對應的命名空間 URI，如果找不到符合的前置詞，則為 null。</returns>
      <param name="prefix">您要解析其命名空間 URI 的前置詞。若要符合預設命名空間，請傳送空字串。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.Int32)">
      <summary>在衍生類別中覆寫時，移至具有指定索引的屬性。</summary>
      <param name="i">屬性的索引。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">參數的值是負數。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String)">
      <summary>在衍生類別中覆寫時，移至具有指定之 <see cref="P:System.Xml.XmlReader.Name" /> 的屬性。</summary>
      <returns>如果找到屬性，則為 true，否則為 false。如果 false，則不會變更讀取器的位置。</returns>
      <param name="name">屬性的限定名稱 (Qualified Name)。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.ArgumentException">參數為空字串。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String,System.String)">
      <summary>在衍生類別中覆寫時，移至具有指定的 <see cref="P:System.Xml.XmlReader.LocalName" /> 和 <see cref="P:System.Xml.XmlReader.NamespaceURI" /> 的屬性。</summary>
      <returns>如果找到屬性，則為 true，否則為 false。如果 false，則不會變更讀取器的位置。</returns>
      <param name="name">屬性的本機名稱。</param>
      <param name="ns">屬性的命名空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.ArgumentNullException">兩個參數值都是 null。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContent">
      <summary>檢查目前節點是否為內容 (非空白區文字、CDATA、Element、EndElement、EntityReference 或 EndEntity) 節點。如果節點並非內容節點，讀取器會先跳至下一個內容節點或檔案結尾。它會略過下列型別的節點：ProcessingInstruction、DocumentType、Comment、Whitespace 或 SignificantWhitespace。</summary>
      <returns>這個方法所找到的目前節點的 <see cref="P:System.Xml.XmlReader.NodeType" />，如果讀取器已經到達輸入資料流的結尾，則為 XmlNodeType.None。</returns>
      <exception cref="T:System.Xml.XmlException">在輸入資料流中遇到錯誤的 XML。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContentAsync">
      <summary>非同步檢查目前節點是否為內容節點。如果節點並非內容節點，讀取器會先跳至下一個內容節點或檔案結尾。</summary>
      <returns>這個方法所找到的目前節點的 <see cref="P:System.Xml.XmlReader.NodeType" />，如果讀取器已經到達輸入資料流的結尾，則為 XmlNodeType.None。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.InvalidOperationException">已呼叫 <see cref="T:System.Xml.XmlReader" /> 非同步方法，而未將 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 旗標設定為 true。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和訊息「如果您想要使用非同步方法，將XmlReaderSettings.Async 設為 true」。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToElement">
      <summary>在衍生類別中覆寫時，移至包含目前屬性節點的項目上。</summary>
      <returns>如果讀取器位於屬性 (讀取器移至擁有該屬性的項目) 上，則為 true，如果讀取器不在屬性 (不會變更讀取器的位置)，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToFirstAttribute">
      <summary>在衍生類別中覆寫時，移至第一個屬性。</summary>
      <returns>如果屬性存在 (讀取器移至第一個屬性)，則為 true，否則為 false (不會變更讀取器的位置)。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToNextAttribute">
      <summary>在衍生類別中覆寫時，移至下一個屬性。</summary>
      <returns>如果有下一個屬性，則為 true，如果沒有其他屬性，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Name">
      <summary>在衍生類別中覆寫時，取得目前節點的限定名稱。</summary>
      <returns>目前節點的限定名稱。例如，對 &lt;bk:book&gt; 項目而言，Name 為 bk:book。傳回的名稱需視節點的 <see cref="P:System.Xml.XmlReader.NodeType" /> 而定。下列節點類型會傳回所列的值。其他所有節點類型都會傳回空字串。節點類型名稱 Attribute屬性的名稱。 DocumentType文件類型名稱。 Element標記名稱。 EntityReference所參考的實體名稱。 ProcessingInstruction處理指示的目標。 XmlDeclarationxml 常值 (Literal) 字串。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NamespaceURI">
      <summary>在衍生類別中覆寫時，取得讀取器所在節點的命名空間 URI (如 W3C 命名空間規格中所定義)。</summary>
      <returns>目前節點的命名空間 URI，否則為空字串。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NameTable">
      <summary>在衍生類別中覆寫時，取得與這個實作相關的 <see cref="T:System.Xml.XmlNameTable" />。</summary>
      <returns>XmlNameTable 可讓您取得節點中字串的原子化版本。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NodeType">
      <summary>在衍生類別中覆寫時，取得目前節點的類型。</summary>
      <returns>其中一個列舉值，指定目前節點的類型。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Prefix">
      <summary>在衍生類別中覆寫時，取得與目前節點相關聯的命名空間前置詞。</summary>
      <returns>與目前節點相關聯的命名空間前置詞。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Read">
      <summary>在衍生類別中覆寫時，從資料流讀取下一個節點。</summary>
      <returns>true如果已成功 ； 讀取下一個節點否則， false。</returns>
      <exception cref="T:System.Xml.XmlException">剖析 XML 時發生錯誤。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAsync">
      <summary>非同步讀取資料流中的下一個節點。</summary>
      <returns>如果成功讀取下一個節點，則為 true，如果沒有其他節點可讀取，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.InvalidOperationException">已呼叫 <see cref="T:System.Xml.XmlReader" /> 非同步方法，而未將 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 旗標設定為 true。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和訊息「如果您想要使用非同步方法，將XmlReaderSettings.Async 設為 true」。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAttributeValue">
      <summary>在衍生類別中覆寫時，將屬性值剖析成一個或多個 Text、EntityReference 或 EndEntity 節點。</summary>
      <returns>如果傳回節點，則為 true。如果在初次呼叫時讀取器不在屬性節點，或者已經讀取全部屬性值，則為 false。空白的屬性 (例如 misc="") 會對含有 String.Empty 值的單一節點傳回 true。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>以指定型别的物件形式讀取內容。</summary>
      <returns>轉換為要求類型的串連文字內容或屬性值。</returns>
      <param name="returnType">要傳回的值型别。附註：使用 .NET Framework 3.5 的版本時，<paramref name="returnType" /> 參數的值現在可以是 <see cref="T:System.DateTimeOffset" /> 型別。</param>
      <param name="namespaceResolver">
        <see cref="T:System.Xml.IXmlNamespaceResolver" /> 物件，用來解析任何與型別轉換相關的命名空間前置詞。例如，將 <see cref="T:System.Xml.XmlQualifiedName" /> 物件轉換為 xs:string 時，可以使用它。這個值可以是 null。</param>
      <exception cref="T:System.FormatException">此內容的目標型別之格式不正確。</exception>
      <exception cref="T:System.InvalidCastException">嘗試的轉換無效。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="returnType" /> 值為 null。</exception>
      <exception cref="T:System.InvalidOperationException">目前節點不是受支援的節點型別。如需詳細資訊，請參閱下表。</exception>
      <exception cref="T:System.OverflowException">讀取 Decimal.MaxValue。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>以非同步的方式讀取做為指定型别之物件的內容。</summary>
      <returns>轉換為要求類型的串連文字內容或屬性值。</returns>
      <param name="returnType">要傳回的值型别。</param>
      <param name="namespaceResolver">
        <see cref="T:System.Xml.IXmlNamespaceResolver" /> 物件，用來解析任何與型別轉換相關的命名空間前置詞。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.InvalidOperationException">已呼叫 <see cref="T:System.Xml.XmlReader" /> 非同步方法，而未將 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 旗標設定為 true。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和訊息「如果您想要使用非同步方法，將XmlReaderSettings.Async 設為 true」。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>讀取內容，並傳回 Base64 已解碼的二進位位元組。</summary>
      <returns>寫入緩衝區的位元組數目。</returns>
      <param name="buffer">將產生的文字複製到其中的緩衝區。這個值不能是 null。</param>
      <param name="index">緩衝區中開始複製結果的位移。</param>
      <param name="count">要複製至緩衝區中的最大位元組數目。從這個方法傳回所複製的實際位元組數目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 值為 null。</exception>
      <exception cref="T:System.InvalidOperationException">目前的節點上不支援 <see cref="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)" />。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">緩衝區的索引或是索引 + 計數大於所配置的緩衝區大小。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.XmlReader" /> 實作不支援這個方法。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>以非同步的方式讀取內容，並傳回 Base64 已解碼的二進位位元組。</summary>
      <returns>寫入緩衝區的位元組數目。</returns>
      <param name="buffer">將產生的文字複製到其中的緩衝區。這個值不能是 null。</param>
      <param name="index">緩衝區中開始複製結果的位移。</param>
      <param name="count">要複製至緩衝區中的最大位元組數目。從這個方法傳回所複製的實際位元組數目。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.InvalidOperationException">已呼叫 <see cref="T:System.Xml.XmlReader" /> 非同步方法，而未將 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 旗標設定為 true。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和訊息「如果您想要使用非同步方法，將XmlReaderSettings.Async 設為 true」。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>讀取內容，並傳回 BinHex 已解碼的二進位位元組。</summary>
      <returns>寫入緩衝區的位元組數目。</returns>
      <param name="buffer">將產生的文字複製到其中的緩衝區。這個值不能是 null。</param>
      <param name="index">緩衝區中開始複製結果的位移。</param>
      <param name="count">要複製至緩衝區中的最大位元組數目。從這個方法傳回所複製的實際位元組數目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 值為 null。</exception>
      <exception cref="T:System.InvalidOperationException">目前的節點上不支援 <see cref="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)" />。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">緩衝區的索引或是索引 + 計數大於所配置的緩衝區大小。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.XmlReader" /> 實作不支援這個方法。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>以非同步的方式讀取內容，並傳回 BinHex 已解碼的二進位位元組。</summary>
      <returns>寫入緩衝區的位元組數目。</returns>
      <param name="buffer">將產生的文字複製到其中的緩衝區。這個值不能是 null。</param>
      <param name="index">緩衝區中開始複製結果的位移。</param>
      <param name="count">要複製至緩衝區中的最大位元組數目。從這個方法傳回所複製的實際位元組數目。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.InvalidOperationException">已呼叫 <see cref="T:System.Xml.XmlReader" /> 非同步方法，而未將 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 旗標設定為 true。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和訊息「如果您想要使用非同步方法，將XmlReaderSettings.Async 設為 true」。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBoolean">
      <summary>以 Boolean 的形式讀取目前位置上的文字內容。</summary>
      <returns>
        <see cref="T:System.Boolean" /> 物件形式的文字內容。</returns>
      <exception cref="T:System.InvalidCastException">嘗試的轉換無效。</exception>
      <exception cref="T:System.FormatException">字串格式無效。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDateTimeOffset">
      <summary>以 <see cref="T:System.DateTimeOffset" /> 物件的形式讀取目前位置的文字內容。</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" /> 物件形式的文字內容。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDecimal">
      <summary>以 <see cref="T:System.Decimal" /> 物件的形式讀取目前位置的文字內容。</summary>
      <returns>
        <see cref="T:System.Decimal" /> 物件形式之目前位置的文字內容。</returns>
      <exception cref="T:System.InvalidCastException">嘗試的轉換無效。</exception>
      <exception cref="T:System.FormatException">字串格式無效。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDouble">
      <summary>以雙精確度浮點數的形式讀取目前位置的文字內容。</summary>
      <returns>雙精確度浮點數形式的文字內容。</returns>
      <exception cref="T:System.InvalidCastException">嘗試的轉換無效。</exception>
      <exception cref="T:System.FormatException">字串格式無效。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsFloat">
      <summary>以單精確度浮點數的形式讀取目前位置的文字內容。</summary>
      <returns>單精確度浮點數形式之目前位置的文字內容。</returns>
      <exception cref="T:System.InvalidCastException">嘗試的轉換無效。</exception>
      <exception cref="T:System.FormatException">字串格式無效。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsInt">
      <summary>以 32 位元帶正負號之整數的形式讀取目前位置的文字內容。</summary>
      <returns>32 位元帶正負號之整數形式的文字內容。</returns>
      <exception cref="T:System.InvalidCastException">嘗試的轉換無效。</exception>
      <exception cref="T:System.FormatException">字串格式無效。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsLong">
      <summary>以 64 位元帶正負號之整數的形式讀取目前位置的文字內容。</summary>
      <returns>64 位元帶正負號之整數形式的文字內容。</returns>
      <exception cref="T:System.InvalidCastException">嘗試的轉換無效。</exception>
      <exception cref="T:System.FormatException">字串格式無效。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObject">
      <summary>以 <see cref="T:System.Object" /> 的形式讀取目前位置的文字內容。</summary>
      <returns>最合適之 Common Language Runtime (CLR) 物件形式的文字內容。</returns>
      <exception cref="T:System.InvalidCastException">嘗試的轉換無效。</exception>
      <exception cref="T:System.FormatException">字串格式無效。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObjectAsync">
      <summary>以 <see cref="T:System.Object" /> 的形式，非同步讀取目前位置的文字內容。</summary>
      <returns>最合適之 Common Language Runtime (CLR) 物件形式的文字內容。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.InvalidOperationException">已呼叫 <see cref="T:System.Xml.XmlReader" /> 非同步方法，而未將 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 旗標設定為 true。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和訊息「如果您想要使用非同步方法，將XmlReaderSettings.Async 設為 true」。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsString">
      <summary>以 <see cref="T:System.String" /> 物件的形式讀取目前位置的文字內容。</summary>
      <returns>
        <see cref="T:System.String" /> 物件形式的文字內容。</returns>
      <exception cref="T:System.InvalidCastException">嘗試的轉換無效。</exception>
      <exception cref="T:System.FormatException">字串格式無效。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsStringAsync">
      <summary>以 <see cref="T:System.String" /> 物件的形式，非同步讀取目前位置的文字內容。</summary>
      <returns>
        <see cref="T:System.String" /> 物件形式的文字內容。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.InvalidOperationException">已呼叫 <see cref="T:System.Xml.XmlReader" /> 非同步方法，而未將 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 旗標設定為 true。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和訊息「如果您想要使用非同步方法，將XmlReaderSettings.Async 設為 true」。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>以要求之類型的形式讀取項目內容。</summary>
      <returns>轉換為要求之類型物件的項目內容。</returns>
      <param name="returnType">要傳回的值型别。附註：使用 .NET Framework 3.5 的版本時，<paramref name="returnType" /> 參數的值現在可以是 <see cref="T:System.DateTimeOffset" /> 型別。</param>
      <param name="namespaceResolver">
        <see cref="T:System.Xml.IXmlNamespaceResolver" /> 物件，用來解析任何與型別轉換相關的命名空間前置詞。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 並不是放置在項目上。</exception>
      <exception cref="T:System.Xml.XmlException">目前的項目包含子項目。-或-項目內容無法轉換成要求的型別。</exception>
      <exception cref="T:System.ArgumentNullException">方法是以 null 引數呼叫。</exception>
      <exception cref="T:System.OverflowException">讀取 Decimal.MaxValue。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver,System.String,System.String)">
      <summary>檢查指定的區域名稱和命名空間 URI 是否與目前的項目相符，然後以要求之類型的形式讀取項目內容。</summary>
      <returns>轉換為要求之類型物件的項目內容。</returns>
      <param name="returnType">要傳回的值型别。附註：使用 .NET Framework 3.5 的版本時，<paramref name="returnType" /> 參數的值現在可以是 <see cref="T:System.DateTimeOffset" /> 型別。</param>
      <param name="namespaceResolver">
        <see cref="T:System.Xml.IXmlNamespaceResolver" /> 物件，用來解析任何與型別轉換相關的命名空間前置詞。</param>
      <param name="localName">項目的本機名稱。</param>
      <param name="namespaceURI">項目的命名空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 並不是放置在項目上。</exception>
      <exception cref="T:System.Xml.XmlException">目前的項目包含子項目。-或-項目內容無法轉換成要求的型別。</exception>
      <exception cref="T:System.ArgumentNullException">方法是以 null 引數呼叫。</exception>
      <exception cref="T:System.ArgumentException">指定的區域名稱和命名空間 URI，與目前正在讀取的項目不相符。</exception>
      <exception cref="T:System.OverflowException">讀取 Decimal.MaxValue。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>以要求之類型的形式，非同步讀取項目內容。</summary>
      <returns>轉換為要求之類型物件的項目內容。</returns>
      <param name="returnType">要傳回的值型别。</param>
      <param name="namespaceResolver">
        <see cref="T:System.Xml.IXmlNamespaceResolver" /> 物件，用來解析任何與型別轉換相關的命名空間前置詞。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.InvalidOperationException">已呼叫 <see cref="T:System.Xml.XmlReader" /> 非同步方法，而未將 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 旗標設定為 true。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和訊息「如果您想要使用非同步方法，將XmlReaderSettings.Async 設為 true」。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>讀取項目，並將 Base64 內容解碼。</summary>
      <returns>寫入緩衝區的位元組數目。</returns>
      <param name="buffer">將產生的文字複製到其中的緩衝區。這個值不能是 null。</param>
      <param name="index">緩衝區中開始複製結果的位移。</param>
      <param name="count">要複製至緩衝區中的最大位元組數目。從這個方法傳回所複製的實際位元組數目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 值為 null。</exception>
      <exception cref="T:System.InvalidOperationException">目前的節點不是項目節點。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">緩衝區的索引或是索引 + 計數大於所配置的緩衝區大小。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.XmlReader" /> 實作不支援這個方法。</exception>
      <exception cref="T:System.Xml.XmlException">項目包含混合內容。</exception>
      <exception cref="T:System.FormatException">內容無法轉換成要求的型別。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>以非同步的方式讀取項目，並將 Base64 內容解碼。</summary>
      <returns>寫入緩衝區的位元組數目。</returns>
      <param name="buffer">將產生的文字複製到其中的緩衝區。這個值不能是 null。</param>
      <param name="index">緩衝區中開始複製結果的位移。</param>
      <param name="count">要複製至緩衝區中的最大位元組數目。從這個方法傳回所複製的實際位元組數目。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.InvalidOperationException">已呼叫 <see cref="T:System.Xml.XmlReader" /> 非同步方法，而未將 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 旗標設定為 true。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和訊息「如果您想要使用非同步方法，將XmlReaderSettings.Async 設為 true」。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>讀取項目，並將 BinHex 內容解碼。</summary>
      <returns>寫入緩衝區的位元組數目。</returns>
      <param name="buffer">將產生的文字複製到其中的緩衝區。這個值不能是 null。</param>
      <param name="index">緩衝區中開始複製結果的位移。</param>
      <param name="count">要複製至緩衝區中的最大位元組數目。從這個方法傳回所複製的實際位元組數目。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 值為 null。</exception>
      <exception cref="T:System.InvalidOperationException">目前的節點不是項目節點。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">緩衝區的索引或是索引 + 計數大於所配置的緩衝區大小。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.XmlReader" /> 實作不支援這個方法。</exception>
      <exception cref="T:System.Xml.XmlException">項目包含混合內容。</exception>
      <exception cref="T:System.FormatException">內容無法轉換成要求的型別。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>以非同步的方式讀取項目，並將 BinHex 內容解碼。</summary>
      <returns>寫入緩衝區的位元組數目。</returns>
      <param name="buffer">將產生的文字複製到其中的緩衝區。這個值不能是 null。</param>
      <param name="index">緩衝區中開始複製結果的位移。</param>
      <param name="count">要複製至緩衝區中的最大位元組數目。從這個方法傳回所複製的實際位元組數目。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.InvalidOperationException">已呼叫 <see cref="T:System.Xml.XmlReader" /> 非同步方法，而未將 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 旗標設定為 true。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和訊息「如果您想要使用非同步方法，將XmlReaderSettings.Async 設為 true」。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean">
      <summary>讀取目前項目，並以 <see cref="T:System.Boolean" /> 物件傳回內容。</summary>
      <returns>做為 <see cref="T:System.Boolean" /> 物件的項目內容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 並不是放置在項目上。</exception>
      <exception cref="T:System.Xml.XmlException">目前的項目包含子項目。-或-項目內容不能轉換為 <see cref="T:System.Boolean" /> 物件。</exception>
      <exception cref="T:System.ArgumentNullException">方法是以 null 引數呼叫。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean(System.String,System.String)">
      <summary>檢查指定的區域名稱和命名空間 URI 是否與目前的項目相符，然後以 <see cref="T:System.Boolean" /> 物件的形式，讀取目前的項目並傳回內容。</summary>
      <returns>做為 <see cref="T:System.Boolean" /> 物件的項目內容。</returns>
      <param name="localName">項目的本機名稱。</param>
      <param name="namespaceURI">項目的命名空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 並不是放置在項目上。</exception>
      <exception cref="T:System.Xml.XmlException">目前的項目包含子項目。-或-項目內容無法轉換成要求的型別。</exception>
      <exception cref="T:System.ArgumentNullException">方法是以 null 引數呼叫。</exception>
      <exception cref="T:System.ArgumentException">指定的區域名稱和命名空間 URI，與目前正在讀取的項目不相符。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal">
      <summary>讀取目前項目，並以 <see cref="T:System.Decimal" /> 物件傳回內容。</summary>
      <returns>做為 <see cref="T:System.Decimal" /> 物件的項目內容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 並不是放置在項目上。</exception>
      <exception cref="T:System.Xml.XmlException">目前的項目包含子項目。-或-項目內容不能轉換為 <see cref="T:System.Decimal" />。</exception>
      <exception cref="T:System.ArgumentNullException">方法是以 null 引數呼叫。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal(System.String,System.String)">
      <summary>檢查指定的區域名稱和命名空間 URI 是否與目前的項目相符，然後以 <see cref="T:System.Decimal" /> 物件的形式，讀取目前的項目並傳回內容。</summary>
      <returns>做為 <see cref="T:System.Decimal" /> 物件的項目內容。</returns>
      <param name="localName">項目的本機名稱。</param>
      <param name="namespaceURI">項目的命名空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 並不是放置在項目上。</exception>
      <exception cref="T:System.Xml.XmlException">目前的項目包含子項目。-或-項目內容不能轉換為 <see cref="T:System.Decimal" />。</exception>
      <exception cref="T:System.ArgumentNullException">方法是以 null 引數呼叫。</exception>
      <exception cref="T:System.ArgumentException">指定的區域名稱和命名空間 URI，與目前正在讀取的項目不相符。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble">
      <summary>以雙精確度浮點數的形式，讀取目前的項目並傳回內容。</summary>
      <returns>雙精確度浮點數形式的項目內容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 並不是放置在項目上。</exception>
      <exception cref="T:System.Xml.XmlException">目前的項目包含子項目。-或-項目內容無法轉換為雙精確度浮點數。</exception>
      <exception cref="T:System.ArgumentNullException">方法是以 null 引數呼叫。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble(System.String,System.String)">
      <summary>檢查指定的區域名稱和命名空間 URI 是否與目前的項目相符，然後以雙精確度浮點數的形式，讀取目前的項目並傳回內容。</summary>
      <returns>雙精確度浮點數形式的項目內容。</returns>
      <param name="localName">項目的本機名稱。</param>
      <param name="namespaceURI">項目的命名空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 並不是放置在項目上。</exception>
      <exception cref="T:System.Xml.XmlException">目前的項目包含子項目。-或-項目內容無法轉換成要求的型別。</exception>
      <exception cref="T:System.ArgumentNullException">方法是以 null 引數呼叫。</exception>
      <exception cref="T:System.ArgumentException">指定的區域名稱和命名空間 URI，與目前正在讀取的項目不相符。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat">
      <summary>以單精確度浮點數的形式，讀取目前的項目並傳回內容。</summary>
      <returns>單精確度浮點數形式的項目內容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 並不是放置在項目上。</exception>
      <exception cref="T:System.Xml.XmlException">目前的項目包含子項目。-或-項目內容無法轉換為單精確度浮點數。</exception>
      <exception cref="T:System.ArgumentNullException">方法是以 null 引數呼叫。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat(System.String,System.String)">
      <summary>檢查指定的區域名稱和命名空間 URI 是否與目前的項目相符，然後以單精確度浮點數的形式，讀取目前的項目並傳回內容。</summary>
      <returns>單精確度浮點數形式的項目內容。</returns>
      <param name="localName">項目的本機名稱。</param>
      <param name="namespaceURI">項目的命名空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 並不是放置在項目上。</exception>
      <exception cref="T:System.Xml.XmlException">目前的項目包含子項目。-或-項目內容無法轉換為單精確度浮點數。</exception>
      <exception cref="T:System.ArgumentNullException">方法是以 null 引數呼叫。</exception>
      <exception cref="T:System.ArgumentException">指定的區域名稱和命名空間 URI，與目前正在讀取的項目不相符。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt">
      <summary>以 32 位元帶正負號之整數的形式，讀取目前的項目並傳回內容。</summary>
      <returns>32 位元帶正負號之整數形式的項目內容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 並不是放置在項目上。</exception>
      <exception cref="T:System.Xml.XmlException">目前的項目包含子項目。-或-項目內容不能轉換為 32 位元帶正負號的整數。</exception>
      <exception cref="T:System.ArgumentNullException">方法是以 null 引數呼叫。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt(System.String,System.String)">
      <summary>檢查指定的區域名稱和命名空間 URI 是否與目前的項目相符，然後以 32 位元帶正負號之整數的形式，讀取目前的項目並傳回內容。</summary>
      <returns>32 位元帶正負號之整數形式的項目內容。</returns>
      <param name="localName">項目的本機名稱。</param>
      <param name="namespaceURI">項目的命名空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 並不是放置在項目上。</exception>
      <exception cref="T:System.Xml.XmlException">目前的項目包含子項目。-或-項目內容不能轉換為 32 位元帶正負號的整數。</exception>
      <exception cref="T:System.ArgumentNullException">方法是以 null 引數呼叫。</exception>
      <exception cref="T:System.ArgumentException">指定的區域名稱和命名空間 URI，與目前正在讀取的項目不相符。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong">
      <summary>以 64 位元帶正負號之整數的形式，讀取目前的項目並傳回內容。</summary>
      <returns>64 位元帶正負號之整數形式的項目內容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 並不是放置在項目上。</exception>
      <exception cref="T:System.Xml.XmlException">目前的項目包含子項目。-或-項目內容不能轉換為 64 位元帶正負號的整數。</exception>
      <exception cref="T:System.ArgumentNullException">方法是以 null 引數呼叫。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong(System.String,System.String)">
      <summary>檢查指定的區域名稱和命名空間 URI 是否與目前的項目相符，然後以 64 位元帶正負號之整數的形式，讀取目前的項目並傳回內容。</summary>
      <returns>64 位元帶正負號之整數形式的項目內容。</returns>
      <param name="localName">項目的本機名稱。</param>
      <param name="namespaceURI">項目的命名空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 並不是放置在項目上。</exception>
      <exception cref="T:System.Xml.XmlException">目前的項目包含子項目。-或-項目內容不能轉換為 64 位元帶正負號的整數。</exception>
      <exception cref="T:System.ArgumentNullException">方法是以 null 引數呼叫。</exception>
      <exception cref="T:System.ArgumentException">指定的區域名稱和命名空間 URI，與目前正在讀取的項目不相符。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject">
      <summary>以 <see cref="T:System.Object" /> 的形式，讀取目前項目並傳回內容。</summary>
      <returns>最合適類型的 Boxed Common Language Runtime (CLR) 物件。<see cref="P:System.Xml.XmlReader.ValueType" /> 屬性會判斷適當的 CLR 型別。如果內容的類型是清單類型，則這個方法會傳回適當類型之 Boxed 物件的陣列。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 並不是放置在項目上。</exception>
      <exception cref="T:System.Xml.XmlException">目前的項目包含子項目。-或-項目內容無法轉換成要求的型別</exception>
      <exception cref="T:System.ArgumentNullException">方法是以 null 引數呼叫。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject(System.String,System.String)">
      <summary>檢查指定的區域名稱和命名空間 URI 是否與目前的項目相符，然後以 <see cref="T:System.Object" /> 的形式，讀取目前的項目並傳回內容。</summary>
      <returns>最合適類型的 Boxed Common Language Runtime (CLR) 物件。<see cref="P:System.Xml.XmlReader.ValueType" /> 屬性會判斷適當的 CLR 型別。如果內容的類型是清單類型，則這個方法會傳回適當類型之 Boxed 物件的陣列。</returns>
      <param name="localName">項目的本機名稱。</param>
      <param name="namespaceURI">項目的命名空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 並不是放置在項目上。</exception>
      <exception cref="T:System.Xml.XmlException">目前的項目包含子項目。-或-項目內容無法轉換成要求的型別。</exception>
      <exception cref="T:System.ArgumentNullException">方法是以 null 引數呼叫。</exception>
      <exception cref="T:System.ArgumentException">指定的區域名稱和命名空間 URI，與目前正在讀取的項目不相符。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObjectAsync">
      <summary>以 <see cref="T:System.Object" /> 的形式，非同步讀取目前項目並傳回內容。</summary>
      <returns>最合適類型的 Boxed Common Language Runtime (CLR) 物件。<see cref="P:System.Xml.XmlReader.ValueType" /> 屬性會判斷適當的 CLR 型別。如果內容的類型是清單類型，則這個方法會傳回適當類型之 Boxed 物件的陣列。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.InvalidOperationException">已呼叫 <see cref="T:System.Xml.XmlReader" /> 非同步方法，而未將 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 旗標設定為 true。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和訊息「如果您想要使用非同步方法，將XmlReaderSettings.Async 設為 true」。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString">
      <summary>讀取目前項目，並以 <see cref="T:System.String" /> 物件傳回內容。</summary>
      <returns>做為 <see cref="T:System.String" /> 物件的項目內容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 並不是放置在項目上。</exception>
      <exception cref="T:System.Xml.XmlException">目前的項目包含子項目。-或-項目內容不能轉換為 <see cref="T:System.String" /> 物件。</exception>
      <exception cref="T:System.ArgumentNullException">方法是以 null 引數呼叫。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString(System.String,System.String)">
      <summary>檢查指定的區域名稱和命名空間 URI 是否與目前的項目相符，然後以 <see cref="T:System.String" /> 物件的形式，讀取目前的項目並傳回內容。</summary>
      <returns>做為 <see cref="T:System.String" /> 物件的項目內容。</returns>
      <param name="localName">項目的本機名稱。</param>
      <param name="namespaceURI">項目的命名空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 並不是放置在項目上。</exception>
      <exception cref="T:System.Xml.XmlException">目前的項目包含子項目。-或-項目內容不能轉換為 <see cref="T:System.String" /> 物件。</exception>
      <exception cref="T:System.ArgumentNullException">方法是以 null 引數呼叫。</exception>
      <exception cref="T:System.ArgumentException">指定的區域名稱和命名空間 URI，與目前正在讀取的項目不相符。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsStringAsync">
      <summary>以非同步的方式讀取目前項目，並以 <see cref="T:System.String" /> 物件傳回內容。</summary>
      <returns>做為 <see cref="T:System.String" /> 物件的項目內容。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.InvalidOperationException">已呼叫 <see cref="T:System.Xml.XmlReader" /> 非同步方法，而未將 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 旗標設定為 true。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和訊息「如果您想要使用非同步方法，將XmlReaderSettings.Async 設為 true」。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadEndElement">
      <summary>檢查目前節點為結尾標記，並使讀取器前進至下一個節點。</summary>
      <exception cref="T:System.Xml.XmlException">目前節點並非結尾標記，或在輸入資料流中遇到錯誤的 XML。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXml">
      <summary>在衍生類別中覆寫時，將所有的內容當做字串讀取，包括標記。</summary>
      <returns>目前節點中所有的 XML 內容，包括標記。如果目前節點沒有子節點，則傳回空字串。如果目前節點既不是項目也不是屬性，則傳回空字串。</returns>
      <exception cref="T:System.Xml.XmlException">XML 不是語式正確的，或在剖析 XML 時發生錯誤。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXmlAsync">
      <summary>以字串形式非同步讀取所有內容，包括標記。</summary>
      <returns>目前節點中所有的 XML 內容，包括標記。如果目前節點沒有子節點，則傳回空字串。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.InvalidOperationException">已呼叫 <see cref="T:System.Xml.XmlReader" /> 非同步方法，而未將 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 旗標設定為 true。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和訊息「如果您想要使用非同步方法，將XmlReaderSettings.Async 設為 true」。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXml">
      <summary>在衍生類別中覆寫時，讀取代表這個節點及其所有子節點的內容，包括標記。</summary>
      <returns>如果讀取器位於項目或屬性節點上，這個方法會傳回目前節點及其所有子節點的所有 XML 內容，包括標記；否則傳回空字串。</returns>
      <exception cref="T:System.Xml.XmlException">XML 不是語式正確的，或在剖析 XML 時發生錯誤。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXmlAsync">
      <summary>非同步讀取表示這個節點及其所有子系的內容，包括標記。</summary>
      <returns>如果讀取器位於項目或屬性節點上，這個方法會傳回目前節點及其所有子節點的所有 XML 內容，包括標記；否則傳回空字串。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.InvalidOperationException">已呼叫 <see cref="T:System.Xml.XmlReader" /> 非同步方法，而未將 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 旗標設定為 true。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和訊息「如果您想要使用非同步方法，將XmlReaderSettings.Async 設為 true」。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement">
      <summary>檢查以確定目前節點為項目，然後使讀取器前進至下一個節點。</summary>
      <exception cref="T:System.Xml.XmlException">在輸入資料流中遇到錯誤的 XML。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String)">
      <summary>檢查目前節點為具有指定的 <see cref="P:System.Xml.XmlReader.Name" /> 的項目，並使讀取器前進至下一個節點。</summary>
      <param name="name">項目的限定名稱。</param>
      <exception cref="T:System.Xml.XmlException">在輸入資料流中遇到錯誤的 XML。-或-項目的 <see cref="P:System.Xml.XmlReader.Name" /> 不符合給指定的 <paramref name="name" />。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String,System.String)">
      <summary>檢查目前內容節點為具有指定的 <see cref="P:System.Xml.XmlReader.LocalName" /> 與 <see cref="P:System.Xml.XmlReader.NamespaceURI" /> 的項目，並使讀取器進至下一個節點。</summary>
      <param name="localname">項目的本機名稱。</param>
      <param name="ns">項目的命名空間 URI。</param>
      <exception cref="T:System.Xml.XmlException">在輸入資料流中遇到錯誤的 XML。-或-找到的項目的 <see cref="P:System.Xml.XmlReader.LocalName" /> 和 <see cref="P:System.Xml.XmlReader.NamespaceURI" /> 屬性不符合指定的引數。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ReadState">
      <summary>在衍生類別中覆寫時，取得讀取器的狀態。</summary>
      <returns>其中一個列舉值，這個值指定讀取器的狀態。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadSubtree">
      <summary>傳回新的 XmlReader 執行個體，可用於讀取目前的節點及其所有子代 (Descendant)。</summary>
      <returns>新的 XML 讀取器執行個體設定為<see cref="F:System.Xml.ReadState.Initial" />。呼叫<see cref="M:System.Xml.XmlReader.Read" />方法會將新的讀取器置於呼叫之前為目前的節點<see cref="M:System.Xml.XmlReader.ReadSubtree" />方法。</returns>
      <exception cref="T:System.InvalidOperationException">XML 讀取器未置於項目時呼叫這個方法。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String)">
      <summary>將 <see cref="T:System.Xml.XmlReader" /> 前進至下一個具有指定限定名稱的子代項目。</summary>
      <returns>如果已找到相符的子代項目，則為 true，否則為 false。如果找不到相符的子項目，則 <see cref="T:System.Xml.XmlReader" /> 會置於項目的結束標記上 (<see cref="P:System.Xml.XmlReader.NodeType" /> 為 XmlNodeType.EndElement)。如果呼叫 <see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String)" /> 時，<see cref="T:System.Xml.XmlReader" /> 並未置於項目上，這個方法會傳回 false，而 <see cref="T:System.Xml.XmlReader" /> 的位置不變。</returns>
      <param name="name">您要移至之項目的限定名稱。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.ArgumentException">參數為空字串。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)">
      <summary>將 <see cref="T:System.Xml.XmlReader" /> 前進至下一個具有指定區域名稱和命名空間 URI 的子代項目。</summary>
      <returns>如果已找到相符的子代項目，則為 true，否則為 false。如果找不到相符的子項目，則 <see cref="T:System.Xml.XmlReader" /> 會置於項目的結束標記上 (<see cref="P:System.Xml.XmlReader.NodeType" /> 為 XmlNodeType.EndElement)。If the <see cref="T:System.Xml.XmlReader" /> is not positioned on an element when <see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)" /> was called, this method returns false and the position of the <see cref="T:System.Xml.XmlReader" /> is not changed.</returns>
      <param name="localName">您要移至之項目的本機名稱。</param>
      <param name="namespaceURI">您要移至之項目的命名空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.ArgumentNullException">兩個參數值都是 null。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String)">
      <summary>在找到具有指定限定名稱的項目之前讀取。</summary>
      <returns>如果已找到相符的項目，則為 true，否則為 false，且 <see cref="T:System.Xml.XmlReader" /> 處於檔案結尾 (EOF) 狀態。</returns>
      <param name="name">項目的限定名稱。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.ArgumentException">參數為空字串。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String,System.String)">
      <summary>在找到具有指定區域名稱和命名空間 URI 的項目之前讀取。</summary>
      <returns>如果已找到相符的項目，則為 true，否則為 false，且 <see cref="T:System.Xml.XmlReader" /> 處於檔案結尾 (EOF) 狀態。</returns>
      <param name="localName">項目的本機名稱。</param>
      <param name="namespaceURI">項目的命名空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.ArgumentNullException">兩個參數值都是 null。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String)">
      <summary>將 XmlReader 前進至下一個具有指定限定名稱的同層級項目。</summary>
      <returns>如果已找到相符的同層級項目，則為 true，否則為 false。如果找不到相符的同層級項目，則 XmlReader 會置於父項目的結束標記上 (<see cref="P:System.Xml.XmlReader.NodeType" /> 為 XmlNodeType.EndElement)。</returns>
      <param name="name">您要移至之同層級項目的限定名稱。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.ArgumentException">參數為空字串。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String,System.String)">
      <summary>將 XmlReader 前進至下一個具有指定區域名稱和命名空間 URI 的同層級項目。</summary>
      <returns>如果找到相符的同層級項目，則為 true，否則為 false。如果找不到相符的同層級項目，則 XmlReader 會置於父項目的結束標記上 (<see cref="P:System.Xml.XmlReader.NodeType" /> 為 XmlNodeType.EndElement)。</returns>
      <param name="localName">您要移至之同層級項目的本機名稱。</param>
      <param name="namespaceURI">您要移至之同層級項目的命名空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.ArgumentNullException">兩個參數值都是 null。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)">
      <summary>讀取 XML 文件中內嵌之大量文字資料流。</summary>
      <returns>讀入緩衝區的字元數目。當不再有文字內容時，會傳回零的值。</returns>
      <param name="buffer">做為寫入文字內容之緩衝區的字元陣列。這個值不能是 null。</param>
      <param name="index">緩衝區中 <see cref="T:System.Xml.XmlReader" /> 開始複製結果的位移。</param>
      <param name="count">要複製至緩衝區中的最大字元數目。從這個方法傳回所複製的實際字元數目。</param>
      <exception cref="T:System.InvalidOperationException">目前的節點沒有值 (<see cref="P:System.Xml.XmlReader.HasValue" /> 為 false)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 值為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">緩衝區的索引或是索引 + 計數大於所配置的緩衝區大小。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.XmlReader" /> 實作不支援這個方法。</exception>
      <exception cref="T:System.Xml.XmlException">XML 資料的語式不正確。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunkAsync(System.Char[],System.Int32,System.Int32)">
      <summary>以非同步的方式讀取 XML 文件中內嵌之大量文字資料流。</summary>
      <returns>讀入緩衝區的字元數目。當不再有文字內容時，會傳回零的值。</returns>
      <param name="buffer">做為寫入文字內容之緩衝區的字元陣列。這個值不能是 null。</param>
      <param name="index">緩衝區中 <see cref="T:System.Xml.XmlReader" /> 開始複製結果的位移。</param>
      <param name="count">要複製至緩衝區中的最大字元數目。從這個方法傳回所複製的實際字元數目。</param>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.InvalidOperationException">已呼叫 <see cref="T:System.Xml.XmlReader" /> 非同步方法，而未將 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 旗標設定為 true。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和訊息「如果您想要使用非同步方法，將XmlReaderSettings.Async 設為 true」。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ResolveEntity">
      <summary>在衍生類別中覆寫時，解析 EntityReference 節點的實體參考。</summary>
      <exception cref="T:System.InvalidOperationException">讀取器並非位於 EntityReference 節點上；這個讀取器實作無法解析實體 (<see cref="P:System.Xml.XmlReader.CanResolveEntity" /> 傳回 false)。</exception>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Settings">
      <summary>取得 <see cref="T:System.Xml.XmlReaderSettings" /> 物件，用於建立這個 <see cref="T:System.Xml.XmlReader" /> 執行個體。</summary>
      <returns>
        <see cref="T:System.Xml.XmlReaderSettings" /> 物件，用於建立這個讀取器執行個體。如果未使用 <see cref="Overload:System.Xml.XmlReader.Create" /> 方法建立這個讀取器，則這個屬性會傳回 null。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Skip">
      <summary>略過目前節點的子節點。</summary>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.SkipAsync">
      <summary>以非同步的方式略過目前節點的子節點。</summary>
      <returns>目前節點。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
      <exception cref="T:System.InvalidOperationException">已呼叫 <see cref="T:System.Xml.XmlReader" /> 非同步方法，而未將 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 旗標設定為 true。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和訊息「如果您想要使用非同步方法，將XmlReaderSettings.Async 設為 true」。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Value">
      <summary>在衍生類別中覆寫時，取得目前節點的文字值。</summary>
      <returns>傳回值需視節點的 <see cref="P:System.Xml.XmlReader.NodeType" /> 而定。下表列出具有傳回值的節點類型。所有其他節點型別都會傳回 String.Empty。節點類型值 Attribute屬性的值。 CDATACDATA 區段的內容。 Comment註解的內容。 DocumentType內部子集。 ProcessingInstruction除了目標之外的完整內容。 SignificantWhitespace在混合內容模型中標記間的泛空白字元。 Text文字節點的內容。 Whitespace標記間的泛空白字元。 XmlDeclaration宣告的內容。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ValueType">
      <summary>取得目前節點的 Common Language Runtime (CLR) 類型。</summary>
      <returns>CLR 類型，對應至節點的具類型值。預設值為 System.String。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlLang">
      <summary>在衍生類別中覆寫時，取得目前的 xml:lang 範圍。</summary>
      <returns>目前的 xml:lang 範圍。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlSpace">
      <summary>在衍生類別中覆寫時，取得目前的 xml:space 範圍。</summary>
      <returns>其中一個 <see cref="T:System.Xml.XmlSpace" /> 值。如果 xml:space 範圍不存在，這個屬性預設值為 XmlSpace.None。</returns>
      <exception cref="T:System.InvalidOperationException">已在上一個非同步作業完成之前呼叫 <see cref="T:System.Xml.XmlReader" /> 方法。在此情況下，就會擲回<see cref="T:System.InvalidOperationException" />和「非同步作業已在進行中。」訊息。</exception>
    </member>
    <member name="T:System.Xml.XmlReaderSettings">
      <summary>指定要在由 <see cref="Overload:System.Xml.XmlReader.Create" /> 方法建立的 <see cref="T:System.Xml.XmlReader" /> 物件上支援的一組功能。</summary>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.#ctor">
      <summary>初始化 <see cref="T:System.Xml.XmlReaderSettings" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.Async">
      <summary>取得或設定非同步 <see cref="T:System.Xml.XmlReader" /> 方法是否可以用於特定 <see cref="T:System.Xml.XmlReader" /> 執行個體。</summary>
      <returns>如果可以使用非同步方法，則為 true，否則為false。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CheckCharacters">
      <summary>取得或設定值，綁表示是否要執行字元檢查。</summary>
      <returns>true 表示執行字元檢查，否則為 false。預設值為 true。注意事項如果 <see cref="T:System.Xml.XmlReader" /> 正在處理文字資料，則它會始終檢查 XML 名稱和文字內容是否有效，而不論屬性設定。將 <see cref="P:System.Xml.XmlReaderSettings.CheckCharacters" /> 設為 false 會關閉字元實體參考的字元檢查。</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Clone">
      <summary>建立 <see cref="T:System.Xml.XmlReaderSettings" /> 執行個體的複本。</summary>
      <returns>複製的 <see cref="T:System.Xml.XmlReaderSettings" /> 物件。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CloseInput">
      <summary>取得或設定值，指出是否應在關閉讀取器時關閉基礎資料流或 <see cref="T:System.IO.TextReader" />。</summary>
      <returns>true 表示關閉讀取器時關閉基礎資料流或 <see cref="T:System.IO.TextReader" />，否則為 false。預設值為 false。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.ConformanceLevel">
      <summary>取得或設定 <see cref="T:System.Xml.XmlReader" /> 將遵循的一致性層級。</summary>
      <returns>其中一個列舉值，指定 XML 讀取器將強制執行的一致性層級。預設值為 <see cref="F:System.Xml.ConformanceLevel.Document" />。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.DtdProcessing">
      <summary>取得或設定決定 DTD 處理的值。</summary>
      <returns>其中一個列舉值，決定 DTD 處理方式。預設值為 <see cref="F:System.Xml.DtdProcessing.Prohibit" />。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreComments">
      <summary>取得或設定值，指出是否忽略註解。</summary>
      <returns>true 表示忽略註解，否則為 false。預設值為 false。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreProcessingInstructions">
      <summary>取得或設定值，指出是否忽略處理指示。</summary>
      <returns>true 表示忽略處理指示，否則為 false。預設值為 false。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreWhitespace">
      <summary>取得或設定值，指出是否忽略不重要的空白字元。</summary>
      <returns>true 表示忽略泛空白字元，否則為 false。預設值為 false。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LineNumberOffset">
      <summary>取得或設定 <see cref="T:System.Xml.XmlReader" /> 物件中的行號位移。</summary>
      <returns>行號位移。預設值為 0。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LinePositionOffset">
      <summary>取得或設定 <see cref="T:System.Xml.XmlReader" /> 物件中的行位置位移。</summary>
      <returns>行位置位移。預設值為 0。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersFromEntities">
      <summary>取得或設定值，指出文件中產生自展開實體的最大可允許字元數。</summary>
      <returns>來自展開實體的最大可允許字元數。預設值為 0。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersInDocument">
      <summary>取得或設定值，指出 XML 文件中最大可允許字元數。零 (0) 的值表示對 XML 文件大小沒有限制。非零值指定大小上限，以字元為單位。</summary>
      <returns>XML 文件的最大可允許字元數。預設值為 0。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.NameTable">
      <summary>取得或設定用於原子化字串比較的 <see cref="T:System.Xml.XmlNameTable" />。</summary>
      <returns>
        <see cref="T:System.Xml.XmlNameTable" />，儲存使用這個 <see cref="T:System.Xml.XmlReaderSettings" /> 物件建立之所有 <see cref="T:System.Xml.XmlReader" /> 執行個體所使用的所有原子化字串。預設值為 null。如果這個值為 null，則建立的 <see cref="T:System.Xml.XmlReader" /> 執行個體會使用新的空 <see cref="T:System.Xml.NameTable" />。</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Reset">
      <summary>將設定類別的成員重設為其預設值。</summary>
    </member>
    <member name="T:System.Xml.XmlSpace">
      <summary>取得目前的 xml:space 範圍。</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Default">
      <summary>xml:space 範圍等於 default。</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.None">
      <summary>無 xml:space 範圍。</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Preserve">
      <summary>xml:space 範圍等於 preserve。</summary>
    </member>
    <member name="T:System.Xml.XmlWriter">
      <summary>表示寫入器，其可提供快速、非快取的順向方法來產生含有 XML 資料之資料流或檔案。</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.#ctor">
      <summary>初始化 <see cref="T:System.Xml.XmlWriter" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream)">
      <summary>使用指定的資料流，建立新 <see cref="T:System.Xml.XmlWriter" /> 執行個體。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 物件。</returns>
      <param name="output">要寫入其中的資料流。<see cref="T:System.Xml.XmlWriter" /> 會寫入 XML 1.0 文字語法，並將其附加至指定的資料流。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream,System.Xml.XmlWriterSettings)">
      <summary>使用資料流和 <see cref="T:System.Xml.XmlWriterSettings" /> 物件，建立新 <see cref="T:System.Xml.XmlWriter" /> 執行個體。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 物件。</returns>
      <param name="output">要寫入其中的資料流。<see cref="T:System.Xml.XmlWriter" /> 會寫入 XML 1.0 文字語法，並將其附加至指定的資料流。</param>
      <param name="settings">用於設定新 <see cref="T:System.Xml.XmlWriter" /> 執行個體的 <see cref="T:System.Xml.XmlWriterSettings" /> 物件。如果是 null，則會使用有預設值的 <see cref="T:System.Xml.XmlWriterSettings" />。如果 <see cref="T:System.Xml.XmlWriter" /> 正配合 <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> 方法使用，您應該使用 <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> 屬性，以取得有正確設定的 <see cref="T:System.Xml.XmlWriterSettings" /> 物件。如此可確保所建立的 <see cref="T:System.Xml.XmlWriter" /> 物件具有正確的輸出設定。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter)">
      <summary>使用指定的 <see cref="T:System.IO.TextWriter" />，建立新 <see cref="T:System.Xml.XmlWriter" /> 執行個體。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 物件。</returns>
      <param name="output">要寫入至其中的 <see cref="T:System.IO.TextWriter" />。<see cref="T:System.Xml.XmlWriter" /> 會寫入 XML 1.0 文字語法，並將其附加至指定的 <see cref="T:System.IO.TextWriter" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter,System.Xml.XmlWriterSettings)">
      <summary>使用 <see cref="T:System.IO.TextWriter" /> 和 <see cref="T:System.Xml.XmlWriterSettings" /> 物件，建立新的 <see cref="T:System.Xml.XmlWriter" /> 執行個體。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 物件。</returns>
      <param name="output">要寫入至其中的 <see cref="T:System.IO.TextWriter" />。<see cref="T:System.Xml.XmlWriter" /> 會寫入 XML 1.0 文字語法，並將其附加至指定的 <see cref="T:System.IO.TextWriter" />。</param>
      <param name="settings">用於設定新 <see cref="T:System.Xml.XmlWriter" /> 執行個體的 <see cref="T:System.Xml.XmlWriterSettings" /> 物件。如果是 null，則會使用有預設值的 <see cref="T:System.Xml.XmlWriterSettings" />。如果 <see cref="T:System.Xml.XmlWriter" /> 正配合 <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> 方法使用，您應該使用 <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> 屬性，以取得有正確設定的 <see cref="T:System.Xml.XmlWriterSettings" /> 物件。如此可確保所建立的 <see cref="T:System.Xml.XmlWriter" /> 物件具有正確的輸出設定。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder)">
      <summary>使用指定的 <see cref="T:System.Text.StringBuilder" />，建立新 <see cref="T:System.Xml.XmlWriter" /> 執行個體。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 物件。</returns>
      <param name="output">要寫入至其中的 <see cref="T:System.Text.StringBuilder" />。<see cref="T:System.Xml.XmlWriter" /> 寫入的內容會附加至 <see cref="T:System.Text.StringBuilder" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder,System.Xml.XmlWriterSettings)">
      <summary>使用 <see cref="T:System.Text.StringBuilder" /> 和 <see cref="T:System.Xml.XmlWriterSettings" /> 物件，建立新的 <see cref="T:System.Xml.XmlWriter" /> 執行個體。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 物件。</returns>
      <param name="output">要寫入至其中的 <see cref="T:System.Text.StringBuilder" />。<see cref="T:System.Xml.XmlWriter" /> 寫入的內容會附加至 <see cref="T:System.Text.StringBuilder" />。</param>
      <param name="settings">用於設定新 <see cref="T:System.Xml.XmlWriter" /> 執行個體的 <see cref="T:System.Xml.XmlWriterSettings" /> 物件。如果是 null，則會使用有預設值的 <see cref="T:System.Xml.XmlWriterSettings" />。如果 <see cref="T:System.Xml.XmlWriter" /> 正配合 <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> 方法使用，您應該使用 <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> 屬性，以取得有正確設定的 <see cref="T:System.Xml.XmlWriterSettings" /> 物件。如此可確保所建立的 <see cref="T:System.Xml.XmlWriter" /> 物件具有正確的輸出設定。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter)">
      <summary>使用指定的 <see cref="T:System.Xml.XmlWriter" />，建立新 <see cref="T:System.Xml.XmlWriter" /> 執行個體。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 物件，包裝於指定的 <see cref="T:System.Xml.XmlWriter" /> 物件附近。</returns>
      <param name="output">您想要當做基礎寫入器使用的 <see cref="T:System.Xml.XmlWriter" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter,System.Xml.XmlWriterSettings)">
      <summary>使用指定的 <see cref="T:System.Xml.XmlWriter" /> 和 <see cref="T:System.Xml.XmlWriterSettings" /> 物件，建立新 <see cref="T:System.Xml.XmlWriter" /> 執行個體。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 物件，包裝於指定的 <see cref="T:System.Xml.XmlWriter" /> 物件附近。</returns>
      <param name="output">您想要當做基礎寫入器使用的 <see cref="T:System.Xml.XmlWriter" /> 物件。</param>
      <param name="settings">用於設定新 <see cref="T:System.Xml.XmlWriter" /> 執行個體的 <see cref="T:System.Xml.XmlWriterSettings" /> 物件。如果是 null，則會使用有預設值的 <see cref="T:System.Xml.XmlWriterSettings" />。如果 <see cref="T:System.Xml.XmlWriter" /> 正配合 <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> 方法使用，您應該使用 <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> 屬性，以取得有正確設定的 <see cref="T:System.Xml.XmlWriterSettings" /> 物件。如此可確保所建立的 <see cref="T:System.Xml.XmlWriter" /> 物件具有正確的輸出設定。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose">
      <summary>將 <see cref="T:System.Xml.XmlWriter" /> 類別目前的執行個體所使用的資源全部釋出。</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose(System.Boolean)">
      <summary>釋放 <see cref="T:System.Xml.XmlWriter" /> 所使用的 Unmanaged 資源，並選擇性釋放 Managed 資源。</summary>
      <param name="disposing">true 表示會同時釋放 Managed 和 Unmanaged 資源，false 則表示只釋放 Unmanaged 資源。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Flush">
      <summary>在衍生類別中覆寫時，將緩衝區的所有內容清空至基礎資料流，然後清空基礎資料流。</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.FlushAsync">
      <summary>以非同步的方式將緩衝區的所有內容清空至基礎資料流，然後清空基礎資料流。</summary>
      <returns>表示非同步 Flush 作業的工作。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.LookupPrefix(System.String)">
      <summary>在衍生類別中覆寫時，傳回最接近命名空間 URI 在目前命名空間範圍中定義的前置詞。</summary>
      <returns>命名空間前置詞；如果在目前範圍中找不到符合的命名空間 URI，則為 null。</returns>
      <param name="ns">您要尋找其前置詞的命名空間 URI。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="ns" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.Settings">
      <summary>取得 <see cref="T:System.Xml.XmlWriterSettings" /> 物件，用於建立這個 <see cref="T:System.Xml.XmlWriter" /> 執行個體。</summary>
      <returns>用於建立這個寫入器執行個體的 <see cref="T:System.Xml.XmlWriterSettings" /> 物件。如果未使用 <see cref="Overload:System.Xml.XmlWriter.Create" /> 方法建立這個寫入器，則這個屬性會傳回 null。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributes(System.Xml.XmlReader,System.Boolean)">
      <summary>在衍生類別中覆寫時，寫出在 <see cref="T:System.Xml.XmlReader" /> 的目前位置找到的所有屬性。</summary>
      <param name="reader">要複製屬性的 XmlReader。</param>
      <param name="defattr">若要從 XmlReader 複製預設屬性，則為 true，否則為 false。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null. </exception>
      <exception cref="T:System.Xml.XmlException">The reader is not positioned on an element, attribute or XmlDeclaration node. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributesAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>以非同步的方式寫出在 <see cref="T:System.Xml.XmlReader" /> 中的目前位置找到的所有屬性。</summary>
      <returns>表示非同步 WriteAttributes 作業的工作。</returns>
      <param name="reader">要複製屬性的 XmlReader。</param>
      <param name="defattr">若要從 XmlReader 複製預設屬性，則為 true，否則為 false。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String)">
      <summary>在衍生類別中覆寫時，寫出具有指定的區域名稱與數值的屬性。</summary>
      <param name="localName">屬性的本機名稱。</param>
      <param name="value">屬性的值。</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String)">
      <summary>在衍生類別中覆寫時，寫入具有指定區域名稱、命名空間 URI 和值的屬性。</summary>
      <param name="localName">屬性的本機名稱。</param>
      <param name="ns">與屬性相關聯的命名空間 URI。</param>
      <param name="value">屬性的值。</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String,System.String)">
      <summary>在衍生類別中覆寫時，寫出具有指定的前置詞、區域名稱、命名空間 URI 及其值的屬性。</summary>
      <param name="prefix">屬性的命名空間前置詞。</param>
      <param name="localName">屬性的本機名稱。</param>
      <param name="ns">屬性的命名空間 URI。</param>
      <param name="value">屬性的值。</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.Xml.XmlException">The <paramref name="localName" /> or <paramref name="ns" /> is null. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeStringAsync(System.String,System.String,System.String,System.String)">
      <summary>以非同步的方式寫出具有指定之前置詞、區域名稱、命名空間 URI 和值的屬性。</summary>
      <returns>表示非同步 WriteAttributeString 作業的工作。</returns>
      <param name="prefix">屬性的命名空間前置詞。</param>
      <param name="localName">屬性的本機名稱。</param>
      <param name="ns">屬性的命名空間 URI。</param>
      <param name="value">屬性的值。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>在衍生類別中覆寫時，以 Base64 格式編碼指定的二進位位元組，並寫出產生的文字。</summary>
      <param name="buffer">要編碼的位元組陣列。</param>
      <param name="index">緩衝區中的位置指示要寫入的位元組開頭。</param>
      <param name="count">要寫入的位元組數。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>以非同步的方式將指定的二進位位元組編碼為 base64 並寫出產生的文字。</summary>
      <returns>表示非同步 WriteBase64 作業的工作。</returns>
      <param name="buffer">要編碼的位元組陣列。</param>
      <param name="index">緩衝區中的位置指示要寫入的位元組開頭。</param>
      <param name="count">要寫入的位元組數。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>在衍生類別中覆寫時，以 BinHex 格式編碼指定的二進位位元組，並寫出產生的文字。</summary>
      <param name="buffer">要編碼的位元組陣列。</param>
      <param name="index">緩衝區中的位置指示要寫入的位元組開頭。</param>
      <param name="count">要寫入的位元組數。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>以非同步的方式將指定的二進位位元組編碼為 BinHex 並寫出產生的文字。</summary>
      <returns>表示非同步 WriteBinHex 作業的工作。</returns>
      <param name="buffer">要編碼的位元組陣列。</param>
      <param name="index">緩衝區中的位置指示要寫入的位元組開頭。</param>
      <param name="count">要寫入的位元組數。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCData(System.String)">
      <summary>在衍生類別中覆寫時，寫出包含指定文字的 &lt;![CDATA[...]]&gt; 區塊。</summary>
      <param name="text">要放在 CDATA 區塊中的文字。</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCDataAsync(System.String)">
      <summary>以非同步的方式寫出包含指定文字的 &lt;![CDATA[...]]&gt; 區塊。</summary>
      <returns>表示非同步 WriteCData 作業的工作。</returns>
      <param name="text">要放在 CDATA 區塊中的文字。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntity(System.Char)">
      <summary>在衍生類別中覆寫時，強制產生指定之 Unicode 字元值的字元實體。</summary>
      <param name="ch">要產生字元實體的 Unicode 字元。</param>
      <exception cref="T:System.ArgumentException">The character is in the surrogate pair character range, 0xd800 - 0xdfff.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntityAsync(System.Char)">
      <summary>以非同步的方式強制產生指定的 Unicode 字元值的字元實體。</summary>
      <returns>表示非同步 WriteCharEntity 作業的工作。</returns>
      <param name="ch">要產生字元實體的 Unicode 字元。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteChars(System.Char[],System.Int32,System.Int32)">
      <summary>在衍生類別中覆寫時，一次將文字寫入一個緩衝區。</summary>
      <param name="buffer">包含要寫入之文字的字元陣列。</param>
      <param name="index">緩衝區中的位置指示要寫入的文字開頭。</param>
      <param name="count">要寫入的字元數。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />; the call results in surrogate pair characters being split or an invalid surrogate pair being written.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="buffer" /> parameter value is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharsAsync(System.Char[],System.Int32,System.Int32)">
      <summary>以非同步的方式一次將文字寫入一個緩衝區。</summary>
      <returns>表示非同步 WriteChars 作業的工作。</returns>
      <param name="buffer">包含要寫入之文字的字元陣列。</param>
      <param name="index">緩衝區中的位置指示要寫入的文字開頭。</param>
      <param name="count">要寫入的字元數。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteComment(System.String)">
      <summary>在衍生類別中覆寫時，寫出包含指定文字的註解 &lt;!--...--&gt;。</summary>
      <param name="text">要放入註解中的文字。</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well-formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCommentAsync(System.String)">
      <summary>以非同步的方式寫出包含指定之文字的註解 &lt;!--...--&gt;。</summary>
      <returns>表示非同步 WriteComment 作業的工作。</returns>
      <param name="text">要放入註解中的文字。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocType(System.String,System.String,System.String,System.String)">
      <summary>在衍生類別中覆寫時，寫入具有指定名稱與選擇性屬性的 DOCTYPE 宣告。</summary>
      <param name="name">DOCTYPE 名稱。這必須不是空白的。</param>
      <param name="pubid">如果為非 null，它也會寫入 PUBLIC "pubid" "sysid"，其中 <paramref name="pubid" /> 與 <paramref name="sysid" /> 會替換為指定之引數的值。</param>
      <param name="sysid">如果 <paramref name="pubid" /> 是 null，而 <paramref name="sysid" /> 為非 null，則它會寫入 SYSTEM "sysid"，其中 <paramref name="sysid" /> 會由這個引數的值所取代。</param>
      <param name="subset">如果非 Null，它會寫入 [subset]，其中 subset 由這個引數的值來替代。</param>
      <exception cref="T:System.InvalidOperationException">This method was called outside the prolog (after the root element). </exception>
      <exception cref="T:System.ArgumentException">The value for <paramref name="name" /> would result in invalid XML.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocTypeAsync(System.String,System.String,System.String,System.String)">
      <summary>以非同步的方式寫入具有指定名稱與選擇性屬性的 DOCTYPE 宣告。</summary>
      <returns>表示非同步 WriteDocType 作業的工作。</returns>
      <param name="name">DOCTYPE 名稱。這必須不是空白的。</param>
      <param name="pubid">如果為非 null，它也會寫入 PUBLIC "pubid" "sysid"，其中 <paramref name="pubid" /> 與 <paramref name="sysid" /> 會替換為指定之引數的值。</param>
      <param name="sysid">如果 <paramref name="pubid" /> 是 null，而 <paramref name="sysid" /> 為非 null，則它會寫入 SYSTEM "sysid"，其中 <paramref name="sysid" /> 會由這個引數的值所取代。</param>
      <param name="subset">如果非 Null，它會寫入 [subset]，其中 subset 由這個引數的值來替代。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String)">
      <summary>寫入具有指定之區域名稱和值的項目。</summary>
      <param name="localName">項目的本機名稱。</param>
      <param name="value">項目的值。</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String)">
      <summary>寫入具有指定之區域名稱、命名空間 URI 和值的項目。</summary>
      <param name="localName">項目的本機名稱。</param>
      <param name="ns">與項目相關聯的命名空間 URI。</param>
      <param name="value">項目的值。</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String,System.String)">
      <summary>寫入具有指定的前置詞、區域名稱、命名空間 URI 和值的項目。</summary>
      <param name="prefix">項目的前置詞。</param>
      <param name="localName">項目的本機名稱。</param>
      <param name="ns">項目的命名空間 URI。</param>
      <param name="value">項目的值。</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementStringAsync(System.String,System.String,System.String,System.String)">
      <summary>以非同步的方式寫入具有指定之前置詞、區域名稱、命名空間 URI 和值的項目。</summary>
      <returns>表示非同步 WriteElementString 作業的工作。</returns>
      <param name="prefix">項目的前置詞。</param>
      <param name="localName">項目的本機名稱。</param>
      <param name="ns">項目的命名空間 URI。</param>
      <param name="value">項目的值。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttribute">
      <summary>在衍生類別中覆寫時，會關閉先前的 <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" /> 呼叫。</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttributeAsync">
      <summary>以非同步的方式關閉上一個 <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" /> 呼叫。</summary>
      <returns>表示非同步 WriteEndAttribute 作業的工作。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocument">
      <summary>在衍生類別中覆寫時，關閉任何開啟的項目或屬性，並將寫入器回復開始狀態。</summary>
      <exception cref="T:System.ArgumentException">The XML document is invalid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocumentAsync">
      <summary>以非同步的方式關閉任何開啟的項目或屬性，並將寫入器回復開始狀態。</summary>
      <returns>表示非同步 WriteEndDocument 作業的工作。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElement">
      <summary>在衍生類別中覆寫時，關閉一個項目並取出對應的命名空間範圍。</summary>
      <exception cref="T:System.InvalidOperationException">This results in an invalid XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElementAsync">
      <summary>以非同步的方式關閉一個項目並取出對應的命名空間範圍。</summary>
      <returns>表示非同步 WriteEndElement 作業的工作。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRef(System.String)">
      <summary>在衍生類別中覆寫時，寫出如 &amp;name; 的實體參考。</summary>
      <param name="name">實體參考的名稱。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRefAsync(System.String)">
      <summary>以非同步的方式將實體參考寫出為 &amp;name;。</summary>
      <returns>表示非同步 WriteEntityRef 作業的工作。</returns>
      <param name="name">實體參考的名稱。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElement">
      <summary>在衍生類別中覆寫時，關閉一個項目並取出對應的命名空間範圍。</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElementAsync">
      <summary>以非同步的方式關閉一個項目並取出對應的命名空間範圍。</summary>
      <returns>表示非同步 WriteFullEndElement 作業的工作。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteName(System.String)">
      <summary>在衍生類別中覆寫時，寫出指定的名稱，根據 W3C XML 1.0 Recommendation (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name) 確定它是有效名稱。</summary>
      <param name="name">要寫入的名稱。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid XML name; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNameAsync(System.String)">
      <summary>以非同步的方式寫出指定的名稱，根據 W3C XML 1.0 Recommendation (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name) 確定它是有效名稱。</summary>
      <returns>表示非同步 WriteName 作業的工作。</returns>
      <param name="name">要寫入的名稱。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmToken(System.String)">
      <summary>在衍生類別中覆寫時，寫出指定的名稱，根據 W3C XML 1.0 Recommendation (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name) 確定它是有效的 NmToken。</summary>
      <param name="name">要寫入的名稱。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid NmToken; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmTokenAsync(System.String)">
      <summary>以非同步的方式寫出指定的名稱，根據 W3C XML 1.0 Recommendation (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name) 確定它是有效的 NmToken。</summary>
      <returns>表示非同步 WriteNmToken 作業的工作。</returns>
      <param name="name">要寫入的名稱。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNode(System.Xml.XmlReader,System.Boolean)">
      <summary>在衍生類別中覆寫時，從讀取器複製所有內容至寫入器，並將讀取器移至下一個同層級 (Sibling) 的開頭。</summary>
      <param name="reader">讀取自 <see cref="T:System.Xml.XmlReader" />。</param>
      <param name="defattr">若要從 XmlReader 複製預設屬性，則為 true，否則為 false。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> contains invalid characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNodeAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>以非同步的方式從讀取器複製所有內容至寫入器，並將讀取器移至下一個同層級 (Sibling) 的開頭。</summary>
      <returns>表示非同步 WriteNode 作業的工作。</returns>
      <param name="reader">讀取自 <see cref="T:System.Xml.XmlReader" />。</param>
      <param name="defattr">若要從 XmlReader 複製預設屬性，則為 true，否則為 false。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstruction(System.String,System.String)">
      <summary>在衍生類別中覆寫時，寫出名稱與文字之間有空白的處理指示，如：&lt;?name text?&gt;。</summary>
      <param name="name">處理指示的名稱。</param>
      <param name="text">要包含在處理指示中的文字。</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.<paramref name="name" /> is either null or String.Empty.This method is being used to create an XML declaration after <see cref="M:System.Xml.XmlWriter.WriteStartDocument" /> has already been called. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstructionAsync(System.String,System.String)">
      <summary>以非同步方式寫出名稱與文字之間有空白的處理指示，如：&lt;?name text?&gt;。</summary>
      <returns>表示非同步 WriteProcessingInstruction 作業的工作。</returns>
      <param name="name">處理指示的名稱。</param>
      <param name="text">要包含在處理指示中的文字。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedName(System.String,System.String)">
      <summary>在衍生類別中覆寫時，寫出命名空間限定名稱。這個方法會查詢在指定之命名空間範圍中的前置詞。</summary>
      <param name="localName">要寫入的區域名稱。</param>
      <param name="ns">這個名稱的命名空間 URI。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="localName" /> is either null or String.Empty.<paramref name="localName" /> is not a valid name. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedNameAsync(System.String,System.String)">
      <summary>以非同步的方式寫出命名空間限定名稱。這個方法會查詢在指定之命名空間範圍中的前置詞。</summary>
      <returns>表示非同步 WriteQualifiedName 作業的工作。</returns>
      <param name="localName">要寫入的區域名稱。</param>
      <param name="ns">這個名稱的命名空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.Char[],System.Int32,System.Int32)">
      <summary>在衍生類別中覆寫時，從字元緩衝區手動寫入未經處理的標記。</summary>
      <param name="buffer">包含要寫入之文字的字元陣列。</param>
      <param name="index">緩衝區中指示要寫入的文字開頭的位置。</param>
      <param name="count">要寫入的字元數。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.String)">
      <summary>在衍生類別中覆寫時，從字串手動寫入未經處理的標記 (Raw Markup)。</summary>
      <param name="data">包含要寫入之文字的字串。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="data" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.Char[],System.Int32,System.Int32)">
      <summary>以非同步的方式從字元緩衝區手動寫入未經處理的標記。</summary>
      <returns>表示非同步 WriteRaw 作業的工作。</returns>
      <param name="buffer">包含要寫入之文字的字元陣列。</param>
      <param name="index">緩衝區中指示要寫入的文字開頭的位置。</param>
      <param name="count">要寫入的字元數。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.String)">
      <summary>以非同步的方式從字串手動寫入未經處理的標記 (Raw Markup)。</summary>
      <returns>表示非同步 WriteRaw 作業的工作。</returns>
      <param name="data">包含要寫入之文字的字串。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String)">
      <summary>寫入具有指定之區域名稱的屬性開頭。</summary>
      <param name="localName">屬性的本機名稱。</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)">
      <summary>寫入具有指定之區域名稱和命名空間 URI 之屬性的開頭。</summary>
      <param name="localName">屬性的本機名稱。</param>
      <param name="ns">屬性的命名空間 URI。</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String,System.String)">
      <summary>在衍生類別中覆寫時，寫入具有指定的前置詞、區域名稱和命名空間 URI 之屬性的開頭。</summary>
      <param name="prefix">屬性的命名空間前置詞。</param>
      <param name="localName">屬性的本機名稱。</param>
      <param name="ns">屬性的命名空間 URI。</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttributeAsync(System.String,System.String,System.String)">
      <summary>以非同步的方式寫入具有指定之前置詞、本機名稱和命名空間 URI 之屬性的開頭。</summary>
      <returns>表示非同步 WriteStartAttribute 作業的工作。</returns>
      <param name="prefix">屬性的命名空間前置詞。</param>
      <param name="localName">屬性的本機名稱。</param>
      <param name="ns">屬性的命名空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument">
      <summary>在衍生類別中覆寫時，使用「1.0」版寫入 XML 宣告。</summary>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument(System.Boolean)">
      <summary>在衍生類別中覆寫時，使用「1.0」版寫入 XML 宣告與獨立屬性。</summary>
      <param name="standalone">如果 true，它會寫入「standalone=yes」；如果 false，它會寫入「standalone=no」。</param>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync">
      <summary>以非同步的方式使用「1.0」版寫入 XML 宣告。</summary>
      <returns>表示非同步 WriteStartDocument 作業的工作。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync(System.Boolean)">
      <summary>以非同步的方式使用「1.0」版寫入 XML 宣告與獨立屬性。</summary>
      <returns>表示非同步 WriteStartDocument 作業的工作。</returns>
      <param name="standalone">如果 true，它會寫入「standalone=yes」；如果 false，它會寫入「standalone=no」。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String)">
      <summary>在衍生類別中覆寫時，寫出具有指定之區域名稱的開頭標記。</summary>
      <param name="localName">項目的本機名稱。</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String)">
      <summary>在衍生類別中覆寫時，寫入指定的開頭標記並與指定的命名空間產生關聯。</summary>
      <param name="localName">項目的本機名稱。</param>
      <param name="ns">與項目相關聯的命名空間 URI。如果這個命名空間已經在範圍中並具有相關聯的前置詞，則寫入器也會自動寫入前置詞。</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String,System.String)">
      <summary>在衍生類別中覆寫時，寫入指定的開頭標記，並與指定的命名空間與前置詞產生關聯。</summary>
      <param name="prefix">項目的命名空間前置詞。</param>
      <param name="localName">項目的本機名稱。</param>
      <param name="ns">與項目相關聯的命名空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElementAsync(System.String,System.String,System.String)">
      <summary>以非同步的方式寫入指定的開頭標記，並將它與指定的命名空間與前置詞產生關聯。</summary>
      <returns>表示非同步 WriteStartElement 作業的工作。</returns>
      <param name="prefix">項目的命名空間前置詞。</param>
      <param name="localName">項目的本機名稱。</param>
      <param name="ns">與項目相關聯的命名空間 URI。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.WriteState">
      <summary>在衍生類別中覆寫時，取得寫入器的狀態。</summary>
      <returns>其中一個 <see cref="T:System.Xml.WriteState" /> 值。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteString(System.String)">
      <summary>在衍生類別中覆寫時，寫入指定的文字內容。</summary>
      <param name="text">要寫入的文字。</param>
      <exception cref="T:System.ArgumentException">The text string contains an invalid surrogate pair.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStringAsync(System.String)">
      <summary>以非同步的方式寫入指定的文字內容。</summary>
      <returns>表示非同步 WriteString 作業的工作。</returns>
      <param name="text">要寫入的文字。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntity(System.Char,System.Char)">
      <summary>在衍生類別中覆寫時，產生和寫入 Surrogate 字元字組的 Surrogate 字元實體。</summary>
      <param name="lowChar">低 Surrogate。這必須是一個介於 0xDC00 和 0xDFFF 之間的值。</param>
      <param name="highChar">高 Surrogate。這必須一個是介於 0xD800 和 0xDBFF 之間的值。</param>
      <exception cref="T:System.ArgumentException">An invalid surrogate character pair was passed.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntityAsync(System.Char,System.Char)">
      <summary>以非同步的方式產生和寫入 Surrogate 字元字組的 Surrogate 字元實體。</summary>
      <returns>表示非同步 WriteSurrogateCharEntity 作業的工作。</returns>
      <param name="lowChar">低 Surrogate。這必須是一個介於 0xDC00 和 0xDFFF 之間的值。</param>
      <param name="highChar">高 Surrogate。這必須一個是介於 0xD800 和 0xDBFF 之間的值。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Boolean)">
      <summary>寫入 <see cref="T:System.Boolean" /> 值。</summary>
      <param name="value">要寫入的 <see cref="T:System.Boolean" /> 值。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.DateTimeOffset)">
      <summary>寫入 <see cref="T:System.DateTimeOffset" /> 值。</summary>
      <param name="value">要寫入的 <see cref="T:System.DateTimeOffset" /> 值。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Decimal)">
      <summary>寫入 <see cref="T:System.Decimal" /> 值。</summary>
      <param name="value">要寫入的 <see cref="T:System.Decimal" /> 值。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Double)">
      <summary>寫入 <see cref="T:System.Double" /> 值。</summary>
      <param name="value">要寫入的 <see cref="T:System.Double" /> 值。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int32)">
      <summary>寫入 <see cref="T:System.Int32" /> 值。</summary>
      <param name="value">要寫入的 <see cref="T:System.Int32" /> 值。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int64)">
      <summary>寫入 <see cref="T:System.Int64" /> 值。</summary>
      <param name="value">要寫入的 <see cref="T:System.Int64" /> 值。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Object)">
      <summary>寫入物件值。</summary>
      <param name="value">要寫入的物件值。附註：使用 .NET Framework 3.5 的版本時，這個方法會接受 <see cref="T:System.DateTimeOffset" /> 做為參數。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Single)">
      <summary>寫入單精確度浮點數。</summary>
      <param name="value">要寫入的單精確度浮點數。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.String)">
      <summary>寫入 <see cref="T:System.String" /> 值。</summary>
      <param name="value">要寫入的 <see cref="T:System.String" /> 值。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespace(System.String)">
      <summary>在衍生類別中覆寫時，寫出指定的空白字元。</summary>
      <param name="ws">空白字元的字串。</param>
      <exception cref="T:System.ArgumentException">The string contains non-white space characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespaceAsync(System.String)">
      <summary>以非同步的方式寫出指定的空白字元。</summary>
      <returns>表示非同步 WriteWhitespace 作業的工作。</returns>
      <param name="ws">空白字元的字串。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlLang">
      <summary>在衍生類別中覆寫時，取得目前的 xml:lang 範圍。</summary>
      <returns>目前的 xml:lang 範圍。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlSpace">
      <summary>在衍生類別中覆寫時，取得表示目前 xml:space 範圍的 <see cref="T:System.Xml.XmlSpace" />。</summary>
      <returns>XmlSpace，表示目前的 xml:space 範圍。值意義 None如果 xml:space 範圍不存在，這是預設值。Default目前的範圍為 xml:space="default"。Preserve目前的範圍為 xml:space="preserve"。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="T:System.Xml.XmlWriterSettings">
      <summary>指定要在由 <see cref="Overload:System.Xml.XmlWriter.Create" /> 方法建立的 <see cref="T:System.Xml.XmlWriter" /> 物件上支援的一組功能。</summary>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.#ctor">
      <summary>初始化 <see cref="T:System.Xml.XmlWriterSettings" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Async">
      <summary>取得或設定值，指出非同步 <see cref="T:System.Xml.XmlWriter" /> 方法是否可以用於特定 <see cref="T:System.Xml.XmlWriter" /> 執行個體。</summary>
      <returns>如果可以使用非同步方法，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CheckCharacters">
      <summary>取得或設定值，這個值表示 XML 寫入器是否應該檢查以確定文件中的所有字元都符合 W3C XML 1.0 建議事項中的＜2.2 字元＞一節。</summary>
      <returns>true 表示執行字元檢查，否則為 false。預設值為 true。</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Clone">
      <summary>建立 <see cref="T:System.Xml.XmlWriterSettings" /> 執行個體的複本。</summary>
      <returns>複製的 <see cref="T:System.Xml.XmlWriterSettings" /> 物件。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CloseOutput">
      <summary>取得或設定值，指出呼叫 <see cref="M:System.Xml.XmlWriter.Close" /> 方法時，<see cref="T:System.Xml.XmlWriter" /> 是否也應該關閉基礎資料流或 <see cref="T:System.IO.TextWriter" />。</summary>
      <returns>true 表示也關閉基礎資料流或 <see cref="T:System.IO.TextWriter" />，否則為 false。預設值為 false。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.ConformanceLevel">
      <summary>取得或設定 XML 寫入器檢查 XML 輸出的一致性層級。</summary>
      <returns>其中一個指定一致性層級 (文件、片段或自動偵測) 的列舉值。預設值為 <see cref="F:System.Xml.ConformanceLevel.Document" />。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Encoding">
      <summary>取得或設定要使用的文字編碼方式類型。</summary>
      <returns>要使用的文字編碼方式。預設值為 Encoding.UTF8。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Indent">
      <summary>取得或設定值，指出是否要縮排項目。</summary>
      <returns>true 表示在新行和縮排上寫入個別項目，否則為 false。預設值為 false。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.IndentChars">
      <summary>取得或設定縮排時使用的字元字串。當 <see cref="P:System.Xml.XmlWriterSettings.Indent" /> 屬性設為 true 時會使用這項設定。</summary>
      <returns>縮排時使用的字元字串。它可以設為任何字串值。不過，若要確保有效的 XML，您應該只指定有效的空白字元 (例如，空格字元、定位字元、歸位字元或換行符號)。預設值為兩個空格。</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.IndentChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NamespaceHandling">
      <summary>取得或設定值，這個值表示 <see cref="T:System.Xml.XmlWriter" /> 是否應該在寫入 XML 內容時移除重複的命名空間宣告。預設行為是讓寫入器輸出寫入器命名空間解析程式中出現的所有命名空間宣告。</summary>
      <returns>
        <see cref="T:System.Xml.NamespaceHandling" /> 列舉類型，用來指定是否要移除 <see cref="T:System.Xml.XmlWriter" /> 中的重複命名空間宣告。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineChars">
      <summary>取得或設定用於分行符號的字元字串。</summary>
      <returns>用於分行符號的字元字串。它可以設為任何字串值。不過，若要確保有效的 XML，您應該只指定有效的空白字元 (例如，空格字元、定位字元、歸位字元或換行符號)。預設為 \r\n (歸位字元、新行)。</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineHandling">
      <summary>取得或設定值，指出是否要將輸出中的分行符號標準化。</summary>
      <returns>其中一個 <see cref="T:System.Xml.NewLineHandling" /> 值。預設值為 <see cref="F:System.Xml.NewLineHandling.Replace" />。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineOnAttributes">
      <summary>取得或設定值，指出是否將屬性寫在新行上。</summary>
      <returns>true 表示將屬性寫在獨立的行上，否則為 false。預設值為 false。注意事項當 <see cref="P:System.Xml.XmlWriterSettings.Indent" /> 屬性值為 false 時，這項設定不會有任何作用。當 <see cref="P:System.Xml.XmlWriterSettings.NewLineOnAttributes" /> 設為 true 時，會在每個屬性之前加上新行和一個額外的縮排層級。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.OmitXmlDeclaration">
      <summary>取得或設定值，指出是否省略 XML 宣告。</summary>
      <returns>true 表示省略 XML 宣告，否則為 false。預設值為 false，表示會寫入 XML 宣告。</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Reset">
      <summary>將設定類別的成員重設為其預設值。</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.WriteEndDocumentOnClose">
      <summary>取得或設定值，指出 <see cref="T:System.Xml.XmlWriter" /> 是否會在呼叫 <see cref="M:System.Xml.XmlWriter.Close" /> 方法時，將結尾標記加入所有未封閉的項目標記。</summary>
      <returns>如果將關閉所有未封閉的項目標記，則為 true，否則為 false。預設值是 true。</returns>
    </member>
    <member name="T:System.Xml.Schema.XmlSchema">
      <summary>依全球資訊網協會 (W3C) XML 結構描述第 1 部分：結構及XML 結構描述第 2 部分：資料類型 所規定之 XML 結構描述的記憶體中表示。</summary>
    </member>
    <member name="T:System.Xml.Schema.XmlSchemaForm">
      <summary>指示屬性 (Attribute) 或項目是否需要以命名空間前置詞限定。</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.None">
      <summary>項目和屬性格式未在結構描述中指定。</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Qualified">
      <summary>項目和屬性必須以命名空間前置詞限定。</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Unqualified">
      <summary>項目和屬性不需要以命名空間前置詞限定。</summary>
    </member>
    <member name="T:System.Xml.Serialization.IXmlSerializable">
      <summary>為 XML 序列化和還原序列化提供自訂格式化。</summary>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.GetSchema">
      <summary>這個方法應保留且不應予以使用。實作 IXmlSerializable 介面時，您應該從這個方法傳回 null (在 Visual Basic 中為 Nothing)，而且如果需要指定自訂結構描述，請改為將 <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" /> 套用至類別。</summary>
      <returns>
        <see cref="T:System.Xml.Schema.XmlSchema" />，描述物件的 XML 表示，該物件由 <see cref="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)" /> 方法產生，由 <see cref="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)" /> 方法取用。</returns>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)">
      <summary>從物件的 XML 表示產生該物件。</summary>
      <param name="reader">還原序列化物件的 <see cref="T:System.Xml.XmlReader" /> 資料流。</param>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)">
      <summary>將物件轉換成其 XML 表示。</summary>
      <param name="writer">序列化物件的 <see cref="T:System.Xml.XmlWriter" /> 資料流。</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlSchemaProviderAttribute">
      <summary>套用至型別後，儲存傳回 XML 結構描述之型別的靜態方法名稱以及控制型別之序列化 (Serialization) 的 <see cref="T:System.Xml.XmlQualifiedName" /> (或用於匿名型別的 <see cref="T:System.Xml.Schema.XmlSchemaType" />)。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaProviderAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" /> 類別的新執行個體，並採用提供型別之 XML 結構描述的靜態方法名稱。</summary>
      <param name="methodName">要實作之靜態方法的名稱。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.IsAny">
      <summary>取得或設定值，以便判斷目標類別是否為萬用字元，或者該類別的結構描述是否僅含有 xs:any 項目。</summary>
      <returns>如果該類別為萬用字元，或者結構描述僅含有 xs:any 項目，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.MethodName">
      <summary>取得提供型別之 XML 結構描述的靜態方法名稱以及其 XML 結構描述資料型別的名稱。</summary>
      <returns>由 XML 基礎結構叫用以傳回 XML 結構描述之方法的名稱。</returns>
    </member>
  </members>
</doc>