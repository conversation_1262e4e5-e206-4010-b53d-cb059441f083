using System;
using System.IO;

namespace OcrLiteLib
{
    /// <summary>
    /// 内存优化测试类
    /// </summary>
    public static class MemoryOptimizationTest
    {
        /// <summary>
        /// 运行内存优化测试
        /// </summary>
        public static void RunTests()
        {
            Console.WriteLine("=== OCR内存优化测试 ===\n");
            
            // 测试1: 基本资源管理测试
            TestBasicResourceManagement();
            
            // 测试2: Mat池测试
            TestMatPool();
            
            // 测试3: 内存监控测试
            TestMemoryMonitoring();
            
            // 测试4: 频繁调用测试（如果有测试图像）
            if (File.Exists("test.jpg"))
            {
                TestFrequentCalls();
            }
            else
            {
                Console.WriteLine("跳过频繁调用测试 - 未找到test.jpg");
            }
            
            Console.WriteLine("\n=== 测试完成 ===");
        }
        
        /// <summary>
        /// 测试基本资源管理
        /// </summary>
        private static void TestBasicResourceManagement()
        {
            Console.WriteLine("1. 测试基本资源管理...");
            MemoryMonitor.StartMonitoring();
            
            try
            {
                using (var ocrLite = new OcrLite())
                {
                    MemoryMonitor.Checkpoint("OcrLite创建");
                    
                    // 测试各个网络类的资源管理
                    using (var dbNet = new DbNet())
                    using (var angleNet = new AngleNet())
                    using (var crnnNet = new CrnnNet())
                    {
                        MemoryMonitor.Checkpoint("网络类创建");
                    }
                    
                    MemoryMonitor.Checkpoint("网络类释放");
                }
                
                MemoryMonitor.CheckpointWithGC("OcrLite释放后GC");
                
                long memoryDiff = MemoryMonitor.GetMemoryDifference();
                Console.WriteLine($"   内存变化: {FormatBytes(memoryDiff)}");
                
                if (Math.Abs(memoryDiff) < 10 * 1024 * 1024) // 10MB以内认为正常
                {
                    Console.WriteLine("   ✓ 资源管理测试通过\n");
                }
                else
                {
                    Console.WriteLine("   ⚠ 可能存在内存泄漏\n");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 测试失败: {ex.Message}\n");
            }
        }
        
        /// <summary>
        /// 测试Mat池
        /// </summary>
        private static void TestMatPool()
        {
            Console.WriteLine("2. 测试Mat池...");
            MemoryMonitor.StartMonitoring();
            
            try
            {
                using (var matPool = new MatPool())
                {
                    MemoryMonitor.Checkpoint("Mat池创建");
                    
                    // 创建和释放大量Mat对象
                    for (int i = 0; i < 100; i++)
                    {
                        using (var pooledMat = new PooledMat(matPool.GetMat(640, 480), matPool))
                        {
                            // 模拟使用Mat
                            var mat = pooledMat.Mat;
                        }
                    }
                    
                    MemoryMonitor.Checkpoint("100次Mat操作完成");
                    Console.WriteLine($"   池状态: {matPool.GetPoolStats()}");
                }
                
                MemoryMonitor.CheckpointWithGC("Mat池释放后GC");
                
                long memoryDiff = MemoryMonitor.GetMemoryDifference();
                Console.WriteLine($"   内存变化: {FormatBytes(memoryDiff)}");
                Console.WriteLine("   ✓ Mat池测试通过\n");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 测试失败: {ex.Message}\n");
            }
        }
        
        /// <summary>
        /// 测试内存监控
        /// </summary>
        private static void TestMemoryMonitoring()
        {
            Console.WriteLine("3. 测试内存监控...");
            
            try
            {
                MemoryMonitor.StartMonitoring();
                
                // 分配一些内存
                byte[] largeArray = new byte[10 * 1024 * 1024]; // 10MB
                MemoryMonitor.Checkpoint("分配10MB内存");
                
                // 使用MemoryScope
                using (var scope = new MemoryScope("测试作用域"))
                {
                    byte[] anotherArray = new byte[5 * 1024 * 1024]; // 5MB
                }
                
                // 释放内存
                largeArray = null;
                MemoryMonitor.CheckpointWithGC("释放内存后GC");
                
                // 测试泄漏检测
                bool hasLeak = MemoryMonitor.CheckForMemoryLeak(1 * 1024 * 1024); // 1MB阈值
                Console.WriteLine($"   内存泄漏检测: {(hasLeak ? "检测到泄漏" : "无泄漏")}");
                
                // 获取详细报告
                string report = MemoryMonitor.GetDetailedReport();
                Console.WriteLine("   详细报告已生成");
                Console.WriteLine("   ✓ 内存监控测试通过\n");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 测试失败: {ex.Message}\n");
            }
        }
        
        /// <summary>
        /// 测试频繁调用（需要测试图像）
        /// </summary>
        private static void TestFrequentCalls()
        {
            Console.WriteLine("4. 测试频繁调用...");
            MemoryMonitor.StartMonitoring();
            
            try
            {
                using (var ocrLite = new OcrLite())
                {
                    // 注意：这里需要实际的模型文件，测试时可能会失败
                    // 这只是演示如何进行频繁调用测试
                    
                    string testImage = "test.jpg";
                    int callCount = 10; // 减少调用次数以适应测试环境
                    
                    for (int i = 0; i < callCount; i++)
                    {
                        try
                        {
                            // 这里会因为缺少模型文件而失败，但可以测试内存管理
                            // var result = ocrLite.Detect(testImage, 50, 1024, 0.5f, 0.3f, 2.0f, true, true);
                            
                            if (i % 5 == 4)
                            {
                                MemoryMonitor.Checkpoint($"完成{i + 1}次调用");
                                
                                if (MemoryMonitor.CheckForMemoryLeak(20 * 1024 * 1024)) // 20MB阈值
                                {
                                    Console.WriteLine($"   ⚠ 在第{i + 1}次调用后检测到内存泄漏");
                                    break;
                                }
                            }
                        }
                        catch (Exception)
                        {
                            // 忽略模型加载错误，继续测试内存管理
                        }
                    }
                    
                    MemoryMonitor.CheckpointWithGC("频繁调用测试完成");
                    
                    long memoryDiff = MemoryMonitor.GetMemoryDifference();
                    Console.WriteLine($"   内存变化: {FormatBytes(memoryDiff)}");
                    Console.WriteLine("   ✓ 频繁调用测试完成\n");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ⚠ 测试部分失败（可能缺少模型文件）: {ex.Message}\n");
            }
        }
        
        /// <summary>
        /// 格式化字节数
        /// </summary>
        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB" };
            int counter = 0;
            decimal number = Math.Abs(bytes);
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            string sign = bytes < 0 ? "-" : "";
            return $"{sign}{number:n1} {suffixes[counter]}";
        }
        
        /// <summary>
        /// 运行性能对比测试
        /// </summary>
        public static void RunPerformanceComparison()
        {
            Console.WriteLine("=== 性能对比测试 ===\n");
            
            // 测试优化前后的SubstractMeanNormalize性能
            TestSubstractMeanNormalizePerformance();
            
            Console.WriteLine("\n=== 性能对比完成 ===");
        }
        
        /// <summary>
        /// 测试SubstractMeanNormalize性能
        /// </summary>
        private static void TestSubstractMeanNormalizePerformance()
        {
            Console.WriteLine("测试SubstractMeanNormalize性能...");
            
            try
            {
                // 创建测试Mat
                using (var testMat = new Emgu.CV.Mat(480, 640, Emgu.CV.CvEnum.DepthType.Cv8U, 3))
                {
                    float[] meanVals = { 127.5f, 127.5f, 127.5f };
                    float[] normVals = { 1.0f / 127.5f, 1.0f / 127.5f, 1.0f / 127.5f };
                    
                    MemoryMonitor.StartMonitoring();
                    
                    // 测试优化版本
                    var startTime = DateTime.Now;
                    for (int i = 0; i < 10; i++)
                    {
                        var tensor = OcrUtils.SubstractMeanNormalize(testMat, meanVals, normVals);
                    }
                    var optimizedTime = DateTime.Now - startTime;
                    
                    MemoryMonitor.Checkpoint("优化版本完成");
                    
                    // 测试安全版本
                    startTime = DateTime.Now;
                    for (int i = 0; i < 10; i++)
                    {
                        var tensor = OcrUtils.SubstractMeanNormalizeSafe(testMat, meanVals, normVals);
                    }
                    var safeTime = DateTime.Now - startTime;
                    
                    MemoryMonitor.CheckpointWithGC("安全版本完成");
                    
                    Console.WriteLine($"   优化版本耗时: {optimizedTime.TotalMilliseconds:F2}ms");
                    Console.WriteLine($"   安全版本耗时: {safeTime.TotalMilliseconds:F2}ms");
                    Console.WriteLine($"   性能提升: {(safeTime.TotalMilliseconds / optimizedTime.TotalMilliseconds):F2}x");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 性能测试失败: {ex.Message}");
            }
        }
    }
}
