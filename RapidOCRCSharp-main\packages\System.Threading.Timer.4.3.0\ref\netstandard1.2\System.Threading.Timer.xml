﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Timer</name>
  </assembly>
  <members>
    <member name="T:System.Threading.Timer">
      <summary>Provides a mechanism for executing a method at specified intervals. This class cannot be inherited.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Timer.#ctor(System.Threading.TimerCallback,System.Object,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the Timer class, using a 32-bit signed integer to specify the time interval.</summary>
      <param name="callback">A <see cref="T:System.Threading.TimerCallback" /> delegate representing a method to be executed. </param>
      <param name="state">An object containing information to be used by the callback method, or null. </param>
      <param name="dueTime">The amount of time to delay before <paramref name="callback" /> is invoked, in milliseconds. Specify <see cref="F:System.Threading.Timeout.Infinite" /> to prevent the timer from starting. Specify zero (0) to start the timer immediately. </param>
      <param name="period">The time interval between invocations of <paramref name="callback" />, in milliseconds. Specify <see cref="F:System.Threading.Timeout.Infinite" /> to disable periodic signaling. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="dueTime" /> or <paramref name="period" /> parameter is negative and is not equal to <see cref="F:System.Threading.Timeout.Infinite" />. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="callback" /> parameter is null. </exception>
    </member>
    <member name="M:System.Threading.Timer.#ctor(System.Threading.TimerCallback,System.Object,System.TimeSpan,System.TimeSpan)">
      <summary>Initializes a new instance of the Timer class, using <see cref="T:System.TimeSpan" /> values to measure time intervals.</summary>
      <param name="callback">A delegate representing a method to be executed. </param>
      <param name="state">An object containing information to be used by the callback method, or null. </param>
      <param name="dueTime">The amount of time to delay before the <paramref name="callback" /> parameter invokes its methods. Specify negative one (-1) milliseconds to prevent the timer from starting. Specify zero (0) to start the timer immediately. </param>
      <param name="period">The time interval between invocations of the methods referenced by <paramref name="callback" />. Specify negative one (-1) milliseconds to disable periodic signaling. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The number of milliseconds in the value of <paramref name="dueTime" /> or <paramref name="period" /> is negative and not equal to <see cref="F:System.Threading.Timeout.Infinite" />, or is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="callback" /> parameter is null. </exception>
    </member>
    <member name="M:System.Threading.Timer.Change(System.Int32,System.Int32)">
      <summary>Changes the start time and the interval between method invocations for a timer, using 32-bit signed integers to measure time intervals.</summary>
      <returns>true if the timer was successfully updated; otherwise, false.</returns>
      <param name="dueTime">The amount of time to delay before the invoking the callback method specified when the <see cref="T:System.Threading.Timer" /> was constructed, in milliseconds. Specify <see cref="F:System.Threading.Timeout.Infinite" /> to prevent the timer from restarting. Specify zero (0) to restart the timer immediately. </param>
      <param name="period">The time interval between invocations of the callback method specified when the <see cref="T:System.Threading.Timer" /> was constructed, in milliseconds. Specify <see cref="F:System.Threading.Timeout.Infinite" /> to disable periodic signaling. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Timer" /> has already been disposed. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="dueTime" /> or <paramref name="period" /> parameter is negative and is not equal to <see cref="F:System.Threading.Timeout.Infinite" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.Timer.Change(System.TimeSpan,System.TimeSpan)">
      <summary>Changes the start time and the interval between method invocations for a timer, using <see cref="T:System.TimeSpan" /> values to measure time intervals.</summary>
      <returns>true if the timer was successfully updated; otherwise, false.</returns>
      <param name="dueTime">A <see cref="T:System.TimeSpan" /> representing the amount of time to delay before invoking the callback method specified when the <see cref="T:System.Threading.Timer" /> was constructed. Specify negative one (-1) milliseconds to prevent the timer from restarting. Specify zero (0) to restart the timer immediately. </param>
      <param name="period">The time interval between invocations of the callback method specified when the <see cref="T:System.Threading.Timer" /> was constructed. Specify negative one (-1) milliseconds to disable periodic signaling. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Timer" /> has already been disposed. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="dueTime" /> or <paramref name="period" /> parameter, in milliseconds, is less than -1. </exception>
      <exception cref="T:System.NotSupportedException">The <paramref name="dueTime" /> or <paramref name="period" /> parameter, in milliseconds, is greater than 4294967294. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.Timer.Dispose">
      <summary>Releases all resources used by the current instance of <see cref="T:System.Threading.Timer" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Threading.TimerCallback">
      <summary>Represents the method that handles calls from a <see cref="T:System.Threading.Timer" />.</summary>
      <param name="state">An object containing application-specific information relevant to the method invoked by this delegate, or null. </param>
      <filterpriority>2</filterpriority>
    </member>
  </members>
</doc>