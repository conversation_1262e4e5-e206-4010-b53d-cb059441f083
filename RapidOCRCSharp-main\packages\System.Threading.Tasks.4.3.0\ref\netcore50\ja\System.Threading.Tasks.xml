﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Tasks</name>
  </assembly>
  <members>
    <member name="T:System.AggregateException">
      <summary>アプリケーションの実行中に発生する 1 つ以上のエラーを表します。</summary>
    </member>
    <member name="M:System.AggregateException.#ctor">
      <summary>エラーを説明するシステム提供のメッセージを使用して、<see cref="T:System.AggregateException" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.AggregateException.#ctor(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>この例外の原因である内部例外への参照を使用して、<see cref="T:System.AggregateException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="innerExceptions">現在の例外の原因である例外。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerExceptions" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="innerExceptions" /> の要素が null です。</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.Exception[])">
      <summary>この例外の原因である内部例外への参照を使用して、<see cref="T:System.AggregateException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="innerExceptions">現在の例外の原因である例外。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerExceptions" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="innerExceptions" /> の要素が null です。</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String)">
      <summary>エラーを説明する指定したメッセージを使用して、<see cref="T:System.AggregateException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外を説明するメッセージ。このコンストラクターの呼び出し元では、この文字列が現在のシステムのカルチャに合わせてローカライズ済みであることを確認しておく必要があります。</param>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>指定したエラー メッセージと、この例外の原因である内部例外への参照を使用して、<see cref="T:System.AggregateException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
      <param name="innerExceptions">現在の例外の原因である例外。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerExceptions" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="innerExceptions" /> の要素が null です。</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Exception)">
      <summary>指定したエラー メッセージと、この例外の原因である内部例外への参照を使用して、<see cref="T:System.AggregateException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外を説明するメッセージ。このコンストラクターの呼び出し元では、この文字列が現在のシステムのカルチャに合わせてローカライズ済みであることを確認しておく必要があります。</param>
      <param name="innerException">現在の例外の原因である例外。<paramref name="innerException" /> パラメーターが null でない場合は、内部例外を処理する catch ブロックで現在の例外が発生します。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerException" /> 引数が null です。</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Exception[])">
      <summary>指定したエラー メッセージと、この例外の原因である内部例外への参照を使用して、<see cref="T:System.AggregateException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
      <param name="innerExceptions">現在の例外の原因である例外。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerExceptions" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="innerExceptions" /> の要素が null です。</exception>
    </member>
    <member name="M:System.AggregateException.Flatten">
      <summary>
        <see cref="T:System.AggregateException" /> インスタンスを単一の新しいインスタンスに平坦化します。</summary>
      <returns>新しい平坦化された <see cref="T:System.AggregateException" />。</returns>
    </member>
    <member name="M:System.AggregateException.GetBaseException">
      <summary>この例外の根本的な原因である <see cref="T:System.AggregateException" /> を返します。</summary>
      <returns>この例外の根本的な原因である <see cref="T:System.AggregateException" /> を返します。</returns>
    </member>
    <member name="M:System.AggregateException.Handle(System.Func{System.Exception,System.Boolean})">
      <summary>この <see cref="T:System.AggregateException" /> に含まれている各 <see cref="T:System.Exception" /> に対してハンドラーを呼び出します。</summary>
      <param name="predicate">それぞれの例外に対して実行する述語。この述語は、処理される <see cref="T:System.Exception" /> を引数として受け取り、例外が処理されたかどうかを示すブール値を返します。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="predicate" /> 引数が null です。</exception>
      <exception cref="T:System.AggregateException">この <see cref="T:System.AggregateException" /> に含まれている例外は処理されませんでした。</exception>
    </member>
    <member name="P:System.AggregateException.InnerExceptions">
      <summary>現在の例外の原因となった <see cref="T:System.Exception" /> インスタンスの読み取り専用コレクションを取得します。</summary>
      <returns>現在の例外の原因となった <see cref="T:System.Exception" /> インスタンスの読み取り専用コレクションを返します。</returns>
    </member>
    <member name="M:System.AggregateException.ToString">
      <summary>現在の <see cref="T:System.AggregateException" /> の文字列形式を作成して返します。</summary>
      <returns>現在の例外の文字列形式。</returns>
    </member>
    <member name="T:System.OperationCanceledException">
      <summary>この例外は、スレッドによって実行されていた操作が取り消されたときにそのスレッドでスローされます。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.OperationCanceledException.#ctor">
      <summary>システム提供のエラー メッセージを使用して、<see cref="T:System.OperationCanceledException" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String)">
      <summary>指定したエラー メッセージを使用して、<see cref="T:System.OperationCanceledException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">エラーを説明する <see cref="T:System.String" />。</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Exception)">
      <summary>指定したエラー メッセージと、この例外の原因である内部例外への参照を使用して、<see cref="T:System.OperationCanceledException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
      <param name="innerException">現在の例外の原因である例外。<paramref name="innerException" /> パラメーターが null でない場合は、内部例外を処理する catch ブロックで現在の例外が発生します。</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Exception,System.Threading.CancellationToken)">
      <summary>指定したエラー メッセージ、この例外の原因である内部例外への参照、およびキャンセル トークンを使用して、<see cref="T:System.OperationCanceledException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
      <param name="innerException">現在の例外の原因である例外。<paramref name="innerException" /> パラメーターが null でない場合は、内部例外を処理する catch ブロックで現在の例外が発生します。</param>
      <param name="token">取り消された操作に関連付けられているキャンセル トークン。</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Threading.CancellationToken)">
      <summary>指定したエラー メッセージとキャンセル トークンを使用して、<see cref="T:System.OperationCanceledException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
      <param name="token">取り消された操作に関連付けられているキャンセル トークン。</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.Threading.CancellationToken)">
      <summary>キャンセル トークンを使用して、<see cref="T:System.OperationCanceledException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="token">取り消された操作に関連付けられているキャンセル トークン。</param>
    </member>
    <member name="P:System.OperationCanceledException.CancellationToken">
      <summary>取り消された操作に関連付けられているトークンを取得します。</summary>
      <returns>取り消された操作に関連付けられているトークンまたは既定のトークン。</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder">
      <summary>タスクを返す非同期メソッドのビルダーを表します。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitOnCompleted``2(``0@,``1@)">
      <summary>指定された awaiter が完了したときに次の操作に進むようにステート マシンをスケジュールします。</summary>
      <param name="awaiter">awaiter。</param>
      <param name="stateMachine">ステート マシン。</param>
      <typeparam name="TAwaiter">awaiter の型。</typeparam>
      <typeparam name="TStateMachine">ステート マシンの型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>指定された awaiter が完了したときに次の操作に進むようにステート マシンをスケジュールします。このメソッドは、部分的に信頼されているコードから呼び出すことができます。</summary>
      <param name="awaiter">awaiter。</param>
      <param name="stateMachine">ステート マシン。</param>
      <typeparam name="TAwaiter">awaiter の型。</typeparam>
      <typeparam name="TStateMachine">ステート マシンの型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Create">
      <summary>
        <see cref="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder" /> クラスのインスタンスを作成します。</summary>
      <returns>ビルダーの新しいインスタンス。</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetException(System.Exception)">
      <summary>タスクを失敗としてマークし、指定された例外をタスクにバインドします。</summary>
      <param name="exception">タスクにバインドする例外。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">タスクは既に完了しています。またはビルダーが初期化されていません。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult">
      <summary>タスクを正常な完了としてマークします。</summary>
      <exception cref="T:System.InvalidOperationException">タスクは既に完了しています。またはビルダーが初期化されていません。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>指定したステート マシンにビルダーを関連付けます。</summary>
      <param name="stateMachine">ビルダーに関連付けるステート マシン インスタンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">ステート マシンは、前に設定されました。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Start``1(``0@)">
      <summary>ステート マシンが関連付けられているビルダーの実行を開始します。</summary>
      <param name="stateMachine">参照により渡されたステート マシン インスタンス。</param>
      <typeparam name="TStateMachine">ステート マシンの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> は null なので、</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Task">
      <summary>このビルダーのタスクを取得します。</summary>
      <returns>このビルダーのタスク。</returns>
      <exception cref="T:System.InvalidOperationException">ビルダーが初期化されていません。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1">
      <summary>タスクを返す非同期メソッドのビルダーを表し、結果のパラメーターを指定します。</summary>
      <typeparam name="TResult">タスクを完了するために使用する結果。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AwaitOnCompleted``2(``0@,``1@)">
      <summary>指定された awaiter が完了したときに次の操作に進むようにステート マシンをスケジュールします。</summary>
      <param name="awaiter">awaiter。</param>
      <param name="stateMachine">ステート マシン。</param>
      <typeparam name="TAwaiter">awaiter の型。</typeparam>
      <typeparam name="TStateMachine">ステート マシンの型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>指定された awaiter が完了したときに次の操作に進むようにステート マシンをスケジュールします。このメソッドは、部分的に信頼されているコードから呼び出すことができます。</summary>
      <param name="awaiter">awaiter。</param>
      <param name="stateMachine">ステート マシン。</param>
      <typeparam name="TAwaiter">awaiter の型。</typeparam>
      <typeparam name="TStateMachine">ステート マシンの型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Create">
      <summary>
        <see cref="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1" /> クラスのインスタンスを作成します。</summary>
      <returns>ビルダーの新しいインスタンス。</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetException(System.Exception)">
      <summary>タスクを失敗としてマークし、指定された例外をタスクにバインドします。</summary>
      <param name="exception">タスクにバインドする例外。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">タスクは既に完了しています。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetResult(`0)">
      <summary>タスクを正常な完了としてマークします。</summary>
      <param name="result">タスクを完了するために使用する結果。</param>
      <exception cref="T:System.InvalidOperationException">タスクは既に完了しています。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>指定したステート マシンにビルダーを関連付けます。</summary>
      <param name="stateMachine">ビルダーに関連付けるステート マシン インスタンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">ステート マシンは、前に設定されました。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Start``1(``0@)">
      <summary>ステート マシンが関連付けられているビルダーの実行を開始します。</summary>
      <param name="stateMachine">参照により渡されたステート マシン インスタンス。</param>
      <typeparam name="TStateMachine">ステート マシンの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> は null なので、</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Task">
      <summary>このビルダーのタスクを取得します。</summary>
      <returns>このビルダーのタスク。</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncVoidMethodBuilder">
      <summary>値を返さない非同期メソッドのビルダーを表します。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitOnCompleted``2(``0@,``1@)">
      <summary>指定された awaiter が完了したときに次の操作に進むようにステート マシンをスケジュールします。</summary>
      <param name="awaiter">awaiter。</param>
      <param name="stateMachine">ステート マシン。</param>
      <typeparam name="TAwaiter">awaiter の型。</typeparam>
      <typeparam name="TStateMachine">ステート マシンの型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>指定された awaiter が完了したときに次の操作に進むようにステート マシンをスケジュールします。このメソッドは、部分的に信頼されているコードから呼び出すことができます。</summary>
      <param name="awaiter">awaiter。</param>
      <param name="stateMachine">ステート マシン。</param>
      <typeparam name="TAwaiter">awaiter の型。</typeparam>
      <typeparam name="TStateMachine">ステート マシンの型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Create">
      <summary>
        <see cref="T:System.Runtime.CompilerServices.AsyncVoidMethodBuilder" /> クラスのインスタンスを作成します。</summary>
      <returns>ビルダーの新しいインスタンス。</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetException(System.Exception)">
      <summary>メソッド ビルダーに例外をバインドします。</summary>
      <param name="exception">バインドする例外。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">ビルダーが初期化されていません。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetResult">
      <summary>メソッド ビルダーを正常に完了済みとしてマークします。</summary>
      <exception cref="T:System.InvalidOperationException">ビルダーが初期化されていません。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>指定したステート マシンにビルダーを関連付けます。</summary>
      <param name="stateMachine">ビルダーに関連付けるステート マシン インスタンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">ステート マシンは、前に設定されました。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start``1(``0@)">
      <summary>ステート マシンが関連付けられているビルダーの実行を開始します。</summary>
      <param name="stateMachine">参照により渡されたステート マシン インスタンス。</param>
      <typeparam name="TStateMachine">ステート マシンの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> は null なので、</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable">
      <summary>タスクに対する待機を構成できる待機可能オブジェクトを提供します。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.GetAwaiter">
      <summary>この待機可能オブジェクトの awaiter を返します。</summary>
      <returns>awaiter。</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1">
      <summary>タスクに対する待機を構成できる待機可能オブジェクトを提供します。</summary>
      <typeparam name="TResult">この <see cref="T:System.Threading.Tasks.Task`1" /> によって生成される結果の型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.GetAwaiter">
      <summary>この待機可能オブジェクトの awaiter を返します。</summary>
      <returns>awaiter。</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter">
      <summary>待機可能なオブジェクト　(<see cref="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1" />) に awaiter を提供します。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.GetResult">
      <summary>完了したタスクに対する待機を終了します。</summary>
      <returns>完了したタスクの結果。</returns>
      <exception cref="T:System.NullReferenceException">awaiter が適切に初期化されませんでした。</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">タスクが取り消されました。</exception>
      <exception cref="T:System.Exception">Faulted 状態で完了したタスク。</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.IsCompleted">
      <summary>待機されているタスクが完了したかどうかを示す値を取得します。</summary>
      <returns>待機対象のタスクが完了している場合は true。それ以外の場合は false。</returns>
      <exception cref="T:System.NullReferenceException">awaiter が適切に初期化されませんでした。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
      <summary>この awaiter に関連付けられているタスクに継続の操作をスケジュールします。</summary>
      <param name="continuation">待機操作の完了時に呼び出すアクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 引数が null です。</exception>
      <exception cref="T:System.NullReferenceException">awaiter が適切に初期化されませんでした。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>この awaiter に関連付けられているタスクに継続の操作をスケジュールします。</summary>
      <param name="continuation">待機操作の完了時に呼び出すアクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 引数が null です。</exception>
      <exception cref="T:System.NullReferenceException">awaiter が適切に初期化されませんでした。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter">
      <summary>待機可能な (<see cref="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable" />) オブジェクトに awaiter を提供します。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.GetResult">
      <summary>完了したタスクに対する待機を終了します。</summary>
      <exception cref="T:System.NullReferenceException">awaiter が適切に初期化されませんでした。</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">タスクが取り消されました。</exception>
      <exception cref="T:System.Exception">Faulted 状態で完了したタスク。</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.IsCompleted">
      <summary>待機されているタスクが完了したかどうかを示す値を取得します。</summary>
      <returns>待たれるタスクが完了した場合は true。それ以外の場合は false。</returns>
      <exception cref="T:System.NullReferenceException">awaiter が適切に初期化されませんでした。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
      <summary>この awaiter に関連付けられているタスクに継続の操作をスケジュールします。</summary>
      <param name="continuation">待機操作の完了時に呼び出すアクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 引数が null です。</exception>
      <exception cref="T:System.NullReferenceException">awaiter が適切に初期化されませんでした。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>この awaiter に関連付けられているタスクに継続の操作をスケジュールします。</summary>
      <param name="continuation">待機操作の完了時に呼び出すアクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 引数が null です。</exception>
      <exception cref="T:System.NullReferenceException">awaiter が適切に初期化されませんでした。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.IAsyncStateMachine">
      <summary>非同期メソッドに生成されるステート マシンを表します。この型はコンパイラでのみ使用されます。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.IAsyncStateMachine.MoveNext">
      <summary>ステート マシンを次の状態に移動します。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.IAsyncStateMachine.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>ステート マシンをヒープに割り当てられたレプリカで構成します。</summary>
      <param name="stateMachine">ヒープに割り当てられたレプリカ。</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.ICriticalNotifyCompletion">
      <summary>待機操作が完了したときに継続をスケジュールする awaiter を表します。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ICriticalNotifyCompletion.UnsafeOnCompleted(System.Action)">
      <summary>インスタンスが完了したときに呼び出される継続の操作をスケジュールします。</summary>
      <param name="continuation">操作の完了時に呼び出すアクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 引数が null (Visual Basic では Nothing) です。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.INotifyCompletion">
      <summary>完了時の継続をスケジュールする操作を表します。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.INotifyCompletion.OnCompleted(System.Action)">
      <summary>インスタンスが完了したときに呼び出される継続の操作をスケジュールします。</summary>
      <param name="continuation">操作の完了時に呼び出すアクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 引数が null (Visual Basic では Nothing) です。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TaskAwaiter">
      <summary>非同期タスクの完了待ちのオブジェクトを提供します。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.GetResult">
      <summary>非同期タスクの完了の待機を終了します。</summary>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> オブジェクトが正常に初期化されませんでした。</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">タスクが取り消されました。</exception>
      <exception cref="T:System.Exception">
        <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 状態で完了したタスク。</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.TaskAwaiter.IsCompleted">
      <summary>非同期タスクが完了したかどうかを示す値を取得します。</summary>
      <returns>タスクが完了した場合は true。それ以外の場合は false。</returns>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> オブジェクトが正常に初期化されませんでした。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.OnCompleted(System.Action)">
      <summary>
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> オブジェクトが、非同期タスクの完了を待機するのをやめたときに実行するアクションを設定します。</summary>
      <param name="continuation">待機操作の完了時に実行するアクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> は null です。</exception>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> オブジェクトが正常に初期化されませんでした。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>この awaiter に関連付けられている非同期タスクに継続の操作をスケジュールします。</summary>
      <param name="continuation">待機操作の完了時に呼び出すアクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> は null です。</exception>
      <exception cref="T:System.InvalidOperationException">awaiter が適切に初期化されませんでした。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TaskAwaiter`1">
      <summary>非同期タスクの完了まで待ってから、結果にパラメーターを提供するオブジェクトを表します。</summary>
      <typeparam name="TResult">タスクの結果。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.GetResult">
      <summary>非同期タスクの完了の待機を終了します。</summary>
      <returns>完了したタスクの結果。</returns>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> オブジェクトが正常に初期化されませんでした。</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">タスクが取り消されました。</exception>
      <exception cref="T:System.Exception">
        <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 状態で完了したタスク。</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.TaskAwaiter`1.IsCompleted">
      <summary>非同期タスクが完了したかどうかを示す値を取得します。</summary>
      <returns>タスクが完了した場合は true。それ以外の場合は false。</returns>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> オブジェクトが正常に初期化されませんでした。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.OnCompleted(System.Action)">
      <summary>
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> オブジェクトが、非同期タスクの完了を待機するのをやめたときに実行するアクションを設定します。</summary>
      <param name="continuation">待機操作の完了時に実行するアクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> は null です。</exception>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> オブジェクトが正常に初期化されませんでした。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.UnsafeOnCompleted(System.Action)">
      <summary>この awaiter に関連付けられている非同期タスクに継続の操作をスケジュールします。</summary>
      <param name="continuation">待機操作の完了時に呼び出すアクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> は null です。</exception>
      <exception cref="T:System.InvalidOperationException">awaiter が適切に初期化されませんでした。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.YieldAwaitable">
      <summary>ターゲット環境に非同期的に切り替える際に待機するコンテキストを提供します。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.GetAwaiter">
      <summary>このクラスのインスタンスの <see cref="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter" /> オブジェクトを取得します。</summary>
      <returns>非同期操作の完了を監視するためのオブジェクト。</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter">
      <summary>ターゲット環境に切り替えると awaiter を提供します。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.GetResult">
      <summary>待機操作を終了します。</summary>
    </member>
    <member name="P:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.IsCompleted">
      <summary>yield が必須でないかどうかを示す値を取得します。</summary>
      <returns>常に false です。これは、<see cref="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter" /> に yield が必須であることを示します。</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.OnCompleted(System.Action)">
      <summary>呼び出す継続を設定します。</summary>
      <param name="continuation">非同期的に呼び出すアクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> は null なので、</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>
        <paramref name="continuation" /> を現在のコンテキストにポストして戻します。</summary>
      <param name="continuation">非同期的に呼び出すアクション。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 引数が null です。</exception>
    </member>
    <member name="T:System.Threading.CancellationToken">
      <summary>操作を取り消す通知を配信します。</summary>
    </member>
    <member name="M:System.Threading.CancellationToken.#ctor(System.Boolean)">
      <summary>
        <see cref="T:System.Threading.CancellationToken" /> を初期化します。</summary>
      <param name="canceled">トークンの取り消された状態。</param>
    </member>
    <member name="P:System.Threading.CancellationToken.CanBeCanceled">
      <summary>このトークンが取り消された状態になることができるかどうかを取得します。</summary>
      <returns>このトークンが取り消された状態になることができる場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="M:System.Threading.CancellationToken.Equals(System.Object)">
      <summary>現在の <see cref="T:System.Threading.CancellationToken" /> インスタンスが指定された <see cref="T:System.Object" /> と等しいかどうかを判断します。</summary>
      <returns>
        <paramref name="other" /> が <see cref="T:System.Threading.CancellationToken" /> であり、2 つのインスタンスが等しい場合は true。それ以外の場合は false。2 つのトークンが同じ <see cref="T:System.Threading.CancellationTokenSource" /> に関連付けられている場合、または両者がパブリック CancellationToken コンストラクターから作成され、その <see cref="P:System.Threading.CancellationToken.IsCancellationRequested" /> 値が等しい場合、2 つのトークンは等しいことになります。</returns>
      <param name="other">このインスタンスと比較する他方のオブジェクト。</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Equals(System.Threading.CancellationToken)">
      <summary>現在の <see cref="T:System.Threading.CancellationToken" /> インスタンスが指定されたトークンと等しいかどうかを判断します。</summary>
      <returns>インスタンスが等しい場合は true。それ以外の場合は false。2 つのトークンが同じ <see cref="T:System.Threading.CancellationTokenSource" /> に関連付けられている場合、または両者がパブリック CancellationToken コンストラクターから作成され、その <see cref="P:System.Threading.CancellationToken.IsCancellationRequested" /> 値が等しい場合、2 つのトークンは等しいことになります。</returns>
      <param name="other">このインスタンスと比較する他方の <see cref="T:System.Threading.CancellationToken" />。</param>
    </member>
    <member name="M:System.Threading.CancellationToken.GetHashCode">
      <summary>
        <see cref="T:System.Threading.CancellationToken" /> のハッシュ関数として機能します。</summary>
      <returns>現在の <see cref="T:System.Threading.CancellationToken" /> インスタンスのハッシュ コード。</returns>
    </member>
    <member name="P:System.Threading.CancellationToken.IsCancellationRequested">
      <summary>このトークンに対して取り消しが要求されたかどうかを取得します。</summary>
      <returns>このトークンに対して取り消しが要求された場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Threading.CancellationToken.None">
      <summary>空の <see cref="T:System.Threading.CancellationToken" /> 値を返します。</summary>
      <returns>空のキャンセル トークン。</returns>
    </member>
    <member name="M:System.Threading.CancellationToken.op_Equality(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>2 つの <see cref="T:System.Threading.CancellationToken" /> インスタンスが等しいかどうかを判断します。</summary>
      <returns>インスタンスが等しい場合は true。それ以外の場合は false。</returns>
      <param name="left">1 つ目のインスタンス。</param>
      <param name="right">2 つ目のインスタンス。</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.op_Inequality(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>2 つの <see cref="T:System.Threading.CancellationToken" /> インスタンスが等しくないかどうかを判断します。</summary>
      <returns>インスタンスが等しくない場合は true。それ以外の場合は false。</returns>
      <param name="left">1 つ目のインスタンス。</param>
      <param name="right">2 つ目のインスタンス。</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action)">
      <summary>この <see cref="T:System.Threading.CancellationToken" /> が取り消されたときに呼び出されるデリゲートを登録します。</summary>
      <returns>コールバックの登録解除に使用できる <see cref="T:System.Threading.CancellationTokenRegistration" /> インスタンス。</returns>
      <param name="callback">
        <see cref="T:System.Threading.CancellationToken" /> が取り消されたときに実行されるデリゲート。</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action,System.Boolean)">
      <summary>この <see cref="T:System.Threading.CancellationToken" /> が取り消されたときに呼び出されるデリゲートを登録します。</summary>
      <returns>コールバックの登録解除に使用できる <see cref="T:System.Threading.CancellationTokenRegistration" /> インスタンス。</returns>
      <param name="callback">
        <see cref="T:System.Threading.CancellationToken" /> が取り消されたときに実行されるデリゲート。</param>
      <param name="useSynchronizationContext">現在の <see cref="T:System.Threading.SynchronizationContext" /> をキャプチャし、<paramref name="callback" /> を呼び出すときに使用するかどうかを示すブール値。</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action{System.Object},System.Object)">
      <summary>この <see cref="T:System.Threading.CancellationToken" /> が取り消されたときに呼び出されるデリゲートを登録します。</summary>
      <returns>コールバックの登録解除に使用できる <see cref="T:System.Threading.CancellationTokenRegistration" /> インスタンス。</returns>
      <param name="callback">
        <see cref="T:System.Threading.CancellationToken" /> が取り消されたときに実行されるデリゲート。</param>
      <param name="state">デリゲートの呼び出し時に <paramref name="callback" /> に渡される状態。null でもかまいません。</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action{System.Object},System.Object,System.Boolean)">
      <summary>この <see cref="T:System.Threading.CancellationToken" /> が取り消されたときに呼び出されるデリゲートを登録します。</summary>
      <returns>コールバックの登録解除に使用できる <see cref="T:System.Threading.CancellationTokenRegistration" /> インスタンス。</returns>
      <param name="callback">
        <see cref="T:System.Threading.CancellationToken" /> が取り消されたときに実行されるデリゲート。</param>
      <param name="state">デリゲートの呼び出し時に <paramref name="callback" /> に渡される状態。null でもかまいません。</param>
      <param name="useSynchronizationContext">現在の <see cref="T:System.Threading.SynchronizationContext" /> をキャプチャし、<paramref name="callback" /> を呼び出すときに使用するかどうかを示すブール値。</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.ThrowIfCancellationRequested">
      <summary>このトークンに対して取り消しが要求された場合、<see cref="T:System.OperationCanceledException" /> をスローします。</summary>
      <exception cref="T:System.OperationCanceledException">The token has had cancellation requested.</exception>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.CancellationToken.WaitHandle">
      <summary>トークンが取り消されたときに通知される <see cref="T:System.Threading.WaitHandle" /> を取得します。</summary>
      <returns>トークンが取り消されたときに通知される <see cref="T:System.Threading.WaitHandle" />。</returns>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="T:System.Threading.CancellationTokenRegistration">
      <summary>
        <see cref="T:System.Threading.CancellationToken" /> に登録されているコールバック デリゲートを表します。</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Dispose">
      <summary>
        <see cref="T:System.Threading.CancellationTokenRegistration" /> クラスの現在のインスタンスによって使用されているすべてのリソースを解放します。</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Equals(System.Object)">
      <summary>現在の <see cref="T:System.Threading.CancellationTokenRegistration" /> インスタンスが、指定された <see cref="T:System.Threading.CancellationTokenRegistration" /> と等しいかどうかを判断します。</summary>
      <returns>これと <paramref name="obj" /> の両方が等しい場合は True。それ以外の場合は False。2 つの <see cref="T:System.Threading.CancellationTokenRegistration" /> インスタンスが、<see cref="T:System.Threading.CancellationToken" /> の同じ Register メソッドの単一の呼び出しの結果を参照している場合、両者は等しいことになります。</returns>
      <param name="obj">このインスタンスと比較する他のオブジェクト。</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Equals(System.Threading.CancellationTokenRegistration)">
      <summary>現在の <see cref="T:System.Threading.CancellationTokenRegistration" /> インスタンスが、指定された <see cref="T:System.Threading.CancellationTokenRegistration" /> と等しいかどうかを判断します。</summary>
      <returns>これと <paramref name="other" /> の両方が等しい場合は True。それ以外の場合は False。 2 つの <see cref="T:System.Threading.CancellationTokenRegistration" /> インスタンスが、<see cref="T:System.Threading.CancellationToken" /> の同じ Register メソッドの単一の呼び出しの結果を参照している場合、両者は等しいことになります。</returns>
      <param name="other">このインスタンスと比較する他の <see cref="T:System.Threading.CancellationTokenRegistration" />。</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.GetHashCode">
      <summary>
        <see cref="T:System.Threading.CancellationTokenRegistration" /> のハッシュ関数として機能します。</summary>
      <returns>現在の <see cref="T:System.Threading.CancellationTokenRegistration" /> インスタンスのハッシュ コード。</returns>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.op_Equality(System.Threading.CancellationTokenRegistration,System.Threading.CancellationTokenRegistration)">
      <summary>2 つの <see cref="T:System.Threading.CancellationTokenRegistration" /> インスタンスが等しいかどうかを判断します。</summary>
      <returns>インスタンスが等しい場合は true、それ以外の場合は false。</returns>
      <param name="left">1 つ目のインスタンス。</param>
      <param name="right">2 つ目のインスタンス。</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.op_Inequality(System.Threading.CancellationTokenRegistration,System.Threading.CancellationTokenRegistration)">
      <summary>2 つの <see cref="T:System.Threading.CancellationTokenRegistration" /> インスタンスが等しくないかどうかを判断します。</summary>
      <returns>インスタンスが等しくない場合は true。それ以外の場合は false。</returns>
      <param name="left">1 つ目のインスタンス。</param>
      <param name="right">2 つ目のインスタンス。</param>
    </member>
    <member name="T:System.Threading.CancellationTokenSource">
      <summary>取り消す必要があることを <see cref="T:System.Threading.CancellationToken" /> に通知します。</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor">
      <summary>
        <see cref="T:System.Threading.CancellationTokenSource" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor(System.Int32)">
      <summary>指定した遅延 (ミリ秒単位) が経過した後に取り消される <see cref="T:System.Threading.CancellationTokenSource" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="millisecondsDelay">この <see cref="T:System.Threading.CancellationTokenSource" /> を取り消すまで待機する時間間隔 (ミリ秒単位)。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsDelay" /> is less than -1. </exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor(System.TimeSpan)">
      <summary>指定した期間の後に取り消される <see cref="T:System.Threading.CancellationTokenSource" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="delay">この <see cref="T:System.Threading.CancellationTokenSource" /> を取り消すまで待機する時間間隔。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" />.<see cref="P:System.TimeSpan.TotalMilliseconds" /> is less than -1 or greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Cancel">
      <summary>取り消しの要求を伝えます。</summary>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">An aggregate exception containing all the exceptions thrown by the registered callbacks on the associated <see cref="T:System.Threading.CancellationToken" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Cancel(System.Boolean)">
      <summary>キャンセルの要求を伝え、残りのコールバックとキャンセル可能な操作を続けるかどうかを指定します。</summary>
      <param name="throwOnFirstException">例外を直ちに伝達する場合は true。それ以外の場合は false。</param>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">An aggregate exception containing all the exceptions thrown by the registered callbacks on the associated <see cref="T:System.Threading.CancellationToken" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CancelAfter(System.Int32)">
      <summary>指定したミリ秒数が経過した後の、この <see cref="T:System.Threading.CancellationTokenSource" /> の取り消し操作をスケジュールします。</summary>
      <param name="millisecondsDelay">この <see cref="T:System.Threading.CancellationTokenSource" /> を取り消すまで待機する時間。</param>
      <exception cref="T:System.ObjectDisposedException">The exception thrown when this <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception thrown when <paramref name="millisecondsDelay" /> is less than -1.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CancelAfter(System.TimeSpan)">
      <summary>指定した期間が経過した後の、この <see cref="T:System.Threading.CancellationTokenSource" /> の取り消し操作を設定します。</summary>
      <param name="delay">この <see cref="T:System.Threading.CancellationTokenSource" /> を取り消すまで待機する時間。</param>
      <exception cref="T:System.ObjectDisposedException">The exception thrown when this <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when <paramref name="delay" /> is less than -1 or greater than Int32.MaxValue.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CreateLinkedTokenSource(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>すべてのソース トークンが取り消された状態であるときに、取り消された状態になる <see cref="T:System.Threading.CancellationTokenSource" /> を作成します。</summary>
      <returns>ソース トークンにリンクされている <see cref="T:System.Threading.CancellationTokenSource" />。</returns>
      <param name="token1">観察する最初のキャンセル トークン。</param>
      <param name="token2">観察する 2 番目のキャンセル トークン。</param>
      <exception cref="T:System.ObjectDisposedException">A <see cref="T:System.Threading.CancellationTokenSource" /> associated with one of the source tokens has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CreateLinkedTokenSource(System.Threading.CancellationToken[])">
      <summary>指定された配列のすべてのソース トークンが取り消された状態であるときに、取り消された状態になる <see cref="T:System.Threading.CancellationTokenSource" /> を作成します。</summary>
      <returns>ソース トークンにリンクされている <see cref="T:System.Threading.CancellationTokenSource" />。</returns>
      <param name="tokens">観察するためのキャンセル トークン インスタンスを含む配列。</param>
      <exception cref="T:System.ObjectDisposedException">A <see cref="T:System.Threading.CancellationTokenSource" /> associated with one of the source tokens has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tokens" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tokens" /> is empty.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Dispose">
      <summary>
        <see cref="T:System.Threading.CancellationTokenSource" /> クラスの現在のインスタンスによって使用されているすべてのリソースを解放します。</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Threading.CancellationTokenSource" /> クラスによって使用されているアンマネージ リソースを解放し、オプションでマネージ リソースも解放します。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="P:System.Threading.CancellationTokenSource.IsCancellationRequested">
      <summary>この <see cref="T:System.Threading.CancellationTokenSource" /> に対して取り消しが要求されているかどうかを取得します。</summary>
      <returns>この <see cref="T:System.Threading.CancellationTokenSource" /> に対して取り消しが要求されている場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Threading.CancellationTokenSource.Token">
      <summary>この <see cref="T:System.Threading.CancellationToken" /> に関連付けられている <see cref="T:System.Threading.CancellationTokenSource" /> を取得します。</summary>
      <returns>この <see cref="T:System.Threading.CancellationToken" /> に関連付けられている <see cref="T:System.Threading.CancellationTokenSource" />。</returns>
      <exception cref="T:System.ObjectDisposedException">The token source has been disposed.</exception>
    </member>
    <member name="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair">
      <summary>同時実行タスクは同時に実行し、排他的なタスク同時に実行しないように、タスクの実行を調整するタスク スケジューラを提供します。</summary>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor">
      <summary>
        <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>指定したスケジューラをターゲットとする <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="taskScheduler">このペアを実行するターゲット スケジューラ。</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler,System.Int32)">
      <summary>最大同時実行レベルを使用して、指定したスケジューラを対象とする <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="taskScheduler">このペアを実行するターゲット スケジューラ。</param>
      <param name="maxConcurrencyLevel">同時実行するタスクの最大数。</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler,System.Int32,System.Int32)">
      <summary>まとめて処理される可能性のあるスケジュールされたタスクの最大同時実行レベルおよび最大数を使用して、指定したスケジューラを対象とする <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="taskScheduler">このペアを実行するターゲット スケジューラ。</param>
      <param name="maxConcurrencyLevel">同時実行するタスクの最大数。</param>
      <param name="maxItemsPerTask">ペアにより使用される、基となるスケジュールされたタスクごとに、処理されるタスクの最大数。</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.Complete">
      <summary>これ以上タスクを受け入れないことをスケジューラ ペアに通知します。</summary>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.Completion">
      <summary>スケジューラが処理を完了すると完了する <see cref="T:System.Threading.Tasks.Task" /> を取得します。</summary>
      <returns>スケジューラが処理を終了したときに完了する非同期操作。</returns>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.ConcurrentScheduler">
      <summary>このペアの他のタスクと同時に実行できるタスクをこのペアにスケジュールするために使用できる <see cref="T:System.Threading.Tasks.TaskScheduler" /> を取得します。</summary>
      <returns>タスクを同時にスケジュールするために使用できるオブジェクト。</returns>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.ExclusiveScheduler">
      <summary>このペアの他のタスクとは排他的に実行する必要があるタスクをこのペアにスケジュールするために使用できる <see cref="T:System.Threading.Tasks.TaskScheduler" /> を取得します。</summary>
      <returns>他のタスクと同時に実行されないタスクをスケジュールするために使用できるオブジェクト。</returns>
    </member>
    <member name="T:System.Threading.Tasks.Task">
      <summary>非同期操作を表します。この種類の .NET Framework ソース コードを参照するには、参照ソースをご覧ください。</summary>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action)">
      <summary>指定したアクションで新しい <see cref="T:System.Threading.Tasks.Task" /> を初期化します。</summary>
      <param name="action">タスクで実行するコードを表すデリゲート。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.CancellationToken)">
      <summary>指定したアクションおよび <see cref="T:System.Threading.Tasks.Task" /> で新しい <see cref="T:System.Threading.CancellationToken" /> を初期化します。</summary>
      <param name="action">タスクで実行するコードを表すデリゲート。</param>
      <param name="cancellationToken">新しいタスクが観察する <see cref="T:System.Threading.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>指定したアクションと作成オプションで新しい <see cref="T:System.Threading.Tasks.Task" /> を初期化します。</summary>
      <param name="action">タスクで実行するコードを表すデリゲート。</param>
      <param name="cancellationToken">新しいタスクが観察する <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="creationOptions">タスクの動作のカスタマイズに使用する <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.Tasks.TaskCreationOptions)">
      <summary>指定したアクションと作成オプションで新しい <see cref="T:System.Threading.Tasks.Task" /> を初期化します。</summary>
      <param name="action">タスクで実行するコードを表すデリゲート。</param>
      <param name="creationOptions">タスクの動作のカスタマイズに使用する <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object)">
      <summary>指定したアクションと状態で新しい <see cref="T:System.Threading.Tasks.Task" /> を初期化します。</summary>
      <param name="action">タスクで実行するコードを表すデリゲート。</param>
      <param name="state">アクションによって使用されるデータを表すオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>指定したアクション、状態、およびオプションで新しい <see cref="T:System.Threading.Tasks.Task" /> を初期化します。</summary>
      <param name="action">タスクで実行するコードを表すデリゲート。</param>
      <param name="state">アクションによって使用されるデータを表すオブジェクト。</param>
      <param name="cancellationToken">新しいタスクが観察する <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>指定したアクション、状態、およびオプションで新しい <see cref="T:System.Threading.Tasks.Task" /> を初期化します。</summary>
      <param name="action">タスクで実行するコードを表すデリゲート。</param>
      <param name="state">アクションによって使用されるデータを表すオブジェクト。</param>
      <param name="cancellationToken">新しいタスクが観察する <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="creationOptions">タスクの動作のカスタマイズに使用する <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>指定したアクション、状態、およびオプションで新しい <see cref="T:System.Threading.Tasks.Task" /> を初期化します。</summary>
      <param name="action">タスクで実行するコードを表すデリゲート。</param>
      <param name="state">アクションによって使用されるデータを表すオブジェクト。</param>
      <param name="creationOptions">タスクの動作のカスタマイズに使用する <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.AsyncState">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" /> が作成されたときに渡される状態オブジェクトを取得します。渡されなかった場合は null。</summary>
      <returns>タスクの作成時にそのタスクに渡された状態データを表す <see cref="T:System.Object" />。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.CompletedTask">
      <summary>既に正常に完了したタスクを取得します。</summary>
      <returns>正常に完了したタスク。</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.ConfigureAwait(System.Boolean)">
      <summary>この <see cref="T:System.Threading.Tasks.Task" /> を待機するために使用する awaiter を構成します。</summary>
      <returns>このタスクを待機するために使用するオブジェクト。</returns>
      <param name="continueOnCapturedContext">継続をキャプチャされた元のコンテキストにマーシャリングする場合は true。それ以外の場合は false。</param>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task})">
      <summary>ターゲットの <see cref="T:System.Threading.Tasks.Task" /> が完了したときに非同期に実行する継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task" /> の完了時に実行するアクション。実行すると、完了したタスクがデリゲートの引数として渡されます。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>キャンセル トークンを受け取って、対象の <see cref="T:System.Threading.Tasks.Task" /> が完了したときに非同期的に実行される継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task" /> の完了時に実行するアクション。実行すると、完了したタスクがデリゲートの引数として渡されます。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>対象のタスクが完了したときに、指定した <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> に従って実行される継続タスクを作成します。この継続タスクは、キャンセル トークンを受け取り、指定されたスケジューラを使用します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">指定した <paramref name="continuationOptions" /> に従って実行するアクション。実行すると、完了したタスクがデリゲートの引数として渡されます。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="continuationOptions">継続タスクのスケジュールおよびその動作を設定するオプション。これには、<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> などの基準および <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> などの実行オプションが含まれます。</param>
      <param name="scheduler">継続タスクに関連付け、それを実行するために使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>対象のタスクが完了したときに、指定した <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> に従って実行される継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">指定した <paramref name="continuationOptions" /> に従って実行するアクション。実行すると、完了したタスクがデリゲートの引数として渡されます。</param>
      <param name="continuationOptions">継続タスクのスケジュールおよびその動作を設定するオプション。これには、<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> などの基準および <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> などの実行オプションが含まれます。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskScheduler)">
      <summary>ターゲットの <see cref="T:System.Threading.Tasks.Task" /> が完了したときに非同期に実行する継続タスクを作成します。継続タスクは、指定されたスケジューラを使用します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task" /> の完了時に実行するアクション。実行すると、完了したタスクがデリゲートの引数として渡されます。</param>
      <param name="scheduler">継続タスクに関連付け、それを実行するために使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. -or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object)">
      <summary>呼び出し元から提供される状態情報を受け取り、対象の <see cref="T:System.Threading.Tasks.Task" /> が完了したときに実行される継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="continuationAction">タスクの完了時に実行するアクション。実行されると、完了したタスクと、呼び出し元が指定する状態オブジェクトが、引数としてデリゲートに渡されます。</param>
      <param name="state">継続アクションによって使用されるデータを表すオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>呼び出し元から提供される状態情報およびキャンセル トークンを受け取り、対象の <see cref="T:System.Threading.Tasks.Task" /> の完了時に非同期的に実行される継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task" /> の完了時に実行するアクション。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトがデリゲートの引数として渡されます。</param>
      <param name="state">継続アクションによって使用されるデータを表すオブジェクト。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>呼び出し元から提供される状態情報およびキャンセル トークンを受け取り、対象の <see cref="T:System.Threading.Tasks.Task" /> の完了時に実行される継続タスクを作成します。継続タスクは、指定した一連の条件に基づき、指定したスケジューラを使用して実行されます。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task" /> の完了時に実行するアクション。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトがデリゲートの引数として渡されます。</param>
      <param name="state">継続アクションによって使用されるデータを表すオブジェクト。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">継続タスクのスケジュールおよびその動作を設定するオプション。これには、<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> などの基準および <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> などの実行オプションが含まれます。</param>
      <param name="scheduler">継続タスクに関連付け、それを実行するために使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>呼び出し元から提供される状態情報を受け取り、対象の <see cref="T:System.Threading.Tasks.Task" /> が完了したときに実行される継続タスクを作成します。継続タスクは、指定した一連の条件に基づいて実行されます。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task" /> の完了時に実行するアクション。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトがデリゲートの引数として渡されます。</param>
      <param name="state">継続アクションによって使用されるデータを表すオブジェクト。</param>
      <param name="continuationOptions">継続タスクのスケジュールおよびその動作を設定するオプション。これには、<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> などの基準および <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> などの実行オプションが含まれます。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>呼び出し元から提供される状態情報を受け取り、対象の <see cref="T:System.Threading.Tasks.Task" /> が完了したときに非同期的に実行される継続タスクを作成します。継続タスクは、指定されたスケジューラを使用します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task" /> の完了時に実行するアクション。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトがデリゲートの引数として渡されます。</param>
      <param name="state">継続アクションによって使用されるデータを表すオブジェクト。</param>
      <param name="scheduler">継続タスクに関連付け、それを実行するために使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0})">
      <summary>対象の <see cref="T:System.Threading.Tasks.Task`1" /> が完了して値を返したときに非同期的に実行される継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に実行する関数。実行すると、完了したタスクがデリゲートの引数として渡されます。</param>
      <typeparam name="TResult"> 継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken)">
      <summary>対象の <see cref="T:System.Threading.Tasks.Task" /> が完了して値を返したときに非同期的に実行される継続タスクを作成します。この継続タスクは、キャンセル トークンを受け取ります。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task" /> の完了時に実行する関数。実行すると、完了したタスクがデリゲートの引数として渡されます。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <typeparam name="TResult"> 継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>指定された継続のオプションに従って実行され、値を返す継続タスクを作成します。継続タスクは、キャンセル トークンを渡され、指定されたスケジューラを使用します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">指定した <paramref name="continuationOptions." /> に従って実行する関数。実行すると、完了したタスクがデリゲートの引数として渡されます。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="continuationOptions">継続タスクのスケジュールおよびその動作を設定するオプション。これには、<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> などの基準および <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> などの実行オプションが含まれます。</param>
      <param name="scheduler">継続タスクに関連付け、それを実行するために使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TResult"> 継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>指定された継続のオプションに従って実行され、値を返す継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">
        <paramref name="continuationOptions" /> で指定した条件に従って実行する関数。実行すると、完了したタスクがデリゲートの引数として渡されます。</param>
      <param name="continuationOptions">継続タスクのスケジュールおよびその動作を設定するオプション。これには、<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> などの基準および <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> などの実行オプションが含まれます。</param>
      <typeparam name="TResult"> 継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskScheduler)">
      <summary>対象の <see cref="T:System.Threading.Tasks.Task" /> が完了して値を返したときに非同期的に実行される継続タスクを作成します。継続タスクは、指定されたスケジューラを使用します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task" /> の完了時に実行する関数。実行すると、完了したタスクがデリゲートの引数として渡されます。</param>
      <param name="scheduler">継続タスクに関連付け、それを実行するために使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TResult"> 継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object)">
      <summary>呼び出し元から提供される状態情報を受け取り、対象の <see cref="T:System.Threading.Tasks.Task" /> が完了したときに非同期的に実行され、値を返す継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task" /> の完了時に実行する関数。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトがデリゲートの引数として渡されます。</param>
      <param name="state">継続関数によって使用されるデータを表すオブジェクト。</param>
      <typeparam name="TResult">継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>対象の <see cref="T:System.Threading.Tasks.Task" /> が完了したときに非同期的に実行され、値を返す継続タスクを作成します。この継続タスクは、呼び出し元から提供される状態情報とキャンセル トークンを受け取ります。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task" /> の完了時に実行する関数。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトがデリゲートの引数として渡されます。</param>
      <param name="state">継続関数によって使用されるデータを表すオブジェクト。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <typeparam name="TResult">継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>対象の <see cref="T:System.Threading.Tasks.Task" /> が完了したときに、指定したタスク継続オプションに基づいて実行され、値を返す継続タスクを作成します。この継続タスクは、呼び出し元から提供される状態情報とキャンセル トークンを受け取り、指定したスケジューラを使用します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task" /> の完了時に実行する関数。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトがデリゲートの引数として渡されます。</param>
      <param name="state">継続関数によって使用されるデータを表すオブジェクト。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">継続タスクのスケジュールおよびその動作を設定するオプション。これには、<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> などの基準および <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> などの実行オプションが含まれます。</param>
      <param name="scheduler">継続タスクに関連付け、それを実行するために使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TResult">継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>対象の <see cref="T:System.Threading.Tasks.Task" /> が完了したときに、指定したタスク継続オプションに基づいて実行される継続タスクを作成します。この継続タスクは、呼び出し元から提供される状態情報を受け取ります。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task" /> の完了時に実行する関数。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトがデリゲートの引数として渡されます。</param>
      <param name="state">継続関数によって使用されるデータを表すオブジェクト。</param>
      <param name="continuationOptions">継続タスクのスケジュールおよびその動作を設定するオプション。これには、<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> などの基準および <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> などの実行オプションが含まれます。</param>
      <typeparam name="TResult">継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>ターゲットの <see cref="T:System.Threading.Tasks.Task" /> が完了したときに非同期に実行する継続タスクを作成します。この継続タスクは、呼び出し元から提供される状態情報を受け取り、指定したスケジューラを使用します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task" /> の完了時に実行する関数。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトがデリゲートの引数として渡されます。</param>
      <param name="state">継続関数によって使用されるデータを表すオブジェクト。</param>
      <param name="scheduler">継続タスクに関連付け、それを実行するために使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TResult">継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.CreationOptions">
      <summary>このタスクの作成に使用される <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> を取得します。</summary>
      <returns>このタスクの作成に使用される <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.CurrentId">
      <summary>現在実行中の <see cref="T:System.Threading.Tasks.Task" /> の一意の ID を返します。</summary>
      <returns>システムによって現在実行中のタスクに割り当てられた整数。</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.Int32)">
      <summary>遅延後に完了するタスクを作成します。</summary>
      <returns>遅延を表すタスク。</returns>
      <param name="millisecondsDelay">戻されるタスクが完了するまでに待機するミリ秒数。無期限に待機する場合は -1。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsDelay" /> argument is less than -1.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.Int32,System.Threading.CancellationToken)">
      <summary>遅延後に完了するキャンセル可能タスクを作成します。</summary>
      <returns>遅延を表すタスク。</returns>
      <param name="millisecondsDelay">戻されるタスクが完了するまでに待機するミリ秒数。無期限に待機する場合は -1。</param>
      <param name="cancellationToken">戻されるタスクが完了する前にチェックされるキャンセル トークン。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsDelay" /> argument is less than -1. </exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled. </exception>
      <exception cref="T:System.ObjectDisposedException">The provided <paramref name="cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.TimeSpan)">
      <summary>指定の時間間隔後に完了するタスクを作成します。</summary>
      <returns>遅延を表すタスク。</returns>
      <param name="delay">戻されるタスクが完了するまでに待機する時間。無期限に待機する場合は TimeSpan.FromMilliseconds(-1)。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" /> represents a negative time interval other than TimeSpan.FromMillseconds(-1). -or-The <paramref name="delay" /> argument's <see cref="P:System.TimeSpan.TotalMilliseconds" /> property is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>指定の時間間隔後に完了するキャンセル可能タスクを作成します。</summary>
      <returns>遅延を表すタスク。</returns>
      <param name="delay">戻されるタスクが完了するまでに待機する時間。無期限に待機する場合は TimeSpan.FromMilliseconds(-1)。</param>
      <param name="cancellationToken">戻されるタスクが完了する前にチェックされるキャンセル トークン。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" /> represents a negative time interval other than TimeSpan.FromMillseconds(-1). -or-The <paramref name="delay" /> argument's <see cref="P:System.TimeSpan.TotalMilliseconds" /> property is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <paramref name="cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.Exception">
      <summary>
        <see cref="T:System.AggregateException" /> が途中で終了する原因となった <see cref="T:System.Threading.Tasks.Task" /> を取得します。<see cref="T:System.Threading.Tasks.Task" /> が正常に完了した場合、または例外がスローされていない場合は、null が戻ります。</summary>
      <returns>
        <see cref="T:System.AggregateException" /> が途中で終了する原因となった <see cref="T:System.Threading.Tasks.Task" />。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.Factory">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" /> インスタンスおよび <see cref="T:System.Threading.Tasks.Task`1" /> インスタンスを作成して構成するためのファクトリ メソッドへのアクセスを提供します。</summary>
      <returns>さまざまな <see cref="T:System.Threading.Tasks.Task" /> オブジェクトおよび <see cref="T:System.Threading.Tasks.Task`1" /> オブジェクトを作成可能なファクトリ オブジェクト。</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromCanceled(System.Threading.CancellationToken)">
      <summary>指定されたキャンセル トークンを使用したキャンセルにより完了した <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>キャンセルされたタスク。</returns>
      <param name="cancellationToken">タスクを完了させるキャンセル トークン。</param>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromCanceled``1(System.Threading.CancellationToken)">
      <summary>指定されたキャンセル トークンを使用したキャンセルにより完了した <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>キャンセルされたタスク。</returns>
      <param name="cancellationToken">タスクを完了させるキャンセル トークン。</param>
      <typeparam name="TResult">タスクによって返される結果の型。</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromException``1(System.Exception)">
      <summary>指定した例外で完了した <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>エラーが発生したタスク。</returns>
      <param name="exception">タスクを完了させる例外。</param>
      <typeparam name="TResult">タスクによって返される結果の型。</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromException(System.Exception)">
      <summary>指定した例外で完了した <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>エラーが発生したタスク。</returns>
      <param name="exception">タスクを完了させる例外。</param>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromResult``1(``0)">
      <summary>指定した結果で成功した <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>正常に完了したタスク。</returns>
      <param name="result">完了したタスクに格納する結果。</param>
      <typeparam name="TResult">タスクによって返される結果の型。</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.GetAwaiter">
      <summary>この <see cref="T:System.Threading.Tasks.Task" /> を待機するために使用する awaiter を取得します。</summary>
      <returns>awaiter のインスタンス。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.Id">
      <summary>この <see cref="T:System.Threading.Tasks.Task" /> インスタンスの一意の ID を取得します。</summary>
      <returns>システムによってこのタスク インスタンスに割り当てられた整数。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsCanceled">
      <summary>この <see cref="T:System.Threading.Tasks.Task" /> インスタンスの実行が取り消されることによって完了したかどうかを示す値を取得します。</summary>
      <returns>タスクが取り消されることによって完了した場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsCompleted">
      <summary>この <see cref="T:System.Threading.Tasks.Task" /> が完了したかどうかを示す値を取得します。</summary>
      <returns>タスクが完了した場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsFaulted">
      <summary>処理されない例外が発生したことが原因で <see cref="T:System.Threading.Tasks.Task" /> が完了したかどうかを示す値を取得します。</summary>
      <returns>タスクがハンドルされない例外をスローした場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Action)">
      <summary>ThreadPool 上で実行する指定された作業をキューに配置し、その作業のタスク ハンドルを返します。</summary>
      <returns>ThreadPool で実行するためにキューに配置された作業を表すタスク。</returns>
      <param name="action">非同期的に実行する処理</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Action,System.Threading.CancellationToken)">
      <summary>ThreadPool 上で実行する指定された作業をキューに配置し、その作業のタスク ハンドルを返します。</summary>
      <returns>ThreadPool で実行するためにキューに配置された作業を表すタスク。</returns>
      <param name="action">非同期的に実行する処理</param>
      <param name="cancellationToken">処理を取り消すために使用されるキャンセル トークン</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{System.Threading.Tasks.Task{``0}})">
      <summary>ThreadPool 上で実行する指定された作業をキューに配置し、Task(TResult) によって返される <paramref name="function" /> のプロキシを返します。</summary>
      <returns>Task(TResult) によって返される Task(TResult) のプロキシを表す <paramref name="function" />。</returns>
      <param name="function">非同期的に実行する処理</param>
      <typeparam name="TResult">プロキシ タスクによって返される結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
      <summary>ThreadPool 上で実行する指定された作業をキューに配置し、Task(TResult) によって返される <paramref name="function" /> のプロキシを返します。</summary>
      <returns>Task(TResult) によって返される Task(TResult) のプロキシを表す <paramref name="function" />。</returns>
      <param name="function">非同期的に実行する処理</param>
      <param name="cancellationToken">処理を取り消すために使用されるキャンセル トークン</param>
      <typeparam name="TResult">プロキシ タスクによって返される結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Func{System.Threading.Tasks.Task})">
      <summary>ThreadPool 上で実行する指定された作業をキューに配置し、<paramref name="function" /> によって返されるタスクのプロキシを返します。</summary>
      <returns>
        <paramref name="function" /> によって返されるタスクのプロキシを表すタスク。</returns>
      <param name="function">非同期的に実行する処理</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Func{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>ThreadPool 上で実行する指定された作業をキューに配置し、<paramref name="function" /> によって返されるタスクのプロキシを返します。</summary>
      <returns>
        <paramref name="function" /> によって返されるタスクのプロキシを表すタスク。</returns>
      <param name="function">非同期的に実行する処理。</param>
      <param name="cancellationToken">処理を取り消すために使用されるキャンセル トークン。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{``0})">
      <summary>スレッド プール上で実行する指定された作業をキューに配置し、その作業を表す <see cref="T:System.Threading.Tasks.Task`1" /> オブジェクトを戻します。</summary>
      <returns>スレッド プールで実行するためキューに配置された処理を表すタスク オブジェクト。</returns>
      <param name="function">非同期的に実行する処理。</param>
      <typeparam name="TResult">タスクの戻り値の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{``0},System.Threading.CancellationToken)">
      <summary>スレッド プール上で実行する指定された作業をキューに配置し、その作業の Task(TResult) ハンドルを返します。</summary>
      <returns>ThreadPool で実行するためにキューに配置された作業を表す Task(TResult)。</returns>
      <param name="function">非同期的に実行する処理</param>
      <param name="cancellationToken">処理を取り消すために使用されるキャンセル トークン</param>
      <typeparam name="TResult">タスクの結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.RunSynchronously">
      <summary>現在の <see cref="T:System.Threading.Tasks.Task" /> で <see cref="T:System.Threading.Tasks.TaskScheduler" /> を同期的に実行します。</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.RunSynchronously(System.Threading.Tasks.TaskScheduler)">
      <summary>指定された <see cref="T:System.Threading.Tasks.Task" /> で <see cref="T:System.Threading.Tasks.TaskScheduler" /> を同期的に実行します。</summary>
      <param name="scheduler">このタスク インラインの実行を試みるスケジューラ。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Start">
      <summary>現在の <see cref="T:System.Threading.Tasks.Task" /> に <see cref="T:System.Threading.Tasks.TaskScheduler" /> の実行をスケジュールし、それを開始します。</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Start(System.Threading.Tasks.TaskScheduler)">
      <summary>指定された <see cref="T:System.Threading.Tasks.Task" /> に <see cref="T:System.Threading.Tasks.TaskScheduler" /> の実行をスケジュールし、それを開始します。</summary>
      <param name="scheduler">このタスクを関連付けて実行する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.Status">
      <summary>このタスクの <see cref="T:System.Threading.Tasks.TaskStatus" /> を取得します。</summary>
      <returns>このタスク インスタンスの現在の <see cref="T:System.Threading.Tasks.TaskStatus" />。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.System#IAsyncResult#AsyncWaitHandle">
      <summary>タスクの完了を待機するために使用できる <see cref="T:System.Threading.WaitHandle" /> を取得します。</summary>
      <returns>タスクの完了を待機するために使用できる <see cref="T:System.Threading.WaitHandle" />。</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.System#IAsyncResult#CompletedSynchronously">
      <summary>操作が同期的に完了したかどうかを示す値を取得します。</summary>
      <returns>操作が同期的に完了した場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" /> の実行が完了するまで待機します。</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Int32)">
      <summary>提供された <see cref="T:System.Threading.Tasks.Task" /> の実行が完了するまで、指定したミリ秒数以内の間、待機します。</summary>
      <returns>割り当てられた時間内に true の実行が完了した場合は <see cref="T:System.Threading.Tasks.Task" />。それ以外の場合は false。</returns>
      <param name="millisecondsTimeout">待機するミリ秒数。無制限に待機する場合は <see cref="F:System.Threading.Timeout.Infinite" /> (-1)。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Int32,System.Threading.CancellationToken)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" /> の実行が完了するまで待機します。タスクの完了前に、タイムアウト期間が経過するか、キャンセル トークンが取り消される場合には、待機が終了します。</summary>
      <returns>割り当てられた時間内に true の実行が完了した場合は <see cref="T:System.Threading.Tasks.Task" />。それ以外の場合は false。</returns>
      <param name="millisecondsTimeout">待機するミリ秒数。無制限に待機する場合は <see cref="F:System.Threading.Timeout.Infinite" /> (-1)。</param>
      <param name="cancellationToken">タスクの完了の待機中に観察するキャンセル トークン。</param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Threading.CancellationToken)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" /> の実行が完了するまで待機します。タスクの完了前にキャンセル トークンが取り消される場合は、待機が終了します。</summary>
      <param name="cancellationToken">タスクの完了の待機中に観察するキャンセル トークン。</param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The task has been disposed.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.TimeSpan)">
      <summary>提供された <see cref="T:System.Threading.Tasks.Task" /> の実行が完了するまで、指定した時間間隔内の間、待機します。</summary>
      <returns>割り当てられた時間内に true の実行が完了した場合は <see cref="T:System.Threading.Tasks.Task" />。それ以外の場合は false。</returns>
      <param name="timeout">待機するミリ秒数を表す <see cref="T:System.TimeSpan" />。無制限に待機する場合は、-1 ミリ秒を表す <see cref="T:System.TimeSpan" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[])">
      <summary>指定したすべての <see cref="T:System.Threading.Tasks.Task" /> オブジェクトの実行が完了するまで待機します。</summary>
      <param name="tasks">待機する <see cref="T:System.Threading.Tasks.Task" /> インスタンスの配列。</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.-or-The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> exception contains an <see cref="T:System.OperationCanceledException" /> exception in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Int32)">
      <summary>提供されたすべての <see cref="T:System.Threading.Tasks.Task" /> オブジェクトの実行が完了するまで、指定したミリ秒数以内の間、待機します。</summary>
      <returns>割り当てられた時間内に true インスタンスすべての実行が完了した場合は <see cref="T:System.Threading.Tasks.Task" />。それ以外の場合は false。</returns>
      <param name="tasks">待機する <see cref="T:System.Threading.Tasks.Task" /> インスタンスの配列。</param>
      <param name="millisecondsTimeout">待機するミリ秒数。無制限に待機する場合は <see cref="F:System.Threading.Timeout.Infinite" /> (-1)。</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Int32,System.Threading.CancellationToken)">
      <summary>指定したミリ秒数まで、または待機が取り消されるまで、提供されたすべての <see cref="T:System.Threading.Tasks.Task" /> オブジェクトの実行が完了するのを待機します。</summary>
      <returns>割り当てられた時間内に true インスタンスすべての実行が完了した場合は <see cref="T:System.Threading.Tasks.Task" />。それ以外の場合は false。</returns>
      <param name="tasks">待機する <see cref="T:System.Threading.Tasks.Task" /> インスタンスの配列。</param>
      <param name="millisecondsTimeout">待機するミリ秒数。無制限に待機する場合は <see cref="F:System.Threading.Timeout.Infinite" /> (-1)。</param>
      <param name="cancellationToken">タスクの完了を待機しているときに観察する <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Threading.CancellationToken)">
      <summary>待機が取り消されない限り、指定したすべての <see cref="T:System.Threading.Tasks.Task" /> オブジェクトの実行が完了するまで待機します。</summary>
      <param name="tasks">待機する <see cref="T:System.Threading.Tasks.Task" /> インスタンスの配列。</param>
      <param name="cancellationToken">タスクの完了を待機しているときに観察する <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.TimeSpan)">
      <summary>提供されたすべてのキャンセル可能な <see cref="T:System.Threading.Tasks.Task" /> オブジェクトの実行が完了するまで、指定した時間間隔の間、待機します。</summary>
      <returns>割り当てられた時間内に true インスタンスすべての実行が完了した場合は <see cref="T:System.Threading.Tasks.Task" />。それ以外の場合は false。</returns>
      <param name="tasks">待機する <see cref="T:System.Threading.Tasks.Task" /> インスタンスの配列。</param>
      <param name="timeout">待機するミリ秒数を表す <see cref="T:System.TimeSpan" />。無制限に待機する場合は、-1 ミリ秒を表す <see cref="T:System.TimeSpan" />。</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null. </exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[])">
      <summary>指定したいずれかの <see cref="T:System.Threading.Tasks.Task" /> オブジェクトの実行が完了するまで待機します。</summary>
      <returns>
        <paramref name="tasks" /> 配列引数内の完了したタスクのインデックス。</returns>
      <param name="tasks">待機する <see cref="T:System.Threading.Tasks.Task" /> インスタンスの配列。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Int32)">
      <summary>提供されたいずれかの <see cref="T:System.Threading.Tasks.Task" /> オブジェクトの実行が完了するまで、指定したミリ秒数以内の間、待機します。</summary>
      <returns>
        <paramref name="tasks" /> 配列引数内の完了したタスクのインデックス。タイムアウトが発生した場合は -1。</returns>
      <param name="tasks">待機する <see cref="T:System.Threading.Tasks.Task" /> インスタンスの配列。</param>
      <param name="millisecondsTimeout">待機するミリ秒数。無制限に待機する場合は <see cref="F:System.Threading.Timeout.Infinite" /> (-1)。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Int32,System.Threading.CancellationToken)">
      <summary>指定したミリ秒数まで、または待機トークンが取り消されるまで、提供されたいずれかの <see cref="T:System.Threading.Tasks.Task" /> オブジェクトの実行が完了するのを待機します。</summary>
      <returns>
        <paramref name="tasks" /> 配列引数内の完了したタスクのインデックス。タイムアウトが発生した場合は -1。</returns>
      <param name="tasks">待機する <see cref="T:System.Threading.Tasks.Task" /> インスタンスの配列。</param>
      <param name="millisecondsTimeout">待機するミリ秒数。無制限に待機する場合は <see cref="F:System.Threading.Timeout.Infinite" /> (-1)。</param>
      <param name="cancellationToken">タスクの完了を待機しているときに監視する <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Threading.CancellationToken)">
      <summary>待機が取り消されない限り、指定したいずれかの <see cref="T:System.Threading.Tasks.Task" /> オブジェクトの実行が完了するまで待機します。</summary>
      <returns>
        <paramref name="tasks" /> 配列引数内の完了したタスクのインデックス。</returns>
      <param name="tasks">待機する <see cref="T:System.Threading.Tasks.Task" /> インスタンスの配列。</param>
      <param name="cancellationToken">タスクの完了を待機しているときに監視する <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.TimeSpan)">
      <summary>提供されたいずれかの <see cref="T:System.Threading.Tasks.Task" /> オブジェクトの実行が完了するまで、指定した時間間隔内の間、待機します。</summary>
      <returns>
        <paramref name="tasks" /> 配列引数内の完了したタスクのインデックス。タイムアウトが発生した場合は -1。</returns>
      <param name="tasks">待機する <see cref="T:System.Threading.Tasks.Task" /> インスタンスの配列。</param>
      <param name="timeout">待機するミリ秒数を表す <see cref="T:System.TimeSpan" />。無制限に待機する場合は、-1 ミリ秒を表す <see cref="T:System.TimeSpan" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
      <summary>列挙可能なコレクション内のすべての <see cref="T:System.Threading.Tasks.Task`1" /> オブジェクトが完了したときに完了するタスクを作成します。</summary>
      <returns>指定されたすべてのタスクの完了を表すタスク。</returns>
      <param name="tasks">完了を待機するタスク。</param>
      <typeparam name="TResult">完了したタスクの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> collection contained a null task. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
      <summary>列挙可能なコレクション内のすべての <see cref="T:System.Threading.Tasks.Task" /> オブジェクトが完了したときに完了するタスクを作成します。</summary>
      <returns>指定されたすべてのタスクの完了を表すタスク。</returns>
      <param name="tasks">完了を待機するタスク。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> collection contained a null task.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll(System.Threading.Tasks.Task[])">
      <summary>配列内のすべての <see cref="T:System.Threading.Tasks.Task" /> オブジェクトが完了したときに完了するタスクを作成します。</summary>
      <returns>指定されたすべてのタスクの完了を表すタスク。</returns>
      <param name="tasks">完了を待機するタスク。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll``1(System.Threading.Tasks.Task{``0}[])">
      <summary>配列内のすべての <see cref="T:System.Threading.Tasks.Task`1" /> オブジェクトが完了したときに完了するタスクを作成します。</summary>
      <returns>指定されたすべてのタスクの完了を表すタスク。</returns>
      <param name="tasks">完了を待機するタスク。</param>
      <typeparam name="TResult">完了したタスクの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
      <summary>指定されたすべてのタスクが完了してから完了するタスクを作成します。</summary>
      <returns>指定されたいずれかのタスクの完了を表すタスク。返されるタスクの結果は完了したタスクです。</returns>
      <param name="tasks">完了を待機するタスク。</param>
      <typeparam name="TResult">完了したタスクの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
      <summary>指定されたすべてのタスクが完了してから完了するタスクを作成します。</summary>
      <returns>指定されたいずれかのタスクの完了を表すタスク。返されるタスクの結果は完了したタスクです。</returns>
      <param name="tasks">完了を待機するタスク。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny(System.Threading.Tasks.Task[])">
      <summary>指定されたすべてのタスクが完了してから完了するタスクを作成します。</summary>
      <returns>指定されたいずれかのタスクの完了を表すタスク。返されるタスクの結果は完了したタスクです。</returns>
      <param name="tasks">完了を待機するタスク。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny``1(System.Threading.Tasks.Task{``0}[])">
      <summary>指定されたすべてのタスクが完了してから完了するタスクを作成します。</summary>
      <returns>指定されたいずれかのタスクの完了を表すタスク。返されるタスクの結果は完了したタスクです。</returns>
      <param name="tasks">完了を待機するタスク。</param>
      <typeparam name="TResult">完了したタスクの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Yield">
      <summary>待機されたときに現在のコンテキストに非同期的に処理を譲る awaitable タスクを作成します。</summary>
      <returns>必要な場合は、要求時に現在のコンテキストに非同期で再度遷移するコンテキスト。現在の <see cref="T:System.Threading.SynchronizationContext" /> が null 以外の場合は、現在のコンテキストとして扱われます。それ以外の場合は、現在の実行タスクに関連付けられているタスク スケジューラは、現在のコンテキストとして扱われます。</returns>
    </member>
    <member name="T:System.Threading.Tasks.Task`1">
      <summary>値を返すことができる非同期操作を表します。</summary>
      <typeparam name="TResult">この <see cref="T:System.Threading.Tasks.Task`1" /> によって生成される結果の型。</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0})">
      <summary>指定の関数で新しい <see cref="T:System.Threading.Tasks.Task`1" /> を初期化します。</summary>
      <param name="function">タスクで実行するコードを表すデリゲート。関数の実行が完了すると、タスクの <see cref="P:System.Threading.Tasks.Task`1.Result" /> プロパティが関数の結果値を返すように設定されます。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.CancellationToken)">
      <summary>指定の関数で新しい <see cref="T:System.Threading.Tasks.Task`1" /> を初期化します。</summary>
      <param name="function">タスクで実行するコードを表すデリゲート。関数の実行が完了すると、タスクの <see cref="P:System.Threading.Tasks.Task`1.Result" /> プロパティが関数の結果値を返すように設定されます。</param>
      <param name="cancellationToken">このタスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>指定の関数と作成オプションを使用して新しい <see cref="T:System.Threading.Tasks.Task`1" /> を初期化します。</summary>
      <param name="function">タスクで実行するコードを表すデリゲート。関数の実行が完了すると、タスクの <see cref="P:System.Threading.Tasks.Task`1.Result" /> プロパティが関数の結果値を返すように設定されます。</param>
      <param name="cancellationToken">新しいタスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="creationOptions">タスクの動作のカスタマイズに使用する <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>指定の関数と作成オプションを使用して新しい <see cref="T:System.Threading.Tasks.Task`1" /> を初期化します。</summary>
      <param name="function">タスクで実行するコードを表すデリゲート。関数の実行が完了すると、タスクの <see cref="P:System.Threading.Tasks.Task`1.Result" /> プロパティが関数の結果値を返すように設定されます。</param>
      <param name="creationOptions">タスクの動作のカスタマイズに使用する <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object)">
      <summary>指定の関数と状態で新しい <see cref="T:System.Threading.Tasks.Task`1" /> を初期化します。</summary>
      <param name="function">タスクで実行するコードを表すデリゲート。関数の実行が完了すると、タスクの <see cref="P:System.Threading.Tasks.Task`1.Result" /> プロパティが関数の結果値を返すように設定されます。</param>
      <param name="state">アクションによって使用されるデータを表すオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken)">
      <summary>指定したアクション、状態、およびオプションで新しい <see cref="T:System.Threading.Tasks.Task`1" /> を初期化します。</summary>
      <param name="function">タスクで実行するコードを表すデリゲート。関数の実行が完了すると、タスクの <see cref="P:System.Threading.Tasks.Task`1.Result" /> プロパティが関数の結果値を返すように設定されます。</param>
      <param name="state">関数によって使用されるデータを表すオブジェクト。</param>
      <param name="cancellationToken">新しいタスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>指定したアクション、状態、およびオプションで新しい <see cref="T:System.Threading.Tasks.Task`1" /> を初期化します。</summary>
      <param name="function">タスクで実行するコードを表すデリゲート。関数の実行が完了すると、タスクの <see cref="P:System.Threading.Tasks.Task`1.Result" /> プロパティが関数の結果値を返すように設定されます。</param>
      <param name="state">関数によって使用されるデータを表すオブジェクト。</param>
      <param name="cancellationToken">新しいタスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="creationOptions">タスクの動作のカスタマイズに使用する <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>指定したアクション、状態、およびオプションで新しい <see cref="T:System.Threading.Tasks.Task`1" /> を初期化します。</summary>
      <param name="function">タスクで実行するコードを表すデリゲート。関数の実行が完了すると、タスクの <see cref="P:System.Threading.Tasks.Task`1.Result" /> プロパティが関数の結果値を返すように設定されます。</param>
      <param name="state">関数によって使用されるデータを表すオブジェクト。</param>
      <param name="creationOptions">タスクの動作のカスタマイズに使用する <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ConfigureAwait(System.Boolean)">
      <summary>この <see cref="T:System.Threading.Tasks.Task`1" /> を待機するために使用する awaiter を構成します。</summary>
      <returns>このタスクを待機するために使用するオブジェクト。</returns>
      <param name="continueOnCapturedContext">継続をキャプチャされた元のコンテキストにマーシャリングする場合は true。それ以外の場合は false。</param>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}})">
      <summary>対象タスクの完了時に、非同期に実行する継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="continuationAction">先行する <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に実行するアクション。実行すると、完了したタスクがデリゲートの引数として渡されます。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.CancellationToken)">
      <summary>対象の <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に、非同期的に実行するキャンセル可能な継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に実行するアクション。実行すると、完了したタスクにデリゲートが引数として渡されます。</param>
      <param name="cancellationToken">新しい継続タスクに渡されるキャンセル トークン。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>
        <paramref name="continuationOptions" /> で指定した条件に従って実行する継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">
        <paramref name="continuationOptions" /> で指定した条件に従って実行するアクション。実行すると、完了したタスクがデリゲートの引数として渡されます。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">継続タスクのスケジュールおよびその動作を設定するオプション。これには、<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> などの基準および <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> などの実行オプションが含まれます。</param>
      <param name="scheduler">継続タスクに関連付け、それを実行するために使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>
        <paramref name="continuationOptions" /> で指定した条件に従って実行する継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">
        <paramref name="continuationOptions" /> で指定した条件に従って実行するアクション。実行すると、完了したタスクがデリゲートの引数として渡されます。</param>
      <param name="continuationOptions">継続タスクのスケジュールおよびその動作を設定するオプション。これには、<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> などの基準および <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> などの実行オプションが含まれます。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskScheduler)">
      <summary>ターゲットの <see cref="T:System.Threading.Tasks.Task`1" /> が完了したときに非同期に実行する継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に実行するアクション。実行すると、完了したタスクがデリゲートの引数として渡されます。</param>
      <param name="scheduler">継続タスクに関連付け、それを実行するために使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object)">
      <summary>状態の情報を渡される継続と、ターゲット <see cref="T:System.Threading.Tasks.Task`1" /> が完了したときに実行する継続を作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に実行するアクション。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトが引数としてデリゲートに渡されます。</param>
      <param name="state">継続アクションによって使用されるデータを表すオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>ターゲットの <see cref="T:System.Threading.Tasks.Task`1" /> が完了したときに実行する継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に実行するアクション。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトがデリゲートの引数として渡されます。</param>
      <param name="state">継続アクションによって使用されるデータを表すオブジェクト。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>ターゲットの <see cref="T:System.Threading.Tasks.Task`1" /> が完了したときに実行する継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に実行するアクション。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトがデリゲートの引数として渡されます。</param>
      <param name="state">継続アクションによって使用されるデータを表すオブジェクト。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">継続タスクのスケジュールおよびその動作を設定するオプション。これには、<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> などの基準および <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> などの実行オプションが含まれます。</param>
      <param name="scheduler">継続タスクに関連付け、それを実行するために使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>ターゲットの <see cref="T:System.Threading.Tasks.Task`1" /> が完了したときに実行する継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に実行するアクション。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトがデリゲートの引数として渡されます。</param>
      <param name="state">継続アクションによって使用されるデータを表すオブジェクト。</param>
      <param name="continuationOptions">継続タスクのスケジュールおよびその動作を設定するオプション。これには、<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> などの基準および <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> などの実行オプションが含まれます。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>ターゲットの <see cref="T:System.Threading.Tasks.Task`1" /> が完了したときに実行する継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">
        <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に実行するアクション。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトがデリゲートの引数として渡されます。</param>
      <param name="state">継続アクションによって使用されるデータを表すオブジェクト。</param>
      <param name="scheduler">継続タスクに関連付け、それを実行するために使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0})">
      <summary>ターゲットの <see cref="T:System.Threading.Tasks.Task`1" /> が完了したときに非同期に実行する継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に実行する関数。実行すると、完了したタスクがデリゲートの引数として渡されます。</param>
      <typeparam name="TNewResult"> 継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.CancellationToken)">
      <summary>ターゲットの <see cref="T:System.Threading.Tasks.Task`1" /> が完了したときに非同期に実行する継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に実行する関数。実行すると、完了したタスクがデリゲートの引数として渡されます。</param>
      <param name="cancellationToken">新しいタスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <typeparam name="TNewResult"> 継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>
        <paramref name="continuationOptions" /> で指定した条件に従って実行する継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">
        <paramref name="continuationOptions" /> で指定した条件に従って実行する関数。実行すると、この完了したタスクがデリゲートの引数として渡されます。</param>
      <param name="cancellationToken">新しいタスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">継続タスクのスケジュールおよびその動作を設定するオプション。これには、<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> などの基準および <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> などの実行オプションが含まれます。</param>
      <param name="scheduler">継続タスクに関連付け、それを実行するために使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TNewResult"> 継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>
        <paramref name="continuationOptions" /> で指定した条件に従って実行する継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">
        <paramref name="continuationOptions" /> で指定した条件に従って実行する関数。実行すると、完了したタスクがデリゲートの引数として渡されます。</param>
      <param name="continuationOptions">継続タスクのスケジュールおよびその動作を設定するオプション。これには、<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> などの基準および <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> などの実行オプションが含まれます。</param>
      <typeparam name="TNewResult"> 継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.Tasks.TaskScheduler)">
      <summary>ターゲットの <see cref="T:System.Threading.Tasks.Task`1" /> が完了したときに非同期に実行する継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に実行する関数。実行すると、完了したタスクがデリゲートの引数として渡されます。</param>
      <param name="scheduler">継続タスクに関連付け、それを実行するために使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TNewResult"> 継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object)">
      <summary>ターゲットの <see cref="T:System.Threading.Tasks.Task`1" /> が完了したときに実行する継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に実行する関数。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトがデリゲートの引数として渡されます。</param>
      <param name="state">継続関数によって使用されるデータを表すオブジェクト。</param>
      <typeparam name="TNewResult">継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>ターゲットの <see cref="T:System.Threading.Tasks.Task`1" /> が完了したときに実行する継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に実行する関数。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトがデリゲートの引数として渡されます。</param>
      <param name="state">継続関数によって使用されるデータを表すオブジェクト。</param>
      <param name="cancellationToken">新しいタスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <typeparam name="TNewResult">継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>ターゲットの <see cref="T:System.Threading.Tasks.Task`1" /> が完了したときに実行する継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に実行する関数。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトがデリゲートの引数として渡されます。</param>
      <param name="state">継続関数によって使用されるデータを表すオブジェクト。</param>
      <param name="cancellationToken">新しいタスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">継続タスクのスケジュールおよびその動作を設定するオプション。これには、<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> などの基準および <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> などの実行オプションが含まれます。</param>
      <param name="scheduler">継続タスクに関連付け、それを実行するために使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TNewResult">継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The  <paramref name="continuationOptions" />  argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>ターゲットの <see cref="T:System.Threading.Tasks.Task`1" /> が完了したときに実行する継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に実行する関数。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトがデリゲートの引数として渡されます。</param>
      <param name="state">継続関数によって使用されるデータを表すオブジェクト。</param>
      <param name="continuationOptions">継続タスクのスケジュールおよびその動作を設定するオプション。これには、<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> などの基準および <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> などの実行オプションが含まれます。</param>
      <typeparam name="TNewResult">継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>ターゲットの <see cref="T:System.Threading.Tasks.Task`1" /> が完了したときに実行する継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">
        <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に実行する関数。実行すると、完了したタスクおよび呼び出し元が指定する状態オブジェクトがデリゲートの引数として渡されます。</param>
      <param name="state">継続関数によって使用されるデータを表すオブジェクト。</param>
      <param name="scheduler">継続タスクに関連付け、それを実行するために使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TNewResult">継続タスクによって生成される結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task`1.Factory">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" /> インスタンスを作成して構成するためのファクトリ メソッドへアクセスを提供します。</summary>
      <returns>さまざまな <see cref="T:System.Threading.Tasks.Task`1" /> オブジェクトを作成できるファクトリ オブジェクト。</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.GetAwaiter">
      <summary>この <see cref="T:System.Threading.Tasks.Task`1" /> を待機するために使用する awaiter を取得します。</summary>
      <returns>awaiter のインスタンス。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task`1.Result">
      <summary>この <see cref="T:System.Threading.Tasks.Task`1" /> の結果値を取得します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> の結果値。これは、タスクの型パラメーターと同じ型です。</returns>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskCanceledException">
      <summary>タスクの取り消しを通知するために使用される例外を表します。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor">
      <summary>エラーを説明するシステム提供のメッセージを使用して、<see cref="T:System.Threading.Tasks.TaskCanceledException" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.String)">
      <summary>エラーを説明する指定したメッセージを使用して、<see cref="T:System.Threading.Tasks.TaskCanceledException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外を説明するメッセージ。このコンストラクターの呼び出し元では、この文字列が現在のシステムのカルチャに合わせてローカライズ済みであることを確認しておく必要があります。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.String,System.Exception)">
      <summary>指定したエラー メッセージと、この例外の原因である内部例外への参照を使用して、<see cref="T:System.Threading.Tasks.TaskCanceledException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外を説明するメッセージ。このコンストラクターの呼び出し元では、この文字列が現在のシステムのカルチャに合わせてローカライズ済みであることを確認しておく必要があります。</param>
      <param name="innerException">現在の例外の原因である例外。<paramref name="innerException" /> パラメーターが null でない場合は、内部例外を処理する catch ブロックで現在の例外が発生します。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.Threading.Tasks.Task)">
      <summary>取り消された <see cref="T:System.Threading.Tasks.Task" /> への参照を使用して、<see cref="T:System.Threading.Tasks.TaskCanceledException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="task">取り消されたタスク。</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskCanceledException.Task">
      <summary>この例外に関連付けられているタスクを取得します。</summary>
      <returns>この例外に関連付けられている <see cref="T:System.Threading.Tasks.Task" /> への参照。</returns>
    </member>
    <member name="T:System.Threading.Tasks.TaskCompletionSource`1">
      <summary>デリゲートに関連付けられていない <see cref="T:System.Threading.Tasks.Task`1" /> のプロデューサー側を表し、<see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> プロパティを通じてコンシューマー側へのアクセスを提供します。</summary>
      <typeparam name="TResult">この <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> に関連付けられている結果値の型。</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor">
      <summary>
        <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> を作成します。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Object)">
      <summary>指定された状態を使用して、<see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> を作成します。</summary>
      <param name="state">基になる <see cref="T:System.Threading.Tasks.Task`1" /> の AsyncState として使用する状態。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>指定された状態とオプションを使用して、<see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> を作成します。</summary>
      <param name="state">基になる <see cref="T:System.Threading.Tasks.Task`1" /> の AsyncState として使用する状態。</param>
      <param name="creationOptions">基になる <see cref="T:System.Threading.Tasks.Task`1" /> の作成時に使用するオプション。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> は、<see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> で使用には無効なオプションを表します。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Threading.Tasks.TaskCreationOptions)">
      <summary>指定されたオプションを使用して、<see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> を作成します。</summary>
      <param name="creationOptions">基になる <see cref="T:System.Threading.Tasks.Task`1" /> の作成時に使用するオプション。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> は、<see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> で使用には無効なオプションを表します。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetCanceled">
      <summary>基になる <see cref="T:System.Threading.Tasks.Task`1" /> を <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> 状態へ遷移させます。</summary>
      <exception cref="T:System.InvalidOperationException">基になる <see cref="T:System.Threading.Tasks.Task`1" /> は既に 3 つの最終的な状態、<see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />、<see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />、<see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> のいずれかにあります。または、基になる <see cref="T:System.Threading.Tasks.Task`1" /> が既に破棄されています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetException(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>基になる <see cref="T:System.Threading.Tasks.Task`1" /> を <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 状態へ遷移させます。</summary>
      <param name="exceptions">この <see cref="T:System.Threading.Tasks.Task`1" /> に関連付ける例外のコレクション。</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> は破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exceptions" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="exceptions" /> に 1 つ以上の null 要素があります。</exception>
      <exception cref="T:System.InvalidOperationException">基になる <see cref="T:System.Threading.Tasks.Task`1" /> は既に 3 つの最終的な状態、<see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />、<see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />、<see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> のいずれかにあります。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetException(System.Exception)">
      <summary>基になる <see cref="T:System.Threading.Tasks.Task`1" /> を <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 状態へ遷移させます。</summary>
      <param name="exception">この <see cref="T:System.Threading.Tasks.Task`1" /> に関連付ける例外。</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> は破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> 引数が null です。</exception>
      <exception cref="T:System.InvalidOperationException">基になる <see cref="T:System.Threading.Tasks.Task`1" /> は既に 3 つの最終的な状態、<see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />、<see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />、<see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> のいずれかにあります。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetResult(`0)">
      <summary>基になる <see cref="T:System.Threading.Tasks.Task`1" /> を <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" /> 状態へ遷移させます。</summary>
      <param name="result">この <see cref="T:System.Threading.Tasks.Task`1" /> に関連付ける結果値。</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> は破棄されました。</exception>
      <exception cref="T:System.InvalidOperationException">基になる <see cref="T:System.Threading.Tasks.Task`1" /> は既に 3 つの最終的な状態、<see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />、<see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />、<see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> のいずれかにあります。</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskCompletionSource`1.Task">
      <summary>この <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> によって作成される <see cref="T:System.Threading.Tasks.Task`1" /> を取得します。</summary>
      <returns>この <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> によって作成された <see cref="T:System.Threading.Tasks.Task`1" /> を返します。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetCanceled">
      <summary>基になる <see cref="T:System.Threading.Tasks.Task`1" /> の <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> 状態への遷移を試みます。</summary>
      <returns>操作が正常に終了した場合は true。操作が失敗した場合またはオブジェクトが既に破棄されている場合は false。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetCanceled(System.Threading.CancellationToken)">
      <summary>基になるを移行しようとしています。<see cref="T:System.Threading.Tasks.Task`1" />に、<see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />状態にあり、キャンセル トークン、取り消されたタスクに格納されている有効になります。</summary>
      <returns>操作が正常に終了した場合は true。それ以外の場合は false。</returns>
      <param name="cancellationToken">キャンセル トークン。 </param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetException(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>基になる <see cref="T:System.Threading.Tasks.Task`1" /> の <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 状態への遷移を試みます。</summary>
      <returns>操作が正常に終了した場合は true。それ以外の場合は false。</returns>
      <param name="exceptions">この <see cref="T:System.Threading.Tasks.Task`1" /> に関連付ける例外のコレクション。</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> は破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exceptions" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="exceptions" /> に 1 つ以上の null 要素があります。または<paramref name="exceptions" /> コレクションは空です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetException(System.Exception)">
      <summary>基になる <see cref="T:System.Threading.Tasks.Task`1" /> の <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 状態への遷移を試みます。</summary>
      <returns>操作が正常に終了した場合は true。それ以外の場合は false。</returns>
      <param name="exception">この <see cref="T:System.Threading.Tasks.Task`1" /> に関連付ける例外。</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> は破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> 引数が null です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetResult(`0)">
      <summary>基になる <see cref="T:System.Threading.Tasks.Task`1" /> の <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" /> 状態への遷移を試みます。</summary>
      <returns>操作が正常に終了した場合は true。それ以外の場合は false。</returns>
      <param name="result">この <see cref="T:System.Threading.Tasks.Task`1" /> に関連付ける結果値。</param>
    </member>
    <member name="T:System.Threading.Tasks.TaskContinuationOptions">
      <summary>
        <see cref="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)" /> メソッドまたは <see cref="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskContinuationOptions)" /> メソッドを使用して作成されるタスクの動作を指定します。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent">
      <summary>継続が子タスクの場合は、タスク階層内の親にアタッチするように指定します。継続元も子タスクである場合にのみ、継続は子タスクになれます。既定では、子タスク (外側のタスクによって作成される内側のタスク) は、親と独立して実行されます。<see cref="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent" /> オプションを使用して、親タスクと子タスクを同期させることもできます。親タスクが <see cref="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach" /> オプションを指定して構成されている場合、子タスクの <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> オプションは無効で、その子タスクはデタッチされた子タスクとして実行されることに注意してください。詳細については、「アタッチされた子タスクとデタッチされた子タスク」を参照してください。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.DenyChildAttach">
      <summary>
        <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> オプションを指定して作成され、アタッチされた子タスクとして実行されることを試行する子タスク (この継続によって作成される入れ子になった内側のタスク) は、親タスクにアタッチされることはできないため、デタッチされた子タスクとして実行されます。詳細については、「アタッチされた子タスクとデタッチされた子タスク」を参照してください。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously">
      <summary>継続タスクを同期的に実行するように指定します。このオプションを指定すると、継続は、前のタスクを最終状態に遷移させた同じスレッドで実行されます。継続の作成時に継続元が既に完了している場合、継続はその継続を作成したスレッドで実行されます。継続元の <see cref="T:System.Threading.CancellationTokenSource" /> が finally ブロック (Visual Basic では Finally) で破棄された場合、このオプションを指定した継続がその finally ブロックで実行されます。同期的に実行するのは、非常に短時間で完了する継続タスクのみでなければなりません。タスクは同期的に実行されるため、<see cref="M:System.Threading.Tasks.Task.Wait" /> などのメソッドを呼び出して、呼び出しスレッドがタスクの完了を待機する必要はありません。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.HideScheduler">
      <summary>
        <see cref="M:System.Threading.Tasks.Task.Run(System.Action)" /> または <see cref="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task})" /> などのメソッドを呼び出すことで継続によって作成されたタスクが、この継続が実行されているスケジューラではなく既定のスケジューラ (<see cref="P:System.Threading.Tasks.TaskScheduler.Default" />) を参照して、現在のスケジューラにすることを指定します。 </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.LazyCancellation">
      <summary>継続取り消しの場合は、継続元が完了するまで、継続が完了しないようにします。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.LongRunning">
      <summary>継続が、実行に時間のかかる、細分化されていない操作であることを示します。これは、<see cref="T:System.Threading.Tasks.TaskScheduler" /> に対し、オーバーサブスクリプションを許可してもよいことを示します。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.None">
      <summary>継続のオプションが指定されていない場合は、継続を実行するときに既定の動作を使用する必要があることを指定します。継続元タスクが完了したら、継続元の最終的な <see cref="P:System.Threading.Tasks.Task.Status" /> プロパティ値に関係なく、継続を非同期的に実行します。継続が子タスクである場合は、デタッチされた入れ子のタスクとして作成されます。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnCanceled">
      <summary>前のタスクが取り消された場合は継続タスクをスケジュールしないように指定します。完了時に継続元の <see cref="P:System.Threading.Tasks.Task.Status" /> プロパティが <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> である場合、継続元は取り消されます。このオプションは、マルチタスクの継続タスクに対しては無効です。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnFaulted">
      <summary>前のタスクで処理されない例外がスローされた場合は継続タスクをスケジュールしないように指定します。完了時に継続元の <see cref="P:System.Threading.Tasks.Task.Status" /> プロパティが <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> である場合、継続元はハンドルされない例外をスローします。このオプションは、マルチタスクの継続タスクに対しては無効です。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnRanToCompletion">
      <summary>前のタスクが完了まで実行された場合は、継続タスクをスケジュールしないように指定します。完了時に継続元の <see cref="P:System.Threading.Tasks.Task.Status" /> プロパティが <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" /> である場合、継続元は完了まで実行されます。このオプションは、マルチタスクの継続タスクに対しては無効です。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled">
      <summary>継続元が取り消された場合にのみ継続をスケジュールするように指定します。完了時に継続元の <see cref="P:System.Threading.Tasks.Task.Status" /> プロパティが <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> である場合、継続元は取り消されます。このオプションは、マルチタスクの継続タスクに対しては無効です。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnFaulted">
      <summary>前のタスクで処理されない例外がスローされた場合にのみ継続タスクをスケジュールするように指定します。完了時に継続元の <see cref="P:System.Threading.Tasks.Task.Status" /> プロパティが <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> である場合、継続元はハンドルされない例外をスローします。<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnFaulted" /> オプションを指定すると、継続元の <see cref="P:System.Threading.Tasks.Task.Exception" /> プロパティが null でないことが保証されます。このプロパティを使用すると、例外をキャッチして、タスクの違反の原因となった例外を確認できます。<see cref="P:System.Threading.Tasks.Task.Exception" /> プロパティにアクセスしない場合、例外はハンドルされません。また、取り消されたタスクまたはエラーが発生したタスクの <see cref="P:System.Threading.Tasks.Task`1.Result" /> プロパティにアクセスしようとする場合も、新しい例外がスローされます。このオプションは、マルチタスクの継続タスクに対しては無効です。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnRanToCompletion">
      <summary>継続元が完了まで実行された場合にのみ継続をスケジュールするように指定します。完了時に継続元の <see cref="P:System.Threading.Tasks.Task.Status" /> プロパティが <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" /> である場合、継続元は完了まで実行されます。このオプションは、マルチタスクの継続タスクに対しては無効です。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.PreferFairness">
      <summary>
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> に対するヒントはスケジュールされた順序でタスクをスケジュールするため、先にスケジュールされたタスクは先に実行される可能性が高く、後からスケジュールされたタスクは後で実行される可能性が高くなります。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.RunContinuationsAsynchronously">
      <summary>継続タスクを非同期的に実行するように指定します。このオプションは <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" /> より優先されます。</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskCreationOptions">
      <summary>タスクの作成および実行に関するオプションの動作を制御するフラグを指定します。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent">
      <summary>タスクがタスク階層の親にアタッチされることを指定します。既定では、子タスク (外側のタスクによって作成される内側のタスク) は、親と独立して実行されます。<see cref="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent" /> オプションを使用して、親タスクと子タスクを同期させることもできます。親タスクが <see cref="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach" /> オプションを指定して構成されている場合、子タスクの <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> オプションは無効で、その子タスクはデタッチされた子タスクとして実行されることに注意してください。詳細については、「アタッチされた子タスクとデタッチされた子タスク」を参照してください。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach">
      <summary>アタッチされた子タスクとして実行を試みる (<see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> オプションを使用して作成される) すべての子タスクが、親タスクにアタッチされるのではなく、デタッチされた子タスクとして実行されるように指定します。詳細については、「アタッチされた子タスクとデタッチされた子タスク」を参照してください。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.HideScheduler">
      <summary>アンビエント スケジューラが作成されたタスクの現在のスケジューラと見なされることを防止します。これは、作成されたタスクで実行される StartNew や ContinueWith のような操作で、<see cref="P:System.Threading.Tasks.TaskScheduler.Default" /> が現在のスケジューラと見なされることを意味します。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.LongRunning">
      <summary>粒度の細かいシステムではなく、タスクが長時間実行され、少量の大きなコンポーネントを含む粒度の粗い操作とすることを指定します。これは、<see cref="T:System.Threading.Tasks.TaskScheduler" /> に対し、オーバーサブスクリプションを許可してもよいことを示します。オーバーサブスクリプションを使用すると、使用可能なハードウェア スレッドよりも多くのスレッドを作成できます。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.None">
      <summary>既定の動作を適用する必要があることを示します。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.PreferFairness">
      <summary>
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> に対し、できる限り公平にタスクをスケジュールするように指示します。つまり、先にスケジュールされたタスクが先に実行され、後からスケジュールされたタスクは後から実行されるようにします。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.RunContinuationsAsynchronously">
      <summary>現在のタスクに追加される継続処理を強制的に非同期実行します。</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskExtensions">
      <summary>特定の種類の <see cref="T:System.Threading.Tasks.Task" /> インスタンスを操作する静的 (Visual Basic の場合は共有) メソッドのセットを提供します。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskExtensions.Unwrap``1(System.Threading.Tasks.Task{System.Threading.Tasks.Task{``0}})">
      <summary>Task&lt;Task&lt;T&gt;&gt; (C#) または Task (Of Task(Of T)) (Visual Basic) の非同期操作を表すプロキシ <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>指定された Task&lt;Task&lt;T&gt;&gt; (C#) または Task (Of Task(Of T)) (Visual Basic) の非同期操作を表す <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="task">ラップを解除する Task&lt;Task&lt;T&gt;&gt; (C#) または Task (Of Task(Of T)) (Visual Basic)。</param>
      <typeparam name="TResult">タスクの結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="task" /> 引数が null の場合にスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskExtensions.Unwrap(System.Threading.Tasks.Task{System.Threading.Tasks.Task})">
      <summary>
        <see cref="M:System.Threading.Tasks.TaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)" /> の非同期操作を表すプロキシ <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>指定された System.Threading.Tasks.Task(Of Task) の非同期操作を表すタスク。</returns>
      <param name="task">ラップを解除する Task&lt;Task&gt; (C#) または Task (Of Task) (Visual Basic)。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="task" /> 引数が null の場合にスローされる例外。</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskFactory">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" /> オブジェクトを作成およびスケジュールするためのサポートを提供します。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor">
      <summary>既定の構成を使用して、<see cref="T:System.Threading.Tasks.TaskFactory" /> インスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.CancellationToken)">
      <summary>指定された構成を使用して、<see cref="T:System.Threading.Tasks.TaskFactory" /> インスタンスを初期化します。</summary>
      <param name="cancellationToken">ファクトリ メソッドの呼び出し時に別の CancellationToken が明示的に指定されていない場合に、この <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> によって作成されたタスクに割り当てられる <see cref="T:System.Threading.Tasks.TaskFactory" />。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>指定された構成を使用して、<see cref="T:System.Threading.Tasks.TaskFactory" /> インスタンスを初期化します。</summary>
      <param name="cancellationToken">ファクトリ メソッドの呼び出し時に別の CancellationToken が明示的に指定されていない場合に、この <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> によって作成されたタスクに割り当てられる既定の <see cref="T:System.Threading.Tasks.TaskFactory" />。</param>
      <param name="creationOptions">この TaskFactory を使用してタスクを作成するときに使用する既定の <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <param name="continuationOptions">この TaskFactory を使用して継続タスクを作成するときに使用する既定の <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />。</param>
      <param name="scheduler">この TaskFactory を使用して作成されたタスクをスケジュールするときに使用する既定の <see cref="T:System.Threading.Tasks.TaskScheduler" />。null 値は、TaskScheduler.Current が使用されることを示します。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数に無効な <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> 値が指定されています。詳細については、の「解説」を参照してください。 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />です。または<paramref name="continuationOptions" /> 引数に無効な値が指定されています。 </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>指定された構成を使用して、<see cref="T:System.Threading.Tasks.TaskFactory" /> インスタンスを初期化します。</summary>
      <param name="creationOptions">この TaskFactory を使用してタスクを作成するときに使用する既定の <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <param name="continuationOptions">この TaskFactory を使用して継続タスクを作成するときに使用する既定の <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数に無効な <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> 値が指定されています。詳細については、の「解説」を参照してください。 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />です。または<paramref name="continuationOptions" /> 引数に無効な値が指定されています。 </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>指定された構成を使用して、<see cref="T:System.Threading.Tasks.TaskFactory" /> インスタンスを初期化します。</summary>
      <param name="scheduler">この TaskFactory を使用して作成されたタスクをスケジュールするときに使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。null 値は、現在の TaskScheduler が使用されることを示します。</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.CancellationToken">
      <summary>このタスク ファクトリの既定のキャンセル トークンを取得します。</summary>
      <returns>このタスク ファクトリの既定のタスク キャンセル トークン。</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.ContinuationOptions">
      <summary>このタスク ファクトリの既定のタスク継続オプションを取得します。</summary>
      <returns>このタスク ファクトリの既定のタスク継続オプション。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]})">
      <summary>一連の指定したタスクが完了したときに開始する継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに実行するアクション デリゲート。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素が破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationAction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列が空か、null 値が含まれています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.CancellationToken)">
      <summary>一連の指定したタスクが完了したときに開始する継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに実行するアクション デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てるキャンセル トークン。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素が破棄されました。または<see cref="T:System.Threading.CancellationTokenSource" /> を作成した <paramref name="cancellationToken" /> は既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationAction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列が空か、null 値が含まれています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>一連の指定したタスクが完了したときに開始する継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに実行するアクション デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てるキャンセル トークン。</param>
      <param name="continuationOptions">新しい継続タスクの動作を制御する列挙値のビットごとの組み合わせ。</param>
      <param name="scheduler">新しい継続タスクをスケジュールするときに使用するオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationAction" /> 引数が null です。または<paramref name="scheduler" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列が空か、null 値が含まれています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>一連の指定したタスクが完了したときに開始する継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに実行するアクション デリゲート。</param>
      <param name="continuationOptions">新しい継続タスクの動作を制御する列挙値のビットごとの組み合わせ。NotOn* メンバーと OnlyOn* メンバーはサポートされていません。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素が破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationAction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引数に無効な値が指定されています。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列が空か、null 値が含まれています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0})">
      <summary>一連の指定したタスクが完了したときに開始する継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> デリゲートによって返され、作成されたタスクに関連付けられている結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素が破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列が空か、null 値が含まれています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.CancellationToken)">
      <summary>一連の指定したタスクが完了したときに開始する継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てるキャンセル トークン。</param>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> デリゲートによって返され、作成されたタスクに関連付けられている結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素が破棄されました。または<see cref="T:System.Threading.CancellationTokenSource" /> を作成した <paramref name="cancellationToken" /> は既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列が空か、null 値が含まれています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>一連の指定したタスクが完了したときに開始する継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てるキャンセル トークン。</param>
      <param name="continuationOptions">新しい継続タスクの動作を制御する列挙値のビットごとの組み合わせ。NotOn* メンバーと OnlyOn* メンバーはサポートされていません。</param>
      <param name="scheduler">新しい継続タスクをスケジュールするときに使用するオブジェクト。</param>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> デリゲートによって返され、作成されたタスクに関連付けられている結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。または<paramref name="scheduler" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列が空か、null 値が含まれています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>一連の指定したタスクが完了したときに開始する継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="continuationOptions">新しい継続タスクの動作を制御する列挙値のビットごとの組み合わせ。NotOn* メンバーと OnlyOn* メンバーはサポートされていません。</param>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> デリゲートによって返され、作成されたタスクに関連付けられている結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素が破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引数に無効な値が指定されています。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列が空か、null 値が含まれています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]})">
      <summary>一連の指定したタスクが完了したときに開始する継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに実行するアクション デリゲート。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素が破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationAction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列が空か、null 値が含まれています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.CancellationToken)">
      <summary>一連の指定したタスクが完了したときに開始する継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに実行するアクション デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てるキャンセル トークン。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素が破棄されました。または<see cref="T:System.Threading.CancellationTokenSource" /> を作成した <paramref name="cancellationToken" /> は既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationAction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列が空か、null 値が含まれています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>一連の指定したタスクが完了したときに開始する継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに実行するアクション デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てるキャンセル トークン。</param>
      <param name="continuationOptions">新しい継続タスクの動作を制御する列挙値のビットごとの組み合わせ。NotOn* メンバーと OnlyOn* メンバーはサポートされていません。</param>
      <param name="scheduler">新しい継続タスクをスケジュールするときに使用するオブジェクト。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationAction" /> 引数が null です。または<paramref name="scheduler" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列が空か、null 値が含まれています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>一連の指定したタスクが完了したときに開始する継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに実行するアクション デリゲート。</param>
      <param name="continuationOptions">新しい継続タスクの動作を制御する列挙値のビットごとの組み合わせ。NotOn* メンバーと OnlyOn* メンバーはサポートされていません。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素が破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationAction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引数に無効な値が指定されています。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列が空か、null 値が含まれています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1})">
      <summary>一連の指定したタスクが完了したときに開始する継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> デリゲートによって返され、作成されたタスクに関連付けられている結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素が破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列が空か、null 値が含まれています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.CancellationToken)">
      <summary>一連の指定したタスクが完了したときに開始する継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てるキャンセル トークン。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> デリゲートによって返され、作成されたタスクに関連付けられている結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素が破棄されました。または<see cref="T:System.Threading.CancellationTokenSource" /> 作成<paramref name=" cancellationToken" /> 既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列が空か、null 値が含まれています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>一連の指定したタスクが完了したときに開始する継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てるキャンセル トークン。</param>
      <param name="continuationOptions">新しい継続タスクの動作を制御する列挙値のビットごとの組み合わせ。NotOn* メンバーと OnlyOn* メンバーはサポートされていません。</param>
      <param name="scheduler">新しい継続タスクをスケジュールするときに使用するオブジェクト。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> デリゲートによって返され、作成されたタスクに関連付けられている結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。または<paramref name="scheduler" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列が空か、null 値が含まれています。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引数に無効な値が指定されています。</exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素が破棄されました。または<see cref="T:System.Threading.CancellationTokenSource" /> を作成した <paramref name="cancellationToken" /> は既に破棄されています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>一連の指定したタスクが完了したときに開始する継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="continuationOptions">新しい継続タスクの動作を制御する列挙値のビットごとの組み合わせ。NotOn* メンバーと OnlyOn* メンバーはサポートされていません。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> デリゲートによって返され、作成されたタスクに関連付けられている結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素が破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引数に無効な値が指定されています。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列が空か、null 値が含まれています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task})">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続 <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに実行するアクション デリゲート。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationAction" /> 引数が nullです。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に含まれる、 null 値。または<paramref name="tasks" /> 配列が空です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続 <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに実行するアクション デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されました。または<paramref name="cancellationToken" /> 既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationAction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に含まれる、 null 値。または<paramref name="tasks" /> 配列が空です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続 <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに実行するアクション デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">作成された継続 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> の動作を制御する <see cref="T:System.Threading.Tasks.Task" /> 値。</param>
      <param name="scheduler">作成された継続 <see cref="T:System.Threading.Tasks.TaskScheduler" /> をスケジュールするときに使用する <see cref="T:System.Threading.Tasks.Task" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null のときにスローされる例外。または<paramref name="continuationAction" /> 引数が null のときにスローされる例外。または<paramref name="scheduler" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれるときにスローされる例外。または<paramref name="tasks" /> 配列が空のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続 <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに実行するアクション デリゲート。</param>
      <param name="continuationOptions">作成された継続 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> の動作を制御する <see cref="T:System.Threading.Tasks.Task" /> 値。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されたときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null のときにスローされる例外。または<paramref name="continuationAction" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引数が無効な TaskContinuationOptions 値を指定したときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれるときにスローされる例外。または<paramref name="tasks" /> 配列が空のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0})">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続 <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> デリゲートによって返され、作成された <see cref="T:System.Threading.Tasks.Task`1" /> に関連付けられている結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されたときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null のときにスローされる例外。または<paramref name="continuationFunction" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれるときにスローされる例外。または<paramref name="tasks" /> 配列が空のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken)">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続 <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> デリゲートによって返され、作成された <see cref="T:System.Threading.Tasks.Task`1" /> に関連付けられている結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されたときにスローされる例外。または指定された <see cref="T:System.Threading.CancellationToken" /> は既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null のときにスローされる例外。または<paramref name="continuationFunction" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれるときにスローされる例外。または<paramref name="tasks" /> 配列が空のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続 <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">作成された継続 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> の動作を制御する <see cref="T:System.Threading.Tasks.Task`1" /> 値。</param>
      <param name="scheduler">作成された継続 <see cref="T:System.Threading.Tasks.TaskScheduler" /> をスケジュールするときに使用する <see cref="T:System.Threading.Tasks.Task`1" />。</param>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> デリゲートによって返され、作成された <see cref="T:System.Threading.Tasks.Task`1" /> に関連付けられている結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null のときにスローされる例外。または<paramref name="continuationFunction" /> 引数が null のときにスローされる例外。または<paramref name="scheduler" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれるときにスローされる例外。または<paramref name="tasks" /> 配列が空のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続 <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="continuationOptions">作成された継続 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> の動作を制御する <see cref="T:System.Threading.Tasks.Task`1" /> 値。</param>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> デリゲートによって返され、作成された <see cref="T:System.Threading.Tasks.Task`1" /> に関連付けられている結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されたときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null のときにスローされる例外。または<paramref name="continuationFunction" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引数が無効な TaskContinuationOptions 値を指定したときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれるときにスローされる例外。または<paramref name="tasks" /> 配列が空のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}})">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続 <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに実行するアクション デリゲート。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されたときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null のときにスローされる例外。または<paramref name="continuationAction" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれるときにスローされる例外。または<paramref name="tasks" /> 配列が空のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続 <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに実行するアクション デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されたときにスローされる例外。または指定された <see cref="T:System.Threading.CancellationToken" /> は既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null のときにスローされる例外。または<paramref name="continuationAction" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれるときにスローされる例外。または<paramref name="tasks" /> 配列が空のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続 <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに実行するアクション デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">作成された継続 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> の動作を制御する <see cref="T:System.Threading.Tasks.Task" /> 値。</param>
      <param name="scheduler">作成された継続 <see cref="T:System.Threading.Tasks.TaskScheduler" /> をスケジュールするときに使用する <see cref="T:System.Threading.Tasks.Task`1" />。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null のときにスローされる例外。または<paramref name="continuationAction" /> 引数が null のときにスローされる例外。または<paramref name="scheduler" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれるときにスローされる例外。または<paramref name="tasks" /> 配列が空のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続 <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationAction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに実行するアクション デリゲート。</param>
      <param name="continuationOptions">作成された継続 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> の動作を制御する <see cref="T:System.Threading.Tasks.Task" /> 値。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されたときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null のときにスローされる例外。または<paramref name="continuationAction" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引数が無効な TaskContinuationOptions 値を指定したときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれるときにスローされる例外。または<paramref name="tasks" /> 配列が空のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1})">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続 <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> デリゲートによって返され、作成された <see cref="T:System.Threading.Tasks.Task`1" /> に関連付けられている結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されたときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null のときにスローされる例外。または<paramref name="continuationFunction" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれるときにスローされる例外。または<paramref name="tasks" /> 配列が空のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.CancellationToken)">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続 <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> デリゲートによって返され、作成された <see cref="T:System.Threading.Tasks.Task`1" /> に関連付けられている結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されたときにスローされる例外。または指定された <see cref="T:System.Threading.CancellationToken" /> は既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null のときにスローされる例外。または<paramref name="continuationFunction" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれるときにスローされる例外。または<paramref name="tasks" /> 配列が空のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続 <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">作成された継続 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> の動作を制御する <see cref="T:System.Threading.Tasks.Task`1" /> 値。</param>
      <param name="scheduler">作成された継続 <see cref="T:System.Threading.Tasks.TaskScheduler" /> をスケジュールするときに使用する <see cref="T:System.Threading.Tasks.Task`1" />。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> デリゲートによって返され、作成された <see cref="T:System.Threading.Tasks.Task`1" /> に関連付けられている結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null のときにスローされる例外。または<paramref name="continuationFunction" /> 引数が null のときにスローされる例外。または<paramref name="scheduler" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれるときにスローされる例外。または<paramref name="tasks" /> 配列が空のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続 <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="continuationOptions">作成された継続 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> の動作を制御する <see cref="T:System.Threading.Tasks.Task`1" /> 値。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <typeparam name="TResult">
        <paramref name="continuationFunction" /> デリゲートによって返され、作成された <see cref="T:System.Threading.Tasks.Task`1" /> に関連付けられている結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されたときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null のときにスローされる例外。または<paramref name="continuationFunction" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引数が無効な TaskContinuationOptions 値を指定したときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれるときにスローされる例外。または<paramref name="tasks" /> 配列が空のときにスローされる例外。</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.CreationOptions">
      <summary>このタスク ファクトリの既定のタスク作成オプションを取得します。</summary>
      <returns>このタスク ファクトリの既定のタスク作成オプション。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表す <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>非同期操作を表す作成された <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表す <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>非同期操作を表す作成された <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task" /> の動作を制御する TaskCreationOptions 値。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``0},System.Object)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表す <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>非同期操作を表す作成された <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表す <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>非同期操作を表す作成された <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task`1" /> の動作を制御する TaskCreationOptions 値。</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。<paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。詳細については、<see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> の「解説」を参照してください。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,System.Object)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表す <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>非同期操作を表す作成された <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="arg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表す <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>非同期操作を表す作成された <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="arg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task" /> の動作を制御する TaskCreationOptions 値。</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。<paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。詳細については、<see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> の「解説」を参照してください。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``1},``0,System.Object)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表す <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>非同期操作を表す作成された <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="arg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数の型。</typeparam>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``1},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表す <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>非同期操作を表す作成された <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="arg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task`1" /> の動作を制御する TaskCreationOptions 値。</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数の型。</typeparam>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。<paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。詳細については、<see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> の「解説」を参照してください。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,System.Object)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表す <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>非同期操作を表す作成された <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="arg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数。</param>
      <param name="arg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数の型。</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表す <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>非同期操作を表す作成された <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="arg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数。</param>
      <param name="arg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task" /> の動作を制御する TaskCreationOptions 値。</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数の型。</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。<paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。詳細については、<see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> の「解説」を参照してください。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``2},``0,``1,System.Object)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表す <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>非同期操作を表す作成された <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="arg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数。</param>
      <param name="arg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数の型。</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数の型。</typeparam>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``2},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表す <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>非同期操作を表す作成された <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="arg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数。</param>
      <param name="arg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task`1" /> の動作を制御する TaskCreationOptions 値。</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数の型。</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数の型。</typeparam>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。<paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。詳細については、<see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> の「解説」を参照してください。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,System.Object)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表す <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>非同期操作を表す作成された <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="arg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数。</param>
      <param name="arg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数。</param>
      <param name="arg3">
        <paramref name="beginMethod" /> デリゲートに渡される第 3 引数。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数の型。</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 3 引数の型。</typeparam>
      <typeparam name="TArg3">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表す <see cref="T:System.Threading.Tasks.Task" /> を作成します。</summary>
      <returns>非同期操作を表す作成された <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="arg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数。</param>
      <param name="arg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数。</param>
      <param name="arg3">
        <paramref name="beginMethod" /> デリゲートに渡される第 3 引数。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task" /> の動作を制御する TaskCreationOptions 値。</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数の型。</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 3 引数の型。</typeparam>
      <typeparam name="TArg3">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。<paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。詳細については、<see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> の「解説」を参照してください。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``4(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``3},``0,``1,``2,System.Object)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表す <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>非同期操作を表す作成された <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="arg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数。</param>
      <param name="arg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数。</param>
      <param name="arg3">
        <paramref name="beginMethod" /> デリゲートに渡される第 3 引数。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数の型。</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 3 引数の型。</typeparam>
      <typeparam name="TArg3">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数の型。</typeparam>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``4(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``3},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表す <see cref="T:System.Threading.Tasks.Task`1" /> を作成します。</summary>
      <returns>非同期操作を表す作成された <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="arg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数。</param>
      <param name="arg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数。</param>
      <param name="arg3">
        <paramref name="beginMethod" /> デリゲートに渡される第 3 引数。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task`1" /> の動作を制御する TaskCreationOptions 値。</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数の型。</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 3 引数の型。</typeparam>
      <typeparam name="TArg3">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数の型。</typeparam>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。<paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。詳細については、<see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> の「解説」を参照してください。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult})">
      <summary>指定された <see cref="T:System.Threading.Tasks.Task" /> の完了時に終了メソッド アクションを実行する <see cref="T:System.IAsyncResult" /> を作成します。</summary>
      <returns>非同期操作を表す <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="asyncResult">完了時に <paramref name="endMethod" /> の処理が開始される IAsyncResult。</param>
      <param name="endMethod">完了した <paramref name="asyncResult" /> を処理するアクション デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult},System.Threading.Tasks.TaskCreationOptions)">
      <summary>指定された <see cref="T:System.Threading.Tasks.Task" /> の完了時に終了メソッド アクションを実行する <see cref="T:System.IAsyncResult" /> を作成します。</summary>
      <returns>非同期操作を表す <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="asyncResult">完了時に <paramref name="endMethod" /> の処理が開始される IAsyncResult。</param>
      <param name="endMethod">完了した <paramref name="asyncResult" /> を処理するアクション デリゲート。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task" /> の動作を制御する TaskCreationOptions 値。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。詳細については、<see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> の「解説」を参照してください。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>指定された <see cref="T:System.Threading.Tasks.Task" /> の完了時に終了メソッド アクションを実行する <see cref="T:System.IAsyncResult" /> を作成します。</summary>
      <returns>非同期操作を表す作成された <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="asyncResult">完了時に <paramref name="endMethod" /> の処理が開始される IAsyncResult。</param>
      <param name="endMethod">完了した <paramref name="asyncResult" /> を処理するアクション デリゲート。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task" /> の動作を制御する TaskCreationOptions 値。</param>
      <param name="scheduler">終了メソッドを実行するタスクをスケジュールするときに使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。または<paramref name="scheduler" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。<paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。詳細については、<see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> の「解説」を参照してください。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0})">
      <summary>指定された <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に終了メソッド関数を実行する <see cref="T:System.IAsyncResult" /> を作成します。</summary>
      <returns>非同期操作を表す <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="asyncResult">完了時に <paramref name="endMethod" /> の処理が開始される IAsyncResult。</param>
      <param name="endMethod">完了した <paramref name="asyncResult" /> を処理する関数デリゲート。</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>指定された <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に終了メソッド関数を実行する <see cref="T:System.IAsyncResult" /> を作成します。</summary>
      <returns>非同期操作を表す <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="asyncResult">完了時に <paramref name="endMethod" /> の処理が開始される IAsyncResult。</param>
      <param name="endMethod">完了した <paramref name="asyncResult" /> を処理する関数デリゲート。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task`1" /> の動作を制御する TaskCreationOptions 値。</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。<paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。詳細については、<see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> の「解説」を参照してください。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>指定された <see cref="T:System.Threading.Tasks.Task`1" /> の完了時に終了メソッド関数を実行する <see cref="T:System.IAsyncResult" /> を作成します。</summary>
      <returns>非同期操作を表す <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="asyncResult">完了時に <paramref name="endMethod" /> の処理が開始される IAsyncResult。</param>
      <param name="endMethod">完了した <paramref name="asyncResult" /> を処理する関数デリゲート。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task`1" /> の動作を制御する TaskCreationOptions 値。</param>
      <param name="scheduler">終了メソッドを実行するタスクをスケジュールするときに使用する <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 引数が null のときにスローされる例外。または<paramref name="endMethod" /> 引数が null のときにスローされる例外。または<paramref name="scheduler" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。<paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。詳細については、<see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> の「解説」を参照してください。</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.Scheduler">
      <summary>このタスク ファクトリの既定のタスク スケジューラを取得します。</summary>
      <returns>このタスク ファクトリの既定のタスク スケジューラ。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action)">
      <summary>タスクを作成して開始します。</summary>
      <returns>開始されたタスク。</returns>
      <param name="action">非同期で実行するアクション デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 引数が null です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.CancellationToken)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" /> を作成して開始します。</summary>
      <returns>開始された <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">非同期で実行するアクション デリゲート。</param>
      <param name="cancellationToken">新しいタスクに割り当てられる <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">指定された <see cref="T:System.Threading.CancellationToken" /> は既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 引数が null のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" /> を作成して開始します。</summary>
      <returns>開始された <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">非同期で実行するアクション デリゲート。</param>
      <param name="cancellationToken">新しい <see cref="T:System.Threading.Tasks.Task" /> に割り当てられる <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task" /> の動作を制御する TaskCreationOptions 値。</param>
      <param name="scheduler">作成された <see cref="T:System.Threading.Tasks.TaskScheduler" /> をスケジュールするときに使用する <see cref="T:System.Threading.Tasks.Task" />。</param>
      <exception cref="T:System.ObjectDisposedException">指定された <see cref="T:System.Threading.CancellationToken" /> は既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 引数が null のときにスローされる例外。または<paramref name="scheduler" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。<paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。詳細については、<see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> の「解説」を参照してください。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.Tasks.TaskCreationOptions)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" /> を作成して開始します。</summary>
      <returns>開始された <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">非同期で実行するアクション デリゲート。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task" /> の動作を制御する TaskCreationOptions 値。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" /> を作成して開始します。</summary>
      <returns>開始された <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">非同期で実行するアクション デリゲート。</param>
      <param name="state">
        <paramref name="action" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 引数が null です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" /> を作成して開始します。</summary>
      <returns>開始された <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">非同期で実行するアクション デリゲート。</param>
      <param name="state">
        <paramref name="action" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <param name="cancellationToken">新しい <see cref="T:System.Threading.Tasks.Task" /> に割り当てられる <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">指定された <see cref="T:System.Threading.CancellationToken" /> は既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 引数が null のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" /> を作成して開始します。</summary>
      <returns>開始された <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">非同期で実行するアクション デリゲート。</param>
      <param name="state">
        <paramref name="action" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <param name="cancellationToken">新しいタスクに割り当てられる <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task" /> の動作を制御する TaskCreationOptions 値。</param>
      <param name="scheduler">作成された <see cref="T:System.Threading.Tasks.TaskScheduler" /> をスケジュールするときに使用する <see cref="T:System.Threading.Tasks.Task" />。</param>
      <exception cref="T:System.ObjectDisposedException">指定された <see cref="T:System.Threading.CancellationToken" /> は既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 引数が null のときにスローされる例外。または<paramref name="scheduler" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。<paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。詳細については、<see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> の「解説」を参照してください。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" /> を作成して開始します。</summary>
      <returns>開始された <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">非同期で実行するアクション デリゲート。</param>
      <param name="state">
        <paramref name="action" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task" /> の動作を制御する TaskCreationOptions 値。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0})">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" /> を作成して開始します。</summary>
      <returns>開始された <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得される結果を返す関数デリゲート。</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引数が null です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.CancellationToken)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" /> を作成して開始します。</summary>
      <returns>開始された <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得される結果を返す関数デリゲート。</param>
      <param name="cancellationToken">新しい <see cref="T:System.Threading.Tasks.Task" /> に割り当てられる <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">指定された <see cref="T:System.Threading.CancellationToken" /> は既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引数が null のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" /> を作成して開始します。</summary>
      <returns>開始された <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得される結果を返す関数デリゲート。</param>
      <param name="cancellationToken">新しいタスクに割り当てられる <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task`1" /> の動作を制御する TaskCreationOptions 値。</param>
      <param name="scheduler">作成された <see cref="T:System.Threading.Tasks.TaskScheduler" /> をスケジュールするときに使用する <see cref="T:System.Threading.Tasks.Task`1" />。</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">指定された <see cref="T:System.Threading.CancellationToken" /> は既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引数が null のときにスローされる例外。または<paramref name="scheduler" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。<paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。詳細については、<see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> の「解説」を参照してください。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" /> を作成して開始します。</summary>
      <returns>開始された <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得される結果を返す関数デリゲート。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task`1" /> の動作を制御する TaskCreationOptions 値。</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。<paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。詳細については、<see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> の「解説」を参照してください。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" /> を作成して開始します。</summary>
      <returns>開始された <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得される結果を返す関数デリゲート。</param>
      <param name="state">
        <paramref name="function" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引数が null のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" /> を作成して開始します。</summary>
      <returns>開始された <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得される結果を返す関数デリゲート。</param>
      <param name="state">
        <paramref name="function" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <param name="cancellationToken">新しい <see cref="T:System.Threading.Tasks.Task" /> に割り当てられる <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">指定された <see cref="T:System.Threading.CancellationToken" /> は既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引数が null のときにスローされる例外。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" /> を作成して開始します。</summary>
      <returns>開始された <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得される結果を返す関数デリゲート。</param>
      <param name="state">
        <paramref name="function" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <param name="cancellationToken">新しいタスクに割り当てられる <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task`1" /> の動作を制御する TaskCreationOptions 値。</param>
      <param name="scheduler">作成された <see cref="T:System.Threading.Tasks.TaskScheduler" /> をスケジュールするときに使用する <see cref="T:System.Threading.Tasks.Task`1" />。</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">指定された <see cref="T:System.Threading.CancellationToken" /> は既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引数が null のときにスローされる例外。または<paramref name="scheduler" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。<paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。詳細については、<see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> の「解説」を参照してください。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" /> を作成して開始します。</summary>
      <returns>開始された <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得される結果を返す関数デリゲート。</param>
      <param name="state">
        <paramref name="function" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task`1" /> の動作を制御する TaskCreationOptions 値。</param>
      <typeparam name="TResult">
        <see cref="T:System.Threading.Tasks.Task`1" /> を通じて取得できる結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引数が null のときにスローされる例外。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。<paramref name="creationOptions" /> 引数が無効な TaskCreationOptions 値を指定したときにスローされる例外。詳細については、<see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> の「解説」を参照してください。</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskFactory`1">
      <summary>
        <see cref="T:System.Threading.Tasks.Task`1" /> オブジェクトを作成およびスケジュールするためのサポートを提供します。</summary>
      <typeparam name="TResult">このクラスのメソッドによって作成される <see cref="T:System.Threading.Tasks.Task`1" /> オブジェクトの戻り値。</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor">
      <summary>既定の構成を使用して、<see cref="T:System.Threading.Tasks.TaskFactory`1" /> インスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.CancellationToken)">
      <summary>既定の構成を使用して、<see cref="T:System.Threading.Tasks.TaskFactory`1" /> インスタンスを初期化します。</summary>
      <param name="cancellationToken">ファクトリ メソッドの呼び出し時に他の取り消しトークンが明示的に指定されていない場合に、この <see cref="T:System.Threading.Tasks.TaskFactory" /> によって作成されたタスクに割り当てられる既定の取り消しトークン。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>指定された構成を使用して、<see cref="T:System.Threading.Tasks.TaskFactory`1" /> インスタンスを初期化します。</summary>
      <param name="cancellationToken">ファクトリ メソッドの呼び出し時に他の取り消しトークンが明示的に指定されていない場合に、この <see cref="T:System.Threading.Tasks.TaskFactory" /> によって作成されたタスクに割り当てられる既定の取り消しトークン。</param>
      <param name="creationOptions">この <see cref="T:System.Threading.Tasks.TaskFactory`1" /> を使用してタスクを作成するときに使用する既定のオプション。</param>
      <param name="continuationOptions">この <see cref="T:System.Threading.Tasks.TaskFactory`1" /> を使用して継続タスクを作成するときに使用する既定のオプション。</param>
      <param name="scheduler">この <see cref="T:System.Threading.Tasks.TaskFactory`1" /> を使用して作成されたタスクをスケジュールするときに使用する既定のスケジューラー。null 値は <see cref="P:System.Threading.Tasks.TaskScheduler.Current" /> を使用する必要があることを示します。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" />、または <paramref name="continuationOptions" /> に無効な値が指定されています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>指定された構成を使用して、<see cref="T:System.Threading.Tasks.TaskFactory`1" /> インスタンスを初期化します。</summary>
      <param name="creationOptions">この <see cref="T:System.Threading.Tasks.TaskFactory`1" /> を使用してタスクを作成するときに使用する既定のオプション。</param>
      <param name="continuationOptions">この <see cref="T:System.Threading.Tasks.TaskFactory`1" /> を使用して継続タスクを作成するときに使用する既定のオプション。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" />、または <paramref name="continuationOptions" /> に無効な値が指定されています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>指定された構成を使用して、<see cref="T:System.Threading.Tasks.TaskFactory`1" /> インスタンスを初期化します。</summary>
      <param name="scheduler">この <see cref="T:System.Threading.Tasks.TaskFactory`1" /> を使用して作成されたタスクをスケジュールするときに使用するスケジューラー。null 値は、現在の <see cref="T:System.Threading.Tasks.TaskScheduler" /> を使用することを示します。</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.CancellationToken">
      <summary>このタスク ファクトリの既定のキャンセル トークンを取得します。</summary>
      <returns>このタスク ファクトリの既定のキャンセル トークン。</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.ContinuationOptions">
      <summary>このタスク ファクトリの <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 列挙値を取得します。</summary>
      <returns>このタスク ファクトリの既定の継続オプションを示す列挙値のいずれか。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0})">
      <summary>指定された一連のタスクの完了時に開始される継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれているか、空です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.CancellationToken)">
      <summary>指定された一連のタスクの完了時に開始される継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる取り消しトークン。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されました。または<see cref="T:System.Threading.CancellationTokenSource" /> 作成<paramref name=" cancellationToken" /> 既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれているか、空です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>指定された一連のタスクの完了時に開始される継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる取り消しトークン。</param>
      <param name="continuationOptions">作成された継続タスクの動作を制御する列挙値のいずれか。NotOn* または OnlyOn* の値が無効です。</param>
      <param name="scheduler">作成された継続タスクをスケジュールするときに使用するスケジューラー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。または<paramref name="scheduler" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれているか、空です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> に無効な値が指定されています。</exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されました。または<see cref="T:System.Threading.CancellationTokenSource" /> 作成<paramref name=" cancellationToken" /> 既に破棄されています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>指定された一連のタスクの完了時に開始される継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="continuationOptions">作成された継続タスクの動作を制御する列挙値のいずれか。NotOn* または OnlyOn* の値が無効です。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引数に無効な値が指定されています。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれているか、空です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0})">
      <summary>指定された一連のタスクの完了時に開始される継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれているか、空です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.CancellationToken)">
      <summary>指定された一連のタスクの完了時に開始される継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる取り消しトークン。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されました。または<see cref="T:System.Threading.CancellationTokenSource" /> 作成<paramref name=" cancellationToken" /> 既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれているか、空です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>指定された一連のタスクの完了時に開始される継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる取り消しトークン。</param>
      <param name="continuationOptions">作成された継続タスクの動作を制御する列挙値のいずれか。NotOn* または OnlyOn* の値が無効です。</param>
      <param name="scheduler">作成された継続タスクをスケジュールするときに使用するスケジューラー。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。または<paramref name="scheduler" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれているか、空です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引数に無効な値が指定されています。</exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されました。または<see cref="T:System.Threading.CancellationTokenSource" /> 作成<paramref name=" cancellationToken" /> 既に破棄されています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>指定された一連のタスクの完了時に開始される継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のすべてのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="continuationOptions">作成された継続タスクの動作を制御する列挙値のいずれか。NotOn* または OnlyOn* の値が無効です。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引数に無効な値が指定されています。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれているか、空です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0})">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列に null 値が含まれているか、空です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.CancellationToken)">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる取り消しトークン。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されました。または<see cref="T:System.Threading.CancellationTokenSource" /> 作成<paramref name=" cancellationToken" /> 既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列は null 値を含みます。または<paramref name="tasks" /> 配列が空です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる取り消しトークン。</param>
      <param name="continuationOptions">作成された継続タスクの動作を制御する列挙値のいずれか。NotOn* または OnlyOn* の値が無効です。</param>
      <param name="scheduler">作成された継続タスクをスケジュールするときに使用するタスク スケジューラー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。または<paramref name="scheduler" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列は null 値を含みます。または<paramref name="tasks" /> 配列が空です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引数に無効な <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 値が指定されています。</exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されました。または<see cref="T:System.Threading.CancellationTokenSource" /> 作成<paramref name=" cancellationToken" /> 既に破棄されています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="continuationOptions">作成された継続タスクの動作を制御する列挙値のいずれか。NotOn* または OnlyOn* の値が無効です。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引数に無効な列挙値が指定されています。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列は null 値を含みます。または<paramref name="tasks" /> 配列が空です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0})">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列は null 値を含みます。または<paramref name="tasks" /> 配列が空です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.CancellationToken)">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続タスクを作成します。</summary>
      <returns>新しい継続タスク。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる取り消しトークン。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されました。または<see cref="T:System.Threading.CancellationTokenSource" /> 作成<paramref name=" cancellationToken" /> 既に破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列は null 値を含みます。または<paramref name="tasks" /> 配列が空です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="cancellationToken">新しい継続タスクに割り当てられる取り消しトークン。</param>
      <param name="continuationOptions">作成された継続タスクの動作を制御する列挙値のいずれか。NotOn* または OnlyOn* の値が無効です。</param>
      <param name="scheduler">作成された継続 <see cref="T:System.Threading.Tasks.TaskScheduler" /> をスケジュールするときに使用する <see cref="T:System.Threading.Tasks.Task`1" />。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。または<paramref name="scheduler" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列は null 値を含みます。または<paramref name="tasks" /> 配列が空です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引数が、TaskContinuationOptions の無効な値を指定しています。</exception>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されました。または<see cref="T:System.Threading.CancellationTokenSource" /> 作成<paramref name=" cancellationToken" /> 既に破棄されています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>指定された一連のタスクのうち任意のタスクが完了したときに開始される継続タスクを作成します。</summary>
      <returns>新しい継続 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">いずれかのタスクが完了したときに開始される継続タスクの配列。</param>
      <param name="continuationFunction">
        <paramref name="tasks" /> 配列内のいずれかのタスクが完了したときに非同期的に実行する関数デリゲート。</param>
      <param name="continuationOptions">作成された継続タスクの動作を制御する列挙値のいずれか。NotOn* または OnlyOn* の値が無効です。</param>
      <typeparam name="TAntecedentResult">継続元の <paramref name="tasks" /> の結果の型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="tasks" /> 配列の要素の 1 つが破棄されました。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 配列が null です。または<paramref name="continuationFunction" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引数に無効な列挙値が指定されています。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 配列は null 値を含みます。または<paramref name="tasks" /> 配列が空です。</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.CreationOptions">
      <summary>このタスク ファクトリの <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> 列挙値を取得します。</summary>
      <returns>このタスク ファクトリの既定の作成オプションを示す列挙値のいずれか。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},System.Object)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表すタスクを作成します。</summary>
      <returns>非同期操作を表す作成されたタスク。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null です。または<paramref name="endMethod" /> 引数が null です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表すタスクを作成します。</summary>
      <returns>非同期操作を表す作成された <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <param name="creationOptions">作成されたタスクの動作を制御する列挙値のいずれか。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null です。または<paramref name="endMethod" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数に無効な値が指定されています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,System.Object)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表すタスクを作成します。</summary>
      <returns>非同期操作を表す作成されたタスク。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="arg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null です。または<paramref name="endMethod" /> 引数が null です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表すタスクを作成します。</summary>
      <returns>非同期操作を表す作成されたタスク。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="arg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <param name="creationOptions">作成されたタスクの動作を制御する列挙値のいずれか。</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null です。または<paramref name="endMethod" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> パラメーターは無効な値を指定します。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,System.Object)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表すタスクを作成します。</summary>
      <returns>非同期操作を表す作成されたタスク。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="arg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数。</param>
      <param name="arg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数の型。</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null です。または<paramref name="endMethod" /> 引数が null です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表すタスクを作成します。</summary>
      <returns>非同期操作を表す作成されたタスク。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="arg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数。</param>
      <param name="arg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <param name="creationOptions">作成された <see cref="T:System.Threading.Tasks.Task`1" /> の動作を制御するオブジェクト。</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数の型。</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null です。または<paramref name="endMethod" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> パラメーターは無効な値を指定します。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,System.Object)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表すタスクを作成します。</summary>
      <returns>非同期操作を表す作成されたタスク。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="arg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数。</param>
      <param name="arg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数。</param>
      <param name="arg3">
        <paramref name="beginMethod" /> デリゲートに渡される第 3 引数。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数の型。</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 3 引数の型。</typeparam>
      <typeparam name="TArg3">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null です。または<paramref name="endMethod" /> 引数が null です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>非同期プログラミング モデルのパターンに準拠した開始メソッドと終了メソッドの組み合わせを表すタスクを作成します。</summary>
      <returns>非同期操作を表す作成されたタスク。</returns>
      <param name="beginMethod">非同期操作を開始するデリゲート。</param>
      <param name="endMethod">非同期操作を終了するデリゲート。</param>
      <param name="arg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数。</param>
      <param name="arg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数。</param>
      <param name="arg3">
        <paramref name="beginMethod" /> デリゲートに渡される第 3 引数。</param>
      <param name="state">
        <paramref name="beginMethod" /> デリゲートによって使用されるデータを格納しているオブジェクト。</param>
      <param name="creationOptions">作成されたタスクの動作を制御するオブジェクト。</param>
      <typeparam name="TArg1">
        <paramref name="beginMethod" /> デリゲートに渡される第 2 引数の型。</typeparam>
      <typeparam name="TArg2">
        <paramref name="beginMethod" /> デリゲートに渡される第 3 引数の型。</typeparam>
      <typeparam name="TArg3">
        <paramref name="beginMethod" /> デリゲートに渡される第 1 引数の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引数が null です。または<paramref name="endMethod" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> パラメーターは無効な値を指定します。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0})">
      <summary>指定された <see cref="T:System.IAsyncResult" /> の完了時に終了メソッド関数を実行するタスクを作成します。</summary>
      <returns>非同期操作を表す <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="asyncResult">完了時に <see cref="T:System.IAsyncResult" /> の処理が開始される <paramref name="endMethod" />。</param>
      <param name="endMethod">完了した <paramref name="asyncResult" /> を処理する関数デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 引数が null です。または<paramref name="endMethod" /> 引数が null です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>指定された <see cref="T:System.IAsyncResult" /> の完了時に終了メソッド関数を実行するタスクを作成します。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <param name="asyncResult">完了時に <see cref="T:System.IAsyncResult" /> の処理が開始される <paramref name="endMethod" />。</param>
      <param name="endMethod">完了した <paramref name="asyncResult" /> を処理する関数デリゲート。</param>
      <param name="creationOptions">作成されたタスクの動作を制御する列挙値のいずれか。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 引数が null です。または<paramref name="endMethod" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引数に無効な値が指定されています。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>指定された <see cref="T:System.IAsyncResult" /> の完了時に終了メソッド関数を実行するタスクを作成します。</summary>
      <returns>非同期操作を表す作成されたタスク。</returns>
      <param name="asyncResult">完了時に <see cref="T:System.IAsyncResult" /> の処理が開始される <paramref name="endMethod" />。</param>
      <param name="endMethod">完了した <paramref name="asyncResult" /> を処理する関数デリゲート。</param>
      <param name="creationOptions">作成されたタスクの動作を制御する列挙値のいずれか。</param>
      <param name="scheduler">終了メソッドを実行するタスクをスケジュールするときに使用するタスク スケジューラー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 引数が null です。または<paramref name="endMethod" /> 引数が null です。または<paramref name="scheduler" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> パラメーターは無効な値を指定します。</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.Scheduler">
      <summary>このタスク ファクトリのタスク スケジューラーを取得します。</summary>
      <returns>このタスク ファクトリのタスク スケジューラー。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0})">
      <summary>タスクを作成して開始します。</summary>
      <returns>開始されたタスク。</returns>
      <param name="function">タスクを通じて取得される結果を返す関数デリゲート。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引数が null です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.CancellationToken)">
      <summary>タスクを作成して開始します。</summary>
      <returns>開始されたタスク。</returns>
      <param name="function">タスクを通じて取得される結果を返す関数デリゲート。</param>
      <param name="cancellationToken">新しいタスクに割り当てられる取り消しトークン。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="cancellationToken" /> を作成したキャンセル トークンのソースが破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引数が null です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>タスクを作成して開始します。</summary>
      <returns>開始されたタスク。</returns>
      <param name="function">タスクを通じて取得される結果を返す関数デリゲート。</param>
      <param name="cancellationToken">新しいタスクに割り当てられる取り消しトークン。</param>
      <param name="creationOptions">作成されたタスクの動作を制御する列挙値のいずれか。</param>
      <param name="scheduler">作成されたタスクをスケジュールするときに使用するタスク スケジューラー。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="cancellationToken" /> を作成したキャンセル トークンのソースが破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引数が null です。または<paramref name="scheduler" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> パラメーターは無効な値を指定します。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>タスクを作成して開始します。</summary>
      <returns>開始された <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">タスクを通じて取得される結果を返す関数デリゲート。</param>
      <param name="creationOptions">作成されたタスクの動作を制御する列挙値のいずれか。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> パラメーターは無効な値を指定します。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object)">
      <summary>タスクを作成して開始します。</summary>
      <returns>開始されたタスク。</returns>
      <param name="function">タスクを通じて取得される結果を返す関数デリゲート。</param>
      <param name="state">
        <paramref name="function" /> デリゲートが使用するデータを格納するオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引数が null です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken)">
      <summary>タスクを作成して開始します。</summary>
      <returns>開始されたタスク。</returns>
      <param name="function">タスクを通じて取得される結果を返す関数デリゲート。</param>
      <param name="state">
        <paramref name="function" /> デリゲートが使用するデータを格納するオブジェクト。</param>
      <param name="cancellationToken">新しいタスクに割り当てられる取り消しトークン。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="cancellationToken" /> を作成したキャンセル トークンのソースが破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引数が null です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>タスクを作成して開始します。</summary>
      <returns>開始されたタスク。</returns>
      <param name="function">タスクを通じて取得される結果を返す関数デリゲート。</param>
      <param name="state">
        <paramref name="function" /> デリゲートが使用するデータを格納するオブジェクト。</param>
      <param name="cancellationToken">新しいタスクに割り当てられる取り消しトークン。</param>
      <param name="creationOptions">作成されたタスクの動作を制御する列挙値のいずれか。</param>
      <param name="scheduler">作成されたタスクをスケジュールするときに使用するタスク スケジューラー。</param>
      <exception cref="T:System.ObjectDisposedException">
        <paramref name="cancellationToken" /> を作成したキャンセル トークンのソースが破棄されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引数が null です。または<paramref name="scheduler" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> パラメーターは無効な値を指定します。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>タスクを作成して開始します。</summary>
      <returns>開始されたタスク。</returns>
      <param name="function">タスクを通じて取得される結果を返す関数デリゲート。</param>
      <param name="state">
        <paramref name="function" /> デリゲートが使用するデータを格納するオブジェクト。</param>
      <param name="creationOptions">作成されたタスクの動作を制御する列挙値のいずれか。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引数が null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> パラメーターは無効な値を指定します。</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskScheduler">
      <summary>スレッドにおけるタスクのキュー作成という下位の作業を処理するオブジェクトを表します。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.#ctor">
      <summary>
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> を初期化します。</summary>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Current">
      <summary>現在実行中のタスクに関連付けられている <see cref="T:System.Threading.Tasks.TaskScheduler" /> を取得します。</summary>
      <returns>現在実行中のタスクに関連付けられている <see cref="T:System.Threading.Tasks.TaskScheduler" /> を返します。</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Default">
      <summary>.NET Framework によって提供される既定の <see cref="T:System.Threading.Tasks.TaskScheduler" /> インスタンスを取得します。</summary>
      <returns>既定の <see cref="T:System.Threading.Tasks.TaskScheduler" /> インスタンスを返します。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.FromCurrentSynchronizationContext">
      <summary>現在の <see cref="T:System.Threading.SynchronizationContext" /> に関連付けられた <see cref="T:System.Threading.Tasks.TaskScheduler" /> を作成します。</summary>
      <returns>現在の <see cref="T:System.Threading.SynchronizationContext" /> (<see cref="P:System.Threading.SynchronizationContext.Current" /> で指定) に関連付けられた <see cref="T:System.Threading.Tasks.TaskScheduler" />。</returns>
      <exception cref="T:System.InvalidOperationException">現在の SynchronizationContext を TaskScheduler として使用することはできません。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.GetScheduledTasks">
      <summary>デバッガー サポートの目的でのみ、現在実行待機中のスケジューラのキューに含まれている <see cref="T:System.Threading.Tasks.Task" /> インスタンスの列挙可能なコレクションを生成します。</summary>
      <returns>デバッガーがこのスケジューラのキューに現在含まれているタスクを走査できるようにする列挙可能なコレクション。</returns>
      <exception cref="T:System.NotSupportedException">このスケジューラは、この時点でキューにあるタスクの一覧を生成できません。</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Id">
      <summary>この <see cref="T:System.Threading.Tasks.TaskScheduler" /> の一意の ID を取得します。</summary>
      <returns>この <see cref="T:System.Threading.Tasks.TaskScheduler" /> の一意の ID を返します。</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.MaximumConcurrencyLevel">
      <summary>この <see cref="T:System.Threading.Tasks.TaskScheduler" /> がサポートできる同時実行レベルの上限を示します。</summary>
      <returns>同時実行レベルの上限を表す整数を返します。既定のスケジューラは、<see cref="F:System.Int32.MaxValue" /> を返します。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.QueueTask(System.Threading.Tasks.Task)">
      <summary>スケジューラのキューに <see cref="T:System.Threading.Tasks.Task" /> を追加します。</summary>
      <param name="task">キューに追加する <see cref="T:System.Threading.Tasks.Task" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="task" /> 引数が null です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryDequeue(System.Threading.Tasks.Task)">
      <summary>このスケジューラのキューに以前含まれていた <see cref="T:System.Threading.Tasks.Task" /> のデキューを試みます。</summary>
      <returns>
        <paramref name="task" /> 引数が正常にデキューされたかどうかを示すブール値。</returns>
      <param name="task">キューから取り出す <see cref="T:System.Threading.Tasks.Task" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="task" /> 引数が null です。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryExecuteTask(System.Threading.Tasks.Task)">
      <summary>このスケジューラ上の指定された <see cref="T:System.Threading.Tasks.Task" /> の実行を試みます。</summary>
      <returns>
        <paramref name="task" /> が正常に実行された場合は true、正常に実行されなかった場合は false。タスクが正常に実行されない原因としては、タスクが既に実行されていた場合や、他のスレッドによって実行中である場合などが挙げられます。</returns>
      <param name="task">実行対象の <see cref="T:System.Threading.Tasks.Task" /> オブジェクト。</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="task" /> はこのスケジューラに関連付けられていません。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)">
      <summary>指定された <see cref="T:System.Threading.Tasks.Task" /> をこの呼び出しで同期的に実行できるかどうかを判断し、できる場合は実行します。</summary>
      <returns>タスクがインラインで実行されたかどうかを示すブール値。</returns>
      <param name="task">実行対象の <see cref="T:System.Threading.Tasks.Task" />。</param>
      <param name="taskWasPreviouslyQueued">タスクが以前キューに追加されていたかどうかを示すブール値。このパラメーターを True に設定すると、タスクが以前キューに追加されていた (スケジュールされていた) 可能性があることを示します。False に設定すると、タスクがキューに追加されていないことを示し、この呼び出しによって、タスクがキューに追加されずにインラインで実行されます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="task" /> 引数が null です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="task" /> は既に実行されました。</exception>
    </member>
    <member name="E:System.Threading.Tasks.TaskScheduler.UnobservedTaskException">
      <summary>エラーが発生したタスクの無視された例外によって、例外のエスカレーション ポリシーが起動される直前に発生します。既定では、このポリシーの起動によりプロセスが終了します。</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskSchedulerException">
      <summary>
        <see cref="T:System.Threading.Tasks.TaskScheduler" /> による無効な操作があったことを通知するために使用される例外を表します。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor">
      <summary>エラーを説明するシステム提供のメッセージを使用して、<see cref="T:System.Threading.Tasks.TaskSchedulerException" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.Exception)">
      <summary>既定のエラー メッセージと、この例外の原因である内部例外への参照を使用して、<see cref="T:System.Threading.Tasks.TaskSchedulerException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="innerException">現在の例外の原因である例外。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.String)">
      <summary>エラーを説明する指定したメッセージを使用して、<see cref="T:System.Threading.Tasks.TaskSchedulerException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外を説明するメッセージ。このコンストラクターの呼び出し元では、この文字列が現在のシステムのカルチャに合わせてローカライズ済みであることを確認しておく必要があります。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.String,System.Exception)">
      <summary>指定したエラー メッセージと、この例外の原因である内部例外への参照を使用して、<see cref="T:System.Threading.Tasks.TaskSchedulerException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外を説明するメッセージ。このコンストラクターの呼び出し元では、この文字列が現在のシステムのカルチャに合わせてローカライズ済みであることを確認しておく必要があります。</param>
      <param name="innerException">現在の例外の原因である例外。<paramref name="innerException" /> パラメーターが null でない場合は、内部例外を処理する catch ブロックで現在の例外が発生します。</param>
    </member>
    <member name="T:System.Threading.Tasks.TaskStatus">
      <summary>
        <see cref="T:System.Threading.Tasks.Task" /> の有効期間における現在の段階を表します。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Canceled">
      <summary>タスクの CancellationToken がシグナル状態であるときに、タスクがこのトークンを使用して OperationCanceledException をスローすることによって取り消しを受領したか、タスクの実行開始前にタスクの CancellationToken が既にシグナル状態でした。詳細については、「タスクのキャンセル」を参照してください。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Created">
      <summary>タスクは初期化されていますが、まだスケジュールされていません。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Faulted">
      <summary>タスクはハンドルされない例外が発生したために終了しました。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.RanToCompletion">
      <summary>タスクの実行は正常に完了しました。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Running">
      <summary>タスクは実行中で、まだ完了していません。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingForActivation">
      <summary>タスクはアクティブ化されるのを待機中で、.NET Framework インフラストラクチャによって内部的にスケジュールされています。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingForChildrenToComplete">
      <summary>タスクは実行を終了し、アタッチされている子タスクの完了を暗黙的に待機しています。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingToRun">
      <summary>タスクの実行はスケジュールされていますが、まだ開始されていません。</summary>
    </member>
    <member name="T:System.Threading.Tasks.UnobservedTaskExceptionEventArgs">
      <summary>エラーが発生した <see cref="T:System.Threading.Tasks.Task" /> の例外が無視されたときに発生するイベントに関するデータを提供します。</summary>
    </member>
    <member name="M:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.#ctor(System.AggregateException)">
      <summary>無視された例外を使用して、<see cref="T:System.Threading.Tasks.UnobservedTaskExceptionEventArgs" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="exception">無視された例外。</param>
    </member>
    <member name="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Exception">
      <summary>無視された例外。</summary>
      <returns>無視された例外。</returns>
    </member>
    <member name="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Observed">
      <summary>この例外が "認識済み" としてマークされているかどうかを示す値を取得します。</summary>
      <returns>この例外が "認識済み" としてマークされている場合は true、それ以外の場合は false。</returns>
    </member>
    <member name="M:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.SetObserved">
      <summary>
        <see cref="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Exception" /> を "認識済み" としてマークすることによって、例外のエスカレーション ポリシーがトリガーされないようにします。既定では、このポリシーがトリガーされるとプロセスが終了します。</summary>
    </member>
  </members>
</doc>