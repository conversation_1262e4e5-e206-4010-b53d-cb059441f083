# OCR库内存优化说明

## 优化概述

本次优化主要解决了RapidOCRCSharp项目中的内存泄漏和高内存占用问题，特别是在频繁调用`Detect`函数时的内存积累问题。

## 主要问题及解决方案

### 1. Mat对象未正确释放
**问题**: 多个函数中创建的Mat对象没有使用using语句或及时Dispose，导致内存泄漏。

**解决方案**:
- 在所有创建Mat对象的地方添加using语句
- 优化`OcrUtils.GetRotateCropImage()`等函数的内存管理
- 确保临时Mat对象及时释放

### 2. 缺少资源管理接口
**问题**: 主要类（OcrLite、DbNet、AngleNet、CrnnNet）未实现IDisposable接口。

**解决方案**:
- 为所有主要类实现IDisposable接口
- 添加析构函数和Dispose方法
- 确保ONNX推理会话正确释放

### 3. 图像预处理效率低下
**问题**: `SubstractMeanNormalize`函数创建大量临时对象，内存访问模式不优化。

**解决方案**:
- 优化内存访问模式，减少缓存未命中
- 直接访问Mat数据，避免创建Image对象
- 提供安全和高效两个版本的实现

### 4. 缺少内存池机制
**问题**: 频繁创建和释放相同尺寸的Mat对象，造成内存碎片。

**解决方案**:
- 实现`MatPool`类，复用常用尺寸的Mat对象
- 提供`PooledMat`包装器，自动归还对象到池中
- 限制池大小，防止内存无限增长

### 5. 缺少内存监控工具
**问题**: 无法跟踪内存使用情况和检测潜在泄漏。

**解决方案**:
- 实现`MemoryMonitor`静态类，提供内存监控功能
- 添加`MemoryScope`类，自动监控代码块的内存使用
- 提供详细的内存报告和泄漏检测

## 新增类说明

### MatPool类
```csharp
using (var matPool = new MatPool())
{
    using (var pooledMat = new PooledMat(matPool.GetMat(640, 480), matPool))
    {
        var mat = pooledMat.Mat;
        // 使用mat进行操作
    } // 自动归还到池中
}
```

### MemoryMonitor类
```csharp
MemoryMonitor.StartMonitoring();
MemoryMonitor.Checkpoint("检查点1");
MemoryMonitor.CheckpointWithGC("强制GC后检查");
bool hasLeak = MemoryMonitor.CheckForMemoryLeak(50 * 1024 * 1024); // 50MB阈值
Console.WriteLine(MemoryMonitor.GetDetailedReport());
```

### MemoryScope类
```csharp
using (var scope = new MemoryScope("OCR处理"))
{
    // 代码块
} // 自动显示内存使用情况
```

## 使用建议

### 1. 简单文本识别
```csharp
// 最简单的使用方式 - 直接获取文本
using (var ocrLite = new OcrLite())
{
    ocrLite.InitModels(detPath, clsPath, recPath, keysPath, numThread);
    string text = ocrLite.DetectText(imagePath);
    Console.WriteLine(text);
} // 自动释放所有资源
```

### 2. 获取详细信息（推荐）
```csharp
// 获取位置、尺寸、置信度等详细信息
using (var ocrLite = new OcrLite())
{
    ocrLite.InitModels(detPath, clsPath, recPath, keysPath, numThread);
    var textBlocks = ocrLite.DetectTextBlocks(imagePath);

    foreach (var block in textBlocks)
    {
        Console.WriteLine($"文本: {block.Text}");
        Console.WriteLine($"位置: [{block.BoxPoints[0].X},{block.BoxPoints[0].Y}] -> [{block.BoxPoints[2].X},{block.BoxPoints[2].Y}]");
        Console.WriteLine($"文本框置信度: {block.BoxScore:F3}");
        Console.WriteLine($"角度置信度: {block.AngleScore:F3}");
        Console.WriteLine($"识别时间: {block.CrnnTime:F1}ms");
    }
}
```

### 3. 带参数的文本识别
```csharp
using (var ocrLite = new OcrLite())
{
    ocrLite.InitModels(detPath, clsPath, recPath, keysPath, numThread);
    var textBlocks = ocrLite.DetectTextBlocks(imagePath, padding: 50, maxSideLen: 1024,
                                             boxScoreThresh: 0.5f, boxThresh: 0.3f,
                                             unClipRatio: 2.0f, doAngle: true, mostAngle: true);
}
```

### 4. 启用内存监控（可选）
```csharp
MemoryMonitor.StartMonitoring();
using (var ocrLite = new OcrLite())
{
    ocrLite.InitModels(detPath, clsPath, recPath, keysPath, numThread);
    var textBlocks = ocrLite.DetectTextBlocks(imagePath);
    MemoryMonitor.Checkpoint("OCR完成");
}
MemoryMonitor.CheckpointWithGC("最终检查");
```

### 5. 频繁调用时的内存检查
```csharp
using (var ocrLite = new OcrLite())
{
    ocrLite.InitModels(detPath, clsPath, recPath, keysPath, numThread);

    for (int i = 0; i < 1000; i++)
    {
        var textBlocks = ocrLite.DetectTextBlocks(imagePath);

        if (i % 50 == 0 && MemoryMonitor.CheckForMemoryLeak())
        {
            Console.WriteLine("检测到内存泄漏!");
            break;
        }
    }
}
```

## 性能改进

### 内存使用优化
- **减少内存分配**: 通过Mat池复用对象，减少频繁的内存分配和释放
- **及时释放资源**: 使用using语句确保资源及时释放
- **优化内存访问**: 改进图像预处理的内存访问模式

### 垃圾回收优化
- **减少GC压力**: 通过对象复用减少垃圾回收频率
- **及时清理**: 主动释放大对象，避免等待GC

### 内存泄漏检测
- **实时监控**: 提供实时内存使用监控
- **自动检测**: 自动检测潜在的内存泄漏
- **详细报告**: 提供详细的内存使用报告

## 注意事项

1. **线程安全**: MatPool使用了ConcurrentQueue，支持多线程环境
2. **内存阈值**: 根据实际应用调整内存泄漏检测阈值
3. **池大小限制**: MatPool限制了每个尺寸的最大对象数量，防止内存无限增长
4. **性能权衡**: 内存监控会带来少量性能开销，生产环境可选择性启用

## 测试建议

1. 运行`OptimizedOcrExample.RunExample()`测试基本功能
2. 运行`OptimizedOcrExample.RunFrequentCallsExample()`测试频繁调用
3. 运行`OptimizedOcrExample.RunMatPoolExample()`测试Mat池功能
4. 使用内存分析工具验证优化效果

## 兼容性

- 保持了原有API的兼容性
- 新增功能为可选使用
- 支持.NET Framework 4.6+
- 兼容现有的Emgu.CV和ONNX Runtime版本
