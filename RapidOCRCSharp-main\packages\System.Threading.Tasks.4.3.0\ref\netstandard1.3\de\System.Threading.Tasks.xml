﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Tasks</name>
  </assembly>
  <members>
    <member name="T:System.AggregateException">
      <summary>Stellt einen oder mehrere Fehler dar, die beim Ausführen einer Anwendung auftreten.</summary>
    </member>
    <member name="M:System.AggregateException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.AggregateException" />-Klasse mit einer vom System generierten Meldung, die den Fehler beschreibt.</summary>
    </member>
    <member name="M:System.AggregateException.#ctor(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.AggregateException" />-Klasse mit Verweisen auf die inneren Ausnahmen, die diese Ausnahme ausgelöst haben.</summary>
      <param name="innerExceptions">Die Ausnahmen, die die aktuelle Ausnahme ausgelöst haben.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="innerExceptions" />-Argument ist NULL.</exception>
      <exception cref="T:System.ArgumentException">Ein Element von <paramref name="innerExceptions" /> ist null.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.Exception[])">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.AggregateException" />-Klasse mit Verweisen auf die inneren Ausnahmen, die diese Ausnahme ausgelöst haben.</summary>
      <param name="innerExceptions">Die Ausnahmen, die die aktuelle Ausnahme ausgelöst haben.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="innerExceptions" />-Argument ist NULL.</exception>
      <exception cref="T:System.ArgumentException">Ein Element von <paramref name="innerExceptions" /> ist null.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.AggregateException" />-Klasse mit einer angegebenen Meldung, die den Fehler beschreibt.</summary>
      <param name="message">Die Meldung, in der die Ausnahme beschrieben wirdDer Aufrufer dieses Konstruktors muss sicherstellen, dass diese Zeichenfolge für die aktuelle Systemkultur lokalisiert wurde.</param>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.AggregateException" />-Klasse mit einer angegebenen Fehlermeldung und Verweisen auf die inneren Ausnahmen, die diese Ausnahme verursacht haben.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird.</param>
      <param name="innerExceptions">Die Ausnahmen, die die aktuelle Ausnahme ausgelöst haben.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="innerExceptions" />-Argument ist NULL.</exception>
      <exception cref="T:System.ArgumentException">Ein Element von <paramref name="innerExceptions" /> ist null.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.AggregateException" />-Klasse mit einer angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Meldung, in der die Ausnahme beschrieben wirdDer Aufrufer dieses Konstruktors muss sicherstellen, dass diese Zeichenfolge für die aktuelle Systemkultur lokalisiert wurde.</param>
      <param name="innerException">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="innerException" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="innerException" />-Argument ist NULL.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Exception[])">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.AggregateException" />-Klasse mit einer angegebenen Fehlermeldung und Verweisen auf die inneren Ausnahmen, die diese Ausnahme verursacht haben.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird.</param>
      <param name="innerExceptions">Die Ausnahmen, die die aktuelle Ausnahme ausgelöst haben.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="innerExceptions" />-Argument ist NULL.</exception>
      <exception cref="T:System.ArgumentException">Ein Element von <paramref name="innerExceptions" /> ist null.</exception>
    </member>
    <member name="M:System.AggregateException.Flatten">
      <summary>Fasst <see cref="T:System.AggregateException" />-Instanzen in einer einzigen neuen Instanz zusammen.</summary>
      <returns>Eine neue, zusammengefasste <see cref="T:System.AggregateException" />.</returns>
    </member>
    <member name="M:System.AggregateException.GetBaseException">
      <summary>Gibt die <see cref="T:System.AggregateException" /> zurück, die diese Ausnahme verursacht hat.</summary>
      <returns>Gibt die <see cref="T:System.AggregateException" /> zurück, die diese Ausnahme verursacht hat.</returns>
    </member>
    <member name="M:System.AggregateException.Handle(System.Func{System.Exception,System.Boolean})">
      <summary>Ruft einen Handler für jede in dieser <see cref="T:System.AggregateException" /> enthaltenen <see cref="T:System.Exception" /> auf.</summary>
      <param name="predicate">Das Prädikat, das für jede Ausnahme ausgeführt werden soll.Das Prädikat akzeptiert als Argument die zu verarbeitende <see cref="T:System.Exception" /> und gibt einen booleschen Wert zurück, der angibt, ob die Ausnahme behandelt wurde.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="predicate" />-Argument ist NULL.</exception>
      <exception cref="T:System.AggregateException">Eine in dieser <see cref="T:System.AggregateException" /> enthaltene Ausnahme wurde nicht behandelt.</exception>
    </member>
    <member name="P:System.AggregateException.InnerExceptions">
      <summary>Ruft eine schreibgeschützte Auflistung der <see cref="T:System.Exception" />-Instanzen ab, die die aktuelle Ausnahme verursacht haben.</summary>
      <returns>Gibt eine schreibgeschützte Auflistung der <see cref="T:System.Exception" />-Instanzen zurück, die die aktuelle Ausnahme verursacht haben.</returns>
    </member>
    <member name="M:System.AggregateException.ToString">
      <summary>Erstellt eine Zeichenfolgenentsprechung der aktuellen <see cref="T:System.AggregateException" /> und gibt diese zurück.</summary>
      <returns>Eine Zeichenfolgenentsprechung der aktuellen Ausnahme.</returns>
    </member>
    <member name="T:System.OperationCanceledException">
      <summary>Die Ausnahme, die einem Thread beim Abbrechen eines von diesem Thread ausgeführten Vorgangs ausgelöst wird.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.OperationCanceledException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.OperationCanceledException" />-Klasse mit einer vom System bereitgestellten Fehlermeldung.</summary>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.OperationCanceledException" />-Klasse mit einer angegebenen Fehlermeldung.</summary>
      <param name="message">Ein <see cref="T:System.String" />, der den Fehler beschreibt.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.OperationCanceledException" />-Klasse mit einer angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird. </param>
      <param name="innerException">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="innerException" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Exception,System.Threading.CancellationToken)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.OperationCanceledException" />-Klasse mit einer angegebenen Fehlermeldung, einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat, und einem Abbruchtoken.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird. </param>
      <param name="innerException">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="innerException" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
      <param name="token">Ein Abbruchtoken, das dem abgebrochenen Vorgang zugeordnet ist.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Threading.CancellationToken)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.OperationCanceledException" />-Klasse mit einer angegebenen Fehlermeldung und einem Abbruchtoken.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird.</param>
      <param name="token">Ein Abbruchtoken, das dem abgebrochenen Vorgang zugeordnet ist.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.Threading.CancellationToken)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.OperationCanceledException" />-Klasse mit einem Abbruchtoken.</summary>
      <param name="token">Ein Abbruchtoken, das dem abgebrochenen Vorgang zugeordnet ist.</param>
    </member>
    <member name="P:System.OperationCanceledException.CancellationToken">
      <summary>Ruft ein Token ab, das dem abgebrochenen Vorgang zugeordnet ist.</summary>
      <returns>Ein Token, das dem abgebrochenen Vorgang zugeordnet ist, oder ein Standardtoken.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder">
      <summary>Stellt einen Generator für asynchrone Methoden dar, die eine Aufgabe zurückgeben.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitOnCompleted``2(``0@,``1@)">
      <summary>Plant den Übergang des Zustandsautomaten zur nächsten Aktion, wenn der angegebene Awaiter abgeschlossen ist.</summary>
      <param name="awaiter">Der Awaiter.</param>
      <param name="stateMachine">Der Zustandsautomat.</param>
      <typeparam name="TAwaiter">Der Awaitertyp.</typeparam>
      <typeparam name="TStateMachine">Der Typ des Zustandsautomaten.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>Plant den Übergang des Zustandsautomaten zur nächsten Aktion, wenn der angegebene Awaiter abgeschlossen ist.Diese Methode kann von teilweise vertrauenswürdigem Code aufgerufen werden.</summary>
      <param name="awaiter">Der Awaiter.</param>
      <param name="stateMachine">Der Zustandsautomat.</param>
      <typeparam name="TAwaiter">Der Awaitertyp.</typeparam>
      <typeparam name="TStateMachine">Der Typ des Zustandsautomaten.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Create">
      <summary>Erstellt eine Instanz der <see cref="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder" />-Klasse.</summary>
      <returns>Eine neue Instanz des Generators.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetException(System.Exception)">
      <summary>Markiert die Aufgabe als fehlgeschlagen und verknüpft die angegebene Ausnahme mit der Aufgabe.</summary>
      <param name="exception">Die Ausnahme, die an die Aufgabe gebunden werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">Die Aufgabe ist bereits abgeschlossen.- oder -Der Builder wird nicht initialisiert.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult">
      <summary>Markiert die Aufgabe als erfolgreich abgeschlossen.</summary>
      <exception cref="T:System.InvalidOperationException">Die Aufgabe ist bereits abgeschlossen.- oder -Der Builder wird nicht initialisiert.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Ordnet den Generator dem angegebenen Zustandsautomaten zu.</summary>
      <param name="stateMachine">Die Zustandsautomatinstanz, die dem Generator zugeordnet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">Der Zustandsautomat wurde zuvor festgelegt.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Start``1(``0@)">
      <summary>Startet den Generator mit dem zugeordneten Zustandsautomaten.</summary>
      <param name="stateMachine">Die Zustandsautomatinstanz, die als Verweis übergeben wird.</param>
      <typeparam name="TStateMachine">Der Typ des Zustandsautomaten.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> ist null.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Task">
      <summary>Ruft die Aufgabe für diesen Generator ab.</summary>
      <returns>Die Aufgabe für diesen Generator.</returns>
      <exception cref="T:System.InvalidOperationException">Der Builder wird nicht initialisiert.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1">
      <summary>Stellt einen Generator für asynchrone Methoden dar, die eine Aufgabe zurückgeben, und stellt einen Parameter für das Ergebnis bereit.</summary>
      <typeparam name="TResult">Das zum Abschließen der Aufgabe zu verwendende Ergebnis.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AwaitOnCompleted``2(``0@,``1@)">
      <summary>Plant den Übergang des Zustandsautomaten zur nächsten Aktion, wenn der angegebene Awaiter abgeschlossen ist.</summary>
      <param name="awaiter">Der Awaiter.</param>
      <param name="stateMachine">Der Zustandsautomat.</param>
      <typeparam name="TAwaiter">Der Awaitertyp.</typeparam>
      <typeparam name="TStateMachine">Der Typ des Zustandsautomaten.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>Plant den Übergang des Zustandsautomaten zur nächsten Aktion, wenn der angegebene Awaiter abgeschlossen ist.Diese Methode kann von teilweise vertrauenswürdigem Code aufgerufen werden.</summary>
      <param name="awaiter">Der Awaiter.</param>
      <param name="stateMachine">Der Zustandsautomat.</param>
      <typeparam name="TAwaiter">Der Awaitertyp.</typeparam>
      <typeparam name="TStateMachine">Der Typ des Zustandsautomaten.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Create">
      <summary>Erstellt eine Instanz der <see cref="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1" />-Klasse.</summary>
      <returns>Eine neue Instanz des Generators.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetException(System.Exception)">
      <summary>Markiert die Aufgabe als fehlgeschlagen und verknüpft die angegebene Ausnahme mit der Aufgabe.</summary>
      <param name="exception">Die Ausnahme, die an die Aufgabe gebunden werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">Die Aufgabe ist bereits abgeschlossen.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetResult(`0)">
      <summary>Markiert die Aufgabe als erfolgreich abgeschlossen.</summary>
      <param name="result">Das zum Abschließen der Aufgabe zu verwendende Ergebnis.</param>
      <exception cref="T:System.InvalidOperationException">Die Aufgabe ist bereits abgeschlossen.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Ordnet den Generator dem angegebenen Zustandsautomaten zu.</summary>
      <param name="stateMachine">Die Zustandsautomatinstanz, die dem Generator zugeordnet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">Der Zustandsautomat wurde zuvor festgelegt.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Start``1(``0@)">
      <summary>Startet den Generator mit dem zugeordneten Zustandsautomaten.</summary>
      <param name="stateMachine">Die Zustandsautomatinstanz, die als Verweis übergeben wird.</param>
      <typeparam name="TStateMachine">Der Typ des Zustandsautomaten.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> ist null.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Task">
      <summary>Ruft die Aufgabe für diesen Generator ab.</summary>
      <returns>Die Aufgabe für diesen Generator.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncVoidMethodBuilder">
      <summary>Stellt einen Generator für asynchrone Methoden dar, die keinen Wert zurückgeben.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitOnCompleted``2(``0@,``1@)">
      <summary>Plant den Übergang des Zustandsautomaten zur nächsten Aktion, wenn der angegebene Awaiter abgeschlossen ist.</summary>
      <param name="awaiter">Der Awaiter.</param>
      <param name="stateMachine">Der Zustandsautomat.</param>
      <typeparam name="TAwaiter">Der Awaitertyp.</typeparam>
      <typeparam name="TStateMachine">Der Typ des Zustandsautomaten.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>Plant den Übergang des Zustandsautomaten zur nächsten Aktion, wenn der angegebene Awaiter abgeschlossen ist.Diese Methode kann von teilweise vertrauenswürdigem Code aufgerufen werden.</summary>
      <param name="awaiter">Der Awaiter.</param>
      <param name="stateMachine">Der Zustandsautomat.</param>
      <typeparam name="TAwaiter">Der Awaitertyp.</typeparam>
      <typeparam name="TStateMachine">Der Typ des Zustandsautomaten.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Create">
      <summary>Erstellt eine Instanz der <see cref="T:System.Runtime.CompilerServices.AsyncVoidMethodBuilder" />-Klasse.</summary>
      <returns>Eine neue Instanz des Generators.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetException(System.Exception)">
      <summary>Bindet eine Ausnahme an den Methodengenerator.</summary>
      <param name="exception">Die zu bindende Ausnahme.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">Der Builder wird nicht initialisiert.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetResult">
      <summary>Markiert den Methodengenerator als erfolgreich abgeschlossen.</summary>
      <exception cref="T:System.InvalidOperationException">Der Builder wird nicht initialisiert.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Ordnet den Generator dem angegebenen Zustandsautomaten zu.</summary>
      <param name="stateMachine">Die Zustandsautomatinstanz, die dem Generator zugeordnet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">Der Zustandsautomat wurde zuvor festgelegt.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start``1(``0@)">
      <summary>Startet den Generator mit dem zugeordneten Zustandsautomaten.</summary>
      <param name="stateMachine">Die Zustandsautomatinstanz, die als Verweis übergeben wird.</param>
      <typeparam name="TStateMachine">Der Typ des Zustandsautomaten.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> ist null.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable">
      <summary>Stellt ein Awaitable-Objekt bereit, das konfigurierte Awaits bei einem Task zulässt.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.GetAwaiter">
      <summary>Gibt einen Awaiter für dieses awaitable Objekt zurück.</summary>
      <returns>Der Awaiter.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1">
      <summary>Stellt ein Awaitable-Objekt bereit, das konfigurierte Awaits bei einem Task zulässt.</summary>
      <typeparam name="TResult">Der von diesem <see cref="T:System.Threading.Tasks.Task`1" /> erzeugte Ergebnistyp.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.GetAwaiter">
      <summary>Gibt einen Awaiter für dieses awaitable Objekt zurück.</summary>
      <returns>Der Awaiter.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter">
      <summary>Stellt einen Awaiter für ein awaitable Objekt bereit (<see cref="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1" />) .</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.GetResult">
      <summary>Beendet den Wartevorgang für den abgeschlossenen Task .</summary>
      <returns>Das Ergebnis der abgeschlossenen Aufgabe.</returns>
      <exception cref="T:System.NullReferenceException">Der Awaiter wurde nicht ordnungsgemäß initialisiert.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">Die Aufgabe wurde abgebrochen.</exception>
      <exception cref="T:System.Exception">Die im Fehlerzustand abgeschlossene Aufgabe.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.IsCompleted">
      <summary>Ruft einen Wert ab, der angibt, ob die Aufgabe, auf die gewartet wird, abgeschlossen wurde.</summary>
      <returns>true, wenn die erwartete Aufgabe abgeschlossen wurde; andernfalls false.</returns>
      <exception cref="T:System.NullReferenceException">Der Awaiter wurde nicht ordnungsgemäß initialisiert.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
      <summary>Plant den Fortsetzungsvorgang für die Aufgabe, die diesem Awaiter zugeordnet ist.</summary>
      <param name="continuation">Die Aktion, die Abschluss der Warte-Vorgangs aufgerufen wird.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="continuation" />-Argument ist null.</exception>
      <exception cref="T:System.NullReferenceException">Der Awaiter wurde nicht ordnungsgemäß initialisiert.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Plant den Fortsetzungsvorgang für die Aufgabe, die diesem Awaiter zugeordnet ist. </summary>
      <param name="continuation">Die Aktion, die Abschluss der Warte-Vorgangs aufgerufen wird.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="continuation" />-Argument ist null.</exception>
      <exception cref="T:System.NullReferenceException">Der Awaiter wurde nicht ordnungsgemäß initialisiert.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter">
      <summary>Stellt einen Awaiter für ein awaitable (<see cref="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable" />) Objekt bereit.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.GetResult">
      <summary>Beendet den Wartevorgang für den abgeschlossenen Task .</summary>
      <exception cref="T:System.NullReferenceException">Der Awaiter wurde nicht ordnungsgemäß initialisiert.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">Die Aufgabe wurde abgebrochen.</exception>
      <exception cref="T:System.Exception">Die im Fehlerzustand abgeschlossene Aufgabe.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.IsCompleted">
      <summary>Ruft einen Wert ab, der angibt, ob die Aufgabe, auf die gewartet wird, abgeschlossen wurde.</summary>
      <returns>true, wenn die erwartete Aufgabe abgeschlossen ist; andernfalls false.</returns>
      <exception cref="T:System.NullReferenceException">Der Awaiter wurde nicht ordnungsgemäß initialisiert.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
      <summary>Plant den Fortsetzungsvorgang für die Aufgabe, die diesem Awaiter zugeordnet ist.</summary>
      <param name="continuation">Die Aktion, die Abschluss der Warte-Vorgangs aufgerufen wird.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="continuation" />-Argument ist null.</exception>
      <exception cref="T:System.NullReferenceException">Der Awaiter wurde nicht ordnungsgemäß initialisiert.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Plant den Fortsetzungsvorgang für die Aufgabe, die diesem Awaiter zugeordnet ist. </summary>
      <param name="continuation">Die Aktion, die Abschluss der Warte-Vorgangs aufgerufen wird.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="continuation" />-Argument ist null.</exception>
      <exception cref="T:System.NullReferenceException">Der Awaiter wurde nicht ordnungsgemäß initialisiert.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.IAsyncStateMachine">
      <summary>Stellt Zustandsautomaten dar, die für asynchrone Methoden generiert werden.Dieser Typ ist ausschließlich zur Compiler-Verwendung vorgesehen.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.IAsyncStateMachine.MoveNext">
      <summary>Verschiebt den Zustandsautomaten zum nächsten Zustand.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.IAsyncStateMachine.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Konfiguriert den Zustandsautomaten mit einem HEAP-zugeordneten Replikat.</summary>
      <param name="stateMachine">Das HEAP-zugeordnete Replikat.</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.ICriticalNotifyCompletion">
      <summary>Stellt einen Awaiter dar, der Fortsetzungen plant, wenn ein Await-Vorgang beendet wird.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ICriticalNotifyCompletion.UnsafeOnCompleted(System.Action)">
      <summary>Plant den Fortsetzungsvorgang, der aufgerufen wird, wenn die Instanz abgeschlossen wird.</summary>
      <param name="continuation">Die Aktion, die beim Abschluss der Vorgangs aufgerufen wird.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="continuation" />-Argument ist ein NULL-Argument (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.INotifyCompletion">
      <summary>Stellt einen Vorgang dar, der Fortsetzungen plant, wenn er abgeschlossen wird.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.INotifyCompletion.OnCompleted(System.Action)">
      <summary>Plant den Fortsetzungsvorgang, der aufgerufen wird, wenn die Instanz abgeschlossen wird.</summary>
      <param name="continuation">Die Aktion, die beim Abschluss der Vorgangs aufgerufen wird.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="continuation" />-Argument ist ein NULL-Argument (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TaskAwaiter">
      <summary>Stellt ein Objekt bereit, das auf den Abschluss einer asynchronen Aufgabe wartet. </summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.GetResult">
      <summary>Beendet das Warten auf den Abschluss der asynchronen Aufgabe.</summary>
      <exception cref="T:System.NullReferenceException">Das <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" />-Objekt wurde nicht ordnungsgemäß initialisiert.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">Die Aufgabe wurde abgebrochen.</exception>
      <exception cref="T:System.Exception">Die Aufgabe hat in einem <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> abgeschlossen.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.TaskAwaiter.IsCompleted">
      <summary>Ruft einen Wert ab, der angibt, ob die asynchrone Aufgabe abgeschlossen wurde.</summary>
      <returns>true, wenn die Aufgabe abgeschlossen wurde, andernfalls false.</returns>
      <exception cref="T:System.NullReferenceException">Das <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" />-Objekt wurde nicht ordnungsgemäß initialisiert.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.OnCompleted(System.Action)">
      <summary>Legt die Aktion fest, die ausgeführt wird, sobald das <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" />-Objekt nicht mehr auf die zu beendende asynchrone Aufgabe wartet.</summary>
      <param name="continuation">Die Aktion, die ausgeführt werden soll, wenn der Wartevorgang abgeschlossen wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> ist null.</exception>
      <exception cref="T:System.NullReferenceException">Das <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" />-Objekt wurde nicht ordnungsgemäß initialisiert.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Plant die Fortsetzungsaktion für die asynchrone Aufgabe, die diesem Awaiter zugeordnet ist.</summary>
      <param name="continuation">Die Aktion, die bei Abschluss der Wartevorgangs aufgerufen wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">Der Awaiter wurde nicht ordnungsgemäß initialisiert.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TaskAwaiter`1">
      <summary>Stellt ein Objekt dar, das auf den Abschluss einer asynchronen Aufgabe wartet und einen Parameter für das Ergebnis bereitstellt.</summary>
      <typeparam name="TResult">Das Ergebnis der Aufgabe.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.GetResult">
      <summary>Beendet das Warten auf den Abschluss der asynchronen Aufgabe.</summary>
      <returns>Das Ergebnis der abgeschlossenen Aufgabe.</returns>
      <exception cref="T:System.NullReferenceException">Das <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" />-Objekt wurde nicht ordnungsgemäß initialisiert.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">Die Aufgabe wurde abgebrochen.</exception>
      <exception cref="T:System.Exception">Die Aufgabe hat in einem <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> abgeschlossen.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.TaskAwaiter`1.IsCompleted">
      <summary>Ruft einen Wert ab, der angibt, ob die asynchrone Aufgabe abgeschlossen wurde.</summary>
      <returns>true, wenn die Aufgabe abgeschlossen wurde, andernfalls false.</returns>
      <exception cref="T:System.NullReferenceException">Das <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" />-Objekt wurde nicht ordnungsgemäß initialisiert.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.OnCompleted(System.Action)">
      <summary>Legt die Aktion fest, die ausgeführt wird, sobald das <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" />-Objekt nicht mehr auf die zu beendende asynchrone Aufgabe wartet.</summary>
      <param name="continuation">Die Aktion, die ausgeführt werden soll, wenn der Wartevorgang abgeschlossen wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> ist null.</exception>
      <exception cref="T:System.NullReferenceException">Das <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" />-Objekt wurde nicht ordnungsgemäß initialisiert.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.UnsafeOnCompleted(System.Action)">
      <summary>Plant die Fortsetzungsaktion für die asynchrone Aufgabe, die diesem Awaiter zugeordnet ist.</summary>
      <param name="continuation">Die Aktion, die bei Abschluss der Wartevorgangs aufgerufen wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">Der Awaiter wurde nicht ordnungsgemäß initialisiert.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.YieldAwaitable">
      <summary>Stellt den Kontext für das Warten bereit, wenn asynchron in einer Zielumgebung gewechselt wird.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.GetAwaiter">
      <summary>Ruft ein <see cref="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter" />-Objekt für diese Instanz der Klasse ab.</summary>
      <returns>Ein -Objekt, mit dem der Abschluss des asynchronen Vorgangs überwacht wird.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter">
      <summary>Stellt einen Awaiter zum Umschalten in einer Zielumgebung bereit.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.GetResult">
      <summary>Beendet den Wartevorgang.</summary>
    </member>
    <member name="P:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.IsCompleted">
      <summary>Ruft einen Wert ab, der angibt, ob ein Ertrag nicht erforderlich ist.</summary>
      <returns>Immer false, das angibt, dass ein Ertrag immer für <see cref="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter" /> erforderlich ist.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.OnCompleted(System.Action)">
      <summary>Legt die Fortsetzung fest, die aufgerufen werden soll.</summary>
      <param name="continuation">Die Aktion zum asynchronen Aufrufen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> ist null.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Stellt die <paramref name="continuation" /> zurück in den aktuellen Kontext.</summary>
      <param name="continuation">Die Aktion zum asynchronen Aufrufen.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="continuation" />-Argument ist null.</exception>
    </member>
    <member name="T:System.Threading.CancellationToken">
      <summary>Gibt eine Benachrichtigung darüber weiter, dass Vorgänge abgebrochen werden sollen.</summary>
    </member>
    <member name="M:System.Threading.CancellationToken.#ctor(System.Boolean)">
      <summary>Initialisiert das <see cref="T:System.Threading.CancellationToken" />.</summary>
      <param name="canceled">Der Zustand "abgebrochen" für das Token.</param>
    </member>
    <member name="P:System.Threading.CancellationToken.CanBeCanceled">
      <summary>Ruft einen Wert ab, der angibt, ob der Zustand "abgebrochen" von diesem Token unterstützt wird.</summary>
      <returns>"true", wenn sich dieses Token im abgebrochenen Zustand befinden kann, andernfalls "false".</returns>
    </member>
    <member name="M:System.Threading.CancellationToken.Equals(System.Object)">
      <summary>Ermittelt, ob die aktuelle <see cref="T:System.Threading.CancellationToken" />-Instanz und die angegebene <see cref="T:System.Object" />-Instanz gleich sind.</summary>
      <returns>"true", wenn <paramref name="other" /> ein <see cref="T:System.Threading.CancellationToken" /> ist und die beiden Instanzen gleich sind, andernfalls "false".Zwei Token sind gleich, wenn sie derselben <see cref="T:System.Threading.CancellationTokenSource" /> zugeordnet sind oder beide aus öffentlichen CancellationToken-Konstruktoren erstellt wurden und ihre <see cref="P:System.Threading.CancellationToken.IsCancellationRequested" />-Werte gleich sind.</returns>
      <param name="other">Das andere Objekt, mit dem diese Instanz verglichen werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Equals(System.Threading.CancellationToken)">
      <summary>Ermittelt, ob die aktuelle <see cref="T:System.Threading.CancellationToken" />-Instanz gleich dem angegebenen Token ist.</summary>
      <returns>"true", wenn die Instanzen gleich sind, andernfalls "false".Zwei Token sind gleich, wenn sie derselben <see cref="T:System.Threading.CancellationTokenSource" /> zugeordnet sind oder beide aus öffentlichen CancellationToken-Konstruktoren erstellt wurden und ihre <see cref="P:System.Threading.CancellationToken.IsCancellationRequested" />-Werte gleich sind.</returns>
      <param name="other">Das andere <see cref="T:System.Threading.CancellationToken" />, mit dem diese Instanz verglichen werden soll.</param>
    </member>
    <member name="M:System.Threading.CancellationToken.GetHashCode">
      <summary>Dient als Hashfunktion für eine <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>Ein Hashcode für die aktuelle <see cref="T:System.Threading.CancellationToken" />-Instanz.</returns>
    </member>
    <member name="P:System.Threading.CancellationToken.IsCancellationRequested">
      <summary>Ruft einen Wert ab, der angibt, ob für dieses Token ein Abbruch angefordert wurde.</summary>
      <returns>"true", wenn der Abbruch für dieses Token angefordert wurde, andernfalls "false".</returns>
    </member>
    <member name="P:System.Threading.CancellationToken.None">
      <summary>Gibt einen leeren <see cref="T:System.Threading.CancellationToken" />-Wert zurück.</summary>
      <returns>Ein leeres Abbruchtoken. </returns>
    </member>
    <member name="M:System.Threading.CancellationToken.op_Equality(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>Stellt fest, ob zwei <see cref="T:System.Threading.CancellationToken" />-Instanzen gleich sind.</summary>
      <returns>"true", wenn die Instanzen gleich sind, andernfalls "false".</returns>
      <param name="left">Die erste Instanz.</param>
      <param name="right">Die zweite Instanz.</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.op_Inequality(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>Ermittelt, ob zwei <see cref="T:System.Threading.CancellationToken" />-Instanzen ungleich sind.</summary>
      <returns>"true", wenn die beiden Instanzen ungleich sind, andernfalls "false".</returns>
      <param name="left">Die erste Instanz.</param>
      <param name="right">Die zweite Instanz.</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action)">
      <summary>Registriert einen Delegaten, der aufgerufen wird, wenn dieses <see cref="T:System.Threading.CancellationToken" /> abgebrochen wird.</summary>
      <returns>Die <see cref="T:System.Threading.CancellationTokenRegistration" />-Instanz, die verwendet werden kann, um die Registrierung des Rückrufs aufzuheben.</returns>
      <param name="callback">Der Delegat, der ausgeführt wird, wenn das <see cref="T:System.Threading.CancellationToken" />-Objekt abgebrochen wird.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action,System.Boolean)">
      <summary>Registriert einen Delegaten, der aufgerufen wird, wenn dieses <see cref="T:System.Threading.CancellationToken" /> abgebrochen wird.</summary>
      <returns>Die <see cref="T:System.Threading.CancellationTokenRegistration" />-Instanz, die verwendet werden kann, um die Registrierung des Rückrufs aufzuheben.</returns>
      <param name="callback">Der Delegat, der ausgeführt wird, wenn das <see cref="T:System.Threading.CancellationToken" />-Objekt abgebrochen wird.</param>
      <param name="useSynchronizationContext">Ein boolescher Wert, der angibt, ob der aktuelle <see cref="T:System.Threading.SynchronizationContext" /> erfasst und beim Aufrufen von <paramref name="callback" /> verwendet werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action{System.Object},System.Object)">
      <summary>Registriert einen Delegaten, der aufgerufen wird, wenn dieses <see cref="T:System.Threading.CancellationToken" /> abgebrochen wird.</summary>
      <returns>Die <see cref="T:System.Threading.CancellationTokenRegistration" />-Instanz, die verwendet werden kann, um die Registrierung des Rückrufs aufzuheben.</returns>
      <param name="callback">Der Delegat, der ausgeführt wird, wenn das <see cref="T:System.Threading.CancellationToken" />-Objekt abgebrochen wird.</param>
      <param name="state">Der Zustand, der beim Aufrufen des Delegaten an <paramref name="callback" /> übergeben werden soll.Dies kann NULL sein.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action{System.Object},System.Object,System.Boolean)">
      <summary>Registriert einen Delegaten, der aufgerufen wird, wenn dieses <see cref="T:System.Threading.CancellationToken" /> abgebrochen wird.</summary>
      <returns>Die <see cref="T:System.Threading.CancellationTokenRegistration" />-Instanz, die verwendet werden kann, um die Registrierung des Rückrufs aufzuheben.</returns>
      <param name="callback">Der Delegat, der ausgeführt wird, wenn das <see cref="T:System.Threading.CancellationToken" />-Objekt abgebrochen wird.</param>
      <param name="state">Der Zustand, der beim Aufrufen des Delegaten an <paramref name="callback" /> übergeben werden soll.Dies kann NULL sein.</param>
      <param name="useSynchronizationContext">Ein boolescher Wert, der angibt, ob der aktuelle <see cref="T:System.Threading.SynchronizationContext" /> erfasst und beim Aufrufen von <paramref name="callback" /> verwendet werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.ThrowIfCancellationRequested">
      <summary>Löst eine <see cref="T:System.OperationCanceledException" /> aus, wenn für dieses Token ein Abbruch angefordert wurde.</summary>
      <exception cref="T:System.OperationCanceledException">The token has had cancellation requested.</exception>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.CancellationToken.WaitHandle">
      <summary>Ruft ein <see cref="T:System.Threading.WaitHandle" /> ab, das signalisiert wird, wenn das Token abgebrochen wird.</summary>
      <returns>Ein <see cref="T:System.Threading.WaitHandle" />, das signalisiert wird, wenn das Token abgebrochen wird.</returns>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="T:System.Threading.CancellationTokenRegistration">
      <summary>Stellt einen Rückrufdelegaten dar, der bei einem <see cref="T:System.Threading.CancellationToken" /> registriert wurde. </summary>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Dispose">
      <summary>Gibt alle von der aktuellen Instanz der <see cref="T:System.Threading.CancellationTokenRegistration" />-Klasse verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Equals(System.Object)">
      <summary>Bestimmt, ob die aktuelle <see cref="T:System.Threading.CancellationTokenRegistration" />-Instanz und die angegebene <see cref="T:System.Threading.CancellationTokenRegistration" />-Instanz gleich sind.</summary>
      <returns>True, wenn diese Instanz und <paramref name="obj" /> gleich sind.Andernfalls false.Zwei <see cref="T:System.Threading.CancellationTokenRegistration" />-Instanzen sind gleich, wenn sie beide auf die Ausgabe eines Aufrufs derselben Register-Methode eines <see cref="T:System.Threading.CancellationToken" /> verweisen.</returns>
      <param name="obj">Das andere Objekt, mit dem diese Instanz verglichen werden soll.</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Equals(System.Threading.CancellationTokenRegistration)">
      <summary>Bestimmt, ob die aktuelle <see cref="T:System.Threading.CancellationTokenRegistration" />-Instanz und die angegebene <see cref="T:System.Threading.CancellationTokenRegistration" />-Instanz gleich sind.</summary>
      <returns>True, wenn diese Instanz und <paramref name="other" /> gleich sind.Andernfalls false. Zwei <see cref="T:System.Threading.CancellationTokenRegistration" />-Instanzen sind gleich, wenn sie beide auf die Ausgabe eines Aufrufs derselben Register-Methode eines <see cref="T:System.Threading.CancellationToken" /> verweisen.</returns>
      <param name="other">Das andere <see cref="T:System.Threading.CancellationTokenRegistration" />, mit dem diese Instanz verglichen werden soll.</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.GetHashCode">
      <summary>Fungiert als Hashfunktion für eine <see cref="T:System.Threading.CancellationTokenRegistration" />.</summary>
      <returns>Ein Hashcode für die aktuelle <see cref="T:System.Threading.CancellationTokenRegistration" />-Instanz.</returns>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.op_Equality(System.Threading.CancellationTokenRegistration,System.Threading.CancellationTokenRegistration)">
      <summary>Bestimmt, ob zwei <see cref="T:System.Threading.CancellationTokenRegistration" />-Instanzen gleich sind.</summary>
      <returns>True, wenn die Instanzen gleich sind, andernfalls false.</returns>
      <param name="left">Die erste Instanz.</param>
      <param name="right">Die zweite Instanz.</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.op_Inequality(System.Threading.CancellationTokenRegistration,System.Threading.CancellationTokenRegistration)">
      <summary>Bestimmt, ob zwei <see cref="T:System.Threading.CancellationTokenRegistration" />-Instanzen ungleich sind.</summary>
      <returns>True, wenn die beiden Instanzen ungleich sind, andernfalls false.</returns>
      <param name="left">Die erste Instanz.</param>
      <param name="right">Die zweite Instanz.</param>
    </member>
    <member name="T:System.Threading.CancellationTokenSource">
      <summary>Signalisiert einem <see cref="T:System.Threading.CancellationToken" />, dass es abgebrochen werden soll.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.CancellationTokenSource" />-Klasse.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor(System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.CancellationTokenSource" />-Klasse, die nach der angegebenen Verzögerung in Millisekunden abgebrochen wird.</summary>
      <param name="millisecondsDelay">Das Zeitintervall in Millisekunden, das vor dem Abbrechen dieser <see cref="T:System.Threading.CancellationTokenSource" /> abgewartet wird. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsDelay" /> is less than -1. </exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor(System.TimeSpan)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.CancellationTokenSource" />-Klasse, die nach der angegebenen Zeitspanne abgebrochen wird.</summary>
      <param name="delay">Das Zeitintervall in Millisekunden, das vor dem Abbrechen dieser <see cref="T:System.Threading.CancellationTokenSource" /> abgewartet wird.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" />.<see cref="P:System.TimeSpan.TotalMilliseconds" /> is less than -1 or greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Cancel">
      <summary>Übermittelt eine Abbruchanforderung.</summary>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">An aggregate exception containing all the exceptions thrown by the registered callbacks on the associated <see cref="T:System.Threading.CancellationToken" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Cancel(System.Boolean)">
      <summary>Teilt eine Anforderung für Abbruch mit und gibt an, ob verbleibenden Rückrufe und abbrechbare Vorgänge verarbeitet werden sollen.</summary>
      <param name="throwOnFirstException">true, wenn Ausnahmen sofort weitergegeben werden sollten, andernfalls false.</param>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">An aggregate exception containing all the exceptions thrown by the registered callbacks on the associated <see cref="T:System.Threading.CancellationToken" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CancelAfter(System.Int32)">
      <summary>Plant einen Abbruch auf diesem <see cref="T:System.Threading.CancellationTokenSource" /> nach der angegebenen Anzahl von Millisekunden.</summary>
      <param name="millisecondsDelay">Die Zeitspanne, die gewartet wird, bevor diese <see cref="T:System.Threading.CancellationTokenSource" /> abgebrochen wird.</param>
      <exception cref="T:System.ObjectDisposedException">The exception thrown when this <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception thrown when <paramref name="millisecondsDelay" /> is less than -1.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CancelAfter(System.TimeSpan)">
      <summary>Plant einen Abbruch auf diesem <see cref="T:System.Threading.CancellationTokenSource" /> nach der angegebenen Zeitspanne.</summary>
      <param name="delay">Die Zeitspanne, die gewartet wird, bevor diese <see cref="T:System.Threading.CancellationTokenSource" /> abgebrochen wird.</param>
      <exception cref="T:System.ObjectDisposedException">The exception thrown when this <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when <paramref name="delay" /> is less than -1 or greater than Int32.MaxValue.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CreateLinkedTokenSource(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>Erstellt eine <see cref="T:System.Threading.CancellationTokenSource" />, für die der Zustand "abgebrochen" festgelegt wird, wenn eines der Quelltoken im Zustand "abgebrochen" ist.</summary>
      <returns>Ein <see cref="T:System.Threading.CancellationTokenSource" />, das mit den Quelltoken verknüpft ist.</returns>
      <param name="token1">Das erste Abbruchtoken, das überwacht werden soll.</param>
      <param name="token2">Das zweite Abbruchtoken, das überwacht werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">A <see cref="T:System.Threading.CancellationTokenSource" /> associated with one of the source tokens has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CreateLinkedTokenSource(System.Threading.CancellationToken[])">
      <summary>Erstellt eine <see cref="T:System.Threading.CancellationTokenSource" />, für die der Zustand „abgebrochen“ festgelegt wird, wenn eines der Quelltoken im angegebenen Array im Zustand „abgebrochen“ ist.</summary>
      <returns>Ein <see cref="T:System.Threading.CancellationTokenSource" />, das mit den Quelltoken verknüpft ist.</returns>
      <param name="tokens">Ein Array, das die Abbruchtokeninstanzen enthält, die beobachtet werden sollen.</param>
      <exception cref="T:System.ObjectDisposedException">A <see cref="T:System.Threading.CancellationTokenSource" /> associated with one of the source tokens has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tokens" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tokens" /> is empty.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Dispose">
      <summary>Gibt alle von der aktuellen Instanz der <see cref="T:System.Threading.CancellationTokenSource" />-Klasse verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Dispose(System.Boolean)">
      <summary>Gibt die von der <see cref="T:System.Threading.CancellationTokenSource" />-Klasse verwendeten nicht verwalteten Ressourcen frei und gibt (optional) auch die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben.</param>
    </member>
    <member name="P:System.Threading.CancellationTokenSource.IsCancellationRequested">
      <summary>Ruft einen Wert ab, der angibt, ob für diese <see cref="T:System.Threading.CancellationTokenSource" /> ein Abbruch angefordert wurde.</summary>
      <returns>true, wenn der Abbruch für diese <see cref="T:System.Threading.CancellationTokenSource" /> angefordert wurde, andernfalls false.</returns>
    </member>
    <member name="P:System.Threading.CancellationTokenSource.Token">
      <summary>Ruft den diesem <see cref="T:System.Threading.CancellationToken" /> zugeordneten <see cref="T:System.Threading.CancellationTokenSource" /> ab.</summary>
      <returns>Das dieser <see cref="T:System.Threading.CancellationToken" /> zugeordnete <see cref="T:System.Threading.CancellationTokenSource" />.</returns>
      <exception cref="T:System.ObjectDisposedException">The token source has been disposed.</exception>
    </member>
    <member name="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair">
      <summary>Stellt Taskplaner bereit, die auszuführende Aufgaben koordinieren, während sie sicherstellen, dass gleichzeitige Aufgaben gleichzeitig ausgeführt werden können, exklusive Aufgaben zu keinerlei Zeitpunkt.</summary>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" />-Klasse.</summary>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" />-Klasse, die auf den angegebenen Planer abzielt.</summary>
      <param name="taskScheduler">Der Zielplaner, auf dem dieses Paar ausgeführt werden soll.</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" />-Klasse für den angegebenen Planer mit einer maximalen Parallelitätsebene.</summary>
      <param name="taskScheduler">Der Zielplaner, auf dem dieses Paar ausgeführt werden soll.</param>
      <param name="maxConcurrencyLevel">Die maximale Anzahl von Tasks, die gleichzeitig ausgeführt werden.</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler,System.Int32,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" />-Klasse für den angegebenen Planer mit einer maximalen Parallelitätsebene und einer maximalen Anzahl von geplanten Aufgaben, die als Einheit verarbeitet werden können.</summary>
      <param name="taskScheduler">Der Zielplaner, auf dem dieses Paar ausgeführt werden soll.</param>
      <param name="maxConcurrencyLevel">Die maximale Anzahl von Tasks, die gleichzeitig ausgeführt werden.</param>
      <param name="maxItemsPerTask">Die maximale Anzahl auszuführender Tasks für jeden zugrunde liegenden geplanten Task, der von dem Paar verwendet wird.</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.Complete">
      <summary>Informiert das Planerpaar, dass es keine weiteren Aufgaben annehmen sollte.</summary>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.Completion">
      <summary>Ruft <see cref="T:System.Threading.Tasks.Task" /> ab, der abgeschlossen wird, wenn der Planer die Verarbeitung abgeschlossen hat.</summary>
      <returns>Der asynchrone Vorgang, der abgeschlossen wird, wenn der Planer die Verarbeitung abschließt.</returns>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.ConcurrentScheduler">
      <summary>Ruft <see cref="T:System.Threading.Tasks.TaskScheduler" /> ab, das verwendet werden kann, um Aufgaben für dieses Paar zu planen, die gleichzeitig mit anderen Aufgaben für dieses Paar ausgeführt werden können.</summary>
      <returns>Ein Objekt, das verwendet werden kann, um Aufgaben gleichzeitig zu planen.</returns>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.ExclusiveScheduler">
      <summary>Ruft <see cref="T:System.Threading.Tasks.TaskScheduler" /> ab, das verwendet werden kann, um Aufgaben für dieses Paar zu planen, die in Bezug auf andere Aufgaben für dieses Paar exklusiv ausgeführt werden müssen.</summary>
      <returns>Ein Objekt, das verwendet werden kann, um Aufgaben zu planen, die nicht gleichzeitig mit anderen Aufgaben ausgeführt werden.</returns>
    </member>
    <member name="T:System.Threading.Tasks.Task">
      <summary>Stellt einen asynchronen Vorgang dar.Informationen zum Durchsuchen des .NET Framework-Quellcodes für diesen Typ finden Sie in der Verweisquelle.</summary>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action)">
      <summary>Initialisiert eine neue <see cref="T:System.Threading.Tasks.Task" /> mit der angegebenen Aktion.</summary>
      <param name="action">Der Delegat, der den in der Aufgabe auszuführenden Code darstellt.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.CancellationToken)">
      <summary>Initialisiert eine neue <see cref="T:System.Threading.Tasks.Task" /> mit den angegebenen Werten für Aktion und <see cref="T:System.Threading.CancellationToken" />.</summary>
      <param name="action">Der Delegat, der den in der Aufgabe auszuführenden Code darstellt.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das die neue Aufgabe berücksichtigt.</param>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initialisiert einen neuen <see cref="T:System.Threading.Tasks.Task" /> mit den angegebenen Werten für Aktion und Erstellungsoptionen.</summary>
      <param name="action">Der Delegat, der den in der Aufgabe auszuführenden Code darstellt.</param>
      <param name="cancellationToken">Das <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />, das die neue Aufgabe berücksichtigt.</param>
      <param name="creationOptions">Die <see cref="T:System.Threading.Tasks.TaskCreationOptions" />, die verwendet werden, um das Verhalten der Aufgabe zu ändern.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initialisiert einen neuen <see cref="T:System.Threading.Tasks.Task" /> mit den angegebenen Werten für Aktion und Erstellungsoptionen.</summary>
      <param name="action">Der Delegat, der den in der Aufgabe auszuführenden Code darstellt.</param>
      <param name="creationOptions">Die <see cref="T:System.Threading.Tasks.TaskCreationOptions" />, die verwendet werden, um das Verhalten der Aufgabe zu ändern. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object)">
      <summary>Initialisiert eine neue <see cref="T:System.Threading.Tasks.Task" /> mit den angegebenen Werten für Aktion und Zustand.</summary>
      <param name="action">Der Delegat, der den in der Aufgabe auszuführenden Code darstellt.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Aktion verwendet werden sollen.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Initialisiert einen neuen <see cref="T:System.Threading.Tasks.Task" /> mit den angegebenen Werten für Aktion, Zustand und Optionen.</summary>
      <param name="action">Der Delegat, der den in der Aufgabe auszuführenden Code darstellt.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Aktion verwendet werden sollen.</param>
      <param name="cancellationToken">Das <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />, das die neue Aufgabe berücksichtigt.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initialisiert einen neuen <see cref="T:System.Threading.Tasks.Task" /> mit den angegebenen Werten für Aktion, Zustand und Optionen.</summary>
      <param name="action">Der Delegat, der den in der Aufgabe auszuführenden Code darstellt.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Aktion verwendet werden sollen.</param>
      <param name="cancellationToken">Das <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />, das die neue Aufgabe berücksichtigt.</param>
      <param name="creationOptions">Die <see cref="T:System.Threading.Tasks.TaskCreationOptions" />, die verwendet werden, um das Verhalten der Aufgabe zu ändern.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initialisiert einen neuen <see cref="T:System.Threading.Tasks.Task" /> mit den angegebenen Werten für Aktion, Zustand und Optionen.</summary>
      <param name="action">Der Delegat, der den in der Aufgabe auszuführenden Code darstellt.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Aktion verwendet werden sollen.</param>
      <param name="creationOptions">Die <see cref="T:System.Threading.Tasks.TaskCreationOptions" />, die verwendet werden, um das Verhalten der Aufgabe zu ändern.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.AsyncState">
      <summary>Ruft das beim Erstellen der <see cref="T:System.Threading.Tasks.Task" /> angegebene Zustandsobjekt ab, oder NULL, wenn kein Zustandsobjekt angegeben wurde.</summary>
      <returns>Ein <see cref="T:System.Object" />, das die Zustandsdaten darstellt, die bei der Erstellung an die Aufgabe übergeben wurden.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.CompletedTask">
      <summary>Ruft eine Aufgabe ab, die bereits erfolgreich abgeschlossen wurde. </summary>
      <returns>Die erfolgreich abgeschlossene Aufgabe. </returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.ConfigureAwait(System.Boolean)">
      <summary>Konfiguriert einen Awaiter, der verwendet wird, um diese <see cref="T:System.Threading.Tasks.Task" /> zu erwarten.</summary>
      <returns>Ein Objekt, das verwendet wird, um diese Aufgabe zu erwarten.</returns>
      <param name="continueOnCapturedContext">true um zu versuchen, die Fortsetzung zurück in den ursprünglich erfassten Text zu marshallen, andernfalls false.</param>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task})">
      <summary>Erstellt eine Fortsetzung, die asynchron ausgeführt wird, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task" /> abgeschlossen wurde.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">Eine Aktion, die beim Abschluss von <see cref="T:System.Threading.Tasks.Task" /> ausgeführt werden soll.Bei der Ausführung wird dem Delegaten die abgeschlossene Aufgabe als Argument übergeben.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>Erstellt eine Fortsetzung, die ein Abbruchtoken empfängt und asynchron ausgeführt wird, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task" /> abgeschlossen wurde.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">Eine Aktion, die beim Abschluss von <see cref="T:System.Threading.Tasks.Task" /> ausgeführt werden soll.Bei der Ausführung wird dem Delegaten die abgeschlossene Aufgabe als Argument übergeben.</param>
      <param name="cancellationToken">Das <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzung, die, sobald der Zieltask abgeschlossen ist, entsprechend den Angaben in <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> ausgeführt wird.Die Fortsetzung empfängt ein Abbruchtoken und verwendet einen angegebenen Zeitplan.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">Eine Aktion, die nach den angegebenen <paramref name="continuationOptions" /> ausgeführt werden soll.Bei der Ausführung wird dem Delegaten die abgeschlossene Aufgabe als Argument übergeben.</param>
      <param name="cancellationToken">Das <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <param name="continuationOptions">Optionen für die Planung und das Verhalten der Fortsetzung.Dazu zählen Kriterien wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> und Ausführungsoptionen wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das der Fortsetzungsaufgabe zugeordnet und für ihre Ausführung verwendet werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt eine Fortsetzung, die, sobald der Zieltask abgeschlossen ist, entsprechend den Angaben in <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> ausgeführt wird.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">Eine Aktion, die nach den angegebenen <paramref name="continuationOptions" /> ausgeführt werden soll.Bei der Ausführung wird dem Delegaten die abgeschlossene Aufgabe als Argument übergeben.</param>
      <param name="continuationOptions">Optionen für die Planung und das Verhalten der Fortsetzung.Dazu zählen Kriterien wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> und Ausführungsoptionen wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzung, die asynchron ausgeführt wird, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task" /> abgeschlossen wurde.Die Fortsetzung verwendet einen angegebenen Zeitplan.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">Eine Aktion, die beim Abschluss von <see cref="T:System.Threading.Tasks.Task" /> ausgeführt werden soll.Bei der Ausführung wird dem Delegaten die abgeschlossene Aufgabe als Argument übergeben.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das der Fortsetzungsaufgabe zugeordnet und für ihre Ausführung verwendet werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. -or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object)">
      <summary>Erstellt eine Fortsetzung, die vom Aufrufer bereitgestellte Zustandsinformationen empfängt, und wird ausgeführt, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task" /> abgeschlossen ist. </summary>
      <returns>Eine neue Fortsetzungsaufgabe. </returns>
      <param name="continuationAction">Eine Aktion, die ausgeführt werden soll, wenn der Task abgeschlossen wurde.Wenn der Delegat ausgeführt wird, werden ihm der abgeschlossene Task und das vom Aufrufer bereitgestellte Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsaktion verwendet werden sollen. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Erstellt eine Fortsetzung, die vom Aufrufer bereitgestellte Zustandsinformationen sowie ein Abbruchtoken empfängt und asynchron ausgeführt wird, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task" /> abgeschlossen wurde.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">Eine Aktion, die beim Abschluss von <see cref="T:System.Threading.Tasks.Task" /> ausgeführt werden soll.Bei der Ausführung werden dem Delegaten die abgeschlossene Aufgabe und das vom Aufrufer angegebene Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsaktion verwendet werden sollen.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzung, die vom Aufrufer bereitgestellte Zustandsinformationen sowie ein Abbruchtoken empfängt und ausgeführt wird, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task" /> abgeschlossen wurde.Die Fortsetzung wird entsprechend einem Satz angegebener Bedingungen ausgeführt und verwendet einen angegebenen Zeitplan.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">Eine Aktion, die beim Abschluss von <see cref="T:System.Threading.Tasks.Task" /> ausgeführt werden soll.Bei der Ausführung werden dem Delegaten die abgeschlossene Aufgabe und das vom Aufrufer angegebene Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsaktion verwendet werden sollen.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <param name="continuationOptions">Optionen für die Planung und das Verhalten der Fortsetzung.Dazu zählen Kriterien wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> und Ausführungsoptionen wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das der Fortsetzungsaufgabe zugeordnet und für ihre Ausführung verwendet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt eine Fortsetzung, die vom Aufrufer bereitgestellte Zustandsinformationen empfängt, und wird ausgeführt, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task" /> abgeschlossen ist.Die Fortsetzung wird entsprechend einem Satz angegebener Bedingungen ausgeführt.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">Eine Aktion, die beim Abschluss von <see cref="T:System.Threading.Tasks.Task" /> ausgeführt werden soll.Bei der Ausführung werden dem Delegaten die abgeschlossene Aufgabe und das vom Aufrufer angegebene Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsaktion verwendet werden sollen.</param>
      <param name="continuationOptions">Optionen für die Planung und das Verhalten der Fortsetzung.Dazu zählen Kriterien wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> und Ausführungsoptionen wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzung, die vom Aufrufer bereitgestellte Zustandsinformationen empfängt, und wird asynchron ausgeführt, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task" /> abgeschlossen ist.Die Fortsetzung verwendet einen angegebenen Zeitplan.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">Eine Aktion, die beim Abschluss von <see cref="T:System.Threading.Tasks.Task" /> ausgeführt werden soll.Bei der Ausführung werden dem Delegaten die abgeschlossene Aufgabe und das vom Aufrufer angegebene Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsaktion verwendet werden sollen.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das der Fortsetzungsaufgabe zugeordnet und für ihre Ausführung verwendet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0})">
      <summary>Erstellt eine Fortsetzung, die asynchron ausgeführt wird, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen wurde, und gibt einen Wert zurück. </summary>
      <returns>Eine neue Fortsetzungsaufgabe. </returns>
      <param name="continuationFunction">Eine Funktion, die ausgeführt werden soll, wenn der <see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist.Bei der Ausführung wird dem Delegaten die abgeschlossene Aufgabe als Argument übergeben.</param>
      <typeparam name="TResult"> Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken)">
      <summary>Erstellt eine Fortsetzung, die asynchron ausgeführt wird, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task" /> abgeschlossen wurde, und gibt einen Wert zurück.Die Fortsetzung empfängt ein Abbruchtoken.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die ausgeführt werden soll, wenn das <see cref="T:System.Threading.Tasks.Task" /> abgeschlossen ist.Bei der Ausführung wird dem Delegaten die abgeschlossene Aufgabe als Argument übergeben.</param>
      <param name="cancellationToken">Das <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <typeparam name="TResult"> Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzung, die entsprechend den angegebenen Fortsetzungsoptionen ausgeführt wird, und gibt einen Wert zurück.An die Fortsetzung wird ein Abbruchtoken übergeben, und sie verwendet einen angegebenen Zeitplan.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die gemäß dem angegebenen <paramref name="continuationOptions." />-Parameter ausgeführt wird. Wenn eine Ausführung stattfindet, wird dem Delegaten die ausgeführte Aufgabe als Argument übergeben.</param>
      <param name="cancellationToken">Das <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <param name="continuationOptions">Optionen für die Planung und das Verhalten der Fortsetzung.Dazu zählen Kriterien wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> und Ausführungsoptionen wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das der Fortsetzungsaufgabe zugeordnet und für ihre Ausführung verwendet werden soll.</param>
      <typeparam name="TResult"> Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt eine Fortsetzung, die entsprechend den angegebenen Fortsetzungsoptionen ausgeführt wird, und gibt einen Wert zurück. </summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die entsprechend der in <paramref name="continuationOptions" /> angegebenen Bedingung ausgeführt wird.Bei der Ausführung wird dem Delegaten die abgeschlossene Aufgabe als Argument übergeben.</param>
      <param name="continuationOptions">Optionen für die Planung und das Verhalten der Fortsetzung.Dazu zählen Kriterien wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> und Ausführungsoptionen wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TResult"> Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzung, die asynchron ausgeführt wird, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task" /> abgeschlossen wurde, und gibt einen Wert zurück.Die Fortsetzung verwendet einen angegebenen Zeitplan.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die ausgeführt werden soll, wenn das <see cref="T:System.Threading.Tasks.Task" /> abgeschlossen ist.Bei der Ausführung wird dem Delegaten die abgeschlossene Aufgabe als Argument übergeben.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das der Fortsetzungsaufgabe zugeordnet und für ihre Ausführung verwendet werden soll.</param>
      <typeparam name="TResult"> Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object)">
      <summary>Erstellt eine Fortsetzung, die vom Aufrufer bereitgestellte Zustandsinformationen empfängt, und wird asynchron ausgeführt, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task" /> abgeschlossen ist, und gibt einen Wert zurück. </summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die ausgeführt werden soll, wenn das <see cref="T:System.Threading.Tasks.Task" /> abgeschlossen ist.Bei der Ausführung werden dem Delegaten die abgeschlossene Aufgabe und das vom Aufrufer angegebene Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsfunktion verwendet werden sollen.</param>
      <typeparam name="TResult">Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>Erstellt eine Fortsetzung, die asynchron ausgeführt wird, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task" /> abgeschlossen wurde, und gibt einen Wert zurück.Die Fortsetzung empfängt vom Aufrufer bereitgestellte Zustandsinformationen sowie ein Abbruchtoken.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die ausgeführt werden soll, wenn das <see cref="T:System.Threading.Tasks.Task" /> abgeschlossen ist.Bei der Ausführung werden dem Delegaten die abgeschlossene Aufgabe und das vom Aufrufer angegebene Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsfunktion verwendet werden sollen.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <typeparam name="TResult">Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzung, die entsprechend den angegebenen Taskfortsetzungsoptionen ausgeführt wird, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task" /> abgeschlossen ist, und gibt einen Wert zurück.Die Fortsetzung empfängt vom Aufrufer bereitgestellte Zustandsinformationen sowie ein Abbruchtoken und verwendet den angegebenen Zeitplan.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die ausgeführt werden soll, wenn das <see cref="T:System.Threading.Tasks.Task" /> abgeschlossen ist.Bei der Ausführung werden dem Delegaten die abgeschlossene Aufgabe und das vom Aufrufer angegebene Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsfunktion verwendet werden sollen.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <param name="continuationOptions">Optionen für die Planung und das Verhalten der Fortsetzung.Dazu zählen Kriterien wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> und Ausführungsoptionen wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das der Fortsetzungsaufgabe zugeordnet und für ihre Ausführung verwendet werden soll.</param>
      <typeparam name="TResult">Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt eine Fortsetzung, die entsprechend den angegebenen Taskfortsetzungsoptionen ausgeführt wird, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task" /> abgeschlossen ist.Die Fortsetzung empfängt vom Aufrufer bereitgestellte Zustandsinformationen.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die ausgeführt werden soll, wenn das <see cref="T:System.Threading.Tasks.Task" /> abgeschlossen ist.Bei der Ausführung werden dem Delegaten die abgeschlossene Aufgabe und das vom Aufrufer angegebene Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsfunktion verwendet werden sollen.</param>
      <param name="continuationOptions">Optionen für die Planung und das Verhalten der Fortsetzung.Dazu zählen Kriterien wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> und Ausführungsoptionen wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TResult">Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzung, die asynchron ausgeführt wird, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task" /> abgeschlossen wurde.Die Fortsetzung empfängt vom Aufrufer bereitgestellte Zustandsinformationen und verwendet einen angegebenen Zeitplan.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die ausgeführt werden soll, wenn das <see cref="T:System.Threading.Tasks.Task" /> abgeschlossen ist.Bei der Ausführung werden dem Delegaten die abgeschlossene Aufgabe und das vom Aufrufer angegebene Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsfunktion verwendet werden sollen.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das der Fortsetzungsaufgabe zugeordnet und für ihre Ausführung verwendet werden soll.</param>
      <typeparam name="TResult">Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.CreationOptions">
      <summary>Ruft die zum Erstellen dieser Aufgabe verwendeten <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> ab.</summary>
      <returns>Die zum Erstellen dieser Aufgabe verwendeten <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.CurrentId">
      <summary>Gibt die eindeutige ID der momentan ausgeführten <see cref="T:System.Threading.Tasks.Task" /> zurück.</summary>
      <returns>Eine ganze Zahl, die der gerade ausgeführten Aufgabe vom System zugewiesen wurde.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.Int32)">
      <summary>Erstellt eine Aufgabe, die nach einer Verzögerung abgeschlossen wird. </summary>
      <returns>Eine Aufgabe, die die Verzögerung darstellt. </returns>
      <param name="millisecondsDelay">Die Anzahl der Millisekunden, die gewartet wird, bevor die zurückgegebene Aufgabe abgeschlossen wird, oder -1, um unbegrenzt zu warten. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsDelay" /> argument is less than -1.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.Int32,System.Threading.CancellationToken)">
      <summary>Erstellt eine abzubrechende Aufgabe, die nach einer Verzögerung abgeschlossen wird. </summary>
      <returns>Eine Aufgabe, die die Verzögerung darstellt. </returns>
      <param name="millisecondsDelay">Die Anzahl der Millisekunden, die gewartet wird, bevor die zurückgegebene Aufgabe abgeschlossen wird, oder -1, um unbegrenzt zu warten. </param>
      <param name="cancellationToken">Das Abbruchtoken, das vor dem Abschließen der zurückgegebenen Aufgabe geprüft wird. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsDelay" /> argument is less than -1. </exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled. </exception>
      <exception cref="T:System.ObjectDisposedException">The provided <paramref name="cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.TimeSpan)">
      <summary>Erstellt eine Aufgabe, die nach Ablauf einer festgelegten Zeitspanne abgeschlossen wird. </summary>
      <returns>Eine Aufgabe, die die Verzögerung darstellt. </returns>
      <param name="delay">Die Zeitspanne, die abgewartet werden soll, bevor die zurückgegebene Aufgabe abgeschlossen wird, oder TimeSpan.FromMilliseconds(-1), um unbegrenzt zu warten. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" /> represents a negative time interval other than TimeSpan.FromMillseconds(-1). -or-The <paramref name="delay" /> argument's <see cref="P:System.TimeSpan.TotalMilliseconds" /> property is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Erstellt eine abzubrechende Aufgabe, die nach Ablauf einer festgelegten Zeitspanne abgeschlossen wird. </summary>
      <returns>Eine Aufgabe, die die Verzögerung darstellt. </returns>
      <param name="delay">Die Zeitspanne, die abgewartet werden soll, bevor die zurückgegebene Aufgabe abgeschlossen wird, oder TimeSpan.FromMilliseconds(-1), um unbegrenzt zu warten. </param>
      <param name="cancellationToken">Das Abbruchtoken, das vor dem Abschließen der zurückgegebenen Aufgabe geprüft wird. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" /> represents a negative time interval other than TimeSpan.FromMillseconds(-1). -or-The <paramref name="delay" /> argument's <see cref="P:System.TimeSpan.TotalMilliseconds" /> property is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <paramref name="cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.Exception">
      <summary>Ruft die <see cref="T:System.AggregateException" /> ab, die die vorzeitige Beendigung der <see cref="T:System.Threading.Tasks.Task" /> verursacht hat.Wenn die <see cref="T:System.Threading.Tasks.Task" /> erfolgreich abgeschlossen wurde oder noch keine Ausnahmen ausgelöst wurden, wird null zurückgegeben.</summary>
      <returns>Die <see cref="T:System.AggregateException" />, die die vorzeitige Beendigung der <see cref="T:System.Threading.Tasks.Task" /> verursacht hat.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.Factory">
      <summary>Bietet Zugriff auf Factorymethoden zum Erstellen und Konfigurieren von <see cref="T:System.Threading.Tasks.Task" />- und <see cref="T:System.Threading.Tasks.Task`1" />-Instanzen.</summary>
      <returns>Ein Factoryobjekt, das eine Vielzahl von <see cref="T:System.Threading.Tasks.Task" />- und <see cref="T:System.Threading.Tasks.Task`1" />-Objekten erstellen kann. </returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromCanceled(System.Threading.CancellationToken)">
      <summary>Erstellt eine durch Abbruch abgeschlossene <see cref="T:System.Threading.Tasks.Task" /> mit einem angegebenen Abbruchtoken.</summary>
      <returns>Die abgebrochene Aufgabe. </returns>
      <param name="cancellationToken">Das Abbruchtoken, mit dem die Aufgabe abgeschlossen werden soll. </param>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromCanceled``1(System.Threading.CancellationToken)">
      <summary>Erstellt eine durch Abbruch abgeschlossene <see cref="T:System.Threading.Tasks.Task`1" /> mit einem angegebenen Abbruchtoken.</summary>
      <returns>Die abgebrochene Aufgabe. </returns>
      <param name="cancellationToken">Das Abbruchtoken, mit dem die Aufgabe abgeschlossen werden soll. </param>
      <typeparam name="TResult">Der Typ des von der Aufgabe zurückgegebenen Ergebnisses. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromException``1(System.Exception)">
      <summary>Erstellt eine durch eine angegebene Ausnahme abgeschlossene <see cref="T:System.Threading.Tasks.Task`1" />. </summary>
      <returns>Die fehlgeschlagene Aufgabe. </returns>
      <param name="exception">Die Ausnahme, mit der die Aufgabe abgeschlossen werden soll. </param>
      <typeparam name="TResult">Der Typ des von der Aufgabe zurückgegebenen Ergebnisses. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromException(System.Exception)">
      <summary>Erstellt eine durch eine angegebene Ausnahme abgeschlossene <see cref="T:System.Threading.Tasks.Task" />. </summary>
      <returns>Die fehlgeschlagene Aufgabe. </returns>
      <param name="exception">Die Ausnahme, mit der die Aufgabe abgeschlossen werden soll. </param>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromResult``1(``0)">
      <summary>Erstellt eine <see cref="T:System.Threading.Tasks.Task`1" />, die erfolgreich mit dem angegebenen Ergebnis abgeschlossen wurde.</summary>
      <returns>Die erfolgreich abgeschlossene Aufgabe.</returns>
      <param name="result">Das in der abgeschlossenen Aufgabe zu speichernde Ergebnis. </param>
      <typeparam name="TResult">Der Typ des von der Aufgabe zurückgegebenen Ergebnisses. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.GetAwaiter">
      <summary>Ruft einen Awaiter ab, der verwendet wird, um diese <see cref="T:System.Threading.Tasks.Task" /> zu erwarten.</summary>
      <returns>Eine Awaiter-Instanz.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.Id">
      <summary>Ruft eine eindeutige ID für diese <see cref="T:System.Threading.Tasks.Task" />-Instanz ab.</summary>
      <returns>Eine ganze Zahl, die dieser Aufgabeninstanz vom System zugewiesen wurde. </returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsCanceled">
      <summary>Ruft einen Wert ab, der angibt, ob diese <see cref="T:System.Threading.Tasks.Task" />-Instanz die Ausführung aufgrund eines Abbruchs abgeschlossen hat.</summary>
      <returns>true, wenn die Aufgabe aufgrund eines Abbruchs beendet wurde, andernfalls false.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsCompleted">
      <summary>Ruft einen Wert ab, der angibt, ob diese <see cref="T:System.Threading.Tasks.Task" /> abgeschlossen wurde.</summary>
      <returns>true, wenn die Aufgabe abgeschlossen wurde, andernfalls false.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsFaulted">
      <summary>Ruft einen Wert ab, der angibt, ob die <see cref="T:System.Threading.Tasks.Task" /> aufgrund einer nicht behandelten Ausnahme abgeschlossen wurde.</summary>
      <returns>true, wenn die Aufgabe einen Ausnahmefehler ausgelöst hat, andernfalls false.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Action)">
      <summary>Fügt die angegebene Verarbeitung zur Ausführung im Threadpool der Warteschlange hinzu und gibt ein Task-Handle für diese Aufgabe zurück.</summary>
      <returns>Eine Aufgabe, die die Arbeit darstellt, die sich in der Warteschlange befindet, um im Threadpool ausgeführt zu werden.</returns>
      <param name="action">Die asynchron auszuführende Arbeit.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Action,System.Threading.CancellationToken)">
      <summary>Fügt die angegebene Verarbeitung zur Ausführung im Threadpool der Warteschlange hinzu und gibt ein Task-Handle für diese Aufgabe zurück.</summary>
      <returns>Eine Aufgabe, die die Arbeit darstellt, die sich in der Warteschlange befindet, um im Threadpool ausgeführt zu werden.</returns>
      <param name="action">Die asynchron auszuführende Arbeit.</param>
      <param name="cancellationToken">Ein Abbruchtoken, das verwendet werden soll, um die Arbeit abzubrechen.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{System.Threading.Tasks.Task{``0}})">
      <summary>Fügt die angegebene Verarbeitung zur Ausführung im ThreadPool der Warteschleife hinzu und gibt einen Proxy für die Task(TResult) zurück, die von  <paramref name="function" /> zurückgegeben wird.</summary>
      <returns>Eine Task(TResult), die einen Proxy für die Task(TResult) darstellt, die durch <paramref name="function" /> zurückgegeben wird.</returns>
      <param name="function">Die asynchron auszuführende Arbeit.</param>
      <typeparam name="TResult">Der Typ des von der Proxy-Aufgabe zurückgegebenen Ergebnisses.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
      <summary>Fügt die angegebene Verarbeitung zur Ausführung im ThreadPool der Warteschleife hinzu und gibt einen Proxy für die Task(TResult) zurück, die von  <paramref name="function" /> zurückgegeben wird.</summary>
      <returns>Eine Task(TResult), die einen Proxy für die Task(TResult) darstellt, die durch <paramref name="function" /> zurückgegeben wird.</returns>
      <param name="function">Die asynchron auszuführende Arbeit.</param>
      <param name="cancellationToken">Ein Abbruchtoken, das verwendet werden soll, um die Arbeit abzubrechen.</param>
      <typeparam name="TResult">Der Typ des von der Proxy-Aufgabe zurückgegebenen Ergebnisses.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Func{System.Threading.Tasks.Task})">
      <summary>Fügt die angegebene Verarbeitung zur Ausführung im ThreadPool der Warteschleife hinzu und gibt einen Proxy für die Aufgabe zurück, die von <paramref name="function" /> zurückgegeben wird.</summary>
      <returns>Eine Aufgabe, die einen Proxy für die Aufgabe darstellt, die durch <paramref name="function" /> zurückgegeben wird.</returns>
      <param name="function">Die asynchron auszuführende Arbeit.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Func{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>Fügt die angegebene Verarbeitung zur Ausführung im ThreadPool der Warteschleife hinzu und gibt einen Proxy für die Aufgabe zurück, die von <paramref name="function" /> zurückgegeben wird.</summary>
      <returns>Eine Aufgabe, die einen Proxy für die Aufgabe darstellt, die durch <paramref name="function" /> zurückgegeben wird.</returns>
      <param name="function">Die asynchron auszuführende Arbeit. </param>
      <param name="cancellationToken">Ein Abbruchtoken, das verwendet werden soll, um die Arbeit abzubrechen. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{``0})">
      <summary>Fügt die angegebene Verarbeitung zur Ausführung im Threadpool der Warteschlange hinzu und gibt ein <see cref="T:System.Threading.Tasks.Task`1" />-Objekt zurück, das diese Aufgabe darstellt. </summary>
      <returns>Ein Aufgabenobjekt, das die Arbeit darstellt, die sich in der Warteschlange befindet, um im Threadpool ausgeführt zu werden. </returns>
      <param name="function">Die asynchron auszuführende Arbeit. </param>
      <typeparam name="TResult">Der Rückgabetyp der Aufgabe. </typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{``0},System.Threading.CancellationToken)">
      <summary>Fügt die angegebene Verarbeitung zur Ausführung im Threadpool der Warteschlange hinzu und gibt ein Task(TResult)-Handle für diese Aufgabe zurück.</summary>
      <returns>Eine Task(TResult) die die Arbeit darstellt, die sich in der Warteschlange befindet, um im Threadpool ausgeführt zu werden.</returns>
      <param name="function">Die asynchron auszuführende Arbeit.</param>
      <param name="cancellationToken">Ein Abbruchtoken, das verwendet werden soll, um die Arbeit abzubrechen.</param>
      <typeparam name="TResult">Der Ergebnistyp der Aufgabe.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.RunSynchronously">
      <summary>Führt den <see cref="T:System.Threading.Tasks.Task" /> synchron mit dem aktuellen <see cref="T:System.Threading.Tasks.TaskScheduler" /> aus.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.RunSynchronously(System.Threading.Tasks.TaskScheduler)">
      <summary>Führt die <see cref="T:System.Threading.Tasks.Task" /> synchron mit dem bereitgestellten <see cref="T:System.Threading.Tasks.TaskScheduler" /> aus.</summary>
      <param name="scheduler">Der Taskplaner, mit dem diese Aufgabe inline ausgeführt werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Start">
      <summary>Startet die <see cref="T:System.Threading.Tasks.Task" /> und plant ihre Ausführung mit dem aktuellen <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Start(System.Threading.Tasks.TaskScheduler)">
      <summary>Startet die <see cref="T:System.Threading.Tasks.Task" /> und plant ihre Ausführung mit dem angegebenen <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
      <param name="scheduler">Der <see cref="T:System.Threading.Tasks.TaskScheduler" />, dem diese Aufgabe zugeordnet und mit dem sie ausgeführt werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.Status">
      <summary>Ruft den <see cref="T:System.Threading.Tasks.TaskStatus" /> dieser Aufgabe ab.</summary>
      <returns>Der aktuelle <see cref="T:System.Threading.Tasks.TaskStatus" /> dieser Aufgabeninstanz.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.System#IAsyncResult#AsyncWaitHandle">
      <summary>Ruft ein <see cref="T:System.Threading.WaitHandle" /> ab, das verwendet werden kann, um auf den Abschluss der Aufgabe zu warten.</summary>
      <returns>Ein <see cref="T:System.Threading.WaitHandle" />, das verwendet werden kann, um auf den Abschluss der Aufgabe zu warten.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.System#IAsyncResult#CompletedSynchronously">
      <summary>Ruft einen Wert ab, der angibt, ob der Vorgang synchron abgeschlossen wurde.</summary>
      <returns>true, wenn der Vorgang synchron abgeschlossen wurde, andernfalls false.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait">
      <summary>Wartet, bis der <see cref="T:System.Threading.Tasks.Task" /> die Ausführung abschließt.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Int32)">
      <summary>Wartet darauf, dass die <see cref="T:System.Threading.Tasks.Task" /> innerhalb einer angegebenen Anzahl an Millisekunden vollständig ausgeführt wird.</summary>
      <returns>true, wenn der <see cref="T:System.Threading.Tasks.Task" /> die Ausführung innerhalb der zugewiesenen Zeit abgeschlossen hat, andernfalls false.</returns>
      <param name="millisecondsTimeout">Die Anzahl von Millisekunden, die gewartet wird, oder <see cref="F:System.Threading.Timeout.Infinite" /> (-1) für Warten ohne Timeout.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Int32,System.Threading.CancellationToken)">
      <summary>Wartet, bis der <see cref="T:System.Threading.Tasks.Task" /> die Ausführung abschließt.Der Wartevorgang wird beendet, wenn ein Timeoutintervall abläuft oder ein Abbruchtoken abgebrochen wird, bevor die Aufgabe abgeschlossen ist.</summary>
      <returns>true, wenn der <see cref="T:System.Threading.Tasks.Task" /> die Ausführung innerhalb der zugewiesenen Zeit abgeschlossen hat, andernfalls false.</returns>
      <param name="millisecondsTimeout">Die Anzahl von Millisekunden, die gewartet wird, oder <see cref="F:System.Threading.Timeout.Infinite" /> (-1) für Warten ohne Timeout. </param>
      <param name="cancellationToken">Ein Abbruchtoken, das beim Warten auf den Abschluss der Aufgabe überwacht werden soll. </param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Threading.CancellationToken)">
      <summary>Wartet, bis der <see cref="T:System.Threading.Tasks.Task" /> die Ausführung abschließt.Der Wartevorgang wird beendet, wenn ein Abbruchtoken abgebrochen wird, bevor die Aufgabe abgeschlossen ist.</summary>
      <param name="cancellationToken">Ein Abbruchtoken, das beim Warten auf den Abschluss der Aufgabe überwacht werden soll. </param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The task has been disposed.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.TimeSpan)">
      <summary>Wartet darauf, dass die <see cref="T:System.Threading.Tasks.Task" /> innerhalb eines angegebenen Zeitintervalls vollständig ausgeführt wird.</summary>
      <returns>true, wenn der <see cref="T:System.Threading.Tasks.Task" /> die Ausführung innerhalb der zugewiesenen Zeit abgeschlossen hat, andernfalls false.</returns>
      <param name="timeout">Eine <see cref="T:System.TimeSpan" />-Struktur, die die Anzahl der zu wartenden Millisekunden angibt, oder eine <see cref="T:System.TimeSpan" />-Struktur, die -1 Millisekunden zum unendlichen Warten angibt.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[])">
      <summary>Wartet, bis alle bereitgestellten <see cref="T:System.Threading.Tasks.Task" />-Objekte die Ausführung abschließen.</summary>
      <param name="tasks">Ein Array von <see cref="T:System.Threading.Tasks.Task" />-Instanzen, auf die gewartet werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.-or-The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> exception contains an <see cref="T:System.OperationCanceledException" /> exception in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Int32)">
      <summary>Wartet darauf, dass alle bereitgestellten <see cref="T:System.Threading.Tasks.Task" />-Objekte innerhalb einer angegebenen Anzahl an Millisekunden vollständig ausgeführt werden.</summary>
      <returns>true wenn alle <see cref="T:System.Threading.Tasks.Task" />-Instanzen die Ausführung innerhalb der zugewiesenen Zeit abgeschlossen haben, andernfalls false.</returns>
      <param name="tasks">Ein Array von <see cref="T:System.Threading.Tasks.Task" />-Instanzen, auf die gewartet werden soll.</param>
      <param name="millisecondsTimeout">Die Anzahl von Millisekunden, die gewartet wird, oder <see cref="F:System.Threading.Timeout.Infinite" /> (-1) für Warten ohne Timeout.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Int32,System.Threading.CancellationToken)">
      <summary>Wartet darauf, dass alle bereitgestellten <see cref="T:System.Threading.Tasks.Task" />-Objekte innerhalb einer angegebenen Anzahl an Millisekunden oder vollständig ausgeführt werden, oder bis zum Abbruch des Wartevorgangs.</summary>
      <returns>true wenn alle <see cref="T:System.Threading.Tasks.Task" />-Instanzen die Ausführung innerhalb der zugewiesenen Zeit abgeschlossen haben, andernfalls false.</returns>
      <param name="tasks">Ein Array von <see cref="T:System.Threading.Tasks.Task" />-Instanzen, auf die gewartet werden soll.</param>
      <param name="millisecondsTimeout">Die Anzahl von Millisekunden, die gewartet wird, oder <see cref="F:System.Threading.Timeout.Infinite" /> (-1) für Warten ohne Timeout.</param>
      <param name="cancellationToken">Ein <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />, das beim Warten auf den Abschluss der Aufgaben überwacht werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Threading.CancellationToken)">
      <summary>Wartet, bis alle bereitgestellten <see cref="T:System.Threading.Tasks.Task" />-Objekte die Ausführung abschließen oder bis der Wartevorgang abgebrochen wird. </summary>
      <param name="tasks">Ein Array von <see cref="T:System.Threading.Tasks.Task" />-Instanzen, auf die gewartet werden soll.</param>
      <param name="cancellationToken">Ein <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />, das beim Warten auf den Abschluss der Aufgaben überwacht werden soll.</param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.TimeSpan)">
      <summary>Wartet darauf, dass alle bereitgestellten <see cref="T:System.Threading.Tasks.Task" />-Objekte, die abgebrochen werden können, innerhalb eines angegebenen Zeitintervalls vollständig ausgeführt werden.</summary>
      <returns>true wenn alle <see cref="T:System.Threading.Tasks.Task" />-Instanzen die Ausführung innerhalb der zugewiesenen Zeit abgeschlossen haben, andernfalls false.</returns>
      <param name="tasks">Ein Array von <see cref="T:System.Threading.Tasks.Task" />-Instanzen, auf die gewartet werden soll.</param>
      <param name="timeout">Eine <see cref="T:System.TimeSpan" />-Struktur, die die Anzahl der zu wartenden Millisekunden angibt, oder eine <see cref="T:System.TimeSpan" />-Struktur, die -1 Millisekunden zum unendlichen Warten angibt.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null. </exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[])">
      <summary>Wartet, bis eines der bereitgestellten <see cref="T:System.Threading.Tasks.Task" />-Objekte die Ausführung abschließt.</summary>
      <returns>Der Index der ausgeführten Aufgabe im <paramref name="tasks" />-Arrayargument.</returns>
      <param name="tasks">Ein Array von <see cref="T:System.Threading.Tasks.Task" />-Instanzen, auf die gewartet werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Int32)">
      <summary>Wartet darauf, dass bereitgestellte <see cref="T:System.Threading.Tasks.Task" />-Objekte innerhalb einer angegebenen Anzahl an Millisekunden vollständig ausgeführt werden.</summary>
      <returns>Der Index der abgeschlossenen Aufgabe im <paramref name="tasks" />-Arrayargument oder -1, wenn das Timeout aufgetreten ist.</returns>
      <param name="tasks">Ein Array von <see cref="T:System.Threading.Tasks.Task" />-Instanzen, auf die gewartet werden soll.</param>
      <param name="millisecondsTimeout">Die Anzahl von Millisekunden, die gewartet wird, oder <see cref="F:System.Threading.Timeout.Infinite" /> (-1) für Warten ohne Timeout.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Int32,System.Threading.CancellationToken)">
      <summary>Wartet darauf, dass bereitgestellte <see cref="T:System.Threading.Tasks.Task" />-Objekte innerhalb einer angegebenen Anzahl an Millisekunden oder vollständig ausgeführt werden, oder bis ein Abbruchtoken abgebrochen wird.</summary>
      <returns>Der Index der abgeschlossenen Aufgabe im <paramref name="tasks" />-Arrayargument oder -1, wenn das Timeout aufgetreten ist.</returns>
      <param name="tasks">Ein Array von <see cref="T:System.Threading.Tasks.Task" />-Instanzen, auf die gewartet werden soll. </param>
      <param name="millisecondsTimeout">Die Anzahl von Millisekunden, die gewartet wird, oder <see cref="F:System.Threading.Timeout.Infinite" /> (-1) für Warten ohne Timeout. </param>
      <param name="cancellationToken">Ein <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />, das beim Warten auf den Abschluss einer Aufgabe überwacht werden soll. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Threading.CancellationToken)">
      <summary>Wartet, bis bereitgestellte <see cref="T:System.Threading.Tasks.Task" />-Objekte die Ausführung abschließen oder bis der Wartevorgang abgebrochen wird.</summary>
      <returns>Der Index der ausgeführten Aufgabe im <paramref name="tasks" />-Arrayargument.</returns>
      <param name="tasks">Ein Array von <see cref="T:System.Threading.Tasks.Task" />-Instanzen, auf die gewartet werden soll. </param>
      <param name="cancellationToken">Ein <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />, das beim Warten auf den Abschluss einer Aufgabe überwacht werden soll. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.TimeSpan)">
      <summary>Wartet darauf, dass bereitgestellte <see cref="T:System.Threading.Tasks.Task" />-Objekte, die abgebrochen werden können, innerhalb eines angegebenen Zeitintervalls vollständig ausgeführt werden.</summary>
      <returns>Der Index der abgeschlossenen Aufgabe im <paramref name="tasks" />-Arrayargument oder -1, wenn das Timeout aufgetreten ist.</returns>
      <param name="tasks">Ein Array von <see cref="T:System.Threading.Tasks.Task" />-Instanzen, auf die gewartet werden soll.</param>
      <param name="timeout">Eine <see cref="T:System.TimeSpan" />-Struktur, die die Anzahl der zu wartenden Millisekunden angibt, oder eine <see cref="T:System.TimeSpan" />-Struktur, die -1 Millisekunden zum unendlichen Warten angibt.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
      <summary>Erstellt eine Aufgabe, die abgeschlossen wird, wenn alle <see cref="T:System.Threading.Tasks.Task`1" />-Objekte in einer aufzählbaren Auflistung abgeschlossen sind. </summary>
      <returns>Eine Aufgabe, die den Abschluss aller angegebenen Aufgaben darstellt. </returns>
      <param name="tasks">Die Aufgaben, auf deren Abschluss gewartet werden soll. </param>
      <typeparam name="TResult">Der Typ der abgeschlossenen Aufgabe. </typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> collection contained a null task. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
      <summary>Erstellt eine Aufgabe, die abgeschlossen wird, wenn alle <see cref="T:System.Threading.Tasks.Task" />-Objekte in einer aufzählbaren Auflistung abgeschlossen sind.</summary>
      <returns>Eine Aufgabe, die den Abschluss aller angegebenen Aufgaben darstellt. </returns>
      <param name="tasks">Die Aufgaben, auf deren Abschluss gewartet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> collection contained a null task.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll(System.Threading.Tasks.Task[])">
      <summary>Erstellt eine Aufgabe, die abgeschlossen wird, wenn alle <see cref="T:System.Threading.Tasks.Task" />-Objekte in einem Array abgeschlossen sind. </summary>
      <returns>Eine Aufgabe, die den Abschluss aller angegebenen Aufgaben darstellt.</returns>
      <param name="tasks">Die Aufgaben, auf deren Abschluss gewartet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll``1(System.Threading.Tasks.Task{``0}[])">
      <summary>Erstellt eine Aufgabe, die abgeschlossen wird, wenn alle <see cref="T:System.Threading.Tasks.Task`1" />-Objekte in einem Array abgeschlossen sind. </summary>
      <returns>Eine Aufgabe, die den Abschluss aller angegebenen Aufgaben darstellt.</returns>
      <param name="tasks">Die Aufgaben, auf deren Abschluss gewartet werden soll.</param>
      <typeparam name="TResult">Der Typ der abgeschlossenen Aufgabe.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
      <summary>Erstellt eine Aufgabe, die abgeschlossen wird, wenn eine der angegebenen Aufgaben abgeschlossen ist.</summary>
      <returns>Eine Aufgabe, die den Abschluss einer der angegebenen Aufgaben darstellt.Das zurückgegebene Ergebnis der Aufgabe ist die Aufgabe, die abgeschlossen wurde.</returns>
      <param name="tasks">Die Aufgaben, auf deren Abschluss gewartet werden soll.</param>
      <typeparam name="TResult">Der Typ der abgeschlossenen Aufgabe.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
      <summary>Erstellt eine Aufgabe, die abgeschlossen wird, wenn eine der angegebenen Aufgaben abgeschlossen ist.</summary>
      <returns>Eine Aufgabe, die den Abschluss einer der angegebenen Aufgaben darstellt.Das zurückgegebene Ergebnis der Aufgabe ist die Aufgabe, die abgeschlossen wurde.</returns>
      <param name="tasks">Die Aufgaben, auf deren Abschluss gewartet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny(System.Threading.Tasks.Task[])">
      <summary>Erstellt eine Aufgabe, die abgeschlossen wird, wenn eine der angegebenen Aufgaben abgeschlossen ist.</summary>
      <returns>Eine Aufgabe, die den Abschluss einer der angegebenen Aufgaben darstellt.Das zurückgegebene Ergebnis der Aufgabe ist die Aufgabe, die abgeschlossen wurde.</returns>
      <param name="tasks">Die Aufgaben, auf deren Abschluss gewartet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny``1(System.Threading.Tasks.Task{``0}[])">
      <summary>Erstellt eine Aufgabe, die abgeschlossen wird, wenn eine der angegebenen Aufgaben abgeschlossen ist.</summary>
      <returns>Eine Aufgabe, die den Abschluss einer der angegebenen Aufgaben darstellt.Das zurückgegebene Ergebnis der Aufgabe ist die Aufgabe, die abgeschlossen wurde.</returns>
      <param name="tasks">Die Aufgaben, auf deren Abschluss gewartet werden soll.</param>
      <typeparam name="TResult">Der Typ der abgeschlossenen Aufgabe.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Yield">
      <summary>Erstellt eine Awaitable-Aufgabe, die asynchron an den aktuellen Kontext liefert, wenn erwartet.</summary>
      <returns>Ein Kontext, der während des Abwartens asynchron wieder in den aktuellen Kontext zum Zeitpunkt des Abwartens übergeht.Wenn der aktuelle <see cref="T:System.Threading.SynchronizationContext" /> nicht Null ist, wird er als der aktuelle Kontext behandelt.Andernfalls wird der Taskplaner, der der aktuell ausgeführten Aufgabe zugeordnet ist, als der aktuelle Kontext behandelt.</returns>
    </member>
    <member name="T:System.Threading.Tasks.Task`1">
      <summary>Stellt einen asynchronen Vorgang dar, der einen Wert zurückgeben kann.</summary>
      <typeparam name="TResult">Der von diesem <see cref="T:System.Threading.Tasks.Task`1" /> erzeugte Ergebnistyp. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0})">
      <summary>Initialisiert einen neuen <see cref="T:System.Threading.Tasks.Task`1" /> mit der angegebenen Funktion.</summary>
      <param name="function">Der Delegat, der den in der Aufgabe auszuführenden Code darstellt.Nachdem die Funktion abgeschlossen wurde, wird die <see cref="P:System.Threading.Tasks.Task`1.Result" />-Eigenschaft der Aufgabe festgelegt, um den Ergebniswert der Funktion zurückzugeben.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.CancellationToken)">
      <summary>Initialisiert einen neuen <see cref="T:System.Threading.Tasks.Task`1" /> mit der angegebenen Funktion.</summary>
      <param name="function">Der Delegat, der den in der Aufgabe auszuführenden Code darstellt.Nachdem die Funktion abgeschlossen wurde, wird die <see cref="P:System.Threading.Tasks.Task`1.Result" />-Eigenschaft der Aufgabe festgelegt, um den Ergebniswert der Funktion zurückzugeben.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das dieser Aufgabe zugewiesen werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initialisiert einen neuen <see cref="T:System.Threading.Tasks.Task`1" /> mit den angegebenen Werten für Funktion und Erstellungsoptionen.</summary>
      <param name="function">Der Delegat, der den in der Aufgabe auszuführenden Code darstellt.Nachdem die Funktion abgeschlossen wurde, wird die <see cref="P:System.Threading.Tasks.Task`1.Result" />-Eigenschaft der Aufgabe festgelegt, um den Ergebniswert der Funktion zurückzugeben.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Aufgabe zugewiesen wird.</param>
      <param name="creationOptions">Die <see cref="T:System.Threading.Tasks.TaskCreationOptions" />, die verwendet werden, um das Verhalten der Aufgabe zu ändern.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initialisiert einen neuen <see cref="T:System.Threading.Tasks.Task`1" /> mit den angegebenen Werten für Funktion und Erstellungsoptionen.</summary>
      <param name="function">Der Delegat, der den in der Aufgabe auszuführenden Code darstellt.Nachdem die Funktion abgeschlossen wurde, wird die <see cref="P:System.Threading.Tasks.Task`1.Result" />-Eigenschaft der Aufgabe festgelegt, um den Ergebniswert der Funktion zurückzugeben.</param>
      <param name="creationOptions">Die <see cref="T:System.Threading.Tasks.TaskCreationOptions" />, die verwendet werden, um das Verhalten der Aufgabe zu ändern.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object)">
      <summary>Initialisiert einen neuen <see cref="T:System.Threading.Tasks.Task`1" /> mit den angegebenen Werten für Funktion und Zustand.</summary>
      <param name="function">Der Delegat, der den in der Aufgabe auszuführenden Code darstellt.Nachdem die Funktion abgeschlossen wurde, wird die <see cref="P:System.Threading.Tasks.Task`1.Result" />-Eigenschaft der Aufgabe festgelegt, um den Ergebniswert der Funktion zurückzugeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Aktion verwendet werden sollen.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken)">
      <summary>Initialisiert einen neuen <see cref="T:System.Threading.Tasks.Task`1" /> mit den angegebenen Werten für Aktion, Zustand und Optionen.</summary>
      <param name="function">Der Delegat, der den in der Aufgabe auszuführenden Code darstellt.Nachdem die Funktion abgeschlossen wurde, wird die <see cref="P:System.Threading.Tasks.Task`1.Result" />-Eigenschaft der Aufgabe festgelegt, um den Ergebniswert der Funktion zurückzugeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Funktion verwendet werden sollen.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das dieser neuen Aufgabe zugewiesen werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initialisiert einen neuen <see cref="T:System.Threading.Tasks.Task`1" /> mit den angegebenen Werten für Aktion, Zustand und Optionen.</summary>
      <param name="function">Der Delegat, der den in der Aufgabe auszuführenden Code darstellt.Nachdem die Funktion abgeschlossen wurde, wird die <see cref="P:System.Threading.Tasks.Task`1.Result" />-Eigenschaft der Aufgabe festgelegt, um den Ergebniswert der Funktion zurückzugeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Funktion verwendet werden sollen.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das dieser neuen Aufgabe zugewiesen werden soll.</param>
      <param name="creationOptions">Die <see cref="T:System.Threading.Tasks.TaskCreationOptions" />, die verwendet werden, um das Verhalten der Aufgabe zu ändern.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Initialisiert einen neuen <see cref="T:System.Threading.Tasks.Task`1" /> mit den angegebenen Werten für Aktion, Zustand und Optionen.</summary>
      <param name="function">Der Delegat, der den in der Aufgabe auszuführenden Code darstellt.Nachdem die Funktion abgeschlossen wurde, wird die <see cref="P:System.Threading.Tasks.Task`1.Result" />-Eigenschaft der Aufgabe festgelegt, um den Ergebniswert der Funktion zurückzugeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Funktion verwendet werden sollen.</param>
      <param name="creationOptions">Die <see cref="T:System.Threading.Tasks.TaskCreationOptions" />, die verwendet werden, um das Verhalten der Aufgabe zu ändern.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ConfigureAwait(System.Boolean)">
      <summary>Konfiguriert einen Awaiter, der verwendet wird, um diese <see cref="T:System.Threading.Tasks.Task`1" /> zu erwarten.</summary>
      <returns>Ein Objekt, das verwendet wird, um diese Aufgabe zu erwarten.</returns>
      <param name="continueOnCapturedContext">"True", um zu versuchen, die Fortsetzung zurück in den ursprünglich erfassten Text zu marshallen; andernfalls "False".</param>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}})">
      <summary>Erstellt eine Fortsetzung, die asynchron ausgeführt wird, wenn die Zielaufgabe abgeschlossen wurde. </summary>
      <returns>Eine neue Fortsetzungsaufgabe. </returns>
      <param name="continuationAction">Eine Aktion, die beim Abschluss vom vorhergehenden <see cref="T:System.Threading.Tasks.Task`1" /> ausgeführt werden soll.Bei der Ausführung wird dem Delegaten die abgeschlossene Aufgabe als Argument übergeben.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.CancellationToken)">
      <summary>Erstellt eine abzubrechende Fortsetzung, die asynchron ausgeführt wird, wenn die Ziel-<see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen wurde.</summary>
      <returns>Eine neue Fortsetzungsaufgabe. </returns>
      <param name="continuationAction">Eine Aktion, die beim Abschluss von <see cref="T:System.Threading.Tasks.Task`1" /> ausgeführt werden soll.Bei der Ausführung wird dem Delegaten die abgeschlossene Aufgabe als Argument übergeben.</param>
      <param name="cancellationToken">Das Abbruchtoken, das an die neue Fortsetzungsaufgabe übergeben werden soll. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzung, die nach der in <paramref name="continuationOptions" /> angegebenen Bedingung ausgeführt wird.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">Eine Aktion, die entsprechend der in <paramref name="continuationOptions" /> angegebenen Bedingung ausgeführt wird.Bei der Ausführung wird dem Delegaten die abgeschlossene Aufgabe als Argument übergeben.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <param name="continuationOptions">Optionen für die Planung und das Verhalten der Fortsetzung.Dazu zählen Kriterien wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> und Ausführungsoptionen wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das der Fortsetzungsaufgabe zugeordnet und für ihre Ausführung verwendet werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt eine Fortsetzung, die nach der in <paramref name="continuationOptions" /> angegebenen Bedingung ausgeführt wird.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">Eine Aktion, die entsprechend der in <paramref name="continuationOptions" /> angegebenen Bedingung ausgeführt wird.Bei der Ausführung wird dem Delegaten die abgeschlossene Aufgabe als Argument übergeben.</param>
      <param name="continuationOptions">Optionen für die Planung und das Verhalten der Fortsetzung.Dazu zählen Kriterien wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> und Ausführungsoptionen wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzung, die asynchron ausgeführt wird, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen wurde.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">Eine Aktion, die beim Abschluss von <see cref="T:System.Threading.Tasks.Task`1" /> ausgeführt werden soll.Bei der Ausführung wird dem Delegaten die abgeschlossene Aufgabe als Argument übergeben.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das der Fortsetzungsaufgabe zugeordnet und für ihre Ausführung verwendet werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object)">
      <summary>Erstellt eine Fortsetzung, an die Zustandsinformationen übergeben werden und die ausgeführt wird, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist. </summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">Eine Aktion, die beim Abschluss von <see cref="T:System.Threading.Tasks.Task`1" /> ausgeführt werden soll.Wenn der Delegat ausgeführt wird, werden ihm der abgeschlossene Task und das vom Aufrufer bereitgestellte Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsaktion verwendet werden sollen. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Erstellt eine Fortsetzung, die ausgeführt wird, wenn die Ziel-<see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">Eine Aktion, die beim Abschluss von <see cref="T:System.Threading.Tasks.Task`1" /> ausgeführt werden soll.Bei der Ausführung werden dem Delegaten die abgeschlossene Aufgabe und das vom Aufrufer angegebene Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsaktion verwendet werden sollen.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzung, die ausgeführt wird, wenn die Ziel-<see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">Eine Aktion, die beim Abschluss von <see cref="T:System.Threading.Tasks.Task`1" /> ausgeführt werden soll.Bei der Ausführung werden dem Delegaten die abgeschlossene Aufgabe und das vom Aufrufer angegebene Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsaktion verwendet werden sollen.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <param name="continuationOptions">Optionen für die Planung und das Verhalten der Fortsetzung.Dazu zählen Kriterien wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> und Ausführungsoptionen wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das der Fortsetzungsaufgabe zugeordnet und für ihre Ausführung verwendet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt eine Fortsetzung, die ausgeführt wird, wenn die Ziel-<see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">Eine Aktion, die beim Abschluss von <see cref="T:System.Threading.Tasks.Task`1" /> ausgeführt werden soll.Bei der Ausführung werden dem Delegaten die abgeschlossene Aufgabe und das vom Aufrufer angegebene Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsaktion verwendet werden sollen.</param>
      <param name="continuationOptions">Optionen für die Planung und das Verhalten der Fortsetzung.Dazu zählen Kriterien wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> und Ausführungsoptionen wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzung, die ausgeführt wird, wenn die Ziel-<see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="continuationAction">Eine Aktion, die beim Abschluss von <see cref="T:System.Threading.Tasks.Task`1" /> ausgeführt werden soll.Bei der Ausführung werden dem Delegaten die abgeschlossene Aufgabe und das vom Aufrufer angegebene Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsaktion verwendet werden sollen.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das der Fortsetzungsaufgabe zugeordnet und für ihre Ausführung verwendet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0})">
      <summary>Erstellt eine Fortsetzung, die asynchron ausgeführt wird, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen wurde.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die ausgeführt werden soll, wenn das <see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist.Bei der Ausführung wird dem Delegaten die abgeschlossene Aufgabe als Argument übergeben.</param>
      <typeparam name="TNewResult"> Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.CancellationToken)">
      <summary>Erstellt eine Fortsetzung, die asynchron ausgeführt wird, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen wurde.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die ausgeführt werden soll, wenn das <see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist.Bei der Ausführung wird dem Delegaten die abgeschlossene Aufgabe als Argument übergeben.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Aufgabe zugewiesen wird.</param>
      <typeparam name="TNewResult"> Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzung, die nach der in <paramref name="continuationOptions" /> angegebenen Bedingung ausgeführt wird.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die entsprechend der in <paramref name="continuationOptions" /> angegebenen Bedingung ausgeführt wird.Bei der Ausführung wird dem Delegaten diese abgeschlossene Aufgabe als Argument übergeben.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Aufgabe zugewiesen wird.</param>
      <param name="continuationOptions">Optionen für die Planung und das Verhalten der Fortsetzung.Dazu zählen Kriterien wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> und Ausführungsoptionen wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das der Fortsetzungsaufgabe zugeordnet und für ihre Ausführung verwendet werden soll.</param>
      <typeparam name="TNewResult"> Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt eine Fortsetzung, die nach der in <paramref name="continuationOptions" /> angegebenen Bedingung ausgeführt wird.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die entsprechend der in <paramref name="continuationOptions" /> angegebenen Bedingung ausgeführt wird.Bei der Ausführung wird dem Delegaten die abgeschlossene Aufgabe als Argument übergeben.</param>
      <param name="continuationOptions">Optionen für die Planung und das Verhalten der Fortsetzung.Dazu zählen Kriterien wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> und Ausführungsoptionen wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TNewResult"> Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzung, die asynchron ausgeführt wird, wenn der Ziel-<see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen wurde.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die ausgeführt werden soll, wenn das <see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist.Bei der Ausführung wird dem Delegaten die abgeschlossene Aufgabe als Argument übergeben.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das der Fortsetzungsaufgabe zugeordnet und für ihre Ausführung verwendet werden soll.</param>
      <typeparam name="TNewResult"> Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object)">
      <summary>Erstellt eine Fortsetzung, die ausgeführt wird, wenn die Ziel-<see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die ausgeführt werden soll, wenn das <see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist.Bei der Ausführung werden dem Delegaten die abgeschlossene Aufgabe und das vom Aufrufer angegebene Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsfunktion verwendet werden sollen.</param>
      <typeparam name="TNewResult">Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>Erstellt eine Fortsetzung, die ausgeführt wird, wenn die Ziel-<see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die ausgeführt werden soll, wenn das <see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist.Bei der Ausführung werden dem Delegaten die abgeschlossene Aufgabe und das vom Aufrufer angegebene Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsfunktion verwendet werden sollen.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Aufgabe zugewiesen wird.</param>
      <typeparam name="TNewResult">Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzung, die ausgeführt wird, wenn die Ziel-<see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die ausgeführt werden soll, wenn das <see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist.Bei der Ausführung werden dem Delegaten die abgeschlossene Aufgabe und das vom Aufrufer angegebene Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsfunktion verwendet werden sollen.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Aufgabe zugewiesen wird.</param>
      <param name="continuationOptions">Optionen für die Planung und das Verhalten der Fortsetzung.Dazu zählen Kriterien wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> und Ausführungsoptionen wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das der Fortsetzungsaufgabe zugeordnet und für ihre Ausführung verwendet werden soll.</param>
      <typeparam name="TNewResult">Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The  <paramref name="continuationOptions" />  argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt eine Fortsetzung, die ausgeführt wird, wenn die Ziel-<see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die ausgeführt werden soll, wenn das <see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist.Bei der Ausführung werden dem Delegaten die abgeschlossene Aufgabe und das vom Aufrufer angegebene Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsfunktion verwendet werden sollen.</param>
      <param name="continuationOptions">Optionen für die Planung und das Verhalten der Fortsetzung.Dazu zählen Kriterien wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" /> und Ausführungsoptionen wie <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TNewResult">Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzung, die ausgeführt wird, wenn die Ziel-<see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist.</summary>
      <returns>Ein neuer Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="continuationFunction">Eine Funktion, die ausgeführt werden soll, wenn das <see cref="T:System.Threading.Tasks.Task`1" /> abgeschlossen ist.Bei der Ausführung werden dem Delegaten die abgeschlossene Aufgabe und das vom Aufrufer angegebene Zustandsobjekt als Argumente übergeben.</param>
      <param name="state">Ein Objekt, das Daten darstellt, die von der Fortsetzungsfunktion verwendet werden sollen.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das der Fortsetzungsaufgabe zugeordnet und für ihre Ausführung verwendet werden soll.</param>
      <typeparam name="TNewResult">Der Typ des von der Fortsetzung generierten Ergebnisses.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task`1.Factory">
      <summary>Bietet Zugriff auf Factorymethoden zum Erstellen und Konfigurieren von <see cref="T:System.Threading.Tasks.Task`1" />-Instanzen.</summary>
      <returns>Ein Factoryobjekt, das eine Vielzahl von <see cref="T:System.Threading.Tasks.Task`1" />-Objekten erstellen kann.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.GetAwaiter">
      <summary>Ruft einen Awaiter ab, der verwendet wird, um diese <see cref="T:System.Threading.Tasks.Task`1" /> zu erwarten.</summary>
      <returns>Eine Awaiter-Instanz.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task`1.Result">
      <summary>Ruft den Ergebniswert dieses <see cref="T:System.Threading.Tasks.Task`1" /> ab.</summary>
      <returns>Der Ergebniswert für diesen <see cref="T:System.Threading.Tasks.Task`1" />, der dem Typparameter der Aufgabe entspricht.</returns>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskCanceledException">
      <summary>Stellt eine Ausnahme dar, die verwendet wurde, um einen Taskabbruch zu übermitteln.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Tasks.TaskCanceledException" />-Klasse mit einer vom System generierten Meldung, die den Fehler beschreibt.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Tasks.TaskCanceledException" />-Klasse mit einer angegebenen Meldung, die den Fehler beschreibt.</summary>
      <param name="message">Die Meldung, in der die Ausnahme beschrieben wirdDer Aufrufer dieses Konstruktors muss sicherstellen, dass diese Zeichenfolge für die aktuelle Systemkultur lokalisiert wurde.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Tasks.TaskCanceledException" />-Klasse mit einer angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Meldung, in der die Ausnahme beschrieben wirdDer Aufrufer dieses Konstruktors muss sicherstellen, dass diese Zeichenfolge für die aktuelle Systemkultur lokalisiert wurde.</param>
      <param name="innerException">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="innerException" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.Threading.Tasks.Task)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Tasks.TaskCanceledException" />-Klasse mit einem Verweis auf das <see cref="T:System.Threading.Tasks.Task" />-Objekt, das abgebrochen wurde.</summary>
      <param name="task">Ein Task, der abgebrochen wurde.</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskCanceledException.Task">
      <summary>Ruft die Aufgabe ab, die dieser Ausnahme zugeordnet ist.</summary>
      <returns>Ein Verweis auf den <see cref="T:System.Threading.Tasks.Task" />, der dieser Ausnahme zugeordnet ist.</returns>
    </member>
    <member name="T:System.Threading.Tasks.TaskCompletionSource`1">
      <summary>Stellt die Producerseite eines <see cref="T:System.Threading.Tasks.Task`1" />-Objekts dar, dessen Bindung an einen Delegaten aufgehoben wurde. Das Element ermöglicht mithilfe der <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" />-Eigenschaft den Zugriff auf die Consumerseite.</summary>
      <typeparam name="TResult">Der Typ des Ergebniswerts, der dem <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />-Objekt zugeordnet ist.</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor">
      <summary>Erstellt eine <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Object)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />-Objekt mit dem angegebenen Zustand.</summary>
      <param name="state">Der Zustand, der als zugrunde liegender AsyncState von <see cref="T:System.Threading.Tasks.Task`1" /> verwendet werden soll.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />-Objekt mit dem angegebenen Zustand und den angegebenen Optionen.</summary>
      <param name="state">Der Zustand, der als zugrunde liegender AsyncState von <see cref="T:System.Threading.Tasks.Task`1" /> verwendet werden soll.</param>
      <param name="creationOptions">Die Optionen, die beim Erstellen des zugrunde liegenden <see cref="T:System.Threading.Tasks.Task`1" />-Objekts zu verwenden sind.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Der <paramref name="creationOptions" /> stellt Optionen dar, die für die Verwendung mit einem <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> ungültig sind.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />-Objekt mit den angegebenen Optionen.</summary>
      <param name="creationOptions">Die Optionen, die beim Erstellen des zugrunde liegenden <see cref="T:System.Threading.Tasks.Task`1" />-Objekts zu verwenden sind.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Der <paramref name="creationOptions" /> stellt Optionen dar, die für die Verwendung mit einem <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> ungültig sind.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetCanceled">
      <summary>Sorgt für den Übergang des zugrunde liegenden <see cref="T:System.Threading.Tasks.Task`1" />-Objekts in den <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />-Zustand.</summary>
      <exception cref="T:System.InvalidOperationException">Der zugrunde liegende <see cref="T:System.Threading.Tasks.Task`1" /> befindet sich bereits in einem der drei Endzustände: <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> oder <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />, oder wenn der zugrunde liegende <see cref="T:System.Threading.Tasks.Task`1" /> bereits freigegeben wurde.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetException(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Sorgt für den Übergang des zugrunde liegenden <see cref="T:System.Threading.Tasks.Task`1" />-Objekts in den <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />-Zustand.</summary>
      <param name="exceptions">Die Auflistung der Ausnahmen, die an das <see cref="T:System.Threading.Tasks.Task`1" />-Objekt gebunden werden sollen.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="exceptions" />-Argument ist Null.</exception>
      <exception cref="T:System.ArgumentException">In <paramref name="exceptions" /> ist mindestens ein NULL-Element vorhanden.</exception>
      <exception cref="T:System.InvalidOperationException">Der zugrunde liegende <see cref="T:System.Threading.Tasks.Task`1" /> ist bereits in einem der drei Endzustände: <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> oder <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetException(System.Exception)">
      <summary>Sorgt für den Übergang des zugrunde liegenden <see cref="T:System.Threading.Tasks.Task`1" />-Objekts in den <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />-Zustand.</summary>
      <param name="exception">Die Ausnahme, die an das <see cref="T:System.Threading.Tasks.Task`1" />-Objekt gebunden werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="exception" />-Argument ist Null.</exception>
      <exception cref="T:System.InvalidOperationException">Der zugrunde liegende <see cref="T:System.Threading.Tasks.Task`1" /> ist bereits in einem der drei Endzustände: <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> oder <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetResult(`0)">
      <summary>Sorgt für den Übergang des zugrunde liegenden <see cref="T:System.Threading.Tasks.Task`1" />-Objekts in den <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />-Zustand.</summary>
      <param name="result">Der Ergebniswert, der an das <see cref="T:System.Threading.Tasks.Task`1" />-Objekt gebunden werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der zugrunde liegende <see cref="T:System.Threading.Tasks.Task`1" /> ist bereits in einem der drei Endzustände: <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> oder <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskCompletionSource`1.Task">
      <summary>Ruft den von der <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> erstellten <see cref="T:System.Threading.Tasks.Task`1" /> ab.</summary>
      <returns>Gibt den von dieser <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> erstellten <see cref="T:System.Threading.Tasks.Task`1" /> zurück.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetCanceled">
      <summary>Versucht, den Übergang des zugrunde liegenden <see cref="T:System.Threading.Tasks.Task`1" />-Objekts in den <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />-Zustand auszuführen.</summary>
      <returns>True, wenn der Vorgang erfolgreich war, false, wenn der Vorgang nicht erfolgreich war oder das Objekt bereits freigegeben wurde.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetCanceled(System.Threading.CancellationToken)">
      <summary>Versucht, den Übergang des zugrunde liegenden <see cref="T:System.Threading.Tasks.Task`1" />-Objekts in den <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />-Zustand auszuführen, und ermöglicht das Speichern eines Abbruchtokens in der abgebrochenen Aufgabe. </summary>
      <returns>true, wenn der Vorgang erfolgreich ist, andernfalls false. </returns>
      <param name="cancellationToken">Ein Abbruchtoken. </param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetException(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Versucht, den Übergang des zugrunde liegenden <see cref="T:System.Threading.Tasks.Task`1" />-Objekts in den <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />-Zustand auszuführen.</summary>
      <returns>True, wenn der Vorgang erfolgreich war, andernfalls false.</returns>
      <param name="exceptions">Die Auflistung der Ausnahmen, die an das <see cref="T:System.Threading.Tasks.Task`1" />-Objekt gebunden werden sollen.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="exceptions" />-Argument ist Null.</exception>
      <exception cref="T:System.ArgumentException">In <paramref name="exceptions" /> ist mindestens ein NULL-Element vorhanden.- oder - Die <paramref name="exceptions" />-Auflistung ist leer.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetException(System.Exception)">
      <summary>Versucht, den Übergang des zugrunde liegenden <see cref="T:System.Threading.Tasks.Task`1" />-Objekts in den <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />-Zustand auszuführen.</summary>
      <returns>True, wenn der Vorgang erfolgreich war, andernfalls false.</returns>
      <param name="exception">Die Ausnahme, die an das <see cref="T:System.Threading.Tasks.Task`1" />-Objekt gebunden werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="exception" />-Argument ist Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetResult(`0)">
      <summary>Versucht, den Übergang des zugrunde liegenden <see cref="T:System.Threading.Tasks.Task`1" />-Objekts in den <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />-Zustand auszuführen.</summary>
      <returns>True, wenn der Vorgang erfolgreich war, andernfalls false. </returns>
      <param name="result">Der Ergebniswert, der an das <see cref="T:System.Threading.Tasks.Task`1" />-Objekt gebunden werden soll.</param>
    </member>
    <member name="T:System.Threading.Tasks.TaskContinuationOptions">
      <summary>Gibt das Verhalten für eine Aufgabe an, die mit der <see cref="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)" />-Methode oder <see cref="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskContinuationOptions)" />-Methode erstellt wird.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent">
      <summary>Gibt an, dass die Fortsetzung, sofern es sich um eine untergeordnete Aufgabe handelt, in der Aufgabenhierarchie mit einem übergeordneten Element verknüpft wird.Die Fortsetzung kann nur dann eine untergeordnete Aufgabe handeln, wenn das vorangehende Element ebenfalls eine untergeordnete Aufgabe ist.Eine untergeordnete Aufgabe (d. h. eine von einer äußeren Aufgabe erstellte innere Aufgabe) wird standardmäßig unabhängig von der übergeordneten Aufgabe ausgeführt.Sie können die <see cref="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent" />-Option verwenden, damit die übergeordneten und untergeordneten Aufgaben synchronisiert werden.Beachten Sie: Wenn eine übergeordnete Aufgabe mit der <see cref="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach" />-Option konfiguriert ist, hat die <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" />-Option in der untergeordneten Aufgabe keine Auswirkungen, und die untergeordnete Aufgabe wird als eine getrennte untergeordnete Aufgabe ausgeführt. Weitere Informationen finden Sie unter Angefügte und getrennte untergeordnete Aufgaben. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.DenyChildAttach">
      <summary>Gibt an, dass jede untergeordnete Aufgabe (d.h. jede geschachtelte innere Aufgabe, die von dieser Fortsetzung erstellt wurde), die mit der <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" />-Option erstellt wurde und deren Ausführung als angefügte untergeordnete Aufgabe versucht wird, nicht an die übergeordnete Aufgabe angefügt werden kann und stattdessen als eine getrennte untergeordnete Aufgabe ausgeführt wird.Weitere Informationen finden Sie unter Angefügte und getrennte untergeordnete Aufgaben.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously">
      <summary>Gibt an, dass die Fortsetzungsaufgabe synchron ausgeführt werden soll.Wenn diese Option angegeben wird, wird die Fortsetzung in dem Thread ausgeführt, der den Übergang der vorangehenden Aufgabe in ihren Endzustand verursacht hat.Falls die vorangehende Aufgabe bereits abgeschlossen wurde, wird die Fortsetzung in dem Thread ausgeführt, der die Fortsetzung erstellt.Wird die <see cref="T:System.Threading.CancellationTokenSource" /> des Vorgängers in einem finally-Block (Finally in Visual Basic) verworfen, wird eine Fortsetzung mit dieser Option in diesem finally-Block ausgeführt.Nur sehr kurze Fortsetzungen sollten synchron ausgeführt werden.Da der Vorgang synchron ausgeführt wird, ist das Aufrufen einer Methode wie <see cref="M:System.Threading.Tasks.Task.Wait" /> nicht erforderlich, um sicherzustellen, dass der aufrufende Thread wartet, bis die Aufgabe abgeschlossen ist. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.HideScheduler">
      <summary>Gibt an, dass Aufgaben, die von der Fortsetzung durch den Aufruf von Methoden wie <see cref="M:System.Threading.Tasks.Task.Run(System.Action)" /> oder <see cref="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task})" /> erstellt wurden, sich auf den Standardplaner (<see cref="P:System.Threading.Tasks.TaskScheduler.Default" />) als aktuellen Planer beziehen, statt auf den Planer, auf dem diese Fortsetzung ausgeführt wird.  </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.LazyCancellation">
      <summary>Im Fall des Fortsetzungsabbruchs wird die Beendigung der Fortsetzung verhindert, bis der Vorgänger abgeschlossen wurde.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.LongRunning">
      <summary>Gibt an, dass die Fortsetzung ein undifferenzierter Vorgang mit langer Laufzeit ist.Enthält einen Hinweis für den <see cref="T:System.Threading.Tasks.TaskScheduler" />, dass möglicherweise zu viele Abonnements gewährt werden.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.None">
      <summary>Gibt, wenn keine Fortsetzungsoptionen angegeben sind, an, dass beim Ausführen einer Fortsetzen das Standardverhalten verwendet werden soll.Die Fortsetzung wird beim Abschluss der vorhergehenden Aufgabe asynchron ausgeführt, unabhängig vom endgültigen Wert der Eigenschaft <see cref="P:System.Threading.Tasks.Task.Status" />.Handelt es sich bei der Fortsetzung um eine untergeordnete Aufgabe, wird diese als getrennte geschachtelte Aufgabe erstellt.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnCanceled">
      <summary>Gibt an, dass die Fortsetzungsaufgabe nicht geplant werden soll, wenn die vorangehende Aufgabe abgebrochen wurde.Ein vorhergehende Aufgabe wird abgebrochen, wenn ihre Eigenschaft "<see cref="P:System.Threading.Tasks.Task.Status" />" beim Abschluss den Wert "<see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />" aufweist.Diese Option ist nicht gültig für die Fortsetzung mehrerer Aufgaben.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnFaulted">
      <summary>Gibt an, dass die Fortsetzungsaufgabe nicht geplant werden soll, wenn der Vorgänger einen Ausnahmefehler ausgelöst hat.Eine vorhergehende Aufgabe löst einen Ausnahmefehler aus, wenn ihre Eigenschaft "<see cref="P:System.Threading.Tasks.Task.Status" />" beim Abschluss den Wert "<see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />" aufweist.Diese Option ist nicht gültig für die Fortsetzung mehrerer Aufgaben.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnRanToCompletion">
      <summary>Gibt an, dass die Fortsetzungsaufgabe nicht geplant werden soll, wenn der Vorgänger bis zum Abschluss ausgeführt wurde.Eine vorhergehende Aufgabe wird bis zum Abschluss ausgeführt, wenn ihre Eigenschaft "<see cref="P:System.Threading.Tasks.Task.Status" />" beim Abschluss den Wert "<see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />" aufweist.Diese Option ist nicht gültig für die Fortsetzung mehrerer Aufgaben.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled">
      <summary>Gibt an, dass die Fortsetzung nur geplant werden soll, wenn die vorangehende Aufgabe abgebrochen wurde.Ein vorhergehende Aufgabe wird abgebrochen, wenn ihre Eigenschaft "<see cref="P:System.Threading.Tasks.Task.Status" />" beim Abschluss den Wert "<see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />" aufweist.Diese Option ist nicht gültig für die Fortsetzung mehrerer Aufgaben.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnFaulted">
      <summary>Gibt an, dass die Fortsetzungsaufgabe nur geplant werden soll, wenn der Vorgänger einen Ausnahmefehler ausgelöst hat.Eine vorhergehende Aufgabe löst einen Ausnahmefehler aus, wenn ihre Eigenschaft "<see cref="P:System.Threading.Tasks.Task.Status" />" beim Abschluss den Wert "<see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />" aufweist.Die Option "<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnFaulted" />" stellt sicher, dass die <see cref="P:System.Threading.Tasks.Task.Exception" />-Eigenschaft im Vorgänger nicht null ist.Sie können diese Eigenschaft verwenden, um die Ausnahme zu erfassen und anzuzeigen, welche Ausnahme das Fehlschlagen der Aufgabe verursacht hat.Wenn Sie nicht auf die <see cref="P:System.Threading.Tasks.Task.Exception" />-Eigenschaft zugreifen, bleibt die Ausnahme unbehandelt.Darüber hinaus wird eine neue Ausnahme ausgelöst, wenn Sie auf die <see cref="P:System.Threading.Tasks.Task`1.Result" />-Eigenschaft einer Aufgabe zugreifen, die abgebrochen wurde oder fehlgeschlagen ist.Diese Option ist nicht gültig für die Fortsetzung mehrerer Aufgaben. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnRanToCompletion">
      <summary>Gibt an, dass die Fortsetzung nur geplant werden soll, wenn die vorangehende Aufgabe bis zum Abschluss ausgeführt wurde.Eine vorhergehende Aufgabe wird bis zum Abschluss ausgeführt, wenn ihre Eigenschaft "<see cref="P:System.Threading.Tasks.Task.Status" />" beim Abschluss den Wert "<see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />" aufweist.Diese Option ist nicht gültig für die Fortsetzung mehrerer Aufgaben.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.PreferFairness">
      <summary>Ein Hinweis für ein <see cref="T:System.Threading.Tasks.TaskScheduler" />, Aufgaben in der Reihenfolge ihrer ursprünglichen Planung zu planen, sodass zu einem früheren Zeitpunkt geplante Aufgaben tendenziell früher, zu einem späteren Zeitpunkt geplante Aufgaben tendenziell später ausgeführt werden. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.RunContinuationsAsynchronously">
      <summary>Gibt an, dass die Fortsetzungsaufgabe asynchron ausgeführt werden soll.Diese Option hat Vorrang vor <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskCreationOptions">
      <summary>Gibt Flags an, die optionales Verhalten für die Erstellung und Ausführung von Aufgaben steuern. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent">
      <summary>Gibt an, dass eine Aufgabe in der Aufgabenhierarchie an ein übergeordnetes Element angefügt wird.Eine untergeordnete Aufgabe (d. h. eine von einer äußeren Aufgabe erstellte innere Aufgabe) wird standardmäßig unabhängig von der übergeordneten Aufgabe ausgeführt.Sie können die <see cref="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent" />-Option verwenden, damit die übergeordneten und untergeordneten Aufgaben synchronisiert werden.Beachten Sie: Wenn eine übergeordnete Aufgabe mit der <see cref="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach" />-Option konfiguriert ist, hat die <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" />-Option in der untergeordneten Aufgabe keine Auswirkungen, und die untergeordnete Aufgabe wird als eine getrennte untergeordnete Aufgabe ausgeführt. Weitere Informationen finden Sie unter Angefügte und getrennte untergeordnete Aufgaben. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach">
      <summary>Gibt an, dass jede untergeordnete Aufgabe, die als angefügte untergeordnete Aufgabe ausgeführt werden soll (d. h. mit der <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" />-Option erstellt), nicht an die übergeordnete Aufgabe angefügt werden kann und stattdessen als eine getrennte untergeordnete Aufgabe ausgeführt wird.Weitere Informationen finden Sie unter Angefügte und getrennte untergeordnete Aufgaben.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.HideScheduler">
      <summary>Verhindert, dass der Ambientenplaner als aktueller Planer in der erstellten Aufgabe sichtbar ist.Dies bedeutet, dass Vorgänge wie StartNew oder ContinueWith, die in der erstellten Aufgabe ausgeführt werden, <see cref="P:System.Threading.Tasks.TaskScheduler.Default" /> als aktuellen Planer aufweisen.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.LongRunning">
      <summary>Gibt an, dass eine Aufgabe ein undifferenzierter Vorgang mit langer Laufzeit sein wird, der weniger größere Komponenten als differenzierte Systeme beinhaltet.Enthält einen Hinweis für den <see cref="T:System.Threading.Tasks.TaskScheduler" />, dass möglicherweise zu viele Abonnements gewährt werden.Durch Überzeichnung können Sie mehr Threads als die Anzahl der verfügbaren Hardwarethreads erstellen.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.None">
      <summary>Gibt an, dass das Standardverhalten verwendet werden soll.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.PreferFairness">
      <summary>Ein Hinweis für einen <see cref="T:System.Threading.Tasks.TaskScheduler" />, eine Aufgabe möglichst fair zu planen. Dies bedeutet, dass früher geplante Aufgaben wahrscheinlich früher und später geplante Aufgaben wahrscheinlich später ausgeführt werden.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.RunContinuationsAsynchronously">
      <summary>Erzwingt die asynchrone Ausführung von Fortsetzungen, die zur aktuellen Aufgabe hinzugefügt werden. </summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskExtensions">
      <summary>Stellt einen Satz mit statischen Methoden (Shared in Visual Basic) für die Arbeit mit bestimmten Arten von <see cref="T:System.Threading.Tasks.Task" />-Instanzen bereit.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskExtensions.Unwrap``1(System.Threading.Tasks.Task{System.Threading.Tasks.Task{``0}})">
      <summary>Erstellt einen Proxy-<see cref="T:System.Threading.Tasks.Task" />, der den asynchronen Vorgang von einem Task&lt;Task&lt;T&gt;&gt; (C#) oder Task (Of Task(Of T)) (Visual Basic) darstellt.</summary>
      <returns>Ein <see cref="T:System.Threading.Tasks.Task" />, der den asynchronen Vorgang des bereitgestellten Task&lt;Task&lt;T&gt;&gt; (C#) oder Task (Of Task(Of T)) (Visual Basic) darstellt.</returns>
      <param name="task">Die zu entpackende Task&lt;Task&lt;T&gt;&gt; (C#) oder Task (Of Task(Of T)) Visual Basic).</param>
      <typeparam name="TResult">Der Typ des Aufgabenergebnisses.</typeparam>
      <exception cref="T:System.ArgumentNullException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="task" />-Argument NULL ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskExtensions.Unwrap(System.Threading.Tasks.Task{System.Threading.Tasks.Task})">
      <summary>Erstellt einen <see cref="T:System.Threading.Tasks.Task" /> für Proxys, der den asynchronen Vorgang eines <see cref="M:System.Threading.Tasks.TaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)" /> darstellt.</summary>
      <returns>Eine Aufgabe, die den asynchronen Vorgang des angegebenen System.Threading.Tasks.Task(Of Task) darstellt.</returns>
      <param name="task">Die zu entpackende Task&lt;Task&gt; (C#) oder Task (Of Task) Visual Basic).</param>
      <exception cref="T:System.ArgumentNullException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="task" />-Argument NULL ist.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskFactory">
      <summary>Bietet Unterstützung für das Erstellen und Planen von <see cref="T:System.Threading.Tasks.Task" />-Objekten. </summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor">
      <summary>Initialisiert eine <see cref="T:System.Threading.Tasks.TaskFactory" />-Instanz mit der Standardkonfiguration.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.CancellationToken)">
      <summary>Initialisiert eine <see cref="T:System.Threading.Tasks.TaskFactory" />-Instanz mit der angegebenen Konfiguration.</summary>
      <param name="cancellationToken">Das <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />, das Aufgaben zugewiesen wird, die von dieser <see cref="T:System.Threading.Tasks.TaskFactory" /> erstellt wurden, sofern beim Aufrufen der Factorymethoden kein anderes CancellationToken explizit angegeben wird.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Initialisiert eine <see cref="T:System.Threading.Tasks.TaskFactory" />-Instanz mit der angegebenen Konfiguration.</summary>
      <param name="cancellationToken">Das standardmäßige <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />, das Aufgaben zugewiesen wird, die von dieser <see cref="T:System.Threading.Tasks.TaskFactory" /> erstellt wurden, sofern beim Aufrufen der Factorymethoden kein anderes CancellationToken explizit angegeben wird.</param>
      <param name="creationOptions">Das zum Erstellen von Aufgaben mit dieser TaskFactory zu verwendende Standard-<see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</param>
      <param name="continuationOptions">Das zum Erstellen von Fortsetzungsaufgaben mit dieser TaskFactory zu verwendende Standard-<see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</param>
      <param name="scheduler">Der beim Planen von Aufgaben mit dieser TaskFactory zu verwendende standardmäßige <see cref="T:System.Threading.Tasks.TaskScheduler" />.Ein NULL-Wert gibt an, dass TaskScheduler.Current verwendet werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Das <paramref name="creationOptions" />-Argument gibt einen ungültigen <see cref="T:System.Threading.Tasks.TaskCreationOptions" />-Wert an.Weitere Informationen finden Sie unter den Hinweisen zur <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.- oder - Das <paramref name="continuationOptions" />-Argument gibt einen ungültigen Wert an.  </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Initialisiert eine <see cref="T:System.Threading.Tasks.TaskFactory" />-Instanz mit der angegebenen Konfiguration.</summary>
      <param name="creationOptions">Das zum Erstellen von Aufgaben mit dieser TaskFactory zu verwendende Standard-<see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</param>
      <param name="continuationOptions">Das zum Erstellen von Fortsetzungsaufgaben mit dieser TaskFactory zu verwendende Standard-<see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Das <paramref name="creationOptions" />-Argument gibt einen ungültigen <see cref="T:System.Threading.Tasks.TaskCreationOptions" />-Wert an.Weitere Informationen finden Sie unter den Hinweisen zur <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.- oder - Das <paramref name="continuationOptions" />-Argument gibt einen ungültigen Wert an.  </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>Initialisiert eine <see cref="T:System.Threading.Tasks.TaskFactory" />-Instanz mit der angegebenen Konfiguration.</summary>
      <param name="scheduler">Der beim Planen von Aufgaben mit dieser TaskFactory zu verwendende <see cref="T:System.Threading.Tasks.TaskScheduler" />.Ein NULL-Wert gibt an, dass der aktuelle TaskScheduler verwendet werden soll.</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.CancellationToken">
      <summary>Ruft das Standardabbruchtoken für diese Aufgabenfactory ab.</summary>
      <returns>Das standardmäßige Aufgabenabbruchtoken für diese Aufgabenfactory.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.ContinuationOptions">
      <summary>Ruft die standardmäßigen Aufgabenfortsetzungsoptionen für diese Aufgabenfactory ab.</summary>
      <returns>Die standardmäßigen Aufgabenfortsetzungsoptionen für diese Aufgabenfactory.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]})">
      <summary>Erstellt eine Fortsetzungsaufgabe, die beginnt, wenn ein Satz angegebener Aufgaben abgeschlossen wurde. </summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationAction">Der Aktionsdelegat, der ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <exception cref="T:System.ObjectDisposedException">Ein Element im <paramref name="tasks" />-Array wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationAction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array ist leer oder enthält einen NULL-Wert.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.CancellationToken)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die beginnt, wenn ein Satz angegebener Aufgaben abgeschlossen wurde.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationAction">Der Aktionsdelegat, der ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Fortsetzungsaufgabe zugeordnet werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">Ein Element im <paramref name="tasks" />-Array wurde freigegeben.- oder - Die <see cref="T:System.Threading.CancellationTokenSource" />, die <paramref name="cancellationToken" /> erstellt hat, wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationAction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array ist leer oder enthält einen NULL-Wert.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die beginnt, wenn ein Satz angegebener Aufgaben abgeschlossen wurde.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationAction">Der Aktionsdelegat, der ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Fortsetzungsaufgabe zugeordnet werden soll.</param>
      <param name="continuationOptions">Eine bitweise Kombination der Enumerationswerte, die das Verhalten der neuen Fortsetzungsaufgabe steuern.</param>
      <param name="scheduler">Das Objekt, das verwendet wird, um die neue Fortsetzungsaufgabe zu planen.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationAction" />-Argument lautet null.- oder - Das <paramref name="scheduler" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array ist leer oder enthält einen NULL-Wert.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die beginnt, wenn ein Satz angegebener Aufgaben abgeschlossen wurde.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationAction">Der Aktionsdelegat, der ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <param name="continuationOptions">Eine bitweise Kombination der Enumerationswerte, die das Verhalten der neuen Fortsetzungsaufgabe steuern.Die Member NotOn* und OnlyOn* werden nicht unterstützt.</param>
      <exception cref="T:System.ObjectDisposedException">Ein Element im <paramref name="tasks" />-Array wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationAction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Das <paramref name="continuationOptions" />-Argument gibt einen ungültigen Wert an. </exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array ist leer oder enthält einen NULL-Wert.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0})">
      <summary>Erstellt eine Fortsetzungsaufgabe, die beginnt, wenn ein Satz angegebener Aufgaben abgeschlossen wurde.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das vom <paramref name="continuationFunction" />-Delegaten zurückgegeben und der erstellten Aufgabe zugeordnet wird.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Ein Element im <paramref name="tasks" />-Array wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array ist leer oder enthält einen NULL-Wert.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.CancellationToken)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die beginnt, wenn ein Satz angegebener Aufgaben abgeschlossen wurde.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Fortsetzungsaufgabe zugeordnet werden soll.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das vom <paramref name="continuationFunction" />-Delegaten zurückgegeben und der erstellten Aufgabe zugeordnet wird.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Ein Element im <paramref name="tasks" />-Array wurde freigegeben.- oder - Die <see cref="T:System.Threading.CancellationTokenSource" />, die <paramref name="cancellationToken" /> erstellt hat, wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array ist leer oder enthält einen NULL-Wert.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die beginnt, wenn ein Satz angegebener Aufgaben abgeschlossen wurde.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Fortsetzungsaufgabe zugeordnet werden soll.</param>
      <param name="continuationOptions">Eine bitweise Kombination der Enumerationswerte, die das Verhalten der neuen Fortsetzungsaufgabe steuern.Die Member NotOn* und OnlyOn* werden nicht unterstützt.</param>
      <param name="scheduler">Das Objekt, das verwendet wird, um die neue Fortsetzungsaufgabe zu planen.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das vom <paramref name="continuationFunction" />-Delegaten zurückgegeben und der erstellten Aufgabe zugeordnet wird.</typeparam>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.- oder - Das <paramref name="scheduler" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array ist leer oder enthält einen NULL-Wert.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die beginnt, wenn ein Satz angegebener Aufgaben abgeschlossen wurde.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <param name="continuationOptions">Eine bitweise Kombination der Enumerationswerte, die das Verhalten der neuen Fortsetzungsaufgabe steuern.Die Member NotOn* und OnlyOn* werden nicht unterstützt.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das vom <paramref name="continuationFunction" />-Delegaten zurückgegeben und der erstellten Aufgabe zugeordnet wird.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Ein Element im <paramref name="tasks" />-Array wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Das <paramref name="continuationOptions" />-Argument gibt einen ungültigen Wert an. </exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array ist leer oder enthält einen NULL-Wert.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]})">
      <summary>Erstellt eine Fortsetzungsaufgabe, die beginnt, wenn ein Satz angegebener Aufgaben abgeschlossen wurde.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationAction">Der Aktionsdelegat, der ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Ein Element im <paramref name="tasks" />-Array wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationAction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array ist leer oder enthält einen NULL-Wert.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.CancellationToken)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die beginnt, wenn ein Satz angegebener Aufgaben abgeschlossen wurde.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationAction">Der Aktionsdelegat, der ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Fortsetzungsaufgabe zugeordnet werden soll.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Ein Element im <paramref name="tasks" />-Array wurde freigegeben.- oder - Die <see cref="T:System.Threading.CancellationTokenSource" />, die <paramref name="cancellationToken" /> erstellt hat, wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationAction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array ist leer oder enthält einen NULL-Wert.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die beginnt, wenn ein Satz angegebener Aufgaben abgeschlossen wurde.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationAction">Der Aktionsdelegat, der ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Fortsetzungsaufgabe zugeordnet werden soll.</param>
      <param name="continuationOptions">Eine bitweise Kombination der Enumerationswerte, die das Verhalten der neuen Fortsetzungsaufgabe steuern.Die Member NotOn* und OnlyOn* werden nicht unterstützt.</param>
      <param name="scheduler">Das Objekt, das verwendet wird, um die neue Fortsetzungsaufgabe zu planen.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationAction" />-Argument lautet null.- oder - Das <paramref name="scheduler" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array ist leer oder enthält einen NULL-Wert.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die beginnt, wenn ein Satz angegebener Aufgaben abgeschlossen wurde.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationAction">Der Aktionsdelegat, der ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <param name="continuationOptions">Eine bitweise Kombination der Enumerationswerte, die das Verhalten der neuen Fortsetzungsaufgabe steuern.Die Member NotOn* und OnlyOn* werden nicht unterstützt.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Ein Element im <paramref name="tasks" />-Array wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationAction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Das <paramref name="continuationOptions" />-Argument gibt einen ungültigen Wert an. </exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array ist leer oder enthält einen NULL-Wert.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1})">
      <summary>Erstellt eine Fortsetzungsaufgabe, die beginnt, wenn ein Satz angegebener Aufgaben abgeschlossen wurde.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <typeparam name="TResult">Der Typ des Ergebnisses, das vom <paramref name="continuationFunction" />-Delegaten zurückgegeben und der erstellten Aufgabe zugeordnet wird.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Ein Element im <paramref name="tasks" />-Array wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array ist leer oder enthält einen NULL-Wert.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.CancellationToken)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die beginnt, wenn ein Satz angegebener Aufgaben abgeschlossen wurde.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Fortsetzungsaufgabe zugeordnet werden soll.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <typeparam name="TResult">Der Typ des Ergebnisses, das vom <paramref name="continuationFunction" />-Delegaten zurückgegeben und der erstellten Aufgabe zugeordnet wird.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Ein Element im <paramref name="tasks" />-Array wurde freigegeben.- oder - Die <see cref="T:System.Threading.CancellationTokenSource" /> erstellten<paramref name=" cancellationToken" /> bereits freigegeben wurde.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array ist leer oder enthält einen NULL-Wert.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die beginnt, wenn ein Satz angegebener Aufgaben abgeschlossen wurde.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Fortsetzungsaufgabe zugeordnet werden soll.</param>
      <param name="continuationOptions">Eine bitweise Kombination der Enumerationswerte, die das Verhalten der neuen Fortsetzungsaufgabe steuern.Die Member NotOn* und OnlyOn* werden nicht unterstützt.</param>
      <param name="scheduler">Das Objekt, das verwendet wird, um die neue Fortsetzungsaufgabe zu planen.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <typeparam name="TResult">Der Typ des Ergebnisses, das vom <paramref name="continuationFunction" />-Delegaten zurückgegeben und der erstellten Aufgabe zugeordnet wird.</typeparam>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.- oder - Das <paramref name="scheduler" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array ist leer oder enthält einen NULL-Wert.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Das <paramref name="continuationOptions" />-Argument gibt einen ungültigen Wert an. </exception>
      <exception cref="T:System.ObjectDisposedException">Ein Element im <paramref name="tasks" />-Array wurde freigegeben.- oder - Die <see cref="T:System.Threading.CancellationTokenSource" />, die <paramref name="cancellationToken" /> erstellt hat, wurde bereits freigegeben.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die beginnt, wenn ein Satz angegebener Aufgaben abgeschlossen wurde.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <param name="continuationOptions">Eine bitweise Kombination der Enumerationswerte, die das Verhalten der neuen Fortsetzungsaufgabe steuern.Die Member NotOn* und OnlyOn* werden nicht unterstützt.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <typeparam name="TResult">Der Typ des Ergebnisses, das vom <paramref name="continuationFunction" />-Delegaten zurückgegeben und der erstellten Aufgabe zugeordnet wird.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Ein Element im <paramref name="tasks" />-Array wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Das <paramref name="continuationOptions" />-Argument gibt einen ungültigen Wert an.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array ist leer oder enthält einen NULL-Wert.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task})">
      <summary>Erstellt ein Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />, das nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationAction">Der Aktionsdelegat, der ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <exception cref="T:System.ObjectDisposedException">Eines der Elemente im <paramref name="tasks" />-Array wurde freigegeben. </exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null. - oder - Die dem <paramref name="continuationAction" /> -Argument ist null. </exception>
      <exception cref="T:System.ArgumentException">Die <paramref name="tasks" /> Array enthält ein null Wert. - oder - Die <paramref name="tasks" /> Array ist leer.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>Erstellt ein Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />, das nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationAction">Der Aktionsdelegat, der ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <exception cref="T:System.ObjectDisposedException">Eines der Elemente im <paramref name="tasks" />-Array wurde freigegeben. - oder - <paramref name="cancellationToken" /> wurde bereits freigegeben. </exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null. - oder - Das <paramref name="continuationAction" />-Argument lautet null. </exception>
      <exception cref="T:System.ArgumentException">Die <paramref name="tasks" /> Array enthält ein null Wert. - oder - Die <paramref name="tasks" /> Array ist leer.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt ein Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />, das nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationAction">Der Aktionsdelegat, der ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <param name="continuationOptions">Der <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />-Wert, der das Verhalten des erstellten Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" /> steuert.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das verwendet wird, um das erstellte Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" /> zu planen.</param>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="continuationAction" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="scheduler" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array einen NULL-Wert enthält.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array leer ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt ein Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />, das nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationAction">Der Aktionsdelegat, der ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <param name="continuationOptions">Der <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />-Wert, der das Verhalten des erstellten Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" /> steuert.</param>
      <exception cref="T:System.ObjectDisposedException">Die Ausnahme, die ausgelöst wird, wenn eines der Elemente im <paramref name="tasks" />-Array freigegeben wurde.</exception>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="continuationAction" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="continuationOptions" />-Argument einen ungültigen TaskContinuationOptions-Wert angibt.</exception>
      <exception cref="T:System.ArgumentException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array einen NULL-Wert enthält.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array leer ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0})">
      <summary>Erstellt ein Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />, das nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das vom <paramref name="continuationFunction" />-Delegaten zurückgegeben und dem erstellten <see cref="T:System.Threading.Tasks.Task`1" />-Element zugeordnet wird.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Die Ausnahme, die ausgelöst wird, wenn eines der Elemente im <paramref name="tasks" />-Array freigegeben wurde.</exception>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="continuationFunction" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array einen NULL-Wert enthält.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array leer ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken)">
      <summary>Erstellt ein Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />, das nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das vom <paramref name="continuationFunction" />-Delegaten zurückgegeben und dem erstellten <see cref="T:System.Threading.Tasks.Task`1" />-Element zugeordnet wird.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Die Ausnahme, die ausgelöst wird, wenn eines der Elemente im <paramref name="tasks" />-Array freigegeben wurde.- oder - Das angegebene <see cref="T:System.Threading.CancellationToken" /> wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="continuationFunction" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array einen NULL-Wert enthält.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array leer ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt ein Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />, das nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <param name="continuationOptions">Der <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />-Wert, der das Verhalten des erstellten Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" /> steuert.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das verwendet wird, um das erstellte Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" /> zu planen.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das vom <paramref name="continuationFunction" />-Delegaten zurückgegeben und dem erstellten <see cref="T:System.Threading.Tasks.Task`1" />-Element zugeordnet wird.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="continuationFunction" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="scheduler" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array einen NULL-Wert enthält.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array leer ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt ein Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />, das nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <param name="continuationOptions">Der <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />-Wert, der das Verhalten des erstellten Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" /> steuert.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das vom <paramref name="continuationFunction" />-Delegaten zurückgegeben und dem erstellten <see cref="T:System.Threading.Tasks.Task`1" />-Element zugeordnet wird.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Die Ausnahme, die ausgelöst wird, wenn eines der Elemente im <paramref name="tasks" />-Array freigegeben wurde.</exception>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="continuationFunction" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="continuationOptions" />-Argument einen ungültigen TaskContinuationOptions-Wert angibt.</exception>
      <exception cref="T:System.ArgumentException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array einen NULL-Wert enthält.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array leer ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}})">
      <summary>Erstellt ein Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />, das nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationAction">Der Aktionsdelegat, der ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Die Ausnahme, die ausgelöst wird, wenn eines der Elemente im <paramref name="tasks" />-Array freigegeben wurde.</exception>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="continuationAction" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array einen NULL-Wert enthält.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array leer ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
      <summary>Erstellt ein Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />, das nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationAction">Der Aktionsdelegat, der ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Die Ausnahme, die ausgelöst wird, wenn eines der Elemente im <paramref name="tasks" />-Array freigegeben wurde.- oder - Das angegebene <see cref="T:System.Threading.CancellationToken" /> wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="continuationAction" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array einen NULL-Wert enthält.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array leer ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt ein Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />, das nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationAction">Der Aktionsdelegat, der ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <param name="continuationOptions">Der <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />-Wert, der das Verhalten des erstellten Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" /> steuert.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das verwendet wird, um das erstellte Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" /> zu planen.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="continuationAction" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="scheduler" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array einen NULL-Wert enthält.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array leer ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt ein Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />, das nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationAction">Der Aktionsdelegat, der ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <param name="continuationOptions">Der <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />-Wert, der das Verhalten des erstellten Fortsetzungs-<see cref="T:System.Threading.Tasks.Task" /> steuert.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Die Ausnahme, die ausgelöst wird, wenn eines der Elemente im <paramref name="tasks" />-Array freigegeben wurde.</exception>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="continuationAction" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="continuationOptions" />-Argument einen ungültigen TaskContinuationOptions-Wert angibt.</exception>
      <exception cref="T:System.ArgumentException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array einen NULL-Wert enthält.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array leer ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1})">
      <summary>Erstellt ein Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />, das nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <typeparam name="TResult">Der Typ des Ergebnisses, das vom <paramref name="continuationFunction" />-Delegaten zurückgegeben und dem erstellten <see cref="T:System.Threading.Tasks.Task`1" />-Element zugeordnet wird.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Die Ausnahme, die ausgelöst wird, wenn eines der Elemente im <paramref name="tasks" />-Array freigegeben wurde.</exception>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="continuationFunction" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array einen NULL-Wert enthält.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array leer ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.CancellationToken)">
      <summary>Erstellt ein Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />, das nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <typeparam name="TResult">Der Typ des Ergebnisses, das vom <paramref name="continuationFunction" />-Delegaten zurückgegeben und dem erstellten <see cref="T:System.Threading.Tasks.Task`1" />-Element zugeordnet wird.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Die Ausnahme, die ausgelöst wird, wenn eines der Elemente im <paramref name="tasks" />-Array freigegeben wurde.- oder - Das angegebene <see cref="T:System.Threading.CancellationToken" /> wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="continuationFunction" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array einen NULL-Wert enthält.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array leer ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt ein Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />, das nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <param name="cancellationToken">Das <see cref="T:System.Threading.CancellationToken" />, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <param name="continuationOptions">Der <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />-Wert, der das Verhalten des erstellten Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" /> steuert.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das verwendet wird, um das erstellte Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" /> zu planen.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <typeparam name="TResult">Der Typ des Ergebnisses, das vom <paramref name="continuationFunction" />-Delegaten zurückgegeben und dem erstellten <see cref="T:System.Threading.Tasks.Task`1" />-Element zugeordnet wird.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="continuationFunction" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="scheduler" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array einen NULL-Wert enthält.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array leer ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt ein Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />, das nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <param name="continuationOptions">Der <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />-Wert, der das Verhalten des erstellten Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" /> steuert.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <typeparam name="TResult">Der Typ des Ergebnisses, das vom <paramref name="continuationFunction" />-Delegaten zurückgegeben und dem erstellten <see cref="T:System.Threading.Tasks.Task`1" />-Element zugeordnet wird.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Die Ausnahme, die ausgelöst wird, wenn eines der Elemente im <paramref name="tasks" />-Array freigegeben wurde.</exception>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="continuationFunction" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="continuationOptions" />-Argument einen ungültigen TaskContinuationOptions-Wert angibt.</exception>
      <exception cref="T:System.ArgumentException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array einen NULL-Wert enthält.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="tasks" />-Array leer ist.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.CreationOptions">
      <summary>Ruft die standardmäßigen Aufgabenerstellungsoptionen für diese Aufgabenfactory ab.</summary>
      <returns>Die standardmäßigen Aufgabenerstellungsoptionen für diese Aufgabenfactory.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task" />, das ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Der erstellte <see cref="T:System.Threading.Tasks.Task" />, der den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="beginMethod" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task" />, das ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Der erstellte <see cref="T:System.Threading.Tasks.Task" />, der den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <param name="creationOptions">Der TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task" /> steuert.</param>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="beginMethod" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``0},System.Object)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task`1" />, das ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Der erstellte <see cref="T:System.Threading.Tasks.Task`1" />, der den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="beginMethod" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task`1" />, das ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Der erstellte <see cref="T:System.Threading.Tasks.Task`1" />, der den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <param name="creationOptions">Der TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task`1" /> steuert.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="beginMethod" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Weitere Informationen finden Sie in den Hinweisen zu <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,System.Object)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task" />, das ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Der erstellte <see cref="T:System.Threading.Tasks.Task" />, der den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="arg1">Das erste an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <typeparam name="TArg1">Der Typ des ersten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="beginMethod" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task" />, das ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Der erstellte <see cref="T:System.Threading.Tasks.Task" />, der den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="arg1">Das erste an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <param name="creationOptions">Der TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task" /> steuert.</param>
      <typeparam name="TArg1">Der Typ des ersten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="beginMethod" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Weitere Informationen finden Sie in den Hinweisen zu <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``1},``0,System.Object)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task`1" />, das ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Der erstellte <see cref="T:System.Threading.Tasks.Task`1" />, der den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="arg1">Das erste an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <typeparam name="TArg1">Der Typ des ersten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="beginMethod" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``1},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task`1" />, das ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Der erstellte <see cref="T:System.Threading.Tasks.Task`1" />, der den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="arg1">Das erste an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <param name="creationOptions">Der TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task`1" /> steuert.</param>
      <typeparam name="TArg1">Der Typ des ersten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="beginMethod" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Weitere Informationen finden Sie in den Hinweisen zu <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,System.Object)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task" />, das ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Der erstellte <see cref="T:System.Threading.Tasks.Task" />, der den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="arg1">Das erste an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="arg2">Das zweite an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <typeparam name="TArg1">Der Typ des zweiten an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TArg2">Der Typ des ersten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="beginMethod" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task" />, das ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Der erstellte <see cref="T:System.Threading.Tasks.Task" />, der den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="arg1">Das erste an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="arg2">Das zweite an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <param name="creationOptions">Der TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task" /> steuert.</param>
      <typeparam name="TArg1">Der Typ des zweiten an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TArg2">Der Typ des ersten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="beginMethod" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Weitere Informationen finden Sie in den Hinweisen zu <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``2},``0,``1,System.Object)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task`1" />, das ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Der erstellte <see cref="T:System.Threading.Tasks.Task`1" />, der den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="arg1">Das erste an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="arg2">Das zweite an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <typeparam name="TArg1">Der Typ des zweiten an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TArg2">Der Typ des ersten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="beginMethod" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``2},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task`1" />, das ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Der erstellte <see cref="T:System.Threading.Tasks.Task`1" />, der den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="arg1">Das erste an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="arg2">Das zweite an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <param name="creationOptions">Der TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task`1" /> steuert.</param>
      <typeparam name="TArg1">Der Typ des zweiten an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TArg2">Der Typ des ersten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="beginMethod" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Weitere Informationen finden Sie in den Hinweisen zu <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,System.Object)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task" />, das ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Der erstellte <see cref="T:System.Threading.Tasks.Task" />, der den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="arg1">Das erste an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="arg2">Das zweite an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="arg3">Das dritte an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <typeparam name="TArg1">Der Typ des zweiten an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TArg2">Der Typ des dritten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TArg3">Der Typ des ersten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="beginMethod" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task" />, das ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Der erstellte <see cref="T:System.Threading.Tasks.Task" />, der den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="arg1">Das erste an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="arg2">Das zweite an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="arg3">Das dritte an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <param name="creationOptions">Der TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task" /> steuert.</param>
      <typeparam name="TArg1">Der Typ des zweiten an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TArg2">Der Typ des dritten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TArg3">Der Typ des ersten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="beginMethod" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Weitere Informationen finden Sie in den Hinweisen zu <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``4(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``3},``0,``1,``2,System.Object)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task`1" />, das ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Der erstellte <see cref="T:System.Threading.Tasks.Task`1" />, der den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="arg1">Das erste an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="arg2">Das zweite an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="arg3">Das dritte an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <typeparam name="TArg1">Der Typ des zweiten an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TArg2">Der Typ des dritten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TArg3">Der Typ des ersten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="beginMethod" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``4(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``3},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task`1" />, das ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Der erstellte <see cref="T:System.Threading.Tasks.Task`1" />, der den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="arg1">Das erste an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="arg2">Das zweite an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="arg3">Das dritte an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <param name="creationOptions">Der TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task`1" /> steuert.</param>
      <typeparam name="TArg1">Der Typ des zweiten an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TArg2">Der Typ des dritten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TArg3">Der Typ des ersten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="beginMethod" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Weitere Informationen finden Sie in den Hinweisen zu <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult})">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task" />-Element, das eine Endmethodenaktion ausführt, wenn ein angegebenes <see cref="T:System.IAsyncResult" />-Objekt abgeschlossen wird.</summary>
      <returns>Ein <see cref="T:System.Threading.Tasks.Task" />-Element, das den asynchronen Vorgang darstellt.</returns>
      <param name="asyncResult">Das IAsyncResult-Element, dessen Abschluss die Verarbeitung des <paramref name="endMethod" />-Elements auslösen sollte.</param>
      <param name="endMethod">Der Aktionsdelegat, der das abgeschlossene <paramref name="asyncResult" />-Objekt verarbeitet.</param>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="asyncResult" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task" />-Element, das eine Endmethodenaktion ausführt, wenn ein angegebenes <see cref="T:System.IAsyncResult" />-Objekt abgeschlossen wird.</summary>
      <returns>Ein <see cref="T:System.Threading.Tasks.Task" />-Element, das den asynchronen Vorgang darstellt.</returns>
      <param name="asyncResult">Das IAsyncResult-Element, dessen Abschluss die Verarbeitung des <paramref name="endMethod" />-Elements auslösen sollte.</param>
      <param name="endMethod">Der Aktionsdelegat, der das abgeschlossene <paramref name="asyncResult" />-Objekt verarbeitet.</param>
      <param name="creationOptions">Der TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task" /> steuert.</param>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="asyncResult" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Weitere Informationen finden Sie in den Hinweisen zu <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task" />-Element, das eine Endmethodenaktion ausführt, wenn ein angegebenes <see cref="T:System.IAsyncResult" />-Objekt abgeschlossen wird.</summary>
      <returns>Der erstellte <see cref="T:System.Threading.Tasks.Task" />, der den asynchronen Vorgang darstellt.</returns>
      <param name="asyncResult">Das IAsyncResult-Element, dessen Abschluss die Verarbeitung des <paramref name="endMethod" />-Elements auslösen sollte.</param>
      <param name="endMethod">Der Aktionsdelegat, der das abgeschlossene <paramref name="asyncResult" />-Objekt verarbeitet.</param>
      <param name="creationOptions">Der TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task" /> steuert.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />-Element, das verwendet wird, um die Aufgabe zu planen, die die Endmethode ausführt.</param>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="asyncResult" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="scheduler" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Weitere Informationen finden Sie in den Hinweisen zu <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0})">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task`1" />-Element, das eine Endmethodenfunktion ausführt, wenn ein angegebenes <see cref="T:System.IAsyncResult" />-Objekt abgeschlossen wird.</summary>
      <returns>Ein <see cref="T:System.Threading.Tasks.Task`1" />-Element, das den asynchronen Vorgang darstellt.</returns>
      <param name="asyncResult">Das IAsyncResult-Element, dessen Abschluss die Verarbeitung des <paramref name="endMethod" />-Elements auslösen sollte.</param>
      <param name="endMethod">Der Funktionsdelegat, der das abgeschlossene <paramref name="asyncResult" />-Objekt verarbeitet.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="asyncResult" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task`1" />-Element, das eine Endmethodenfunktion ausführt, wenn ein angegebenes <see cref="T:System.IAsyncResult" />-Objekt abgeschlossen wird.</summary>
      <returns>Ein <see cref="T:System.Threading.Tasks.Task`1" />-Element, das den asynchronen Vorgang darstellt.</returns>
      <param name="asyncResult">Das IAsyncResult-Element, dessen Abschluss die Verarbeitung des <paramref name="endMethod" />-Elements auslösen sollte.</param>
      <param name="endMethod">Der Funktionsdelegat, der das abgeschlossene <paramref name="asyncResult" />-Objekt verarbeitet.</param>
      <param name="creationOptions">Der TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task`1" /> steuert.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="asyncResult" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Weitere Informationen finden Sie in den Hinweisen zu <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt ein <see cref="T:System.Threading.Tasks.Task`1" />-Element, das eine Endmethodenfunktion ausführt, wenn ein angegebenes <see cref="T:System.IAsyncResult" />-Objekt abgeschlossen wird.</summary>
      <returns>Ein <see cref="T:System.Threading.Tasks.Task`1" />-Element, das den asynchronen Vorgang darstellt.</returns>
      <param name="asyncResult">Das IAsyncResult-Element, dessen Abschluss die Verarbeitung des <paramref name="endMethod" />-Elements auslösen sollte.</param>
      <param name="endMethod">Der Funktionsdelegat, der das abgeschlossene <paramref name="asyncResult" />-Objekt verarbeitet.</param>
      <param name="creationOptions">Der TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task`1" /> steuert.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />-Element, das verwendet wird, um die Aufgabe zu planen, die die Endmethode ausführt.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="asyncResult" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="endMethod" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="scheduler" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Weitere Informationen finden Sie in den Hinweisen zu <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.Scheduler">
      <summary>Ruft den standardmäßigen Aufgabenplaner für diese Aufgabenfactory ab.</summary>
      <returns>Der standardmäßige Aufgabenplaner für diese Aufgabenfactory.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action)">
      <summary>Erstellt und startet eine Aufgabe.</summary>
      <returns>Die begonnene Aufgabe.</returns>
      <param name="action">Der Aktionsdelegat, der asynchron ausgeführt werden soll.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="action" />-Argument ist Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.CancellationToken)">
      <summary>Erstellt und startet ein <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Der gestartete <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="action">Der Aktionsdelegat, der asynchron ausgeführt werden soll.</param>
      <param name="cancellationToken">Das <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />, das der neuen Aufgabe zugewiesen wird.</param>
      <exception cref="T:System.ObjectDisposedException">Das angegebene <see cref="T:System.Threading.CancellationToken" /> wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="action" />-Argument NULL ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt und startet ein <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Der gestartete <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="action">Der Aktionsdelegat, der asynchron ausgeführt werden soll.</param>
      <param name="cancellationToken">Das <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />-Element, das der neuen <see cref="T:System.Threading.Tasks.Task" /> zugewiesen wird</param>
      <param name="creationOptions">Ein TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task" />-Elements steuert</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das zum Planen des erstellten <see cref="T:System.Threading.Tasks.Task" /> verwendet wird.</param>
      <exception cref="T:System.ObjectDisposedException">Das angegebene <see cref="T:System.Threading.CancellationToken" /> wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="action" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="scheduler" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Weitere Informationen finden Sie in den Hinweisen zu <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt und startet ein <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Der gestartete <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="action">Der Aktionsdelegat, der asynchron ausgeführt werden soll.</param>
      <param name="creationOptions">Ein TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task" />-Elements steuert</param>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="action" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object)">
      <summary>Erstellt und startet ein <see cref="T:System.Threading.Tasks.Task" />. </summary>
      <returns>Der gestartete <see cref="T:System.Threading.Tasks.Task" />. </returns>
      <param name="action">Der Aktionsdelegat, der asynchron ausgeführt werden soll. </param>
      <param name="state">Ein Objekt, das vom <paramref name="action" />-Delegaten zu verwendende Daten enthält. </param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="action" />-Argument lautet null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Erstellt und startet ein <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Der gestartete <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="action">Der Aktionsdelegat, der asynchron ausgeführt werden soll.</param>
      <param name="state">Ein Objekt, das vom <paramref name="action" />-Delegaten zu verwendende Daten enthält.</param>
      <param name="cancellationToken">Das <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />-Element, das der neuen <see cref="T:System.Threading.Tasks.Task" /> zugewiesen wird</param>
      <exception cref="T:System.ObjectDisposedException">Das angegebene <see cref="T:System.Threading.CancellationToken" /> wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="action" />-Argument NULL ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt und startet ein <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Der gestartete <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="action">Der Aktionsdelegat, der asynchron ausgeführt werden soll.</param>
      <param name="state">Ein Objekt, das vom <paramref name="action" />-Delegaten zu verwendende Daten enthält.</param>
      <param name="cancellationToken">Das <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />, das der neuen Aufgabe zugewiesen wird.</param>
      <param name="creationOptions">Ein TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task" />-Elements steuert</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das zum Planen des erstellten <see cref="T:System.Threading.Tasks.Task" /> verwendet wird.</param>
      <exception cref="T:System.ObjectDisposedException">Das angegebene <see cref="T:System.Threading.CancellationToken" /> wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="action" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="scheduler" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Weitere Informationen finden Sie in den Hinweisen zu <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt und startet ein <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Der gestartete <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="action">Der Aktionsdelegat, der asynchron ausgeführt werden soll.</param>
      <param name="state">Ein Objekt, das vom <paramref name="action" />-Delegaten zu verwendende Daten enthält.</param>
      <param name="creationOptions">Ein TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task" />-Elements steuert</param>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="action" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0})">
      <summary>Erstellt und startet ein <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Der gestartete <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="function">Ein Funktionsdelegat, der das zukünftige Ergebnis zurückgibt, das mithilfe des <see cref="T:System.Threading.Tasks.Task`1" />-Elements verfügbar sein wird.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist. </typeparam>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="function" />-Argument lautet null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.CancellationToken)">
      <summary>Erstellt und startet ein <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Der gestartete <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="function">Ein Funktionsdelegat, der das zukünftige Ergebnis zurückgibt, das mithilfe des <see cref="T:System.Threading.Tasks.Task`1" />-Elements verfügbar sein wird.</param>
      <param name="cancellationToken">Das <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />-Element, das der neuen <see cref="T:System.Threading.Tasks.Task" /> zugewiesen wird</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Das angegebene <see cref="T:System.Threading.CancellationToken" /> wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="function" />-Argument NULL ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt und startet ein <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Der gestartete <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="function">Ein Funktionsdelegat, der das zukünftige Ergebnis zurückgibt, das mithilfe des <see cref="T:System.Threading.Tasks.Task`1" />-Elements verfügbar sein wird.</param>
      <param name="cancellationToken">Das <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />, das der neuen Aufgabe zugewiesen wird.</param>
      <param name="creationOptions">Ein TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task`1" />-Elements steuert.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das zum Planen des erstellten <see cref="T:System.Threading.Tasks.Task`1" /> verwendet wird.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Das angegebene <see cref="T:System.Threading.CancellationToken" /> wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="function" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="scheduler" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Weitere Informationen finden Sie in den Hinweisen zu <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt und startet ein <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Der gestartete <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="function">Ein Funktionsdelegat, der das zukünftige Ergebnis zurückgibt, das mithilfe des <see cref="T:System.Threading.Tasks.Task`1" />-Elements verfügbar sein wird.</param>
      <param name="creationOptions">Ein TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task`1" />-Elements steuert.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="function" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Weitere Informationen finden Sie in den Hinweisen zu <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object)">
      <summary>Erstellt und startet ein <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Der gestartete <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="function">Ein Funktionsdelegat, der das zukünftige Ergebnis zurückgibt, das mithilfe des <see cref="T:System.Threading.Tasks.Task`1" />-Elements verfügbar sein wird.</param>
      <param name="state">Ein Objekt, das vom <paramref name="function" />-Delegaten zu verwendende Daten enthält.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="function" />-Argument NULL ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>Erstellt und startet ein <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Der gestartete <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="function">Ein Funktionsdelegat, der das zukünftige Ergebnis zurückgibt, das mithilfe des <see cref="T:System.Threading.Tasks.Task`1" />-Elements verfügbar sein wird.</param>
      <param name="state">Ein Objekt, das vom <paramref name="function" />-Delegaten zu verwendende Daten enthält.</param>
      <param name="cancellationToken">Das <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />-Element, das der neuen <see cref="T:System.Threading.Tasks.Task" /> zugewiesen wird</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Das angegebene <see cref="T:System.Threading.CancellationToken" /> wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="function" />-Argument NULL ist.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt und startet ein <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Der gestartete <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="function">Ein Funktionsdelegat, der das zukünftige Ergebnis zurückgibt, das mithilfe des <see cref="T:System.Threading.Tasks.Task`1" />-Elements verfügbar sein wird.</param>
      <param name="state">Ein Objekt, das vom <paramref name="function" />-Delegaten zu verwendende Daten enthält.</param>
      <param name="cancellationToken">Das <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />, das der neuen Aufgabe zugewiesen wird.</param>
      <param name="creationOptions">Ein TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task`1" />-Elements steuert.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das zum Planen des erstellten <see cref="T:System.Threading.Tasks.Task`1" /> verwendet wird.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Das angegebene <see cref="T:System.Threading.CancellationToken" /> wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="function" />-Argument NULL ist.- oder - Diese Ausnahme wird ausgelöst, wenn das <paramref name="scheduler" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Weitere Informationen finden Sie in den Hinweisen zu <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt und startet ein <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Der gestartete <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="function">Ein Funktionsdelegat, der das zukünftige Ergebnis zurückgibt, das mithilfe des <see cref="T:System.Threading.Tasks.Task`1" />-Elements verfügbar sein wird.</param>
      <param name="state">Ein Objekt, das vom <paramref name="function" />-Delegaten zu verwendende Daten enthält.</param>
      <param name="creationOptions">Ein TaskCreationOptions-Wert, der das Verhalten des erstellten <see cref="T:System.Threading.Tasks.Task`1" />-Elements steuert.</param>
      <typeparam name="TResult">Der Typ des Ergebnisses, das über das <see cref="T:System.Threading.Tasks.Task`1" />-Element verfügbar ist.</typeparam>
      <exception cref="T:System.ArgumentNullException">Diese Ausnahme wird ausgelöst, wenn das <paramref name="function" />-Argument NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Die Ausnahme, die ausgelöst wird, wenn das <paramref name="creationOptions" />-Argument einen ungültigen TaskCreationOptions-Wert angibt.Weitere Informationen finden Sie in den Hinweisen zu <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskFactory`1">
      <summary>Bietet Unterstützung für das Erstellen und Planen von <see cref="T:System.Threading.Tasks.Task`1" />-Objekten.</summary>
      <typeparam name="TResult">Der Rückgabewert der <see cref="T:System.Threading.Tasks.Task`1" />-Objekte, die von den Methoden dieser Klasse erstellt werden. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor">
      <summary>Initialisiert eine <see cref="T:System.Threading.Tasks.TaskFactory`1" />-Instanz mit der Standardkonfiguration.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.CancellationToken)">
      <summary>Initialisiert eine <see cref="T:System.Threading.Tasks.TaskFactory`1" />-Instanz mit der Standardkonfiguration.</summary>
      <param name="cancellationToken">Das Standardabbruchtoken, das von dieser <see cref="T:System.Threading.Tasks.TaskFactory" /> erstellten Aufgaben zugewiesen wird, sofern beim Aufrufen der Factorymethoden kein anderes Abbruchtoken explizit angegeben wird.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Initialisiert eine <see cref="T:System.Threading.Tasks.TaskFactory`1" />-Instanz mit der angegebenen Konfiguration.</summary>
      <param name="cancellationToken">Das Standardabbruchtoken, das von dieser <see cref="T:System.Threading.Tasks.TaskFactory" /> erstellten Aufgaben zugewiesen wird, sofern beim Aufrufen der Factorymethoden kein anderes Abbruchtoken explizit angegeben wird.</param>
      <param name="creationOptions">Die zum Erstellen von Aufgaben mit dieser <see cref="T:System.Threading.Tasks.TaskFactory`1" /> zu verwendenden Standardoptionen.</param>
      <param name="continuationOptions">Die zum Erstellen von Fortsetzungsaufgaben mit dieser <see cref="T:System.Threading.Tasks.TaskFactory`1" /> zu verwendenden Standardoptionen.</param>
      <param name="scheduler">Der beim Planen von Aufgaben, die mit dieser <see cref="T:System.Threading.Tasks.TaskFactory`1" /> erstellt wurden, zu verwendende Standardplaner.Ein NULL-Wert gibt an, dass <see cref="P:System.Threading.Tasks.TaskScheduler.Current" /> verwendet werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> oder <paramref name="continuationOptions" /> gibt einen ungültigen Wert an.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Initialisiert eine <see cref="T:System.Threading.Tasks.TaskFactory`1" />-Instanz mit der angegebenen Konfiguration.</summary>
      <param name="creationOptions">Die zum Erstellen von Aufgaben mit dieser <see cref="T:System.Threading.Tasks.TaskFactory`1" /> zu verwendenden Standardoptionen.</param>
      <param name="continuationOptions">Die zum Erstellen von Fortsetzungsaufgaben mit dieser <see cref="T:System.Threading.Tasks.TaskFactory`1" /> zu verwendenden Standardoptionen.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> oder <paramref name="continuationOptions" /> gibt einen ungültigen Wert an.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>Initialisiert eine <see cref="T:System.Threading.Tasks.TaskFactory`1" />-Instanz mit der angegebenen Konfiguration.</summary>
      <param name="scheduler">Der zum Planen von Aufgaben, die mit dieser <see cref="T:System.Threading.Tasks.TaskFactory`1" /> erstellt wurden, zu verwendende Planer.Ein NULL-Wert gibt an, dass der aktuelle <see cref="T:System.Threading.Tasks.TaskScheduler" /> verwendet werden soll.</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.CancellationToken">
      <summary>Ruft das Standardabbruchtoken für diese Aufgabenfactory ab.</summary>
      <returns>Das Standardabbruchtoken für diese Aufgabenfactory.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.ContinuationOptions">
      <summary>Ruft den <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />-Enumerationswert für diese Aufgabenfactory ab.</summary>
      <returns>Einer der Enumerationswerte, die die Standardfortsetzungsoptionen für diese Aufgabenfactory angibt.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0})">
      <summary>Erstellt eine Fortsetzungsaufgabe, die nach dem Abschluss eines Satzes angegebener Aufgaben gestartet wird.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <exception cref="T:System.ObjectDisposedException">Eines der Elemente im <paramref name="tasks" />-Array wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" />-Array ist null.- oder - <paramref name="continuationFunction" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array enthält einen NULL-Wert oder ist leer.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.CancellationToken)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die nach dem Abschluss eines Satzes angegebener Aufgaben gestartet wird.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <exception cref="T:System.ObjectDisposedException">Eines der Elemente im <paramref name="tasks" />-Array wurde freigegeben.- oder - Die <see cref="T:System.Threading.CancellationTokenSource" /> erstellten<paramref name=" cancellationToken" /> bereits freigegeben wurde.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - <paramref name="continuationFunction" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array enthält einen NULL-Wert oder ist leer.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die nach dem Abschluss eines Satzes angegebener Aufgaben gestartet wird.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <param name="continuationOptions">Einer der Enumerationswerte, der das Verhalten der erstellten Fortsetzungsaufgabe steuert.Die Werte NotOn* oder OnlyOn* sind nicht gültig.</param>
      <param name="scheduler">Der Planer, der verwendet wird, um die erstellte Fortsetzungsaufgabe zu planen.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.- oder - Das <paramref name="scheduler" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array enthält einen NULL-Wert oder ist leer.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> gibt einen ungültigen Wert an.</exception>
      <exception cref="T:System.ObjectDisposedException">Eines der Elemente im <paramref name="tasks" />-Array wurde freigegeben.- oder - Die <see cref="T:System.Threading.CancellationTokenSource" /> erstellten<paramref name=" cancellationToken" /> bereits freigegeben wurde.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die nach dem Abschluss eines Satzes angegebener Aufgaben gestartet wird.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <param name="continuationOptions">Einer der Enumerationswerte, der das Verhalten der erstellten Fortsetzungsaufgabe steuert.Die Werte NotOn* oder OnlyOn* sind nicht gültig.</param>
      <exception cref="T:System.ObjectDisposedException">Eines der Elemente im <paramref name="tasks" />-Array wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Das <paramref name="continuationOptions" />-Argument gibt einen ungültigen Wert an.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array enthält einen NULL-Wert oder ist leer.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0})">
      <summary>Erstellt eine Fortsetzungsaufgabe, die nach dem Abschluss eines Satzes angegebener Aufgaben gestartet wird.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Eines der Elemente im <paramref name="tasks" />-Array wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array enthält einen NULL-Wert oder ist leer.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.CancellationToken)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die nach dem Abschluss eines Satzes angegebener Aufgaben gestartet wird.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Eines der Elemente im <paramref name="tasks" />-Array wurde freigegeben.- oder - Die <see cref="T:System.Threading.CancellationTokenSource" /> erstellten<paramref name=" cancellationToken" /> bereits freigegeben wurde.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array enthält einen NULL-Wert oder ist leer.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die nach dem Abschluss eines Satzes angegebener Aufgaben gestartet wird.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <param name="continuationOptions">Einer der Enumerationswerte, der das Verhalten der erstellten Fortsetzungsaufgabe steuert.Die Werte NotOn* oder OnlyOn* sind nicht gültig.</param>
      <param name="scheduler">Der Planer, der verwendet wird, um die erstellte Fortsetzungsaufgabe zu planen.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.- oder - Das <paramref name="scheduler" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array enthält einen NULL-Wert oder ist leer.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Das <paramref name="continuationOptions" />-Argument gibt einen ungültigen Wert an.</exception>
      <exception cref="T:System.ObjectDisposedException">Eines der Elemente im <paramref name="tasks" />-Array wurde freigegeben.- oder - Die <see cref="T:System.Threading.CancellationTokenSource" /> erstellten<paramref name=" cancellationToken" /> bereits freigegeben wurde.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die nach dem Abschluss eines Satzes angegebener Aufgaben gestartet wird.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn alle Aufgaben im <paramref name="tasks" />-Array abgeschlossen wurden.</param>
      <param name="continuationOptions">Einer der Enumerationswerte, der das Verhalten der erstellten Fortsetzungsaufgabe steuert.Die Werte NotOn* oder OnlyOn* sind nicht gültig.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Eines der Elemente im <paramref name="tasks" />-Array wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Das <paramref name="continuationOptions" />-Argument gibt einen ungültigen Wert an.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array enthält einen NULL-Wert oder ist leer.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0})">
      <summary>Erstellt eine Fortsetzungsaufgabe, die nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird. </summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <exception cref="T:System.ObjectDisposedException">Eines der Elemente im <paramref name="tasks" />-Array wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array enthält einen NULL-Wert oder ist leer.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.CancellationToken)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <exception cref="T:System.ObjectDisposedException">Eines der Elemente im <paramref name="tasks" />-Array wurde freigegeben.- oder - Die <see cref="T:System.Threading.CancellationTokenSource" /> erstellten<paramref name=" cancellationToken" /> bereits freigegeben wurde.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist NULL.- oder - Das <paramref name="continuationFunction" />-Argument ist Null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array enthält einen NULL-Wert.- oder - Die <paramref name="tasks" /> Array ist leer.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <param name="continuationOptions">Einer der Enumerationswerte, der das Verhalten der erstellten Fortsetzungsaufgabe steuert.Die Werte NotOn* oder OnlyOn* sind nicht gültig.</param>
      <param name="scheduler">Der Aufgabenplaner, der verwendet wird, um die erstellte Fortsetzungsaufgabe zu planen.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.- oder - Das <paramref name="scheduler" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array enthält einen NULL-Wert.- oder - Die <paramref name="tasks" /> Array ist leer.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Das <paramref name="continuationOptions" />-Argument gibt einen ungültigen <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />-Wert an.</exception>
      <exception cref="T:System.ObjectDisposedException">Eines der Elemente im <paramref name="tasks" />-Array wurde freigegeben.- oder - Die <see cref="T:System.Threading.CancellationTokenSource" /> erstellten<paramref name=" cancellationToken" /> bereits freigegeben wurde. </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <param name="continuationOptions">Einer der Enumerationswerte, der das Verhalten der erstellten Fortsetzungsaufgabe steuert.Die Werte NotOn* oder OnlyOn* sind nicht gültig.</param>
      <exception cref="T:System.ObjectDisposedException">Eines der Elemente im <paramref name="tasks" />-Array wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Vom <paramref name="continuationOptions" />-Argument gibt einen ungültigen Enumerationswert an.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array enthält einen NULL-Wert.- oder - Die <paramref name="tasks" /> Array ist leer.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0})">
      <summary>Erstellt eine Fortsetzungsaufgabe, die nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Eines der Elemente im <paramref name="tasks" />-Array wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array enthält einen NULL-Wert.- oder - Die <paramref name="tasks" /> Array ist leer.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.CancellationToken)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Die neue Fortsetzungsaufgabe.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Eines der Elemente im <paramref name="tasks" />-Array wurde freigegeben.- oder - Die <see cref="T:System.Threading.CancellationTokenSource" /> erstellten<paramref name=" cancellationToken" /> bereits freigegeben wurde.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array enthält einen NULL-Wert.- oder - Die <paramref name="tasks" /> Array ist leer.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Fortsetzungsaufgabe zugewiesen wird.</param>
      <param name="continuationOptions">Einer der Enumerationswerte, der das Verhalten der erstellten Fortsetzungsaufgabe steuert.Die Werte NotOn* oder OnlyOn* sind nicht gültig.</param>
      <param name="scheduler">Das <see cref="T:System.Threading.Tasks.TaskScheduler" />, das verwendet wird, um das erstellte Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" /> zu planen.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.- oder - Das <paramref name="scheduler" />-Argument ist Null.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array enthält einen NULL-Wert.- oder - Die <paramref name="tasks" /> Array ist leer.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Vom <paramref name="continuationOptions" />-Argument wird ein ungültiger TaskContinuationOptions-Wert angegeben.</exception>
      <exception cref="T:System.ObjectDisposedException">Eines der Elemente im <paramref name="tasks" />-Array wurde freigegeben.- oder - Die <see cref="T:System.Threading.CancellationTokenSource" /> erstellten<paramref name=" cancellationToken" /> bereits freigegeben wurde. </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Erstellt eine Fortsetzungsaufgabe, die nach dem Abschluss einer Aufgabe im bereitgestellten Satz gestartet wird.</summary>
      <returns>Der neue Fortsetzungs-<see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="tasks">Das Array von Aufgaben, mit denen fortgefahren wird, wenn eine Aufgabe abgeschlossen ist.</param>
      <param name="continuationFunction">Der Funktionsdelegat, der asynchron ausgeführt werden soll, wenn eine Aufgabe im <paramref name="tasks" />-Array abgeschlossen wird.</param>
      <param name="continuationOptions">Einer der Enumerationswerte, der das Verhalten der erstellten Fortsetzungsaufgabe steuert.Die Werte NotOn* oder OnlyOn* sind nicht gültig.</param>
      <typeparam name="TAntecedentResult">Der Ergebnistyp des vorangehenden <paramref name="tasks" />-Elements.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Eines der Elemente im <paramref name="tasks" />-Array wurde freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="tasks" />-Array ist null.- oder - Das <paramref name="continuationFunction" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Vom <paramref name="continuationOptions" />-Argument gibt einen ungültigen Enumerationswert an.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="tasks" />-Array enthält einen NULL-Wert.- oder - Die <paramref name="tasks" /> Array ist leer.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.CreationOptions">
      <summary>Ruft den <see cref="T:System.Threading.Tasks.TaskCreationOptions" />-Enumerationswert für diese Aufgabenfactory ab.</summary>
      <returns>Einer der Enumerationswerte, die die Standarderstellungsoptionen für diese Aufgabenfactory angibt.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},System.Object)">
      <summary>Erstellt eine Aufgabe, die ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Die erstellte Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="beginMethod" />-Argument lautet null.- oder - Das <paramref name="endMethod" />-Argument lautet null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt eine Aufgabe, die ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Der erstellte <see cref="T:System.Threading.Tasks.Task`1" />, der den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <param name="creationOptions">Einer der Enumerationswerte, der das Verhalten der erstellten Aufgabe steuert.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="beginMethod" />-Argument lautet null.- oder - Das <paramref name="endMethod" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Das <paramref name="creationOptions" />-Argument gibt einen ungültigen Wert an.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,System.Object)">
      <summary>Erstellt eine Aufgabe, die ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Die erstellte Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="arg1">Das erste an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <typeparam name="TArg1">Der Typ des ersten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="beginMethod" />-Argument lautet null.- oder - Das <paramref name="endMethod" />-Argument lautet null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt eine Aufgabe, die ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Die erstellte Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="arg1">Das erste an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <param name="creationOptions">Einer der Enumerationswerte, der das Verhalten der erstellten Aufgabe steuert.</param>
      <typeparam name="TArg1">Der Typ des ersten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="beginMethod" />-Argument lautet null.- oder - Das <paramref name="endMethod" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Parameter <paramref name="creationOptions" /> gibt einen ungültigen Wert an.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,System.Object)">
      <summary>Erstellt eine Aufgabe, die ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Die erstellte Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="arg1">Das erste an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="arg2">Das zweite an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <typeparam name="TArg1">Der Typ des zweiten an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TArg2">Der Typ des ersten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="beginMethod" />-Argument lautet null.- oder - Das <paramref name="endMethod" />-Argument lautet null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt eine Aufgabe, die ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Die erstellte Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="arg1">Das erste an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="arg2">Das zweite an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <param name="creationOptions">Ein Objekt, das das Verhalten der erstellten <see cref="T:System.Threading.Tasks.Task`1" /> steuert.</param>
      <typeparam name="TArg1">Der Typ des zweiten an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TArg2">Der Typ des ersten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="beginMethod" />-Argument lautet null.- oder - Das <paramref name="endMethod" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Parameter <paramref name="creationOptions" /> gibt einen ungültigen Wert an.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,System.Object)">
      <summary>Erstellt eine Aufgabe, die ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Die erstellte Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="arg1">Das erste an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="arg2">Das zweite an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="arg3">Das dritte an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <typeparam name="TArg1">Der Typ des zweiten an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TArg2">Der Typ des dritten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TArg3">Der Typ des ersten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="beginMethod" />-Argument lautet null.- oder - Das <paramref name="endMethod" />-Argument lautet null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt eine Aufgabe, die ein dem asynchronen Programmiermodellmuster entsprechendes Paar von Begin- und End-Methoden darstellt.</summary>
      <returns>Die erstellte Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <param name="beginMethod">Der Delegat, der den asynchronen Vorgang startet.</param>
      <param name="endMethod">Der Delegat, der den asynchronen Vorgang beendet.</param>
      <param name="arg1">Das erste an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="arg2">Das zweite an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="arg3">Das dritte an den <paramref name="beginMethod" />-Delegaten übergebene Argument.</param>
      <param name="state">Ein Objekt, das vom <paramref name="beginMethod" />-Delegaten zu verwendende Daten enthält.</param>
      <param name="creationOptions">Ein Objekt, das das Verhalten der erstellten Aufgabe steuert.</param>
      <typeparam name="TArg1">Der Typ des zweiten an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TArg2">Der Typ des dritten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <typeparam name="TArg3">Der Typ des ersten, an den <paramref name="beginMethod" />-Delegaten übergebenen Arguments.</typeparam>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="beginMethod" />-Argument lautet null.- oder - Das <paramref name="endMethod" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Parameter <paramref name="creationOptions" /> gibt einen ungültigen Wert an.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0})">
      <summary>Erstellt eine Aufgabe, die eine EndMethod-Funktion ausführt, wenn ein angegebenes <see cref="T:System.IAsyncResult" /> abgeschlossen wird.</summary>
      <returns>Ein <see cref="T:System.Threading.Tasks.Task`1" />-Element, das den asynchronen Vorgang darstellt.</returns>
      <param name="asyncResult">Das <see cref="T:System.IAsyncResult" />, dessen Abschluss die Verarbeitung der <paramref name="endMethod" /> auslösen sollte.</param>
      <param name="endMethod">Der Funktionsdelegat, der das abgeschlossene <paramref name="asyncResult" />-Objekt verarbeitet.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="asyncResult" />-Argument lautet null.- oder - Das <paramref name="endMethod" />-Argument lautet null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt eine Aufgabe, die eine EndMethod-Funktion ausführt, wenn ein angegebenes <see cref="T:System.IAsyncResult" /> abgeschlossen wird.</summary>
      <returns>Eine Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <param name="asyncResult">Das <see cref="T:System.IAsyncResult" />, dessen Abschluss die Verarbeitung der <paramref name="endMethod" /> auslösen sollte.</param>
      <param name="endMethod">Der Funktionsdelegat, der das abgeschlossene <paramref name="asyncResult" />-Objekt verarbeitet.</param>
      <param name="creationOptions">Einer der Enumerationswerte, der das Verhalten der erstellten Aufgabe steuert.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="asyncResult" />-Argument lautet null.- oder - Das <paramref name="endMethod" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Das <paramref name="creationOptions" />-Argument gibt einen ungültigen Wert an.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt eine Aufgabe, die eine EndMethod-Funktion ausführt, wenn ein angegebenes <see cref="T:System.IAsyncResult" /> abgeschlossen wird.</summary>
      <returns>Die erstellte Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <param name="asyncResult">Das <see cref="T:System.IAsyncResult" />, dessen Abschluss die Verarbeitung der <paramref name="endMethod" /> auslösen sollte.</param>
      <param name="endMethod">Der Funktionsdelegat, der das abgeschlossene <paramref name="asyncResult" />-Objekt verarbeitet.</param>
      <param name="creationOptions">Einer der Enumerationswerte, der das Verhalten der erstellten Aufgabe steuert.</param>
      <param name="scheduler">Der Aufgabenplaner, der verwendet wird, um die Aufgabe zu planen, die EndMethod ausführt.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="asyncResult" />-Argument lautet null.- oder - Das <paramref name="endMethod" />-Argument lautet null.- oder - Das <paramref name="scheduler" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Parameter <paramref name="creationOptions" /> gibt einen ungültigen Wert an.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.Scheduler">
      <summary>Ruft das Aufgabenplaner für diese Aufgabenfactory ab.</summary>
      <returns>Der Aufgabenplaner für diese Aufgabenfactory.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0})">
      <summary>Erstellt und startet eine Aufgabe.</summary>
      <returns>Die begonnene Aufgabe.</returns>
      <param name="function">Ein Funktionsdelegat, der das zukünftige Ergebnis zurückgibt, das mithilfe der Aufgabe verfügbar sein wird.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="function" />-Argument lautet null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.CancellationToken)">
      <summary>Erstellt und startet eine Aufgabe.</summary>
      <returns>Die begonnene Aufgabe.</returns>
      <param name="function">Ein Funktionsdelegat, der das zukünftige Ergebnis zurückgibt, das mithilfe der Aufgabe verfügbar sein wird.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Aufgabe zugewiesen wird.</param>
      <exception cref="T:System.ObjectDisposedException">Die Abbruchtokenquelle, die <paramref name="cancellationToken" /> erstellt hat, wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="function" />-Argument lautet null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt und startet eine Aufgabe.</summary>
      <returns>Die begonnene Aufgabe.</returns>
      <param name="function">Ein Funktionsdelegat, der das zukünftige Ergebnis zurückgibt, das mithilfe der Aufgabe verfügbar sein wird.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Aufgabe zugewiesen wird.</param>
      <param name="creationOptions">Einer der Enumerationswerte, der das Verhalten der erstellten Aufgabe steuert.</param>
      <param name="scheduler">Der Aufgabenplaner, der verwendet wird, um die erstellte Fortsetzungsaufgabe zu planen.</param>
      <exception cref="T:System.ObjectDisposedException">Die Abbruchtokenquelle, die <paramref name="cancellationToken" /> erstellt hat, wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="function" />-Argument lautet null.- oder - Das <paramref name="scheduler" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Parameter <paramref name="creationOptions" /> gibt einen ungültigen Wert an.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt und startet eine Aufgabe.</summary>
      <returns>Der gestartete <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="function">Ein Funktionsdelegat, der das zukünftige Ergebnis zurückgibt, das mithilfe der Aufgabe verfügbar sein wird.</param>
      <param name="creationOptions">Einer der Enumerationswerte, der das Verhalten der erstellten Aufgabe steuert.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="function" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Parameter <paramref name="creationOptions" /> gibt einen ungültigen Wert an.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object)">
      <summary>Erstellt und startet eine Aufgabe.</summary>
      <returns>Die begonnene Aufgabe.</returns>
      <param name="function">Ein Funktionsdelegat, der das zukünftige Ergebnis zurückgibt, das mithilfe der Aufgabe verfügbar sein wird.</param>
      <param name="state">Ein Objekt, das die vom <paramref name="function" />-Delegaten zu verwendenden Daten enthält.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="function" />-Argument lautet null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken)">
      <summary>Erstellt und startet eine Aufgabe.</summary>
      <returns>Die begonnene Aufgabe.</returns>
      <param name="function">Ein Funktionsdelegat, der das zukünftige Ergebnis zurückgibt, das mithilfe der Aufgabe verfügbar sein wird.</param>
      <param name="state">Ein Objekt, das die vom <paramref name="function" />-Delegaten zu verwendenden Daten enthält.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Aufgabe zugewiesen wird.</param>
      <exception cref="T:System.ObjectDisposedException">Die Abbruchtokenquelle, die <paramref name="cancellationToken" /> erstellt hat, wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="function" />-Argument lautet null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Erstellt und startet eine Aufgabe.</summary>
      <returns>Die begonnene Aufgabe.</returns>
      <param name="function">Ein Funktionsdelegat, der das zukünftige Ergebnis zurückgibt, das mithilfe der Aufgabe verfügbar sein wird.</param>
      <param name="state">Ein Objekt, das die vom <paramref name="function" />-Delegaten zu verwendenden Daten enthält.</param>
      <param name="cancellationToken">Das Abbruchtoken, das der neuen Aufgabe zugewiesen wird.</param>
      <param name="creationOptions">Einer der Enumerationswerte, der das Verhalten der erstellten Aufgabe steuert.</param>
      <param name="scheduler">Der Aufgabenplaner, der verwendet wird, um die erstellte Fortsetzungsaufgabe zu planen.</param>
      <exception cref="T:System.ObjectDisposedException">Die Abbruchtokenquelle, die <paramref name="cancellationToken" /> erstellt hat, wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="function" />-Argument lautet null.- oder - Das <paramref name="scheduler" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Parameter <paramref name="creationOptions" /> gibt einen ungültigen Wert an.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Erstellt und startet eine Aufgabe.</summary>
      <returns>Die begonnene Aufgabe.</returns>
      <param name="function">Ein Funktionsdelegat, der das zukünftige Ergebnis zurückgibt, das mithilfe der Aufgabe verfügbar sein wird.</param>
      <param name="state">Ein Objekt, das die vom <paramref name="function" />-Delegaten zu verwendenden Daten enthält.</param>
      <param name="creationOptions">Einer der Enumerationswerte, der das Verhalten der erstellten Aufgabe steuert.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="function" />-Argument lautet null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Parameter <paramref name="creationOptions" /> gibt einen ungültigen Wert an.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskScheduler">
      <summary>Stellt ein Objekt dar, das das Einfügen von Aufgaben in Threadwarteschlangen auf niedriger Ebene behandelt.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.#ctor">
      <summary>Initialisiert den <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Current">
      <summary>Ruft den zugeordneten <see cref="T:System.Threading.Tasks.TaskScheduler" /> der derzeit ausgeführten Aufgabe ab.</summary>
      <returns>Gibt den zugeordneten <see cref="T:System.Threading.Tasks.TaskScheduler" /> der momentan ausgeführten Aufgabe zurück.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Default">
      <summary>Ruft die <see cref="T:System.Threading.Tasks.TaskScheduler" />-Standardinstanz ab, die von .NET Framework bereitgestellt wird.</summary>
      <returns>Gibt die <see cref="T:System.Threading.Tasks.TaskScheduler" />-Standardinstanz zurück.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.FromCurrentSynchronizationContext">
      <summary>Erstellt einen <see cref="T:System.Threading.Tasks.TaskScheduler" /> mit dem aktuellen <see cref="T:System.Threading.SynchronizationContext" />.</summary>
      <returns>Ein <see cref="T:System.Threading.Tasks.TaskScheduler" />, der dem aktuellen <see cref="T:System.Threading.SynchronizationContext" /> entsprechend <see cref="P:System.Threading.SynchronizationContext.Current" /> zugeordnet ist.</returns>
      <exception cref="T:System.InvalidOperationException">Der aktuelle SynchronizationContext kann nicht als TaskScheduler verwendet werden.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.GetScheduledTasks">
      <summary>Nur für Debuggerunterstützung: Generiert eine Enumeration von <see cref="T:System.Threading.Tasks.Task" />-Instanzen, die sich gegenwärtig in der Warteschlange des Taskplaners befinden und auf ihre Ausführung warten.</summary>
      <returns>Eine Aufzählung, in der von einem Debugger die gegenwärtig in der Warteschlange dieses Taskplaners enthaltenen Aufgaben durchsucht werden können.</returns>
      <exception cref="T:System.NotSupportedException">Dieser Planer kann derzeit keine Liste in der Warteschlange stehender Aufgaben generieren.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Id">
      <summary>Ruft die eindeutige ID für diesen <see cref="T:System.Threading.Tasks.TaskScheduler" /> ab.</summary>
      <returns>Gibt die eindeutige ID für diesen <see cref="T:System.Threading.Tasks.TaskScheduler" /> zurück.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.MaximumConcurrencyLevel">
      <summary>Gibt die maximale Parallelitätsebene an, die dieser <see cref="T:System.Threading.Tasks.TaskScheduler" /> unterstützt.</summary>
      <returns>Gibt eine ganze Zahl zurück, die die maximale Parallelitätsebene darstellt.Der Standardplaner gibt <see cref="F:System.Int32.MaxValue" /> zurück.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.QueueTask(System.Threading.Tasks.Task)">
      <summary>Fügt einen <see cref="T:System.Threading.Tasks.Task" /> in die Warteschlange des Planers ein. </summary>
      <param name="task">Der <see cref="T:System.Threading.Tasks.Task" />, der in die Warteschlange eingereiht werden soll.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="task" />-Argument ist Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryDequeue(System.Threading.Tasks.Task)">
      <summary>Versucht, einen zuvor in die Warteschlange dieses Planers eingereihten <see cref="T:System.Threading.Tasks.Task" /> aus der Warteschlange zu entfernen.</summary>
      <returns>Ein boolescher Wert, der angibt, ob das <paramref name="task" />-Argument erfolgreich aus der Warteschlange entfernt wurde.</returns>
      <param name="task">Das <see cref="T:System.Threading.Tasks.Task" />, das aus der Warteschlange entfernt werden soll.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="task" />-Argument ist Null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryExecuteTask(System.Threading.Tasks.Task)">
      <summary>Versucht, den angegebenen <see cref="T:System.Threading.Tasks.Task" /> mit diesem Planer auszuführen.</summary>
      <returns>Ein boolescher Wert, der true ist, wenn <paramref name="task" /> erfolgreich ausgeführt wurde. Andernfalls ist dieser Wert false.Ein häufiger Grund für Ausführungsfehler besteht darin, dass die Aufgabe zuvor ausgeführt wurde oder gerade von einem anderen Thread ausgeführt wird.</returns>
      <param name="task">Ein auszuführendes <see cref="T:System.Threading.Tasks.Task" />-Objekt.</param>
      <exception cref="T:System.InvalidOperationException">Der <paramref name="task" /> ist nicht diesem Planer zugeordnet.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)">
      <summary>Bestimmt, ob der angegebene <see cref="T:System.Threading.Tasks.Task" /> in diesem Aufruf synchron ausgeführt werden kann, und führt ihn aus, wenn dies der Fall ist.</summary>
      <returns>Ein boolescher Wert, der angibt, ob die Aufgabe inline ausgeführt wurde.</returns>
      <param name="task">Das auszuführende <see cref="T:System.Threading.Tasks.Task" />.</param>
      <param name="taskWasPreviouslyQueued">Ein boolescher Wert, der angibt, ob die Aufgabe zuvor in die Warteschlange eingereiht wurde.Wenn dieser Parameter true ist, wurde die Aufgabe möglicherweise zuvor in die Warteschlange eingereiht (geplant). Ist er false, wurde die Aufgabe bekanntermaßen nicht in die Warteschlange eingereiht, und dieser Aufruf wird ausgeführt, um die Aufgabe inline auszuführen, ohne sie in die Warteschlange einzureihen.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="task" />-Argument ist Null.</exception>
      <exception cref="T:System.InvalidOperationException">Der <paramref name="task" /> wurde bereits ausgeführt.</exception>
    </member>
    <member name="E:System.Threading.Tasks.TaskScheduler.UnobservedTaskException">
      <summary>Tritt auf, wenn die unüberwachte Ausnahme einer fehlgeschlagenen Aufgabe im Begriff ist, die Ausnahmeausweitungsrichtlinie auszulösen. Durch die Ausweitungsrichtlinie wird der Prozess standardmäßig beendet.</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskSchedulerException">
      <summary>Stellt eine Ausnahme dar, die verwendet wird, um einen ungültigen Vorgang eines <see cref="T:System.Threading.Tasks.TaskScheduler" /> zu übermitteln.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Tasks.TaskSchedulerException" />-Klasse mit einer vom System generierten Meldung, die den Fehler beschreibt.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Tasks.TaskSchedulerException" />-Klasse unter Verwendung einer Standardfehlermeldung und eines Verweises auf die interne Ausnahme, die die Ursache dieser Ausnahme ist.</summary>
      <param name="innerException">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Tasks.TaskSchedulerException" />-Klasse mit einer angegebenen Meldung, die den Fehler beschreibt.</summary>
      <param name="message">Die Meldung, in der die Ausnahme beschrieben wirdDer Aufrufer dieses Konstruktors muss sicherstellen, dass diese Zeichenfolge für die aktuelle Systemkultur lokalisiert wurde.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Tasks.TaskSchedulerException" />-Klasse mit einer angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Meldung, in der die Ausnahme beschrieben wirdDer Aufrufer dieses Konstruktors muss sicherstellen, dass diese Zeichenfolge für die aktuelle Systemkultur lokalisiert wurde.</param>
      <param name="innerException">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="innerException" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="T:System.Threading.Tasks.TaskStatus">
      <summary>Stellt die aktuelle Phase im Lebenszyklus eines <see cref="T:System.Threading.Tasks.Task" /> dar.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Canceled">
      <summary>Die Aufgabe hat den Abbruch durch Auslösen einer OperationCanceledException mit einem eigenen CancellationToken bestätigt, während das Token im Zustand "signalisiert" war, oder das CancellationToken der Aufgabe wurde bereits vor dem Start der Aufgabenausführung signalisiert.Weitere Informationen finden Sie unter Aufgabenabbruch.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Created">
      <summary>Die Aufgabe wurde initialisiert, aber noch nicht geplant.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Faulted">
      <summary>Die Aufgabe wurde aufgrund eines Ausnahmefehlers abgeschlossen.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.RanToCompletion">
      <summary>Die Ausführung der Aufgabe wurde erfolgreich abgeschlossen.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Running">
      <summary>Die Aufgabe wird ausgeführt, wurde aber noch nicht abgeschlossen.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingForActivation">
      <summary>Die Aufgabe wartet auf ihre Aktivierung und interne Planung durch die .NET Framework-Infrastruktur.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingForChildrenToComplete">
      <summary>Die Aufgabe hat die Ausführung beendet und wartet implizit auf den Abschluss angefügter untergeordneter Aufgaben.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingToRun">
      <summary>Die Aufgabe wurde zur Ausführung geplant, aber noch nicht gestartet.</summary>
    </member>
    <member name="T:System.Threading.Tasks.UnobservedTaskExceptionEventArgs">
      <summary>Stellt Daten für das Ereignis bereit, das ausgelöst wird, wenn die Ausnahme eines fehlerhaften bemängelte <see cref="T:System.Threading.Tasks.Task" />-Objekts nicht beobachtet wird.</summary>
    </member>
    <member name="M:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.#ctor(System.AggregateException)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Tasks.UnobservedTaskExceptionEventArgs" />-Klasse mit der nicht überwachten Ausnahme.</summary>
      <param name="exception">Die Ausnahme, deren Überwachung eingestellt wurde.</param>
    </member>
    <member name="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Exception">
      <summary>Die Ausnahme, deren Überwachung eingestellt wurde.</summary>
      <returns>Die Ausnahme, deren Überwachung eingestellt wurde.</returns>
    </member>
    <member name="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Observed">
      <summary>Ruft ab, ob diese Ausnahme als "überwacht" gekennzeichnet wurde.</summary>
      <returns>True, wenn diese Ausnahme als "überwacht" markiert wurde, andernfalls false.</returns>
    </member>
    <member name="M:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.SetObserved">
      <summary>Markiert die <see cref="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Exception" />-Eigenschaft als "überwacht". Dadurch wird verhindert, dass eine Ausnahmeneskalierungsrichtlinie ausgelöst wird, durch die der Prozess standardmäßig beendet wird.</summary>
    </member>
  </members>
</doc>