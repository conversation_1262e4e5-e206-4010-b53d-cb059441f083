﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace OcrLiteLib
{
    /// <summary>
    /// 并行批量处理器
    /// </summary>
    public class ParallelBatchProcessor : IDisposable
    {
        private readonly OcrServicePool _servicePool;
        private bool _disposed = false;

        public ParallelBatchProcessor(string detPath, string clsPath, string recPath, string keysPath,
                                    int numThread = 1, int maxConcurrency = -1)
        {
            if (maxConcurrency == -1)
            {
                // 对于I/O密集型任务，使用更多线程
                maxConcurrency = Math.Max(Environment.ProcessorCount, Environment.ProcessorCount * 2);
            }

            _servicePool = new OcrServicePool(detPath, clsPath, recPath, keysPath, numThread, maxConcurrency);

            // 优化线程池设置
            OptimizeThreadPool();
        }

        /// <summary>
        /// 优化线程池设置以提高CPU利用率
        /// </summary>
        private void OptimizeThreadPool()
        {
            // 增加线程池的最小线程数，减少线程创建延迟
            int workerThreads, completionPortThreads;
            System.Threading.ThreadPool.GetMinThreads(out workerThreads, out completionPortThreads);

            int optimalWorkerThreads = Math.Max(workerThreads, Environment.ProcessorCount * 2);
            int optimalCompletionThreads = Math.Max(completionPortThreads, Environment.ProcessorCount);

            System.Threading.ThreadPool.SetMinThreads(optimalWorkerThreads, optimalCompletionThreads);

            // 设置最大线程数
            System.Threading.ThreadPool.GetMaxThreads(out workerThreads, out completionPortThreads);
            int maxWorkerThreads = Math.Max(workerThreads, Environment.ProcessorCount * 4);
            int maxCompletionThreads = Math.Max(completionPortThreads, Environment.ProcessorCount * 2);

            System.Threading.ThreadPool.SetMaxThreads(maxWorkerThreads, maxCompletionThreads);

            Console.WriteLine($"线程池优化: 最小工作线程={optimalWorkerThreads}, 最大工作线程={maxWorkerThreads}");
        }

        /// <summary>
        /// 预热处理器
        /// </summary>
        public async Task WarmupAsync()
        {
            await _servicePool.WarmupAsync();
        }

        /// <summary>
        /// 并行处理图片文件夹
        /// </summary>
        public async Task<List<ParallelProcessResult>> ProcessDirectoryAsync(string directoryPath, 
            string[] supportedExtensions = null, 
            int padding = 50, int maxSideLen = 1536, 
            float boxScoreThresh = 0.6f, float boxThresh = 0.3f, float unClipRatio = 2.0f, 
            bool doAngle = true, bool mostAngle = true,
            IProgress<ProcessProgress> progress = null,
            CancellationToken cancellationToken = default)
        {
            if (!Directory.Exists(directoryPath))
            {
                throw new DirectoryNotFoundException($"目录不存在: {directoryPath}");
            }

            if (supportedExtensions == null)
            {
                supportedExtensions = new[] { ".jpg", ".jpeg", ".png", ".bmp", ".gif" };
            }

            var imageFiles = Directory.GetFiles(directoryPath)
                .Where(f => supportedExtensions.Contains(Path.GetExtension(f).ToLower()))
                .ToArray();

            if (imageFiles.Length == 0)
            {
                Console.WriteLine($"在目录 {directoryPath} 中未找到支持的图片文件");
                return new List<ParallelProcessResult>();
            }

            Console.WriteLine($"找到 {imageFiles.Length} 个图片文件，开始并行处理...");

            return await ProcessFilesAsync(imageFiles, padding, maxSideLen, boxScoreThresh, boxThresh, 
                                         unClipRatio, doAngle, mostAngle, progress, cancellationToken);
        }

        /// <summary>
        /// 并行处理图片文件列表
        /// </summary>
        public async Task<List<ParallelProcessResult>> ProcessFilesAsync(string[] imageFiles,
            int padding = 50, int maxSideLen = 1536,
            float boxScoreThresh = 0.6f, float boxThresh = 0.3f, float unClipRatio = 2.0f,
            bool doAngle = true, bool mostAngle = true,
            IProgress<ProcessProgress> progress = null,
            CancellationToken cancellationToken = default)
        {
            var results = new List<ParallelProcessResult>();
            var completedCount = 0;
            var totalCount = imageFiles.Length;
            var lockObject = new object();

            var overallStopwatch = Stopwatch.StartNew();

            // 使用更激进的并发控制，提高CPU利用率
            var maxConcurrentTasks = Math.Max(Environment.ProcessorCount * 2, 8);
            var semaphore = new SemaphoreSlim(maxConcurrentTasks, maxConcurrentTasks);

            Console.WriteLine($"使用并发任务数: {maxConcurrentTasks}");

            var tasks = imageFiles.Select(async imagePath =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    var result = await ProcessSingleImageAsync(imagePath, padding, maxSideLen, 
                                                             boxScoreThresh, boxThresh, unClipRatio, 
                                                             doAngle, mostAngle, cancellationToken);

                    lock (lockObject)
                    {
                        results.Add(result);
                        completedCount++;

                        // 报告进度
                        progress?.Report(new ProcessProgress
                        {
                            CompletedCount = completedCount,
                            TotalCount = totalCount,
                            CurrentFile = Path.GetFileName(imagePath),
                            ElapsedTime = overallStopwatch.Elapsed,
                            IsCompleted = completedCount == totalCount
                        });

                        // 每处理10张图片输出一次进度
                        if (completedCount % 10 == 0 || completedCount == totalCount)
                        {
                            var avgTime1 = overallStopwatch.ElapsedMilliseconds / completedCount;
                            var remainingTime = (totalCount - completedCount) * avgTime1;
                            Console.WriteLine($"进度: {completedCount}/{totalCount} ({completedCount * 100.0 / totalCount:F1}%), " +
                                            $"平均: {avgTime1}ms/张, 预计剩余: {remainingTime / 1000:F1}s");
                        }
                    }

                    return result;
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(tasks);
            overallStopwatch.Stop();

            // 输出统计信息
            var successCount = results.Count(r => r.IsSuccess);
            var failureCount = results.Count(r => !r.IsSuccess);
            var totalTime = overallStopwatch.ElapsedMilliseconds;
            var avgTime = successCount > 0 ? results.Where(r => r.IsSuccess).Average(r => r.ProcessingTimeMs) : 0;

            Console.WriteLine($"\n=== 并行处理完成 ===");
            Console.WriteLine($"总文件数: {totalCount}");
            Console.WriteLine($"成功: {successCount}, 失败: {failureCount}");
            Console.WriteLine($"总耗时: {totalTime}ms ({totalTime / 1000.0:F1}s)");
            Console.WriteLine($"平均耗时: {avgTime:F1}ms/张");
            Console.WriteLine($"吞吐量: {successCount * 1000.0 / totalTime:F1} 张/秒");

            if (failureCount > 0)
            {
                Console.WriteLine("\n失败的文件:");
                foreach (var failure in results.Where(r => !r.IsSuccess))
                {
                    Console.WriteLine($"  {Path.GetFileName(failure.ImagePath)}: {failure.Error?.Message}");
                }
            }

            return results;
        }

        /// <summary>
        /// 处理单张图片
        /// </summary>
        private async Task<ParallelProcessResult> ProcessSingleImageAsync(string imagePath,
            int padding, int maxSideLen, float boxScoreThresh, float boxThresh, float unClipRatio,
            bool doAngle, bool mostAngle, CancellationToken cancellationToken)
        {
            var result = new ParallelProcessResult
            {
                ImagePath = imagePath,
                ThreadId = Thread.CurrentThread.ManagedThreadId
            };

            var stopwatch = Stopwatch.StartNew();

            try
            {
                using (var pooledService = await _servicePool.AcquireServiceAsync())
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    OcrServiceOptimized.PerformanceStats stats;
                    result.TextBlocks = pooledService.DetectTextBlocks(imagePath, padding, maxSideLen,
                                                                      boxScoreThresh, boxThresh, unClipRatio,
                                                                      doAngle, mostAngle, out stats);
                    result.Stats = stats;
                }
            }
            catch (Exception ex)
            {
                result.Error = ex;
            }
            finally
            {
                stopwatch.Stop();
                result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
            }

            return result;
        }

        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;

            _servicePool?.Dispose();
        }
    }

    /// <summary>
    /// 处理进度信息
    /// </summary>
    public class ProcessProgress
    {
        public int CompletedCount { get; set; }
        public int TotalCount { get; set; }
        public string CurrentFile { get; set; }
        public TimeSpan ElapsedTime { get; set; }
        public bool IsCompleted { get; set; }

        public double ProgressPercentage => TotalCount > 0 ? (CompletedCount * 100.0 / TotalCount) : 0;

        public override string ToString()
        {
            return $"{CompletedCount}/{TotalCount} ({ProgressPercentage:F1}%) - {CurrentFile} - 耗时: {ElapsedTime:mm\\:ss}";
        }
    }
}
