﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="clipper_library" version="6.2.1" targetFramework="net461" />
  <package id="Emgu.CV" version="4.4.0.4099" targetFramework="net461" />
  <package id="Emgu.CV.runtime.windows" version="4.4.0.4099" targetFramework="net461" />
  <package id="Microsoft.ML.OnnxRuntime" version="1.22.1" targetFramework="net461" />
  <package id="Microsoft.ML.OnnxRuntime.Managed" version="1.22.1" targetFramework="net461" />
  <package id="Microsoft.NETCore.Platforms" version="1.1.0" targetFramework="net461" />
  <package id="Microsoft.Win32.Primitives" version="4.3.0" targetFramework="net461" />
  <package id="NETStandard.Library" version="1.6.1" targetFramework="net461" />
  <package id="System.AppContext" version="4.3.0" targetFramework="net461" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net461" />
  <package id="System.Collections" version="4.3.0" targetFramework="net461" />
  <package id="System.Collections.Concurrent" version="4.3.0" targetFramework="net461" />
  <package id="System.Console" version="4.3.0" targetFramework="net461" />
  <package id="System.Diagnostics.Debug" version="4.3.0" targetFramework="net461" />
  <package id="System.Diagnostics.DiagnosticSource" version="4.3.0" targetFramework="net461" />
  <package id="System.Diagnostics.Tools" version="4.3.0" targetFramework="net461" />
  <package id="System.Diagnostics.Tracing" version="4.3.0" targetFramework="net461" />
  <package id="System.Drawing.Primitives" version="4.3.0" targetFramework="net461" />
  <package id="System.Globalization" version="4.3.0" targetFramework="net461" />
  <package id="System.Globalization.Calendars" version="4.3.0" targetFramework="net461" />
  <package id="System.IO" version="4.3.0" targetFramework="net461" />
  <package id="System.IO.Compression" version="4.3.0" targetFramework="net461" />
  <package id="System.IO.Compression.ZipFile" version="4.3.0" targetFramework="net461" />
  <package id="System.IO.FileSystem" version="4.3.0" targetFramework="net461" />
  <package id="System.IO.FileSystem.Primitives" version="4.3.0" targetFramework="net461" />
  <package id="System.Linq" version="4.3.0" targetFramework="net461" />
  <package id="System.Linq.Expressions" version="4.3.0" targetFramework="net461" />
  <package id="System.Memory" version="4.5.5" targetFramework="net461" />
  <package id="System.Net.Http" version="4.3.0" targetFramework="net461" />
  <package id="System.Net.Primitives" version="4.3.0" targetFramework="net461" />
  <package id="System.Net.Sockets" version="4.3.0" targetFramework="net461" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net461" />
  <package id="System.ObjectModel" version="4.3.0" targetFramework="net461" />
  <package id="System.Reflection" version="4.3.0" targetFramework="net461" />
  <package id="System.Reflection.Extensions" version="4.3.0" targetFramework="net461" />
  <package id="System.Reflection.Primitives" version="4.3.0" targetFramework="net461" />
  <package id="System.Resources.ResourceManager" version="4.3.0" targetFramework="net461" />
  <package id="System.Runtime" version="4.3.1" targetFramework="net461" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net461" />
  <package id="System.Runtime.Extensions" version="4.3.0" targetFramework="net461" />
  <package id="System.Runtime.Handles" version="4.3.0" targetFramework="net461" />
  <package id="System.Runtime.InteropServices" version="4.3.0" targetFramework="net461" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net461" />
  <package id="System.Runtime.Numerics" version="4.3.0" targetFramework="net461" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net461" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net461" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net461" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net461" />
  <package id="System.Text.Encoding" version="4.3.0" targetFramework="net461" />
  <package id="System.Text.Encoding.Extensions" version="4.3.0" targetFramework="net461" />
  <package id="System.Text.RegularExpressions" version="4.3.0" targetFramework="net461" />
  <package id="System.Threading" version="4.3.0" targetFramework="net461" />
  <package id="System.Threading.Tasks" version="4.3.0" targetFramework="net461" />
  <package id="System.Threading.Timer" version="4.3.0" targetFramework="net461" />
  <package id="System.Xml.ReaderWriter" version="4.3.0" targetFramework="net461" />
  <package id="System.Xml.XDocument" version="4.3.0" targetFramework="net461" />
</packages>