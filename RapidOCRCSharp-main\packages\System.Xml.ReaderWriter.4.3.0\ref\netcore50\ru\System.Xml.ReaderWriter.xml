﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.ReaderWriter</name>
  </assembly>
  <members>
    <member name="T:System.Xml.ConformanceLevel">
      <summary>Задает количество проверок ввода-вывода, которые выполняют объекты <see cref="T:System.Xml.XmlReader" /> и <see cref="T:System.Xml.XmlWriter" />.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Auto">
      <summary>Объект <see cref="T:System.Xml.XmlReader" /> или <see cref="T:System.Xml.XmlWriter" /> автоматически определяет, проверять ли весь документ или фрагмент документа, и выполняет соответствующую проверку.В случае использования программы-оболочки для другого объекта <see cref="T:System.Xml.XmlReader" /> или <see cref="T:System.Xml.XmlWriter" /> внешний объект не выполняет никаких дополнительных проверок на соответствие.Проверка на соответствие выполняется базовым объектом.Сведения об определении уровня соответствия см. в описании свойств <see cref="P:System.Xml.XmlReaderSettings.ConformanceLevel" /> и <see cref="P:System.Xml.XmlWriterSettings.ConformanceLevel" />.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Document">
      <summary>Данные XML соответствуют правилам для XML-документов версии 1.0 с правильным форматом в соответствии с определением консорциума W3C.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Fragment">
      <summary>Данные XML являются XML-фрагментом с правильным форматом в соответствии с определением консорциума W3C.</summary>
    </member>
    <member name="T:System.Xml.DtdProcessing">
      <summary>Задает параметры обработки DTD.Перечисление <see cref="T:System.Xml.DtdProcessing" /> используется классом <see cref="T:System.Xml.XmlReaderSettings" />.</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Ignore">
      <summary>Элемент DOCTYPE будет проигнорирован.Обработка DTD выполнена не будет.</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Prohibit">
      <summary>Указывает, что при обнаружении DTD будет создано исключение <see cref="T:System.Xml.XmlException" /> с сообщением о том, что DTD запрещены.Это поведение установлено по умолчанию.</summary>
    </member>
    <member name="T:System.Xml.IXmlLineInfo">
      <summary>Предоставляет интерфейс, позволяющий классу возвращать информацию о строке и положении в ней.</summary>
    </member>
    <member name="M:System.Xml.IXmlLineInfo.HasLineInfo">
      <summary>Возвращает значение, определяющее возможность возвращения классом сведений о строке.</summary>
      <returns>Значение true, если могут быть предоставлены свойства <see cref="P:System.Xml.IXmlLineInfo.LineNumber" /> и <see cref="P:System.Xml.IXmlLineInfo.LinePosition" />, в противном случае — false.</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LineNumber">
      <summary>Получает текущий номер строки.</summary>
      <returns>Номер текущей строки или значение 0, если информация о строке недоступна (например, метод <see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" /> возвращает значение false).</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LinePosition">
      <summary>Получает текущее положение строки.</summary>
      <returns>Текущее положение строки или значение 0, если информация о строке недоступна (например, метод <see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" /> возвращает значение false).</returns>
    </member>
    <member name="T:System.Xml.IXmlNamespaceResolver">
      <summary>Предоставляет доступ только для чтения к набору сопоставлений префиксов и пространств имен.</summary>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>Получает коллекцию определенных соответствий префиксов и пространств имен, которые в настоящий момент находятся в области.</summary>
      <returns>Объект <see cref="T:System.Collections.IDictionary" />, содержащий все текущие пространства имен в области.</returns>
      <param name="scope">С помощью значения <see cref="T:System.Xml.XmlNamespaceScope" /> указывается тип узлов пространства имен, которые следует возвратить.</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupNamespace(System.String)">
      <summary>Получает универсальный код ресурса (URI) пространства имен, соответствующий заданному префиксу.</summary>
      <returns>URI пространства имен, сопоставленное с префиксом; null, если префикс не сопоставлен с URI пространства имен.</returns>
      <param name="prefix">Префикс, URI пространства имен которого нужно найти.</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupPrefix(System.String)">
      <summary>Получает префикс, соответствующий заданному универсальному коду ресурса (URI) пространства имен.</summary>
      <returns>Префикс, сопоставленный URI пространства имен; null если URI пространства имен не сопоставлено с префиксом.</returns>
      <param name="namespaceName">URI пространства имен, префикс которого нужно найти.</param>
    </member>
    <member name="T:System.Xml.NamespaceHandling">
      <summary>Указывает, нужно ли удалять дубликаты объявлений в объекте <see cref="T:System.Xml.XmlWriter" />. </summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.Default">
      <summary>Указывает, что удалять дубликаты объявлений не будут удалены.</summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.OmitDuplicates">
      <summary>Указывает, что дубликаты объявлений будут удалены.Чтобы дубликат пространства имен был удален, должны совпадать префиксы пространств имен.</summary>
    </member>
    <member name="T:System.Xml.NameTable">
      <summary>Реализует однопотоковый объект <see cref="T:System.Xml.XmlNameTable" />.</summary>
    </member>
    <member name="M:System.Xml.NameTable.#ctor">
      <summary>Инициализирует новый экземпляр класса NameTable.</summary>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>Атомизирует заданную строку и добавляет ее к объекту NameTable.</summary>
      <returns>Атомизированная строка или существующая строка, если таковая уже имеется в объекте NameTable.Если значение параметра <paramref name="len" /> равно нулю, возвращается поле String.Empty.</returns>
      <param name="key">Массив символов, содержащий добавляемую строку. </param>
      <param name="start">Отсчитываемый от нуля индекс в массиве, задающий первый символ строки. </param>
      <param name="len">Количество знаков в строке. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />– или – <paramref name="start" /> &gt;= <paramref name="key" />.Length – или – <paramref name="len" /> &gt;= <paramref name="key" />.Length Вышеприведенные условия не вызывают исключение, если значение <paramref name="len" /> =0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.String)">
      <summary>Атомизирует заданную строку и добавляет ее к объекту NameTable.</summary>
      <returns>Атомизированная строка или существующая строка, если таковая уже имеется в объекте NameTable.</returns>
      <param name="key">Строка для добавления. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="key" /> имеет значение null. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>Получает атомизированную строку, содержащую те же символы, что и заданный диапазон символов в данном массиве.</summary>
      <returns>Атомизированная строка или значение null, если строка еще не атомизирована.Если значение параметра <paramref name="len" /> равно нулю, возвращается поле String.Empty.</returns>
      <param name="key">Массив символов, содержащий искомое имя. </param>
      <param name="start">Отсчитываемый от нуля индекс в массиве, задающий первый символ имени. </param>
      <param name="len">Число символов в имени. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />– или – <paramref name="start" /> &gt;= <paramref name="key" />.Length – или – <paramref name="len" /> &gt;= <paramref name="key" />.Length Вышеприведенные условия не вызывают исключение, если значение <paramref name="len" /> =0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.String)">
      <summary>Получает атомизированную строку с заданным значением.</summary>
      <returns>Объект атомизированной строки или значение null, если строка еще не атомизирована.</returns>
      <param name="value">Искомое имя. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="value" /> имеет значение null. </exception>
    </member>
    <member name="T:System.Xml.NewLineHandling">
      <summary>Задает способ обработки разрывов строк.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Entitize">
      <summary>Символы новой строки преобразовываются.Благодаря этому параметру сохраняются все символы, когда результат читается нормализующим считывателем <see cref="T:System.Xml.XmlReader" />.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.None">
      <summary>Символы новой строки не меняются.Выходные данные совпадают со входными.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Replace">
      <summary>Знаки новой строки заменяются для обеспечения соответствия со знаком, указанным в свойстве <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" />.</summary>
    </member>
    <member name="T:System.Xml.ReadState">
      <summary>Задает состояние читателя.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Closed">
      <summary>Вызван метод <see cref="M:System.Xml.XmlReader.Close" />.</summary>
    </member>
    <member name="F:System.Xml.ReadState.EndOfFile">
      <summary>Конец файла успешно достигнут.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Error">
      <summary>Произошла ошибка, препятствующая продолжению операции чтения.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Initial">
      <summary>Метод Read не был вызван.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Interactive">
      <summary>Вызван метод Read.Для читателя можно вызвать дополнительные методы.</summary>
    </member>
    <member name="T:System.Xml.WriteState">
      <summary>Задает состояние объекта <see cref="T:System.Xml.XmlWriter" />.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Attribute">
      <summary>Указывает, что значение атрибута записывается.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Closed">
      <summary>Указывает, что был вызван метод <see cref="M:System.Xml.XmlWriter.Close" />.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Content">
      <summary>Указывает, что содержимое элемента записывается.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Element">
      <summary>Указывает, что открывающий тег элемента записывается.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Error">
      <summary>Было сгенерировано исключение, которое оставило объект <see cref="T:System.Xml.XmlWriter" /> в недопустимом состоянии.Можно вызвать метод <see cref="M:System.Xml.XmlWriter.Close" />, чтобы перевести объект <see cref="T:System.Xml.XmlWriter" /> в состояние <see cref="F:System.Xml.WriteState.Closed" />.Вызов любого другого метода <see cref="T:System.Xml.XmlWriter" /> приведет к созданию исключения <see cref="T:System.InvalidOperationException" />.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Prolog">
      <summary>Указывает, что пролог записывается.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Start">
      <summary>Указывает, что метод Write еще не вызван.</summary>
    </member>
    <member name="T:System.Xml.XmlConvert">
      <summary>Кодирует и декодирует имена XML и предоставляет методы для преобразования между типами общеязыковой среды выполнения и типами языков определения схем XML (XSD).При преобразовании типов данных возвращаемые значения не зависят от языкового стандарта.</summary>
    </member>
    <member name="M:System.Xml.XmlConvert.DecodeName(System.String)">
      <summary>Декодирует имя.Этот метод изменяет действие методов <see cref="M:System.Xml.XmlConvert.EncodeName(System.String)" /> и <see cref="M:System.Xml.XmlConvert.EncodeLocalName(System.String)" /> на обратное.</summary>
      <returns>Декодированное имя.</returns>
      <param name="name">Преобразуемое имя. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeLocalName(System.String)">
      <summary>Преобразует имя в допустимое локальное имя XML.</summary>
      <returns>Закодированное имя.</returns>
      <param name="name">Имя для кодирования. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeName(System.String)">
      <summary>Преобразует имя в допустимое имя XML.</summary>
      <returns>Возвращает имя с любыми недопустимыми знаками, замещенными escape-строкой.</returns>
      <param name="name">Преобразуемое имя. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeNmToken(System.String)">
      <summary>Проверяет допустимость имени на соответствие со спецификацией XML.</summary>
      <returns>Закодированное имя.</returns>
      <param name="name">Имя для кодирования. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToBoolean(System.String)">
      <summary>Преобразует <see cref="T:System.String" /> в эквивалент <see cref="T:System.Boolean" />.</summary>
      <returns>Значение Boolean равно true или false.</returns>
      <param name="s">Преобразуемая строка. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> does not represent a Boolean value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToByte(System.String)">
      <summary>Преобразует <see cref="T:System.String" /> в эквивалент <see cref="T:System.Byte" />.</summary>
      <returns>Эквивалент строки Byte.</returns>
      <param name="s">Преобразуемая строка. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToChar(System.String)">
      <summary>Преобразует <see cref="T:System.String" /> в эквивалент <see cref="T:System.Char" />.</summary>
      <returns>Значение Char, представляющее отдельный знак.</returns>
      <param name="s">Строка, содержащая отдельный преобразуемый знак. </param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="s" /> parameter is null. </exception>
      <exception cref="T:System.FormatException">The <paramref name="s" /> parameter contains more than one character. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTime(System.String,System.Xml.XmlDateTimeSerializationMode)">
      <summary>Преобразует <see cref="T:System.String" /> в <see cref="T:System.DateTime" /> с помощью указанного <see cref="T:System.Xml.XmlDateTimeSerializationMode" /></summary>
      <returns>Эквивалент <see cref="T:System.DateTime" /> для значения <see cref="T:System.String" />.</returns>
      <param name="s">Преобразуемое значение <see cref="T:System.String" />.</param>
      <param name="dateTimeOption">Одно из значений <see cref="T:System.Xml.XmlDateTimeSerializationMode" />, указывающее, следует ли преобразовывать данные в локальное время или сохранять их во времени в формате UTC, если дата в формате UTC.</param>
      <exception cref="T:System.NullReferenceException">
        <paramref name="s" /> is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="dateTimeOption" /> value is null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is an empty string or is not in a valid format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String)">
      <summary>Преобразует предоставленное значение <see cref="T:System.String" /> в эквивалентное значение <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>Эквивалент указанной строки <see cref="T:System.DateTimeOffset" />.</returns>
      <param name="s">Преобразуемая строка.Примечание.   Строка должна соответствовать подмножеству в соответствии с рекомендацией W3C для типа XML dateTime.Дополнительные сведения см. по адресу http://www.w3.org/TR/xmlschema-2/#dateTime.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The argument passed to this method is outside the range of allowable values.For information about allowable values, see <see cref="T:System.DateTimeOffset" />.</exception>
      <exception cref="T:System.FormatException">The argument passed to this method does not conform to a subset of the W3C Recommendations for the XML dateTime type.For more information see http://www.w3.org/TR/xmlschema-2/#dateTime.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String)">
      <summary>Преобразует предоставленное значение <see cref="T:System.String" /> в эквивалентное значение <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>Эквивалент указанной строки <see cref="T:System.DateTimeOffset" />.</returns>
      <param name="s">Преобразуемая строка.</param>
      <param name="format">Формат, из которого преобразуется параметр <paramref name="s" />.Параметр формата может быть любым поднабором в соответствии с рекомендацией W3C для типа XML dateTime.(Дополнительные сведения см. по адресу http://www.w3.org/TR/xmlschema-2/#dateTime.) Строка <paramref name="s" /> проверяется по этому формату.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> or <paramref name="format" /> is an empty string or is not in the specified format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String[])">
      <summary>Преобразует предоставленное значение <see cref="T:System.String" /> в эквивалентное значение <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>Эквивалент указанной строки <see cref="T:System.DateTimeOffset" />.</returns>
      <param name="s">Преобразуемая строка.</param>
      <param name="formats">Массив форматов, из которого можно преобразовать параметр <paramref name="s" />.Каждый формат в параметре <paramref name="formats" /> может быть любым подмножеством в соответствии с рекомендациями консорциума W3C для типа XML dateTime.(Дополнительные сведения см. по адресу http://www.w3.org/TR/xmlschema-2/#dateTime.) Строка <paramref name="s" /> проверяется по одному из этих форматов.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDecimal(System.String)">
      <summary>Преобразует <see cref="T:System.String" /> в эквивалент <see cref="T:System.Decimal" />.</summary>
      <returns>Эквивалент строки Decimal.</returns>
      <param name="s">Преобразуемая строка. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Decimal.MinValue" /> or greater than <see cref="F:System.Decimal.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDouble(System.String)">
      <summary>Преобразует <see cref="T:System.String" /> в эквивалент <see cref="T:System.Double" />.</summary>
      <returns>Эквивалент строки Double.</returns>
      <param name="s">Преобразуемая строка. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Double.MinValue" /> or greater than <see cref="F:System.Double.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToGuid(System.String)">
      <summary>Преобразует <see cref="T:System.String" /> в эквивалент <see cref="T:System.Guid" />.</summary>
      <returns>Эквивалент строки Guid.</returns>
      <param name="s">Преобразуемая строка. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt16(System.String)">
      <summary>Преобразует <see cref="T:System.String" /> в эквивалент <see cref="T:System.Int16" />.</summary>
      <returns>Эквивалент строки Int16.</returns>
      <param name="s">Преобразуемая строка. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int16.MinValue" /> or greater than <see cref="F:System.Int16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt32(System.String)">
      <summary>Преобразует <see cref="T:System.String" /> в эквивалент <see cref="T:System.Int32" />.</summary>
      <returns>Эквивалент строки Int32.</returns>
      <param name="s">Преобразуемая строка. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int32.MinValue" /> or greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt64(System.String)">
      <summary>Преобразует <see cref="T:System.String" /> в эквивалент <see cref="T:System.Int64" />.</summary>
      <returns>Эквивалент строки Int64.</returns>
      <param name="s">Преобразуемая строка. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int64.MinValue" /> or greater than <see cref="F:System.Int64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSByte(System.String)">
      <summary>Преобразует <see cref="T:System.String" /> в эквивалент <see cref="T:System.SByte" />.</summary>
      <returns>Эквивалент строки SByte.</returns>
      <param name="s">Преобразуемая строка. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.SByte.MinValue" /> or greater than <see cref="F:System.SByte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSingle(System.String)">
      <summary>Преобразует <see cref="T:System.String" /> в эквивалент <see cref="T:System.Single" />.</summary>
      <returns>Эквивалент строки Single.</returns>
      <param name="s">Преобразуемая строка. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Single.MinValue" /> or greater than <see cref="F:System.Single.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Boolean)">
      <summary>Преобразует объект <see cref="T:System.Boolean" /> в значение типа <see cref="T:System.String" />.</summary>
      <returns>Строковое представление Boolean, то есть true или false.</returns>
      <param name="value">Преобразуемое значение. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Byte)">
      <summary>Преобразует объект <see cref="T:System.Byte" /> в значение типа <see cref="T:System.String" />.</summary>
      <returns>Строковое представление объекта Byte.</returns>
      <param name="value">Преобразуемое значение. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Char)">
      <summary>Преобразует объект <see cref="T:System.Char" /> в значение типа <see cref="T:System.String" />.</summary>
      <returns>Строковое представление объекта Char.</returns>
      <param name="value">Преобразуемое значение. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTime,System.Xml.XmlDateTimeSerializationMode)">
      <summary>Преобразует объект <see cref="T:System.DateTime" /> в объект <see cref="T:System.String" /> с помощью заданного значения <see cref="T:System.Xml.XmlDateTimeSerializationMode" />.</summary>
      <returns>Эквивалент <see cref="T:System.String" /> для значения <see cref="T:System.DateTime" />.</returns>
      <param name="value">Преобразуемое значение <see cref="T:System.DateTime" />.</param>
      <param name="dateTimeOption">Одно из значений <see cref="T:System.Xml.XmlDateTimeSerializationMode" />, указывающее, как следует обрабатывать значение <see cref="T:System.DateTime" />.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="dateTimeOption" /> value is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> or <paramref name="dateTimeOption" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset)">
      <summary>Преобразует предоставленную структуру <see cref="T:System.DateTimeOffset" /> в объект <see cref="T:System.String" />.</summary>
      <returns>Представление <see cref="T:System.String" /> для предоставленной структуры <see cref="T:System.DateTimeOffset" />.</returns>
      <param name="value">Преобразуемая структура <see cref="T:System.DateTimeOffset" />.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset,System.String)">
      <summary>Преобразует предоставленную структуру <see cref="T:System.DateTimeOffset" /> в объект <see cref="T:System.String" /> в указанном формате.</summary>
      <returns>Представление <see cref="T:System.String" /> в указанном формате предоставленной структуры <see cref="T:System.DateTimeOffset" />.</returns>
      <param name="value">Преобразуемая структура <see cref="T:System.DateTimeOffset" />.</param>
      <param name="format">Формат, в который преобразуется параметр <paramref name="s" />.Параметр формата может быть любым поднабором в соответствии с рекомендацией W3C для типа XML dateTime.(Дополнительные сведения см. по адресу http://www.w3.org/TR/xmlschema-2/#dateTime.)</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Decimal)">
      <summary>Преобразует объект <see cref="T:System.Decimal" /> в значение типа <see cref="T:System.String" />.</summary>
      <returns>Строковое представление объекта Decimal.</returns>
      <param name="value">Преобразуемое значение. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Double)">
      <summary>Преобразует объект <see cref="T:System.Double" /> в значение типа <see cref="T:System.String" />.</summary>
      <returns>Строковое представление объекта Double.</returns>
      <param name="value">Преобразуемое значение. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Guid)">
      <summary>Преобразует объект <see cref="T:System.Guid" /> в значение типа <see cref="T:System.String" />.</summary>
      <returns>Строковое представление объекта Guid.</returns>
      <param name="value">Преобразуемое значение. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int16)">
      <summary>Преобразует объект <see cref="T:System.Int16" /> в значение типа <see cref="T:System.String" />.</summary>
      <returns>Строковое представление объекта Int16.</returns>
      <param name="value">Преобразуемое значение. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int32)">
      <summary>Преобразует объект <see cref="T:System.Int32" /> в значение типа <see cref="T:System.String" />.</summary>
      <returns>Строковое представление объекта Int32.</returns>
      <param name="value">Преобразуемое значение. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int64)">
      <summary>Преобразует объект <see cref="T:System.Int64" /> в значение типа <see cref="T:System.String" />.</summary>
      <returns>Строковое представление объекта Int64.</returns>
      <param name="value">Преобразуемое значение. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.SByte)">
      <summary>Преобразует объект <see cref="T:System.SByte" /> в значение типа <see cref="T:System.String" />.</summary>
      <returns>Строковое представление объекта SByte.</returns>
      <param name="value">Преобразуемое значение. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Single)">
      <summary>Преобразует объект <see cref="T:System.Single" /> в значение типа <see cref="T:System.String" />.</summary>
      <returns>Строковое представление объекта Single.</returns>
      <param name="value">Преобразуемое значение. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.TimeSpan)">
      <summary>Преобразует объект <see cref="T:System.TimeSpan" /> в значение типа <see cref="T:System.String" />.</summary>
      <returns>Строковое представление объекта TimeSpan.</returns>
      <param name="value">Преобразуемое значение. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt16)">
      <summary>Преобразует объект <see cref="T:System.UInt16" /> в значение типа <see cref="T:System.String" />.</summary>
      <returns>Строковое представление объекта UInt16.</returns>
      <param name="value">Преобразуемое значение. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt32)">
      <summary>Преобразует объект <see cref="T:System.UInt32" /> в значение типа <see cref="T:System.String" />.</summary>
      <returns>Строковое представление объекта UInt32.</returns>
      <param name="value">Преобразуемое значение. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt64)">
      <summary>Преобразует объект <see cref="T:System.UInt64" /> в значение типа <see cref="T:System.String" />.</summary>
      <returns>Строковое представление объекта UInt64.</returns>
      <param name="value">Преобразуемое значение. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToTimeSpan(System.String)">
      <summary>Преобразует <see cref="T:System.String" /> в эквивалент <see cref="T:System.TimeSpan" />.</summary>
      <returns>Эквивалент строки TimeSpan.</returns>
      <param name="s">Преобразуемая строка.Формат строки должен соответствовать рекомендации по продолжительности в зависимости от типа данных W3C XML-схемы (часть 2).</param>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in correct format to represent a TimeSpan value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt16(System.String)">
      <summary>Преобразует <see cref="T:System.String" /> в эквивалент <see cref="T:System.UInt16" />.</summary>
      <returns>Эквивалент строки UInt16.</returns>
      <param name="s">Преобразуемая строка. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt16.MinValue" /> or greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt32(System.String)">
      <summary>Преобразует <see cref="T:System.String" /> в эквивалент <see cref="T:System.UInt32" />.</summary>
      <returns>Эквивалент строки UInt32.</returns>
      <param name="s">Преобразуемая строка. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt32.MinValue" /> or greater than <see cref="F:System.UInt32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt64(System.String)">
      <summary>Преобразует <see cref="T:System.String" /> в эквивалент <see cref="T:System.UInt64" />.</summary>
      <returns>Эквивалент строки UInt64.</returns>
      <param name="s">Преобразуемая строка. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt64.MinValue" /> or greater than <see cref="F:System.UInt64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyName(System.String)">
      <summary>Проверяет допустимость имени в соответствии с рекомендацией W3C XML.</summary>
      <returns>Имя, если это допустимое имя XML.</returns>
      <param name="name">Имя для проверки. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid XML name. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNCName(System.String)">
      <summary>Проверяет, что имя является допустимым именем NCName в соответствии с рекомендациями по XML консорциума W3C.NCName — это имя, которое не может содержать двоеточия.</summary>
      <returns>Указанное имя не является допустимым именем NCName.</returns>
      <param name="name">Имя для проверки. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid non-colon name. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNMTOKEN(System.String)">
      <summary>Проверяет, является ли строка допустимым NMTOKEN, в соответствии с рекомендацией по типам данных W3C XML-схемы (часть 2).</summary>
      <returns>Токен имени, если это допустимый NMTOKEN.</returns>
      <param name="name">Строка, которую следует проверить.</param>
      <exception cref="T:System.Xml.XmlException">The string is not a valid name token.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyPublicId(System.String)">
      <summary>Возвращает экземпляр переданной строки, если все знаки в строковом аргументе являются допустимыми знаками открытых идентификаторов.</summary>
      <returns>Возвращает переданную строку, если все знаки в аргументе являются допустимыми знаками открытых идентификаторов.</returns>
      <param name="publicId">Объект <see cref="T:System.String" />, содержащий идентификатор для проверки.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyWhitespace(System.String)">
      <summary>Возвращает экземпляр переданной строки, если все знаки в строковом аргументе являются допустимыми знаками-разделителями. </summary>
      <returns>Возвращает экземпляр переданной строки, если все знаки в строковом аргументе являются допустимыми знаками-разделителями; в противном случае возвращает значение null.</returns>
      <param name="content">Объект <see cref="T:System.String" /> для проверки.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyXmlChars(System.String)">
      <summary>Возвращает переданную строку, если все знаки и пары знаков-заполнителей в строковом аргументе являются допустимыми знаками XML; в противном случае создается XmlException со сведениями о первом встретившемся недопустимом знаке. </summary>
      <returns>Возвращает переданную строку, если все знаки и пары знаков-заполнителей в строковом аргументе являются допустимыми знаками XML; в противном случае создается XmlException со сведениями о первом встретившемся недопустимом знаке.</returns>
      <param name="content">Объект <see cref="T:System.String" />, содержащий знаки для проверки.</param>
    </member>
    <member name="T:System.Xml.XmlDateTimeSerializationMode">
      <summary>Определяет способ обработки значения времени при преобразовании между строками и объектами <see cref="T:System.DateTime" />.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Local">
      <summary>Обрабатывать как местное время.Если объект <see cref="T:System.DateTime" /> представляет время в формате UTC, оно будет преобразовано в местное время.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.RoundtripKind">
      <summary>Данные о часовом поясе необходимо сохранять при преобразовании.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Unspecified">
      <summary>Обрабатывать как местное время, если объект <see cref="T:System.DateTime" /> преобразовывается в строку.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Utc">
      <summary>Обрабатывать как время в формате UTC.Если объект <see cref="T:System.DateTime" /> представляет местное время, оно будет преобразовано во время в формате UTC.</summary>
    </member>
    <member name="T:System.Xml.XmlException">
      <summary>Подробные сведения о последнем исключении.</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor">
      <summary>Инициализирует новый экземпляр класса XmlException.</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса XmlException, используя указанное сообщение об ошибке.</summary>
      <param name="message">Описание ошибки. </param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception)">
      <summary>Инициализирует новый экземпляр класса XmlException.</summary>
      <param name="message">Описание условий возникновения ошибки. </param>
      <param name="innerException">
        <see cref="T:System.Exception" />, породивший XmlException (при наличии).Это значение может быть равно null.</param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception,System.Int32,System.Int32)">
      <summary>Инициализирует новый экземпляр класса XmlException, используя заданное сообщение, внутреннее исключение, номер строки и позицию в строке.</summary>
      <param name="message">Описание ошибки. </param>
      <param name="innerException">Исключение, которое вызвало текущее исключение.Это значение может быть равно null.</param>
      <param name="lineNumber">Номер строки, показывающий, где произошла ошибка. </param>
      <param name="linePosition">Размещение строки, показывающее, где произошла ошибка. </param>
    </member>
    <member name="P:System.Xml.XmlException.LineNumber">
      <summary>Получает номер строки, показывающий, где произошла ошибка.</summary>
      <returns>Номер строки, показывающий, где произошла ошибка.</returns>
    </member>
    <member name="P:System.Xml.XmlException.LinePosition">
      <summary>Получает размещение строки, показывающее, где произошла ошибка.</summary>
      <returns>Размещение строки, показывающее, где произошла ошибка.</returns>
    </member>
    <member name="P:System.Xml.XmlException.Message">
      <summary>Получает сообщение, которое описывает текущее исключение.</summary>
      <returns>Сообщение об ошибке с объяснением причин исключения.</returns>
    </member>
    <member name="T:System.Xml.XmlNamespaceManager">
      <summary>Разрешает, добавляет и удаляет пространства имен из коллекции и обеспечивает управление областью для этих пространств имен. </summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.#ctor(System.Xml.XmlNameTable)">
      <summary>Выполняет инициализацию нового экземпляра класса <see cref="T:System.Xml.XmlNamespaceManager" /> с заданным объектом <see cref="T:System.Xml.XmlNameTable" />.</summary>
      <param name="nameTable">Используемый <see cref="T:System.Xml.XmlNameTable" />. </param>
      <exception cref="T:System.NullReferenceException">null is passed to the constructor </exception>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.AddNamespace(System.String,System.String)">
      <summary>Добавляет заданное пространство имен в коллекцию.</summary>
      <param name="prefix">Префикс, который требуется связать с добавляемым пространством имен.Используйте String.Empty для добавления пространства имен по умолчанию.Примечание.Если объект <see cref="T:System.Xml.XmlNamespaceManager" /> будет использоваться для разрешения пространств имен в выражении языка XPath, необходимо указать префикс.Если выражение XPath не содержит префикс, предполагается, что универсальным кодом ресурса (URI) для пространства имен является пустое пространство имен.Дополнительные сведения о выражениях языка XPath и <see cref="T:System.Xml.XmlNamespaceManager" /> см. в разделах с описанием методов <see cref="M:System.Xml.XmlNode.SelectNodes(System.String)" /> и <see cref="M:System.Xml.XPath.XPathExpression.SetContext(System.Xml.XmlNamespaceManager)" />.</param>
      <param name="uri">Добавляемое пространство имен. </param>
      <exception cref="T:System.ArgumentException">The value for <paramref name="prefix" /> is "xml" or "xmlns". </exception>
      <exception cref="T:System.ArgumentNullException">The value for <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.DefaultNamespace">
      <summary>Возвращает универсальный код ресурса (URI) для пространства имен по умолчанию.</summary>
      <returns>Возвращает URI для пространства имен по умолчанию или String.Empty, если пространство имен по умолчанию отсутствует.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetEnumerator">
      <summary>Возвращает перечислитель для выполнения итерации по пространствам имен в объекте <see cref="T:System.Xml.XmlNamespaceManager" />.</summary>
      <returns>Перечислитель <see cref="T:System.Collections.IEnumerator" />, содержащий префиксы, которые хранятся объектом <see cref="T:System.Xml.XmlNamespaceManager" />.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>Возвращает коллекцию пространств имен, уникальными идентификаторами которых являются префиксы, используемые для перечисления пространств имен в текущей области видимости.</summary>
      <returns>Коллекция пар префикс-пространство имен в текущей области видимости.</returns>
      <param name="scope">Значение перечисления, указывающее тип узлов пространств имен, которые требуется возвратить.</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.HasNamespace(System.String)">
      <summary>Возвращает значение, указывающее, определено ли пространство имен для указанного префикса в текущей области видимости, занесенной в стек.</summary>
      <returns>Значение true, если пространство имен определено; в противном случае — значение false.</returns>
      <param name="prefix">Префикс пространства имен, которое нужно найти. </param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupNamespace(System.String)">
      <summary>Возвращает URI пространства имен для указанного префикса.</summary>
      <returns>Возвращает универсальный код ресурса (URI) пространства имен для префикса <paramref name="prefix" /> или значение null, если нет сопоставленного пространства имен.Возвращаемая строка является атомизированной.Дополнительные сведения об атомизированных строках см. в описании класса <see cref="T:System.Xml.XmlNameTable" />.</returns>
      <param name="prefix">Префикс, для которого требуется разрешить URI пространства имен.Чтобы сопоставить пространство имен по умолчанию, необходимо передать String.Empty.</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupPrefix(System.String)">
      <summary>Находит префикс, объявленный для заданного URI пространства имен.</summary>
      <returns>Соответствующий префикс.Если нет сопоставленного префикса, данный метод возвращает String.Empty.Если предоставляется значение NULL, возвращается то же значение null.</returns>
      <param name="uri">Пространство имен, которое необходимо разрешить для получения префикса. </param>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.NameTable">
      <summary>Получает <see cref="T:System.Xml.XmlNameTable" />, связанную с данным объектом.</summary>
      <returns>
        <see cref="T:System.Xml.XmlNameTable" />, используемая данным объектом.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PopScope">
      <summary>Извлекает из стека область видимости пространства имен.</summary>
      <returns>Значение true, если в стеке остались области пространств имен; значение false, если больше нет пространств имен, которые требуется извлечь.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PushScope">
      <summary>Заносит область видимости пространства имен в стек.</summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.RemoveNamespace(System.String,System.String)">
      <summary>Удаляет заданное пространство имен с указанным префиксом.</summary>
      <param name="prefix">Префикс пространства имен. </param>
      <param name="uri">Пространство имен, которое требуется удалить по указанному префиксу.Пространство имен удаляется из текущей области видимости пространств имен.Пространства имен вне текущей области видимости игнорируются.</param>
      <exception cref="T:System.ArgumentNullException">The value of <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="T:System.Xml.XmlNamespaceScope">
      <summary>Определяет область пространства имен.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.All">
      <summary>Все пространства имен, определенные в области текущего узла.Сюда входит пространство xmlns:xml, всегда объявляемое неявно.Порядок возвращения пространств имен не задан.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.ExcludeXml">
      <summary>Все пространства имен, определенные в области текущего узла, кроме пространства xmlns:xml, всегда объявляемого неявно.Порядок возвращения пространств имен не задан.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.Local">
      <summary>Все пространства имен, определенные локально для текущего узла.</summary>
    </member>
    <member name="T:System.Xml.XmlNameTable">
      <summary>Таблица атомизированных объектов строки.</summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.XmlNameTable" />. </summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>При переопределении в производном классе атомизирует заданную строку и добавляет ее в таблицу XmlNameTable.</summary>
      <returns>Новая атомизированная строка или существующая строка, если таковая уже имеется.Если параметр length имеет значение нуль, возвращается String.Empty.</returns>
      <param name="array">Массив символов, содержащий добавляемое имя. </param>
      <param name="offset">Отсчитываемый от нуля индекс в массиве, задающий первый символ имени. </param>
      <param name="length">Число символов в имени. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />– или – <paramref name="offset" /> &gt;= <paramref name="array" />.Length – или – <paramref name="length" /> &gt; <paramref name="array" />.Length Вышеприведенные условия не вызывают исключение, если значение <paramref name="length" /> =0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.String)">
      <summary>При переопределении в производном классе атомизирует заданную строку и добавляет ее в таблицу XmlNameTable.</summary>
      <returns>Новая атомизированная строка или существующая строка, если таковая уже имеется.</returns>
      <param name="array">Добавляемое имя. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="array" /> имеет значение null. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>При переопределении в производном классе получает атомизированную строку, содержащую те же символы, что и заданный диапазон символов в заданном массиве.</summary>
      <returns>Атомизированная строка или значение null, если строка еще не атомизирована.Если параметр <paramref name="length" /> имеет значение нуль, возвращается String.Empty.</returns>
      <param name="array">Массив символов, содержащий искомое имя. </param>
      <param name="offset">Отсчитываемый от нуля индекс в массиве, задающий первый символ имени. </param>
      <param name="length">Число символов в имени. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />– или – <paramref name="offset" /> &gt;= <paramref name="array" />.Length – или – <paramref name="length" /> &gt; <paramref name="array" />.Length Вышеприведенные условия не вызывают исключение, если значение <paramref name="length" /> =0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.String)">
      <summary>При переопределении в производном классе получает атомизированную строку, содержащую то же значение, что и заданная строка.</summary>
      <returns>Атомизированная строка или значение null, если строка еще не атомизирована.</returns>
      <param name="array">Искомое имя. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="array" /> имеет значение null. </exception>
    </member>
    <member name="T:System.Xml.XmlNodeType">
      <summary>Задает типа узла.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Attribute">
      <summary>Атрибут (например, id='123' ).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.CDATA">
      <summary>Раздел CDATA (например, &lt;![CDATA[my escaped text]]&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Comment">
      <summary>Комментарий (например, &lt;!-- my comment --&gt; ).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Document">
      <summary>Объект документа, являющийся корневым элементом дерева документов, предоставляет доступ ко всему XML-документу.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentFragment">
      <summary>Фрагмент документа.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentType">
      <summary>Объявление типа документа, обозначенное следующим тегом (например, &lt;!DOCTYPE...&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Element">
      <summary>Элемент (например, &lt;item&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndElement">
      <summary>Тег конечного элемента (например, &lt;/item&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndEntity">
      <summary>Возвращается, когда объект XmlReader доходит до конца замены сущности в результате вызова <see cref="M:System.Xml.XmlReader.ResolveEntity" />.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Entity">
      <summary>Объявление сущности (например, &lt;!ENTITY...&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EntityReference">
      <summary>Ссылка на сущность (например, &amp;num;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.None">
      <summary>Возвращается объектом <see cref="T:System.Xml.XmlReader" />, если не был вызван метод Read.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Notation">
      <summary>Нотация в объявлении типа документа (например, &lt;!NOTATION...&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.ProcessingInstruction">
      <summary>Инструкция по обработке (например, &lt;?pi test?&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.SignificantWhitespace">
      <summary>Пробел между элементами разметки в смешанной модели содержимого или пробел в области xml:space="preserve".</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Text">
      <summary>Текстовое содержимое узла.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Whitespace">
      <summary>Пробел между разметкой.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.XmlDeclaration">
      <summary>Объявление XML (например, &lt;?xml version='1.0'?&gt;).</summary>
    </member>
    <member name="T:System.Xml.XmlParserContext">
      <summary>Предоставляет все контекстные данные, необходимые <see cref="T:System.Xml.XmlReader" /> для анализа фрагмента XML.</summary>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace)">
      <summary>Инициализирует новый экземпляр класса XmlParserContext с помощью указанных значений <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, базового URI, xml:space, xml:lang и значений типов документов.</summary>
      <param name="nt">Класс <see cref="T:System.Xml.XmlNameTable" />, используемый для разъединения строк.Если значение параметра равно null, вместо этого класса используется таблица имен, которая служит для создания <paramref name="nsMgr" />.Дополнительные сведения о разъединенных строках см. в разделе <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">Класс <see cref="T:System.Xml.XmlNamespaceManager" />, используемый для поиска сведений о пространстве имен, или значение null. </param>
      <param name="docTypeName">Имя объявления типа документа. </param>
      <param name="pubId">Открытый идентификатор. </param>
      <param name="sysId">Идентификатор системы. </param>
      <param name="internalSubset">Внутренний набор DTD.Набор DTD используется для разрешения сущностей, но не для проверки документа.</param>
      <param name="baseURI">Базовый URI для фрагмента XML (размещение, из которого загружен фрагмент). </param>
      <param name="xmlLang">Область xml:lang. </param>
      <param name="xmlSpace">Значение <see cref="T:System.Xml.XmlSpace" />, показывающее область xml:space. </param>
      <exception cref="T:System.Xml.XmlException">Параметр <paramref name="nt" /> отличается от таблицы XmlNameTable, используемой для создания объекта <paramref name="nsMgr" />. </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>Инициализирует новый экземпляр класса XmlParserContext с помощью указанных значений <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, базового URI, xml:space, xml:lang, кодировки и значений типов документов.</summary>
      <param name="nt">Класс <see cref="T:System.Xml.XmlNameTable" />, используемый для разъединения строк.Если значение параметра равно null, вместо этого класса используется таблица имен, которая служит для создания <paramref name="nsMgr" />.Дополнительные сведения о разъединенных строках см. в разделе <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">Класс <see cref="T:System.Xml.XmlNamespaceManager" />, используемый для поиска сведений о пространстве имен, или значение null. </param>
      <param name="docTypeName">Имя объявления типа документа. </param>
      <param name="pubId">Открытый идентификатор. </param>
      <param name="sysId">Идентификатор системы. </param>
      <param name="internalSubset">Внутренний набор DTD.DTD используется для разрешения сущностей, но не для проверки документа.</param>
      <param name="baseURI">Базовый URI для фрагмента XML (размещение, из которого загружен фрагмент). </param>
      <param name="xmlLang">Область xml:lang. </param>
      <param name="xmlSpace">Значение <see cref="T:System.Xml.XmlSpace" />, показывающее область xml:space. </param>
      <param name="enc">Объект <see cref="T:System.Text.Encoding" />, показывающий параметр кодировки. </param>
      <exception cref="T:System.Xml.XmlException">Параметр <paramref name="nt" /> отличается от таблицы XmlNameTable, используемой для создания объекта <paramref name="nsMgr" />. </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace)">
      <summary>Инициализирует новый экземпляр класса XmlParserContext с помощью заданных значений <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, xml:lang и xml:space.</summary>
      <param name="nt">Класс <see cref="T:System.Xml.XmlNameTable" />, используемый для разъединения строк.Если значение параметра равно null, вместо этого класса используется таблица имен, которая служит для создания <paramref name="nsMgr" />.Дополнительные сведения о разъединенных строках см. в разделе <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">Класс <see cref="T:System.Xml.XmlNamespaceManager" />, используемый для поиска сведений о пространстве имен, или значение null. </param>
      <param name="xmlLang">Область xml:lang. </param>
      <param name="xmlSpace">Значение <see cref="T:System.Xml.XmlSpace" />, показывающее область xml:space. </param>
      <exception cref="T:System.Xml.XmlException">Параметр <paramref name="nt" /> отличается от таблицы XmlNameTable, используемой для создания объекта <paramref name="nsMgr" />. </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>Инициализирует новый экземпляр класса XmlParserContext с помощью указанных значений <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, xml:lang, xml:space и кодировки.</summary>
      <param name="nt">Класс <see cref="T:System.Xml.XmlNameTable" />, используемый для разъединения строк.Если значение параметра равно null, вместо этого класса используется таблица имен, которая служит для создания <paramref name="nsMgr" />.Дополнительные сведения о разъединенных строках см. в разделе <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">Класс <see cref="T:System.Xml.XmlNamespaceManager" />, используемый для поиска сведений о пространстве имен, или значение null. </param>
      <param name="xmlLang">Область xml:lang. </param>
      <param name="xmlSpace">Значение <see cref="T:System.Xml.XmlSpace" />, показывающее область xml:space. </param>
      <param name="enc">Объект <see cref="T:System.Text.Encoding" />, показывающий параметр кодировки. </param>
      <exception cref="T:System.Xml.XmlException">Параметр <paramref name="nt" /> отличается от таблицы XmlNameTable, используемой для создания объекта <paramref name="nsMgr" />. </exception>
    </member>
    <member name="P:System.Xml.XmlParserContext.BaseURI">
      <summary>Получает или задает базовый URI.</summary>
      <returns>Базовый URI, используемый для разрешения файла DTD.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.DocTypeName">
      <summary>Получает или задает имя объявления типа документа.</summary>
      <returns>Имя объявления типа документа.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.Encoding">
      <summary>Получает или задает тип кодировки.</summary>
      <returns>Объект <see cref="T:System.Text.Encoding" />, показывающий тип кодировки.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.InternalSubset">
      <summary>Получает или задает внутренний набор DTD.</summary>
      <returns>Внутренний набор DTD.Например, данное свойство возвращает содержимое между квадратными скобками &lt;!DOCTYPE doc [...]&gt;.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NamespaceManager">
      <summary>Получает или задает объект <see cref="T:System.Xml.XmlNamespaceManager" />.</summary>
      <returns>XmlNamespaceManager.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NameTable">
      <summary>Получает класс <see cref="T:System.Xml.XmlNameTable" />, используемый для разъединения строк.Дополнительные сведения о разъединенных строках см. в разделе <see cref="T:System.Xml.XmlNameTable" />.</summary>
      <returns>XmlNameTable.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.PublicId">
      <summary>Получает или задает открытый идентификатор.</summary>
      <returns>Открытый идентификатор.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.SystemId">
      <summary>Получает или задает идентификатор системы.</summary>
      <returns>Идентификатор системы.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlLang">
      <summary>Получает или задает текущую область xml:lang.</summary>
      <returns>Текущая ограниченная область действия xml:lang.Если в области отсутствует xml:lang, возвращается значение String.Empty.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlSpace">
      <summary>Получает или задает текущую область xml:space.</summary>
      <returns>Значение <see cref="T:System.Xml.XmlSpace" />, показывающее область xml:space.</returns>
    </member>
    <member name="T:System.Xml.XmlQualifiedName">
      <summary>Представляет полное имя XML.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.XmlQualifiedName" /> с указанным именем.</summary>
      <param name="name">Локальное имя, используемое в качестве имени объекта <see cref="T:System.Xml.XmlQualifiedName" />. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.XmlQualifiedName" /> с заданными именем и пространством имен.</summary>
      <param name="name">Локальное имя, используемое в качестве имени объекта <see cref="T:System.Xml.XmlQualifiedName" />. </param>
      <param name="ns">Пространство имен для объекта <see cref="T:System.Xml.XmlQualifiedName" />. </param>
    </member>
    <member name="F:System.Xml.XmlQualifiedName.Empty">
      <summary>Предоставляет пустое полное имя <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект <see cref="T:System.Xml.XmlQualifiedName" /> текущему объекту <see cref="T:System.Xml.XmlQualifiedName" />. </summary>
      <returns>Значение true, если оба объекта являются одним и тем же объектом экземпляра; в противном случае — значение false.</returns>
      <param name="other">Объект <see cref="T:System.Xml.XmlQualifiedName" /> для сравнения. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.GetHashCode">
      <summary>Возвращает хэш-код для <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Хэш-код объекта.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.IsEmpty">
      <summary>Получает значение, определяющее, пуст ли объект <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Значение true, если имя и пространство имен представляют собой пустые строки; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Name">
      <summary>Получает строковое представление полного имени объекта <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Строковое представление полного имени или String.Empty, если для объекта не определено имя.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Namespace">
      <summary>Получает строковое представление пространства имен для объекта <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Строковое представление пространства имен или String.Empty, если для объекта не определено пространство имен.</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Equality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>Сравнивает два объекта <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Значение true, если у двух объектов совпадают имена и пространства имен; в противном случае — значение false.</returns>
      <param name="a">Объект <see cref="T:System.Xml.XmlQualifiedName" /> для сравнения. </param>
      <param name="b">Объект <see cref="T:System.Xml.XmlQualifiedName" /> для сравнения. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Inequality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>Сравнивает два объекта <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Значение true, если у двух объектов не совпадают имена и пространства имен; в противном случае — значение false.</returns>
      <param name="a">Объект <see cref="T:System.Xml.XmlQualifiedName" /> для сравнения. </param>
      <param name="b">Объект <see cref="T:System.Xml.XmlQualifiedName" /> для сравнения. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString">
      <summary>Возвращает строковое значение полного имени <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Строковое значение полного имени <see cref="T:System.Xml.XmlQualifiedName" /> в формате namespace:localname.Если у объекта не определено пространство имен, данный метод вернет только локальное имя.</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString(System.String,System.String)">
      <summary>Возвращает строковое значение полного имени <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Строковое значение полного имени <see cref="T:System.Xml.XmlQualifiedName" /> в формате namespace:localname.Если у объекта не определено пространство имен, данный метод вернет только локальное имя.</returns>
      <param name="name">Имя объекта. </param>
      <param name="ns">Пространство имен объекта. </param>
    </member>
    <member name="T:System.Xml.XmlReader">
      <summary>Предоставляет средство чтения, обеспечивающее быстрый прямой доступ (без кэширования) к данным XML.Чтобы просмотреть исходный код .NET Framework для этого типа, см. ссылки на источник.</summary>
    </member>
    <member name="M:System.Xml.XmlReader.#ctor">
      <summary>Инициализирует новый экземпляр класса XmlReader.</summary>
    </member>
    <member name="P:System.Xml.XmlReader.AttributeCount">
      <summary>Когда переопределено в производном классе, возвращает количество атрибутов текущего узла.</summary>
      <returns>Количество атрибутов текущего узла.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.BaseURI">
      <summary>Когда переопределено в производном классе, возвращает базовый URI текущего узла.</summary>
      <returns>Базовый URI текущего узла.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadBinaryContent">
      <summary>Получает значение, указывающее, реализует ли объект <see cref="T:System.Xml.XmlReader" /> методы чтения двоичного содержимого.</summary>
      <returns>Значение true, если реализуются методы чтения двоичного содержимого; в противном случае — false.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadValueChunk">
      <summary>Возвращает значение, указывающее, реализует ли объект <see cref="T:System.Xml.XmlReader" /> метод <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" />.</summary>
      <returns>Значение true, если объект <see cref="T:System.Xml.XmlReader" /> реализует метод <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" />; в противном случае false.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanResolveEntity">
      <summary>Возвращает значение, определяющее, способно ли данное средство чтения выполнять синтаксический анализ и разрешение сущностей.</summary>
      <returns>Значение true, если средство чтения позволяет анализировать и разрешать объекты; в противном случае — false.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream)">
      <summary>Создает новый <see cref="T:System.Xml.XmlReader" /> с помощью указанного потока с параметрами по умолчанию.</summary>
      <returns>Объект, используемый для чтения XML-данных в потоке.</returns>
      <param name="input">Поток, содержащий XML-данные.<see cref="T:System.Xml.XmlReader" /> просматривает первые байты потока в поисках метки порядка следования байтов или другого признака кодировки.Эта кодировка после определения используется в последующем считывании потока, а процедура обработки продолжает анализировать входные данные как поток символов Юникода.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="input" /> — null.</exception>
      <exception cref="T:System.Security.SecurityException">У объекта <see cref="T:System.Xml.XmlReader" /> нет достаточных разрешений для доступа к расположению XML-данных.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings)">
      <summary>Создает новый <see cref="T:System.Xml.XmlReader" /> экземпляра с параметрами и указанного потока.</summary>
      <returns>Объект, используемый для чтения XML-данных в потоке.</returns>
      <param name="input">Поток, содержащий XML-данные.<see cref="T:System.Xml.XmlReader" /> просматривает первые байты потока в поисках метки порядка следования байтов или другого признака кодировки.Эта кодировка после определения используется в последующем считывании потока, а процедура обработки продолжает анализировать входные данные как поток символов Юникода.</param>
      <param name="settings">Параметры для нового <see cref="T:System.Xml.XmlReader" /> экземпляра.Данное значение может быть null.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="input" /> — null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>Создает новый <see cref="T:System.Xml.XmlReader" /> экземпляра, используя указанные сведения о потоке, параметры и контекст для синтаксического анализа.</summary>
      <returns>Объект, используемый для чтения XML-данных в потоке.</returns>
      <param name="input">Поток, содержащий XML-данные. <see cref="T:System.Xml.XmlReader" /> просматривает первые байты потока в поисках метки порядка следования байтов или другого признака кодировки.Эта кодировка после определения используется в последующем считывании потока, а процедура обработки продолжает анализировать входные данные как поток символов Юникода.</param>
      <param name="settings">Параметры для нового <see cref="T:System.Xml.XmlReader" /> экземпляра.Данное значение может быть null.</param>
      <param name="inputContext">Сведения о контексте, необходимыми для синтаксического анализа XML-фрагмент.Контекстные сведения могут содержать используемый класс <see cref="T:System.Xml.XmlNameTable" />, кодировку, область пространства имен, текущий xml:lang, область xml:space, базовый URI и DTD.Данное значение может быть null.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="input" /> — null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader)">
      <summary>Создает новый <see cref="T:System.Xml.XmlReader" /> экземпляра с помощью модуля чтения указанного текста.</summary>
      <returns>Объект, используемый для чтения XML-данных в потоке.</returns>
      <param name="input">Модуль чтения текста для чтения XML-данных.Модуль чтения текста возвращает поток символов Юникода, поэтому кодировки, указанной в объявлении XML не используется средство чтения XML для декодирования потока данных.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="input" /> — null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings)">
      <summary>Создает новый <see cref="T:System.Xml.XmlReader" /> экземпляра, используя указанный текст чтения и параметры.</summary>
      <returns>Объект, используемый для чтения XML-данных в потоке.</returns>
      <param name="input">Модуль чтения текста для чтения XML-данных.Модуль чтения текста возвращает поток символов Юникода, поэтому кодировки, указанной в объявлении XML не используется средством чтения XML для декодирования потока данных.</param>
      <param name="settings">Параметры для нового <see cref="T:System.Xml.XmlReader" />.Данное значение может быть null.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="input" /> — null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>Создает новый <see cref="T:System.Xml.XmlReader" /> экземпляра, используя указанный текст чтения, параметры и контекст сведения для синтаксического анализа.</summary>
      <returns>Объект, используемый для чтения XML-данных в потоке.</returns>
      <param name="input">Модуль чтения текста для чтения XML-данных.Модуль чтения текста возвращает поток символов Юникода, поэтому кодировки, указанной в объявлении XML не используется средством чтения XML для декодирования потока данных.</param>
      <param name="settings">Параметры для нового <see cref="T:System.Xml.XmlReader" /> экземпляра.Данное значение может быть null.</param>
      <param name="inputContext">Сведения о контексте, необходимыми для синтаксического анализа XML-фрагмент.Контекстные сведения могут содержать используемый класс <see cref="T:System.Xml.XmlNameTable" />, кодировку, область пространства имен, текущий xml:lang, область xml:space, базовый URI и DTD.Данное значение может быть null.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="input" /> — null.</exception>
      <exception cref="T:System.ArgumentException">Значения присвоены как свойству <see cref="P:System.Xml.XmlReaderSettings.NameTable" />, так и свойству <see cref="P:System.Xml.XmlParserContext.NameTable" />.(Только одно из этих свойств NameTable можно установить и использовать).</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String)">
      <summary>Создает новый экземпляр <see cref="T:System.Xml.XmlReader" /> с указанным URI.</summary>
      <returns>Объект, используемый для чтения XML-данных в потоке.</returns>
      <param name="inputUri">URI для файла, содержащего XML-данные.Класс <see cref="T:System.Xml.XmlUrlResolver" /> используется для преобразования пути к классическому формату данных.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="inputUri" /> — null.</exception>
      <exception cref="T:System.Security.SecurityException">У объекта <see cref="T:System.Xml.XmlReader" /> нет достаточных разрешений для доступа к расположению XML-данных.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл, указанный URI, не существует.</exception>
      <exception cref="T:System.UriFormatException">В .NET для приложений Магазина Windows или переносимой библиотеке классов вместо этого перехватите исключение базового класса <see cref="T:System.FormatException" />.Формат URI является неправильным.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String,System.Xml.XmlReaderSettings)">
      <summary>Создает новый <see cref="T:System.Xml.XmlReader" /> экземпляра, используя указанный URI и параметры.</summary>
      <returns>Объект, используемый для чтения XML-данных в потоке.</returns>
      <param name="inputUri">URI файла с XML-данными.Объект <see cref="T:System.Xml.XmlResolver" /> в объекте <see cref="T:System.Xml.XmlReaderSettings" /> используется для преобразования пути в стандартный формат данных.Если <see cref="P:System.Xml.XmlReaderSettings.XmlResolver" /> равно null, используется объект <see cref="T:System.Xml.XmlUrlResolver" />.</param>
      <param name="settings">Параметры для нового <see cref="T:System.Xml.XmlReader" /> экземпляра.Данное значение может быть null.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="inputUri" /> — null.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Не удается найти файл, заданный URI.</exception>
      <exception cref="T:System.UriFormatException">В .NET для приложений Магазина Windows или переносимой библиотеке классов вместо этого перехватите исключение базового класса <see cref="T:System.FormatException" />.Формат URI является неправильным.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.Xml.XmlReader,System.Xml.XmlReaderSettings)">
      <summary>Создает новый <see cref="T:System.Xml.XmlReader" /> экземпляра, используя указанное средство чтения XML и параметры.</summary>
      <returns>Объект, который заключается в оболочку вокруг указанного <see cref="T:System.Xml.XmlReader" /> объекта.</returns>
      <param name="reader">Объект, который требуется использовать в качестве базового средства чтения XML.</param>
      <param name="settings">Параметры для нового <see cref="T:System.Xml.XmlReader" /> экземпляра.Уровень соответствия объекта <see cref="T:System.Xml.XmlReaderSettings" /> должен или быть равным уровню соответствия базового средства чтения, или иметь значение <see cref="F:System.Xml.ConformanceLevel.Auto" />.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="reader" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Если объект <see cref="T:System.Xml.XmlReaderSettings" /> задает уровень соответствия, который не согласован с уровнем соответствия базового средства чтения.-или-Базовый объект <see cref="T:System.Xml.XmlReader" /> находится в состоянии <see cref="F:System.Xml.ReadState.Error" /> или <see cref="F:System.Xml.ReadState.Closed" />.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Depth">
      <summary>Когда переопределено в производном классе, возвращает глубину текущего узла в XML-документе.</summary>
      <returns>Глубина текущего узла в XML-документе.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose">
      <summary>Освобождает все ресурсы, используемые текущим экземпляром класса <see cref="T:System.Xml.XmlReader" />.</summary>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose(System.Boolean)">
      <summary>Освобождает неуправляемые ресурсы, используемые объектом <see cref="T:System.Xml.XmlReader" />, а при необходимости освобождает также управляемые ресурсы.</summary>
      <param name="disposing">trueЧтобы освободить управляемые и неуправляемые ресурсы; false чтобы освободить только неуправляемые ресурсы.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.EOF">
      <summary>Когда переопределено в производном классе, возвращает значение, показывающее, позиционировано ли средство чтения в конец потока.</summary>
      <returns>Значение true, если средство чтения установлено в конец потока; в противном случае — false.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.Int32)">
      <summary>Когда переопределено в производном классе, возвращает значение атрибута по указанному индексу.</summary>
      <returns>Значение указанного атрибута.Этот метод не изменяет позицию средства чтения.</returns>
      <param name="i">Индекс атрибута.Индексация начинается с нуля.(Индекс первого атрибута равен нулю.)</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="i" /> выходит за пределы допустимого диапазона.Оно должно быть неотрицательным и меньшим, чем размер коллекции атрибутов.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String)">
      <summary>При переопределении в производном классе получает значение атрибута с указанным свойством <see cref="P:System.Xml.XmlReader.Name" />.</summary>
      <returns>Значение указанного атрибута.Если атрибут не найден или значение равно String.Empty, возвращается значение null.</returns>
      <param name="name">Полное имя атрибута.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" />is null.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String,System.String)">
      <summary>При переопределении в производном классе получает значение атрибута с указанными свойствами <see cref="P:System.Xml.XmlReader.LocalName" /> и <see cref="P:System.Xml.XmlReader.NamespaceURI" />.</summary>
      <returns>Значение указанного атрибута.Если атрибут не найден или значение равно String.Empty, возвращается значение null.Этот метод не изменяет позицию средства чтения.</returns>
      <param name="name">Локальное имя атрибута.</param>
      <param name="namespaceURI">URI пространства имен атрибута.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" />is null.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetValueAsync">
      <summary>Асинхронно возвращает значение текущего узла.</summary>
      <returns>Значение текущего узла.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.InvalidOperationException">Асинхронный метод <see cref="T:System.Xml.XmlReader" /> вызван без присвоения флагу <see cref="P:System.Xml.XmlReaderSettings.Async" /> значения true.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "Если требуется использовать асинхронные методы, присвойте свойству XmlReaderSettings.Async значение true".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasAttributes">
      <summary>Возвращает значение, показывающее, имеются ли атрибуты у текущего узла.</summary>
      <returns>Значение true, если текущий узел содержит атрибуты; в противном случае — false.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasValue">
      <summary>При переопределении в производном классе получает значение, показывающее, имеет ли текущий узел свойство <see cref="P:System.Xml.XmlReader.Value" />.</summary>
      <returns>Значение true, если узел, на котором расположено средство чтения, может иметь значение Value; в противном случае — false.Если значение равно false, узел принимает значение String.Empty.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsDefault">
      <summary>Когда переопределено в производном классе, возвращает значение, определяющее, является ли текущий узел атрибутом, созданным из значения по умолчанию, определенного в DTD или схеме.</summary>
      <returns>Значение true, если текущий узел является атрибутом, значение которого было создано из значения по умолчанию, определенного в DTD или схеме; значение false, если значение атрибута было задано явно.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsEmptyElement">
      <summary>При переопределении в производном классе получает значение, определяющее, является ли текущий узел пустым элементом (например, &lt;MyElement/&gt;).</summary>
      <returns>Значение true, если текущий узел является элементом (свойство <see cref="P:System.Xml.XmlReader.NodeType" /> имеет значение XmlNodeType.Element), который заканчивается на /&gt;; в противном случае — false.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsName(System.String)">
      <summary>Возвращает значение, определяющее, является ли строковый аргумент допустимым именем XML.</summary>
      <returns>Значение true, если имя является допустимым; в противном случае — false.</returns>
      <param name="str">Имя для проверки.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="str" /> — null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsNameToken(System.String)">
      <summary>Возвращает значение, определяющее, является ли строковый аргумент допустимым токеном имени XML.</summary>
      <returns>Значение true, если аргумент является допустимой лексемой имени; в противном случае — false.</returns>
      <param name="str">Токен имени для проверки.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="str" /> — null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement">
      <summary>Вызывает метод <see cref="M:System.Xml.XmlReader.MoveToContent" /> и проверяет, является ли текущий узел содержимого открывающим тегом или пустым тегом элемента.</summary>
      <returns>Значение true, если метод <see cref="M:System.Xml.XmlReader.MoveToContent" /> находит открывающий тег или пустой тег элемента; значение false, если тип найденного узла отличается от XmlNodeType.Element.</returns>
      <exception cref="T:System.Xml.XmlException">Во входном потоке обнаружен неправильный XML-код.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String)">
      <summary>Вызывает метод <see cref="M:System.Xml.XmlReader.MoveToContent" /> и проверяет, является ли текущий узел содержимого открывающим тегом или пустым тегом элемента, а также соответствует ли значение свойства <see cref="P:System.Xml.XmlReader.Name" /> элемента заданному аргументу.</summary>
      <returns>Значение true, если полученный в результате узел является элементом, а свойство Name совпадает с указанной строкой.Значение false, если обнаружен узел с типом, отличным от XmlNodeType.Element, или если свойство Name элемента не совпадает с указанной строкой.</returns>
      <param name="name">Строка противопоставляется значению свойства Name найденного элемента.</param>
      <exception cref="T:System.Xml.XmlException">Во входном потоке обнаружен неправильный XML-код.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String,System.String)">
      <summary>Вызывает метод <see cref="M:System.Xml.XmlReader.MoveToContent" /> и проверяет, является ли текущий узел содержимого открывающим тегом или пустым тегом элемента, а также соответствуют ли значения свойств <see cref="P:System.Xml.XmlReader.LocalName" /> и <see cref="P:System.Xml.XmlReader.NamespaceURI" /> элемента заданным строкам.</summary>
      <returns>Значение true, если полученный в результате узел является элементом.Значение false, если обнаружен узел с типом, отличным от XmlNodeType.Element, или если свойства LocalName и NamespaceURI элемента не совпадают с указанными строками.</returns>
      <param name="localname">Строка, которая противопоставляется значению свойства LocalName найденного элемента.</param>
      <param name="ns">Строка, которая противопоставляется значению свойства NamespaceURI найденного элемента.</param>
      <exception cref="T:System.Xml.XmlException">Во входном потоке обнаружен неправильный XML-код.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.Int32)">
      <summary>Когда переопределено в производном классе, возвращает значение атрибута по указанному индексу.</summary>
      <returns>Значение указанного атрибута.</returns>
      <param name="i">Индекс атрибута.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String)">
      <summary>При переопределении в производном классе получает значение атрибута с указанным свойством <see cref="P:System.Xml.XmlReader.Name" />.</summary>
      <returns>Значение указанного атрибута.Если атрибут не найден, возвращается значение null.</returns>
      <param name="name">Полное имя атрибута.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String,System.String)">
      <summary>При переопределении в производном классе получает значение атрибута с указанными свойствами <see cref="P:System.Xml.XmlReader.LocalName" /> и <see cref="P:System.Xml.XmlReader.NamespaceURI" />.</summary>
      <returns>Значение указанного атрибута.Если атрибут не найден, возвращается значение null.</returns>
      <param name="name">Локальное имя атрибута.</param>
      <param name="namespaceURI">URI пространства имен атрибута.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.LocalName">
      <summary>Когда переопределено в производном классе, возвращает локальное имя текущего узла.</summary>
      <returns>Имя текущего узла с удаленным префиксом.Например, LocalName имеет значение book для элемента &lt;bk:book&gt;.Для безымянных типов узлов (например, Text, Comment и т. д.) данное свойство возвращает String.Empty.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.LookupNamespace(System.String)">
      <summary>Когда переопределено в производном классе, разрешает префикс пространства имен в области видимости текущего элемента.</summary>
      <returns>URI пространства имен, которое отображает префикс, или значение null, если соответствующий префикс не найден.</returns>
      <param name="prefix">Префикс, для которого требуется разрешить URI пространства имен.Чтобы сопоставить пространство имен по умолчанию, необходимо передать пустую строку.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.Int32)">
      <summary>Когда переопределено в производном классе, переходит к атрибуту с указанным индексом.</summary>
      <param name="i">Индекс атрибута.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр имеет отрицательное значение.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String)">
      <summary>При переопределении в производном классе перемещает к атрибуту с указанным <see cref="P:System.Xml.XmlReader.Name" />.</summary>
      <returns>Значение true, если атрибут найден; в противном случае — false.Если значение false, позиция средства чтения не изменяется.</returns>
      <param name="name">Полное имя атрибута.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.ArgumentException">Параметр является пустой строкой.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String,System.String)">
      <summary>При переопределении в производном классе перемещает к атрибуту с указанными <see cref="P:System.Xml.XmlReader.LocalName" /> и <see cref="P:System.Xml.XmlReader.NamespaceURI" />.</summary>
      <returns>Значение true, если атрибут найден; в противном случае — false.Если значение false, позиция средства чтения не изменяется.</returns>
      <param name="name">Локальное имя атрибута.</param>
      <param name="ns">URI пространства имен атрибута.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.ArgumentNullException">Оба параметра имеют значение null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContent">
      <summary>Проверяет, является ли текущий узел узлом содержимого (текст без пустого пространства, CDATA, Element, EndElement, EntityReference или EndEntity).Если узел не является узлом содержимого, средство чтения пропускает этот узел и переходит к следующему узлу содержимого или в конец файла.Пропускаются узлы следующих типов: ProcessingInstruction, DocumentType, Comment, Whitespace и SignificantWhitespace.</summary>
      <returns>Значение <see cref="P:System.Xml.XmlReader.NodeType" /> для текущего узла, найденного с помощью метода, или значение XmlNodeType.None, если средство чтения достигло конца потока входных данных.</returns>
      <exception cref="T:System.Xml.XmlException">В входном потоке обнаружен неправильный XML.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContentAsync">
      <summary>В асинхронном режиме проверяет, является ли текущий узел узлом содержимого.Если узел не является узлом содержимого, средство чтения пропускает этот узел и переходит к следующему узлу содержимого или в конец файла.</summary>
      <returns>Значение <see cref="P:System.Xml.XmlReader.NodeType" /> для текущего узла, найденного с помощью метода, или значение XmlNodeType.None, если средство чтения достигло конца потока входных данных.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.InvalidOperationException">Асинхронный метод <see cref="T:System.Xml.XmlReader" /> вызван без присвоения флагу <see cref="P:System.Xml.XmlReaderSettings.Async" /> значения true.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "Если требуется использовать асинхронные методы, присвойте свойству XmlReaderSettings.Async значение true".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToElement">
      <summary>Когда переопределено в производном классе, переходит к элементу, содержащему текущий узел атрибута.</summary>
      <returns>Значение true, если средство чтения находится на атрибуте (средство чтения перемещается к элементу с этим атрибутом); в противном случае — false (позиция средства чтения не изменяется).</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToFirstAttribute">
      <summary>Когда переопределено в производном классе, переходит к первому атрибуту.</summary>
      <returns>Значение true, если атрибут существует (средство чтения перемещается к первому атрибуту); в противном случае — false (позиция средства чтения не изменяется).</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToNextAttribute">
      <summary>Когда переопределено в производном классе, переходит к следующему атрибуту.</summary>
      <returns>Значение true, если присутствует следующий атрибут; значение false, если другие атрибуты отсутствуют.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Name">
      <summary>Когда переопределено в производном классе, возвращает полное имя текущего узла.</summary>
      <returns>Полное имя текущего узла.Например, Name имеет значение bk:book для элемента &lt;bk:book&gt;.Возвращаемое имя зависит от значения свойства <see cref="P:System.Xml.XmlReader.NodeType" /> узла.Значения возвращаются для представленных ниже типов узлов.Для других типов узлов возвращается пустая строка.Тип узла Имя AttributeИмя атрибута. DocumentTypeИмя типа документа. ElementИмя тега. EntityReferenceИмя сущности, на которую существует ссылка. ProcessingInstructionКонечное приложение инструкции обработки. XmlDeclarationСтрока символов xml. </returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NamespaceURI">
      <summary>Когда переопределено в производном классе, возвращает URI пространства имен (определенное в спецификации W3C Namespace) узла, на котором расположено средство чтения.</summary>
      <returns>Пространство имен URI текущего узла; в противном случае — пустая строка.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NameTable">
      <summary>При переопределении в производном классе получает класс <see cref="T:System.Xml.XmlNameTable" />, связанный с данной реализацией.</summary>
      <returns>Класс XmlNameTable, позволяющий получать в узле разделенную версию строки.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NodeType">
      <summary>Когда переопределено в производном классе, возвращает тип текущего узла.</summary>
      <returns>Одно из значений перечисления, которые задают тип текущего узла.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Prefix">
      <summary>Когда переопределено в производном классе, возвращает префикс пространства имен, связанный с текущим узлом.</summary>
      <returns>Префикс пространства имен, связанный с текущим узлом.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Read">
      <summary>При переопределении в производном классе считывает следующий узел из потока.</summary>
      <returns>trueЕсли чтение прошло успешно. в противном случае — false.</returns>
      <exception cref="T:System.Xml.XmlException">При синтаксическом анализе XML возникла ошибка.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAsync">
      <summary>Асинхронно считывает следующий узел из потока.</summary>
      <returns>Значение true, если чтение прошло успешно; значение false, если отсутствуют узлы для чтения.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.InvalidOperationException">Асинхронный метод <see cref="T:System.Xml.XmlReader" /> вызван без присвоения флагу <see cref="P:System.Xml.XmlReaderSettings.Async" /> значения true.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "Если требуется использовать асинхронные методы, присвойте свойству XmlReaderSettings.Async значение true".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAttributeValue">
      <summary>При переопределении в производном классе разбирает значение атрибута в один или более узлов Text, EntityReference или EndEntity.</summary>
      <returns>Значение true, если присутствуют возвращаемые узлы.Значение false, если средство чтения не расположено на узле атрибута при первом вызове или все значения атрибута считаны.Пустой атрибут (например, misc="") возвращает значение true с отдельным узлом, имеющим значение String.Empty.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Считывает содержимое объекта указанного типа.</summary>
      <returns>Объединенное текстовое содержимое или значение атрибута, преобразованное в требуемый тип.</returns>
      <param name="returnType">Тип возвращаемого значения.Примечание.   С выпуском платформы .NET Framework 3.5 значение параметра <paramref name="returnType" /> может иметь тип <see cref="T:System.DateTimeOffset" />.</param>
      <param name="namespaceResolver">Объект <see cref="T:System.Xml.IXmlNamespaceResolver" />, используемый для разрешения любых префиксов пространств имен, имеющих отношение к преобразованию типов.Например, этот объект можно использовать при преобразовании объекта <see cref="T:System.Xml.XmlQualifiedName" /> в xs:string.Данное значение может быть null.</param>
      <exception cref="T:System.FormatException">Содержимое имеет неверный формат для типа целевого объекта.</exception>
      <exception cref="T:System.InvalidCastException">Недопустимая попытка приведения.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="returnType" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Текущий узел не принадлежит к поддерживаемому типу узлов.Дополнительные сведения приведены в таблице ниже.</exception>
      <exception cref="T:System.OverflowException">Чтение значения Decimal.MaxValue.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Асинхронно считывает содержимое как объект указанного типа.</summary>
      <returns>Объединенное текстовое содержимое или значение атрибута, преобразованное в требуемый тип.</returns>
      <param name="returnType">Тип возвращаемого значения.</param>
      <param name="namespaceResolver">Объект <see cref="T:System.Xml.IXmlNamespaceResolver" />, используемый для разрешения любых префиксов пространств имен, имеющих отношение к преобразованию типов.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.InvalidOperationException">Асинхронный метод <see cref="T:System.Xml.XmlReader" /> вызван без присвоения флагу <see cref="P:System.Xml.XmlReaderSettings.Async" /> значения true.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "Если требуется использовать асинхронные методы, присвойте свойству XmlReaderSettings.Async значение true".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>Считывает содержимое и возвращает раскодированные двоичные байты Base64.</summary>
      <returns>Количество байтов, записанных в буфер.</returns>
      <param name="buffer">Буфер, в который копируется полученный текст.Это значение не может быть равно null.</param>
      <param name="index">Смещение в буфере, с которого следует начать копировать результат.</param>
      <param name="count">Максимальное количество копируемых в буфер байтов.Этот метод возвращает фактическое количество скопированных байтов.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="buffer" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)" /> не поддерживается на текущем узле.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение индекса в буфере или сумма значений индекса и счетчика больше, чем выделенный размер буфера.</exception>
      <exception cref="T:System.NotSupportedException">Реализация <see cref="T:System.Xml.XmlReader" /> не поддерживает данный метод.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Асинхронно считывает содержимое и возвращает декодированные из кодировки Base64 двоичные байты.</summary>
      <returns>Количество байтов, записанных в буфер.</returns>
      <param name="buffer">Буфер, в который копируется полученный текст.Это значение не может быть равно null.</param>
      <param name="index">Смещение в буфере, с которого следует начать копировать результат.</param>
      <param name="count">Максимальное количество копируемых в буфер байтов.Этот метод возвращает фактическое количество скопированных байтов.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.InvalidOperationException">Асинхронный метод <see cref="T:System.Xml.XmlReader" /> вызван без присвоения флагу <see cref="P:System.Xml.XmlReaderSettings.Async" /> значения true.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "Если требуется использовать асинхронные методы, присвойте свойству XmlReaderSettings.Async значение true".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>Считывает содержимое и возвращает раскодированные двоичные байты BinHex.</summary>
      <returns>Количество байтов, записанных в буфер.</returns>
      <param name="buffer">Буфер, в который копируется полученный текст.Это значение не может быть равно null.</param>
      <param name="index">Смещение в буфере, с которого следует начать копировать результат.</param>
      <param name="count">Максимальное количество копируемых в буфер байтов.Этот метод возвращает фактическое количество скопированных байтов.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="buffer" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)" /> не поддерживается на текущем узле.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение индекса в буфере или сумма значений индекса и счетчика больше, чем выделенный размер буфера.</exception>
      <exception cref="T:System.NotSupportedException">Реализация <see cref="T:System.Xml.XmlReader" /> не поддерживает данный метод.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Асинхронно считывает содержимое и возвращает раскодированные двоичные байты BinHex.</summary>
      <returns>Количество байтов, записанных в буфер.</returns>
      <param name="buffer">Буфер, в который копируется полученный текст.Это значение не может быть равно null.</param>
      <param name="index">Смещение в буфере, с которого следует начать копировать результат.</param>
      <param name="count">Максимальное количество копируемых в буфер байтов.Этот метод возвращает фактическое количество скопированных байтов.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.InvalidOperationException">Асинхронный метод <see cref="T:System.Xml.XmlReader" /> вызван без присвоения флагу <see cref="P:System.Xml.XmlReaderSettings.Async" /> значения true.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "Если требуется использовать асинхронные методы, присвойте свойству XmlReaderSettings.Async значение true".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBoolean">
      <summary>Считывает содержимое текста в текущей позиции как значение Boolean.</summary>
      <returns>Текстовое содержимое в виде объекта <see cref="T:System.Boolean" />.</returns>
      <exception cref="T:System.InvalidCastException">Недопустимая попытка приведения.</exception>
      <exception cref="T:System.FormatException">Недопустимый формат строки.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDateTimeOffset">
      <summary>Считывает содержимое текста в текущем положении как объект <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>Текстовое содержимое в виде объекта <see cref="T:System.DateTimeOffset" />.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDecimal">
      <summary>Считывает содержимое текста в текущем положении как объект <see cref="T:System.Decimal" />.</summary>
      <returns>Содержимое текста в текущей позиции как объект <see cref="T:System.Decimal" />.</returns>
      <exception cref="T:System.InvalidCastException">Недопустимая попытка приведения.</exception>
      <exception cref="T:System.FormatException">Недопустимый формат строки.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDouble">
      <summary>Считывает текстовое содержимое в текущей позиции как число с плавающей запятой двойной точности.</summary>
      <returns>Текстовое содержимое в виде числа с плавающей запятой двойной точности.</returns>
      <exception cref="T:System.InvalidCastException">Недопустимая попытка приведения.</exception>
      <exception cref="T:System.FormatException">Недопустимый формат строки.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsFloat">
      <summary>Считывает содержимое текста в текущей позиции как число с плавающей запятой одиночной точности.</summary>
      <returns>Содержимое текста в текущей позиции как число с плавающей запятой одиночной точности.</returns>
      <exception cref="T:System.InvalidCastException">Недопустимая попытка приведения.</exception>
      <exception cref="T:System.FormatException">Недопустимый формат строки.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsInt">
      <summary>Считывает текстовое содержимое в текущей позиции как 32-разрядное целое число со знаком.</summary>
      <returns>Содержимое как 32-разрядное целое число со знаком.</returns>
      <exception cref="T:System.InvalidCastException">Недопустимая попытка приведения.</exception>
      <exception cref="T:System.FormatException">Недопустимый формат строки.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsLong">
      <summary>Считывает текстовое содержимое в текущей позиции как 64-разрядное целое число со знаком.</summary>
      <returns>Содержимое как 64-разрядное целое число со знаком.</returns>
      <exception cref="T:System.InvalidCastException">Недопустимая попытка приведения.</exception>
      <exception cref="T:System.FormatException">Недопустимый формат строки.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObject">
      <summary>Считывает содержимое текста в текущей позиции как значение <see cref="T:System.Object" />.</summary>
      <returns>Текстовое содержимое как самый подходящий объект CLR.</returns>
      <exception cref="T:System.InvalidCastException">Недопустимая попытка приведения.</exception>
      <exception cref="T:System.FormatException">Недопустимый формат строки.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObjectAsync">
      <summary>Асинхронно считывает содержимое текста в текущем положении как объект <see cref="T:System.Object" />.</summary>
      <returns>Текстовое содержимое как самый подходящий объект CLR.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.InvalidOperationException">Асинхронный метод <see cref="T:System.Xml.XmlReader" /> вызван без присвоения флагу <see cref="P:System.Xml.XmlReaderSettings.Async" /> значения true.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "Если требуется использовать асинхронные методы, присвойте свойству XmlReaderSettings.Async значение true".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsString">
      <summary>Считывает содержимое текста в текущем положении как объект <see cref="T:System.String" />.</summary>
      <returns>Текстовое содержимое в виде объекта <see cref="T:System.String" />.</returns>
      <exception cref="T:System.InvalidCastException">Недопустимая попытка приведения.</exception>
      <exception cref="T:System.FormatException">Недопустимый формат строки.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsStringAsync">
      <summary>Асинхронно считывает содержимое текста в текущем положении как объект <see cref="T:System.String" />.</summary>
      <returns>Текстовое содержимое в виде объекта <see cref="T:System.String" />.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.InvalidOperationException">Асинхронный метод <see cref="T:System.Xml.XmlReader" /> вызван без присвоения флагу <see cref="P:System.Xml.XmlReaderSettings.Async" /> значения true.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "Если требуется использовать асинхронные методы, присвойте свойству XmlReaderSettings.Async значение true".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Считывает содержимое элемента в качестве требуемого типа.</summary>
      <returns>Содержимое элемента, преобразованное в требуемый типизированный объект.</returns>
      <param name="returnType">Тип возвращаемого значения.Примечание.   С выпуском платформы .NET Framework 3.5 значение параметра <paramref name="returnType" /> может иметь тип <see cref="T:System.DateTimeOffset" />.</param>
      <param name="namespaceResolver">Объект <see cref="T:System.Xml.IXmlNamespaceResolver" />, используемый для разрешения любых префиксов пространств имен, имеющих отношение к преобразованию типов.</param>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Xml.XmlReader" /> не расположен на элементе.</exception>
      <exception cref="T:System.Xml.XmlException">Текущий элемент содержит дочерние элементы.-или-Не удается преобразовать содержимое элемента в запрошенный тип.</exception>
      <exception cref="T:System.ArgumentNullException">Метод вызван с аргументами null.</exception>
      <exception cref="T:System.OverflowException">Чтение значения Decimal.MaxValue.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver,System.String,System.String)">
      <summary>Проверяет, совпадают ли указанные локальное имя и URI пространства имен с таковыми для текущего элемента, затем считывает содержимое элемента как требуемый тип.</summary>
      <returns>Содержимое элемента, преобразованное в требуемый типизированный объект.</returns>
      <param name="returnType">Тип возвращаемого значения.Примечание.   С выпуском платформы .NET Framework 3.5 значение параметра <paramref name="returnType" /> может иметь тип <see cref="T:System.DateTimeOffset" />.</param>
      <param name="namespaceResolver">Объект <see cref="T:System.Xml.IXmlNamespaceResolver" />, используемый для разрешения любых префиксов пространств имен, имеющих отношение к преобразованию типов.</param>
      <param name="localName">Локальное имя элемента.</param>
      <param name="namespaceURI">Пространство имен URI элемента.</param>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Xml.XmlReader" /> не расположен на элементе.</exception>
      <exception cref="T:System.Xml.XmlException">Текущий элемент содержит дочерние элементы.-или-Не удается преобразовать содержимое элемента в запрошенный тип.</exception>
      <exception cref="T:System.ArgumentNullException">Метод вызван с аргументами null.</exception>
      <exception cref="T:System.ArgumentException">Указанное локальное имя и URI пространства имен не совпадают с аналогичными параметрами текущего считываемого элемента.</exception>
      <exception cref="T:System.OverflowException">Чтение значения Decimal.MaxValue.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Асинхронно считывает содержимое элемента как запрашиваемый тип.</summary>
      <returns>Содержимое элемента, преобразованное в требуемый типизированный объект.</returns>
      <param name="returnType">Тип возвращаемого значения.</param>
      <param name="namespaceResolver">Объект <see cref="T:System.Xml.IXmlNamespaceResolver" />, используемый для разрешения любых префиксов пространств имен, имеющих отношение к преобразованию типов.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.InvalidOperationException">Асинхронный метод <see cref="T:System.Xml.XmlReader" /> вызван без присвоения флагу <see cref="P:System.Xml.XmlReaderSettings.Async" /> значения true.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "Если требуется использовать асинхронные методы, присвойте свойству XmlReaderSettings.Async значение true".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>Считывает элемент и раскодирует содержимое Base64.</summary>
      <returns>Количество байтов, записанных в буфер.</returns>
      <param name="buffer">Буфер, в который копируется полученный текст.Это значение не может быть равно null.</param>
      <param name="index">Смещение в буфере, с которого следует начать копировать результат.</param>
      <param name="count">Максимальное количество копируемых в буфер байтов.Этот метод возвращает фактическое количество скопированных байтов.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="buffer" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Текущий узел не является узлом элемента.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение индекса в буфере или сумма значений индекса и счетчика больше, чем выделенный размер буфера.</exception>
      <exception cref="T:System.NotSupportedException">Реализация <see cref="T:System.Xml.XmlReader" /> не поддерживает данный метод.</exception>
      <exception cref="T:System.Xml.XmlException">Содержимое элемента — смешанное.</exception>
      <exception cref="T:System.FormatException">Невозможно преобразовать содержимое в требуемый тип.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Асинхронно считывает элемент и декодирует содержимое Base64.</summary>
      <returns>Количество байтов, записанных в буфер.</returns>
      <param name="buffer">Буфер, в который копируется полученный текст.Это значение не может быть равно null.</param>
      <param name="index">Смещение в буфере, с которого следует начать копировать результат.</param>
      <param name="count">Максимальное количество копируемых в буфер байтов.Этот метод возвращает фактическое количество скопированных байтов.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.InvalidOperationException">Асинхронный метод <see cref="T:System.Xml.XmlReader" /> вызван без присвоения флагу <see cref="P:System.Xml.XmlReaderSettings.Async" /> значения true.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "Если требуется использовать асинхронные методы, присвойте свойству XmlReaderSettings.Async значение true".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>Считывает элемент и раскодирует содержимое BinHex.</summary>
      <returns>Количество байтов, записанных в буфер.</returns>
      <param name="buffer">Буфер, в который копируется полученный текст.Это значение не может быть равно null.</param>
      <param name="index">Смещение в буфере, с которого следует начать копировать результат.</param>
      <param name="count">Максимальное количество копируемых в буфер байтов.Этот метод возвращает фактическое количество скопированных байтов.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="buffer" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Текущий узел не является узлом элемента.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение индекса в буфере или сумма значений индекса и счетчика больше, чем выделенный размер буфера.</exception>
      <exception cref="T:System.NotSupportedException">Реализация <see cref="T:System.Xml.XmlReader" /> не поддерживает данный метод.</exception>
      <exception cref="T:System.Xml.XmlException">Содержимое элемента — смешанное.</exception>
      <exception cref="T:System.FormatException">Невозможно преобразовать содержимое в требуемый тип.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Асинхронно считывает элемент и декодирует содержимое BinHex.</summary>
      <returns>Количество байтов, записанных в буфер.</returns>
      <param name="buffer">Буфер, в который копируется полученный текст.Это значение не может быть равно null.</param>
      <param name="index">Смещение в буфере, с которого следует начать копировать результат.</param>
      <param name="count">Максимальное количество копируемых в буфер байтов.Этот метод возвращает фактическое количество скопированных байтов.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.InvalidOperationException">Асинхронный метод <see cref="T:System.Xml.XmlReader" /> вызван без присвоения флагу <see cref="P:System.Xml.XmlReaderSettings.Async" /> значения true.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "Если требуется использовать асинхронные методы, присвойте свойству XmlReaderSettings.Async значение true".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean">
      <summary>Считывает текущий элемент и возвращает содержимое объекта <see cref="T:System.Boolean" />.</summary>
      <returns>Содержимое элемента в виде объекта <see cref="T:System.Boolean" />.</returns>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Xml.XmlReader" /> не расположен на элементе.</exception>
      <exception cref="T:System.Xml.XmlException">Текущий элемент содержит дочерние элементы.-или-Содержимое элемента нельзя преобразовать в объект <see cref="T:System.Boolean" />.</exception>
      <exception cref="T:System.ArgumentNullException">Метод вызван с аргументами null.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean(System.String,System.String)">
      <summary>Проверяет соответствие указанного URI локального имени и пространства имен с URI текущего элемента, затем считывает текущий элемент и возвращает содержимое как объект <see cref="T:System.Boolean" />.</summary>
      <returns>Содержимое элемента в виде объекта <see cref="T:System.Boolean" />.</returns>
      <param name="localName">Локальное имя элемента.</param>
      <param name="namespaceURI">Пространство имен URI элемента.</param>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Xml.XmlReader" /> не расположен на элементе.</exception>
      <exception cref="T:System.Xml.XmlException">Текущий элемент содержит дочерние элементы.-или-Не удается преобразовать содержимое элемента в запрошенный тип.</exception>
      <exception cref="T:System.ArgumentNullException">Метод вызван с аргументами null.</exception>
      <exception cref="T:System.ArgumentException">Указанное локальное имя и URI пространства имен не совпадают с аналогичными параметрами текущего считываемого элемента.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal">
      <summary>Считывает текущий элемент и возвращает содержимое объекта <see cref="T:System.Decimal" />.</summary>
      <returns>Содержимое элемента в виде объекта <see cref="T:System.Decimal" />.</returns>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Xml.XmlReader" /> не расположен на элементе.</exception>
      <exception cref="T:System.Xml.XmlException">Текущий элемент содержит дочерние элементы.-или-Содержимое элемента нельзя преобразовать в объект типа <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.ArgumentNullException">Метод вызван с аргументами null.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal(System.String,System.String)">
      <summary>Проверяет соответствие указанного URI локального имени и пространства имен с URI текущего элемента, затем считывает текущий элемент и возвращает содержимое как объект <see cref="T:System.Decimal" />.</summary>
      <returns>Содержимое элемента в виде объекта <see cref="T:System.Decimal" />.</returns>
      <param name="localName">Локальное имя элемента.</param>
      <param name="namespaceURI">Пространство имен URI элемента.</param>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Xml.XmlReader" /> не расположен на элементе.</exception>
      <exception cref="T:System.Xml.XmlException">Текущий элемент содержит дочерние элементы.-или-Содержимое элемента нельзя преобразовать в объект типа <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.ArgumentNullException">Метод вызван с аргументами null.</exception>
      <exception cref="T:System.ArgumentException">Указанное локальное имя и URI пространства имен не совпадают с аналогичными параметрами текущего считываемого элемента.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble">
      <summary>Считывает текущий элемент и возвращает содержимое как число с плавающей запятой двойной точности.</summary>
      <returns>Содержимое элемента в виде числа с плавающей запятой двойной точности.</returns>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Xml.XmlReader" /> не расположен на элементе.</exception>
      <exception cref="T:System.Xml.XmlException">Текущий элемент содержит дочерние элементы.-или-Содержимое элемента нельзя преобразовать в число с плавающей запятой двойной точности.</exception>
      <exception cref="T:System.ArgumentNullException">Метод вызван с аргументами null.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble(System.String,System.String)">
      <summary>Проверяет, совпадают ли указанные локальное имя и URI пространства имен с таковыми для текущего элемента, затем считывает текущий элемент и возвращает содержимое как число с плавающей запятой двойной точности.</summary>
      <returns>Содержимое элемента в виде числа с плавающей запятой двойной точности.</returns>
      <param name="localName">Локальное имя элемента.</param>
      <param name="namespaceURI">Пространство имен URI элемента.</param>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Xml.XmlReader" /> не расположен на элементе.</exception>
      <exception cref="T:System.Xml.XmlException">Текущий элемент содержит дочерние элементы.-или-Не удается преобразовать содержимое элемента в запрошенный тип.</exception>
      <exception cref="T:System.ArgumentNullException">Метод вызван с аргументами null.</exception>
      <exception cref="T:System.ArgumentException">Указанное локальное имя и URI пространства имен не совпадают с аналогичными параметрами текущего считываемого элемента.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat">
      <summary>Считывает текущий элемент и возвращает содержимое как число с плавающей запятой одиночной точности.</summary>
      <returns>Содержимое элемента в виде числа с плавающей запятой одиночной точности.</returns>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Xml.XmlReader" /> не расположен на элементе.</exception>
      <exception cref="T:System.Xml.XmlException">Текущий элемент содержит дочерние элементы.-или-Содержимое элемента нельзя преобразовать в число с плавающей запятой одиночной точности.</exception>
      <exception cref="T:System.ArgumentNullException">Метод вызван с аргументами null.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat(System.String,System.String)">
      <summary>Проверяет, совпадают ли указанные локальное имя и URI пространства имен с таковыми для текущего элемента, затем считывает текущий элемент и возвращает содержимое как число с плавающей запятой одиночной точности.</summary>
      <returns>Содержимое элемента в виде числа с плавающей запятой одиночной точности.</returns>
      <param name="localName">Локальное имя элемента.</param>
      <param name="namespaceURI">Пространство имен URI элемента.</param>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Xml.XmlReader" /> не расположен на элементе.</exception>
      <exception cref="T:System.Xml.XmlException">Текущий элемент содержит дочерние элементы.-или-Содержимое элемента нельзя преобразовать в число с плавающей запятой одиночной точности.</exception>
      <exception cref="T:System.ArgumentNullException">Метод вызван с аргументами null.</exception>
      <exception cref="T:System.ArgumentException">Указанное локальное имя и URI пространства имен не совпадают с аналогичными параметрами текущего считываемого элемента.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt">
      <summary>Считывает текущий элемент и возвращает содержимое в виде 32-разрядного целого числа со знаком.</summary>
      <returns>Содержимое элемента как целое 32-разрядное целое число со знаком.</returns>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Xml.XmlReader" /> не расположен на элементе.</exception>
      <exception cref="T:System.Xml.XmlException">Текущий элемент содержит дочерние элементы.-или-Содержимое элемента не может быть преобразовано в 32-разрядное знаковое целое число.</exception>
      <exception cref="T:System.ArgumentNullException">Метод вызван с аргументами null.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt(System.String,System.String)">
      <summary>Проверяет, совпадают ли указанные локальное имя и URI пространства имен с таковыми для текущего элемента, затем считывает текущий элемент и возвращает содержимое как 32-разрядное целое число со знаком.</summary>
      <returns>Содержимое элемента как целое 32-разрядное целое число со знаком.</returns>
      <param name="localName">Локальное имя элемента.</param>
      <param name="namespaceURI">Пространство имен URI элемента.</param>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Xml.XmlReader" /> не расположен на элементе.</exception>
      <exception cref="T:System.Xml.XmlException">Текущий элемент содержит дочерние элементы.-или-Содержимое элемента не может быть преобразовано в 32-разрядное знаковое целое число.</exception>
      <exception cref="T:System.ArgumentNullException">Метод вызван с аргументами null.</exception>
      <exception cref="T:System.ArgumentException">Указанное локальное имя и URI пространства имен не совпадают с аналогичными параметрами текущего считываемого элемента.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong">
      <summary>Считывает текущий элемент и возвращает содержимое в виде 64-разрядного целого числа со знаком.</summary>
      <returns>Содержимое элемента как целое 64-разрядное целое число со знаком.</returns>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Xml.XmlReader" /> не расположен на элементе.</exception>
      <exception cref="T:System.Xml.XmlException">Текущий элемент содержит дочерние элементы.-или-Содержимое элемента не может быть преобразовано в 64-разрядное знаковое целое число.</exception>
      <exception cref="T:System.ArgumentNullException">Метод вызван с аргументами null.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong(System.String,System.String)">
      <summary>Проверяет, совпадают ли указанные локальное имя и URI пространства имен с таковыми для текущего элемента, затем считывает текущий элемент и возвращает содержимое как 64-разрядное целое число со знаком.</summary>
      <returns>Содержимое элемента как целое 64-разрядное целое число со знаком.</returns>
      <param name="localName">Локальное имя элемента.</param>
      <param name="namespaceURI">Пространство имен URI элемента.</param>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Xml.XmlReader" /> не расположен на элементе.</exception>
      <exception cref="T:System.Xml.XmlException">Текущий элемент содержит дочерние элементы.-или-Содержимое элемента не может быть преобразовано в 64-разрядное знаковое целое число.</exception>
      <exception cref="T:System.ArgumentNullException">Метод вызван с аргументами null.</exception>
      <exception cref="T:System.ArgumentException">Указанное локальное имя и URI пространства имен не совпадают с аналогичными параметрами текущего считываемого элемента.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject">
      <summary>Прочитывает текущий элемент и возвращает содержимое в качестве объекта <see cref="T:System.Object" />.</summary>
      <returns>Упакованный объект CLR наиболее подходящего типа.Свойство <see cref="P:System.Xml.XmlReader.ValueType" /> служит для определения подходящего типа CLR.Если содержимое типизировано как тип списка, этот метод возвращает массив упакованных объектов соответствующего типа.</returns>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Xml.XmlReader" /> не расположен на элементе.</exception>
      <exception cref="T:System.Xml.XmlException">Текущий элемент содержит дочерние элементы.-или-Содержимое элемента невозможно преобразовать в запрошенный тип.</exception>
      <exception cref="T:System.ArgumentNullException">Метод вызван с аргументами null.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject(System.String,System.String)">
      <summary>Проверяет соответствие указанного URI локального имени и пространства имен с URI текущего элемента, затем считывает текущий элемент и возвращает содержимое как объект <see cref="T:System.Object" />.</summary>
      <returns>Упакованный объект CLR наиболее подходящего типа.Свойство <see cref="P:System.Xml.XmlReader.ValueType" /> служит для определения подходящего типа CLR.Если содержимое типизировано как тип списка, этот метод возвращает массив упакованных объектов соответствующего типа.</returns>
      <param name="localName">Локальное имя элемента.</param>
      <param name="namespaceURI">Пространство имен URI элемента.</param>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Xml.XmlReader" /> не расположен на элементе.</exception>
      <exception cref="T:System.Xml.XmlException">Текущий элемент содержит дочерние элементы.-или-Не удается преобразовать содержимое элемента в запрошенный тип.</exception>
      <exception cref="T:System.ArgumentNullException">Метод вызван с аргументами null.</exception>
      <exception cref="T:System.ArgumentException">Указанное локальное имя и URI пространства имен не совпадают с аналогичными параметрами текущего считываемого элемента.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObjectAsync">
      <summary>Асинхронно считывает текущий элемент и возвращает содержимое как объект <see cref="T:System.Object" />.</summary>
      <returns>Упакованный объект CLR наиболее подходящего типа.Свойство <see cref="P:System.Xml.XmlReader.ValueType" /> служит для определения подходящего типа CLR.Если содержимое типизировано как тип списка, этот метод возвращает массив упакованных объектов соответствующего типа.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.InvalidOperationException">Асинхронный метод <see cref="T:System.Xml.XmlReader" /> вызван без присвоения флагу <see cref="P:System.Xml.XmlReaderSettings.Async" /> значения true.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "Если требуется использовать асинхронные методы, присвойте свойству XmlReaderSettings.Async значение true".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString">
      <summary>Считывает текущий элемент и возвращает содержимое объекта <see cref="T:System.String" />.</summary>
      <returns>Содержимое элемента в виде объекта <see cref="T:System.String" />.</returns>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Xml.XmlReader" /> не расположен на элементе.</exception>
      <exception cref="T:System.Xml.XmlException">Текущий элемент содержит дочерние элементы.-или-Содержимое элемента нельзя преобразовать в объект <see cref="T:System.String" />.</exception>
      <exception cref="T:System.ArgumentNullException">Метод вызван с аргументами null.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString(System.String,System.String)">
      <summary>Проверяет соответствие указанного URI локального имени и пространства имен с URI текущего элемента, затем считывает текущий элемент и возвращает содержимое как объект <see cref="T:System.String" />.</summary>
      <returns>Содержимое элемента в виде объекта <see cref="T:System.String" />.</returns>
      <param name="localName">Локальное имя элемента.</param>
      <param name="namespaceURI">Пространство имен URI элемента.</param>
      <exception cref="T:System.InvalidOperationException">Объект <see cref="T:System.Xml.XmlReader" /> не расположен на элементе.</exception>
      <exception cref="T:System.Xml.XmlException">Текущий элемент содержит дочерние элементы.-или-Содержимое элемента нельзя преобразовать в объект <see cref="T:System.String" />.</exception>
      <exception cref="T:System.ArgumentNullException">Метод вызван с аргументами null.</exception>
      <exception cref="T:System.ArgumentException">Указанное локальное имя и URI пространства имен не совпадают с аналогичными параметрами текущего считываемого элемента.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsStringAsync">
      <summary>Асинхронно считывает текущий элемент и возвращает содержимое как объект <see cref="T:System.String" />.</summary>
      <returns>Содержимое элемента в виде объекта <see cref="T:System.String" />.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.InvalidOperationException">Асинхронный метод <see cref="T:System.Xml.XmlReader" /> вызван без присвоения флагу <see cref="P:System.Xml.XmlReaderSettings.Async" /> значения true.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "Если требуется использовать асинхронные методы, присвойте свойству XmlReaderSettings.Async значение true".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadEndElement">
      <summary>Проверяет, является ли текущий узел содержимого закрывающим тегом, и позиционирует средство чтения на следующий узел.</summary>
      <exception cref="T:System.Xml.XmlException">Текущий узел не является закрывающим тегом или если во входном потоке обнаружен неверный XML.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXml">
      <summary>Когда переопределено в производном классе, считывает как строку все содержимое, включая разметку.</summary>
      <returns>Все содержимое XML-кода в текущем узле, включая разметку.Если текущий узел не имеет дочерних узлов, возвращается пустая строка.Если текущий узел не является элементом или атрибутом, возвращается пустая строка.</returns>
      <exception cref="T:System.Xml.XmlException">Неправильный формат XML, или при синтаксическом анализе XML произошла ошибка.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXmlAsync">
      <summary>Асинхронно считывает в виде строки все содержимое, включая разметку.</summary>
      <returns>Все содержимое XML-кода в текущем узле, включая разметку.Если текущий узел не имеет дочерних узлов, возвращается пустая строка.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.InvalidOperationException">Асинхронный метод <see cref="T:System.Xml.XmlReader" /> вызван без присвоения флагу <see cref="P:System.Xml.XmlReaderSettings.Async" /> значения true.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "Если требуется использовать асинхронные методы, присвойте свойству XmlReaderSettings.Async значение true".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXml">
      <summary>Когда переопределено в производном классе, считывает содержимое, включая разметку, представляющую этот узел и все его дочерние узлы.</summary>
      <returns>Если средство чтения позиционировано на узел элемента или атрибута, данный метод возвращает все содержимое XML текущего узла и всех его дочерних узлов, включая разметку; в противном случае возвращается пустая строка.</returns>
      <exception cref="T:System.Xml.XmlException">Неправильный формат XML, или при синтаксическом анализе XML произошла ошибка.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXmlAsync">
      <summary>Асинхронно считывает содержимое, включая разметку, представляющее этот узел и все его дочерние узлы.</summary>
      <returns>Если средство чтения позиционировано на узел элемента или атрибута, данный метод возвращает все содержимое XML текущего узла и всех его дочерних узлов, включая разметку; в противном случае возвращается пустая строка.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.InvalidOperationException">Асинхронный метод <see cref="T:System.Xml.XmlReader" /> вызван без присвоения флагу <see cref="P:System.Xml.XmlReaderSettings.Async" /> значения true.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "Если требуется использовать асинхронные методы, присвойте свойству XmlReaderSettings.Async значение true".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement">
      <summary>Проверяет, является ли текущий узел элементом и перемещает модуль чтения к следующему узлу.</summary>
      <exception cref="T:System.Xml.XmlException">В входном потоке обнаружен неправильный XML.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String)">
      <summary>Проверяет, является ли текущий узел элементом с заданным <see cref="P:System.Xml.XmlReader.Name" />, и перемещает средство чтения на следующий узел.</summary>
      <param name="name">Полное имя элемента.</param>
      <exception cref="T:System.Xml.XmlException">В входном потоке обнаружен неправильный XML. -или- <see cref="P:System.Xml.XmlReader.Name" /> элемента не соответствует заданному <paramref name="name" />.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String,System.String)">
      <summary>Проверяет, является ли текущий узел элементом с заданным <see cref="P:System.Xml.XmlReader.LocalName" /> и <see cref="P:System.Xml.XmlReader.NamespaceURI" />, и перемещает средство чтения на следующий узел.</summary>
      <param name="localname">Локальное имя элемента.</param>
      <param name="ns">Пространство имен URI элемента.</param>
      <exception cref="T:System.Xml.XmlException">В входном потоке обнаружен неправильный XML.-или-Свойства <see cref="P:System.Xml.XmlReader.LocalName" /> и <see cref="P:System.Xml.XmlReader.NamespaceURI" /> найденного элемента не совпадают с предоставленными аргументами.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ReadState">
      <summary>Когда переопределено в производном классе, возвращает состояние средства чтения.</summary>
      <returns>Одно из значений перечисления, указывающее состояние модуля чтения.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadSubtree">
      <summary>Возвращает новый экземпляр XmlReader, который может использоваться для считывания текущего узла и всех его потомков.</summary>
      <returns>Установить новый экземпляр средства чтения XML <see cref="F:System.Xml.ReadState.Initial" />.Вызов <see cref="M:System.Xml.XmlReader.Read" /> метод помещает новый модуль чтения на узел, который был текущим перед вызовом <see cref="M:System.Xml.XmlReader.ReadSubtree" /> метод.</returns>
      <exception cref="T:System.InvalidOperationException">XML чтения не позиционировано на элементе при вызове этого метода.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String)">
      <summary>Переводит <see cref="T:System.Xml.XmlReader" /> к следующему сопоставленному элементу-потомку с указанным проверенным именем.</summary>
      <returns>true, если найден сопоставленный элемент-потомок; в противном случае — false.Если сопоставленный дочерний элемент не найден, средство чтения <see cref="T:System.Xml.XmlReader" /> позиционируется на закрывающем теге (<see cref="P:System.Xml.XmlReader.NodeType" /> является XmlNodeType.EndElement) родительского элемента.Если средство чтения <see cref="T:System.Xml.XmlReader" /> не размещено на элементе при вызове метода <see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String)" />, последний возвращает значение false и положение <see cref="T:System.Xml.XmlReader" /> не изменяется.</returns>
      <param name="name">Полное имя элемента, на который следует переместиться.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.ArgumentException">Параметр является пустой строкой.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)">
      <summary>Переводит <see cref="T:System.Xml.XmlReader" /> к следующему элементу-потомку с указанным локальным именем и URI пространства имен.</summary>
      <returns>true, если найден сопоставленный элемент-потомок; в противном случае — false.Если сопоставленный дочерний элемент не найден, средство чтения <see cref="T:System.Xml.XmlReader" /> позиционируется на закрывающем теге (<see cref="P:System.Xml.XmlReader.NodeType" /> является XmlNodeType.EndElement) родительского элемента.Если средство чтения <see cref="T:System.Xml.XmlReader" /> не размещено на элементе при вызове метода <see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)" />, последний возвращает значение false и положение <see cref="T:System.Xml.XmlReader" /> не изменяется.</returns>
      <param name="localName">Локальное имя элемента, на который следует переместиться.</param>
      <param name="namespaceURI">URI пространства имен элемента, на который следует переместиться.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.ArgumentNullException">Оба параметра имеют значение null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String)">
      <summary>Выполняет чтение до обнаружения элемента с указанным полным именем.</summary>
      <returns>Значение true, если найден соответствующий элемент; в противном случае —false и перемещение <see cref="T:System.Xml.XmlReader" /> в конец файла.</returns>
      <param name="name">Полное имя элемента.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.ArgumentException">Параметр является пустой строкой.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String,System.String)">
      <summary>Выполняет чтение до обнаружения указанных локального имени и URI пространства имен.</summary>
      <returns>Значение true, если найден соответствующий элемент; в противном случае —false и перемещение <see cref="T:System.Xml.XmlReader" /> в конец файла.</returns>
      <param name="localName">Локальное имя элемента.</param>
      <param name="namespaceURI">Пространство имен URI элемента.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.ArgumentNullException">Оба параметра имеют значение null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String)">
      <summary>Переводит XmlReader к следующему сопоставленному родственному элементу с указанным проверенным именем.</summary>
      <returns>true, если найден сопоставленный родственный элемент; в противном случае — false.Если сопоставленный родственный элемент не найден, средство чтения XmlReader позиционируется на закрывающем теге (<see cref="P:System.Xml.XmlReader.NodeType" /> является XmlNodeType.EndElement) родительского элемента.</returns>
      <param name="name">Полное имя элемента того же уровня, на который следует переместиться.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.ArgumentException">Параметр является пустой строкой.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String,System.String)">
      <summary>Переводит XmlReader к следующему родственному элементу с указанным локальным именем и URI пространства имен.</summary>
      <returns>Значение true, если найден сопоставленный родственный элемент; в противном случае — значение false.Если сопоставленный родственный элемент не найден, средство чтения XmlReader позиционируется на закрывающем теге (<see cref="P:System.Xml.XmlReader.NodeType" /> является XmlNodeType.EndElement) родительского элемента.</returns>
      <param name="localName">Локальное имя элемента того же уровня, на который следует переместиться.</param>
      <param name="namespaceURI">Универсальный код ресурса (URI) пространства имен элемента того же уровня, на который следует переместиться.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.ArgumentNullException">Оба параметра имеют значение null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)">
      <summary>Считывает большие потоки текста, внедренного в XML-документ.</summary>
      <returns>Количество символов, считанных в буфер.По окончании текстового содержимого возвращается нуль.</returns>
      <param name="buffer">Массив символов, выполняющий функции буфера, в который записывается текстовое содержимое.Это значение не может быть равно null.</param>
      <param name="index">Смещение в буфере, где <see cref="T:System.Xml.XmlReader" /> может начать копировать результаты.</param>
      <param name="count">Максимальное количество копируемых в буфер символов.Этот метод возвращает фактическое количество скопированных символов.</param>
      <exception cref="T:System.InvalidOperationException">У текущего узла нет значения (значение свойства <see cref="P:System.Xml.XmlReader.HasValue" /> — false).</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="buffer" /> — null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение индекса в буфере или сумма значений индекса и счетчика больше, чем выделенный размер буфера.</exception>
      <exception cref="T:System.NotSupportedException">Реализация <see cref="T:System.Xml.XmlReader" /> не поддерживает данный метод.</exception>
      <exception cref="T:System.Xml.XmlException">Данные XML имеют неправильный формат.</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunkAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Асинхронно считывает большие потоки текста, внедренного в XML-документ.</summary>
      <returns>Количество символов, считанных в буфер.По окончании текстового содержимого возвращается нуль.</returns>
      <param name="buffer">Массив символов, выполняющий функции буфера, в который записывается текстовое содержимое.Это значение не может быть равно null.</param>
      <param name="index">Смещение в буфере, где <see cref="T:System.Xml.XmlReader" /> может начать копировать результаты.</param>
      <param name="count">Максимальное количество копируемых в буфер символов.Этот метод возвращает фактическое количество скопированных символов.</param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.InvalidOperationException">Асинхронный метод <see cref="T:System.Xml.XmlReader" /> вызван без присвоения флагу <see cref="P:System.Xml.XmlReaderSettings.Async" /> значения true.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "Если требуется использовать асинхронные методы, присвойте свойству XmlReaderSettings.Async значение true".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ResolveEntity">
      <summary>При переопределении в производном классе разрешает ссылки для сущностей для узлов EntityReference.</summary>
      <exception cref="T:System.InvalidOperationException">Средство чтения не расположено на узле EntityReference; эта реализация средства чтения не может разрешить сущности (свойство <see cref="P:System.Xml.XmlReader.CanResolveEntity" /> возвращает значение false).</exception>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Settings">
      <summary>Получает объект <see cref="T:System.Xml.XmlReaderSettings" />, используемый для создания данного экземпляра <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Объект <see cref="T:System.Xml.XmlReaderSettings" />, использованный для создания этого экземпляра средства чтения.Если это средство чтения не было создано с помощью метода <see cref="Overload:System.Xml.XmlReader.Create" />, это свойство возвращает null.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Skip">
      <summary>Пропускает дочерний узел текущего узла.</summary>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="M:System.Xml.XmlReader.SkipAsync">
      <summary>Асинхронно пропускает дочерние узлы текущего узла.</summary>
      <returns>Текущий узел.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
      <exception cref="T:System.InvalidOperationException">Асинхронный метод <see cref="T:System.Xml.XmlReader" /> вызван без присвоения флагу <see cref="P:System.Xml.XmlReaderSettings.Async" /> значения true.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "Если требуется использовать асинхронные методы, присвойте свойству XmlReaderSettings.Async значение true".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Value">
      <summary>Когда переопределено в производном классе, возвращает текстовое значение текущего узла.</summary>
      <returns>Возвращаемое значение зависит от значения свойства <see cref="P:System.Xml.XmlReader.NodeType" /> узла.В следующей таблице представлен список возвращаемых типов узлов со значениями.Все прочие типы узлов возвращают значение String.Empty.Тип узла Значение AttributeЗначение атрибута. CDATAСодержимое раздела CDATA. CommentСодержимое комментария. DocumentTypeВнутреннее подмножество. ProcessingInstructionПолное содержимое, исключая конечное приложение. SignificantWhitespaceПустое пространство в разметке модели со смешанным содержимым. TextСодержимое текстового узла. WhitespaceПустое пространство между разметкой. XmlDeclarationСодержимое объявления. </returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ValueType">
      <summary>Возвращает тип CLR текущего узла.</summary>
      <returns>Тип CLR, соответствующий типизированному значению узла.Значение по умолчанию — System.String.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlLang">
      <summary>При переопределении в производном классе получает текущую область действия xml:lang.</summary>
      <returns>Текущая ограниченная область действия xml:lang.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlSpace">
      <summary>При переопределении в производном классе получает текущую область действия xml:space.</summary>
      <returns>Одно из значений <see cref="T:System.Xml.XmlSpace" />.Если ограниченная область действия xml:space отсутствует, данное свойство принимает значение XmlSpace.None.</returns>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="T:System.Xml.XmlReader" /> вызван до завершения предыдущей асинхронной операции.В этом случае вызывается исключение <see cref="T:System.InvalidOperationException" /> с сообщением "асинхронная операция уже выполняется".</exception>
    </member>
    <member name="T:System.Xml.XmlReaderSettings">
      <summary>Задает набор функций, которые должны поддерживаться объектом <see cref="T:System.Xml.XmlReader" />, создаваемым с помощью метода <see cref="Overload:System.Xml.XmlReader.Create" />. </summary>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.XmlReaderSettings" />.</summary>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.Async">
      <summary>Получает или задает значение, указывающее, можно ли использовать асинхронные методы <see cref="T:System.Xml.XmlReader" /> для конкретного экземпляра <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Значение true, если могут использоваться асинхронные методы; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CheckCharacters">
      <summary>Возвращает или задает значение, показывающее, осуществляется ли проверка символов.</summary>
      <returns>Значение true — проверка осуществляется; в противном случае — false.Значение по умолчанию — true.ПримечаниеЕсли средство чтения <see cref="T:System.Xml.XmlReader" /> обрабатывает текстовые данные, всегда происходит проверка допустимости XML-имен и текстового содержимого независимо от значения этого свойства.Задание свойству <see cref="P:System.Xml.XmlReaderSettings.CheckCharacters" /> значения false отключает проверку символов для ссылок на сущности символов.</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Clone">
      <summary>Создает копию экземпляра <see cref="T:System.Xml.XmlReaderSettings" />.</summary>
      <returns>Точная копия объекта <see cref="T:System.Xml.XmlReaderSettings" />.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CloseInput">
      <summary>Возвращает или задает значение, указывающее, следует ли закрыть основной поток или <see cref="T:System.IO.TextReader" /> при закрытии средства чтения.</summary>
      <returns>Значение true — закрыть основной поток или <see cref="T:System.IO.TextReader" /> при закрытии средства чтения; в противном случае — false.Значение по умолчанию — false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.ConformanceLevel">
      <summary>Возвращает или задает уровень соответствия для <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Одно из значений перечисления, указывающее уровень совместимости, который будет обеспечивать средства чтения XML.Значение по умолчанию — <see cref="F:System.Xml.ConformanceLevel.Document" />.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.DtdProcessing">
      <summary>Получает или задает значение, определяющее обработку определений DTD.</summary>
      <returns>Одно из значений перечисления, которое определяет обработку DTD.Значение по умолчанию — <see cref="F:System.Xml.DtdProcessing.Prohibit" />.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreComments">
      <summary>Возвращает или задает значение, указывающее, следует ли игнорировать комментарии.</summary>
      <returns>true — игнорировать комментарии; в противном случае false.Значение по умолчанию — false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreProcessingInstructions">
      <summary>Возвращает или задает значение, указывающее, следует ли игнорировать инструкции по обработке.</summary>
      <returns>true — игнорировать инструкции обработки; в противном случае false.Значение по умолчанию — false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreWhitespace">
      <summary>Возвращает или задает значение, определяющее, будут ли игнорироваться незначимые символы-разделители.</summary>
      <returns>Значение true, если пустое пространство будет игнорироваться; в противном случае — false.Значение по умолчанию — false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LineNumberOffset">
      <summary>Возвращает или задает смещение номера строки объекта <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Смещение номера строки.Значение по умолчанию — 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LinePositionOffset">
      <summary>Возвращает или задает смещение позиции строки объекта <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Смещение позиции строки.Значение по умолчанию — 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersFromEntities">
      <summary>Возвращает или задает значение, указывающее максимально допустимое количество символов в документе, которые возникают вследствие расширения сущностей.</summary>
      <returns>Наибольшее количество символов вследствие расширения сущностей.Значение по умолчанию — 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersInDocument">
      <summary>Возвращает или задает значение, указывающее максимально допустимое число символов в XML-документе.Нуль (0) означает отсутствие ограничений на размер XML-документа.Значение, не равное нулю, указывает максимальное количество символов.</summary>
      <returns>Максимально допустимое количество символов в XML-документе.Значение по умолчанию — 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.NameTable">
      <summary>Возвращает или задает таблицу <see cref="T:System.Xml.XmlNameTable" />, используемую для разделенных сравнений строк.</summary>
      <returns>Таблица <see cref="T:System.Xml.XmlNameTable" />, в которой хранятся все разделенные строки, используемые экземплярами <see cref="T:System.Xml.XmlReader" />, созданными с помощью объекта <see cref="T:System.Xml.XmlReaderSettings" />.Значение по умолчанию — null.Созданный экземпляр <see cref="T:System.Xml.XmlReader" /> будет использовать новую пустую таблицу <see cref="T:System.Xml.NameTable" />, если это значение будет равно null.</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Reset">
      <summary>Повторно загружает значения по умолчанию для элементов класса параметров.</summary>
    </member>
    <member name="T:System.Xml.XmlSpace">
      <summary>Задает текущую область xml:space.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Default">
      <summary>Область xml:space соответствует значению default.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.None">
      <summary>Нет области xml:space.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Preserve">
      <summary>Область xml:space соответствует значению preserve.</summary>
    </member>
    <member name="T:System.Xml.XmlWriter">
      <summary>Представляет средство записи, обеспечивающее быстрый прямой способ (без кэширования) создания потоков или файлов, содержащих XML-данные.</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.XmlWriter" />.</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream)">
      <summary>Создает новый экземпляр <see cref="T:System.Xml.XmlWriter" /> с использованием указанного потока.</summary>
      <returns>Объект <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">Поток, в который будет выполняться запись.<see cref="T:System.Xml.XmlWriter" /> записывает синтаксис текста XML 1.0 и добавляет его к указанному потоку.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream,System.Xml.XmlWriterSettings)">
      <summary>Создает новый экземпляр <see cref="T:System.Xml.XmlWriter" /> с помощью потока и объекта <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
      <returns>Объект <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">Поток, в который будет выполняться запись.<see cref="T:System.Xml.XmlWriter" /> записывает синтаксис текста XML 1.0 и добавляет его к указанному потоку.</param>
      <param name="settings">Объект <see cref="T:System.Xml.XmlWriterSettings" />, использованный для настройки нового экземпляра<see cref="T:System.Xml.XmlWriter" />.Если значение равно null, используется <see cref="T:System.Xml.XmlWriterSettings" /> с параметрами по умолчанию.Если <see cref="T:System.Xml.XmlWriter" /> используется вместе с методом <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />, необходимо использовать свойство <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> для получения объекта <see cref="T:System.Xml.XmlWriterSettings" /> с верными параметрами.Это гарантирует правильность параметров выходных данных для объекта <see cref="T:System.Xml.XmlWriter" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter)">
      <summary>Создает новый экземпляр <see cref="T:System.Xml.XmlWriter" /> с использованием указанного <see cref="T:System.IO.TextWriter" />.</summary>
      <returns>Объект <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">
        <see cref="T:System.IO.TextWriter" />, в которое необходимо записать.Объект <see cref="T:System.Xml.XmlWriter" /> записывает синтаксис текста XML 1.0 и добавляет его к указанному потоку <see cref="T:System.IO.TextWriter" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter,System.Xml.XmlWriterSettings)">
      <summary>Создает новый экземпляр <see cref="T:System.Xml.XmlWriter" /> с использованием объектов <see cref="T:System.IO.TextWriter" /> и <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
      <returns>Объект <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">
        <see cref="T:System.IO.TextWriter" />, в который необходимо записать.Объект <see cref="T:System.Xml.XmlWriter" /> записывает синтаксис текста XML 1.0 и добавляет его к указанному потоку <see cref="T:System.IO.TextWriter" />.</param>
      <param name="settings">Объект <see cref="T:System.Xml.XmlWriterSettings" />, использованный для настройки нового экземпляра<see cref="T:System.Xml.XmlWriter" />.Если значение равно null, используется <see cref="T:System.Xml.XmlWriterSettings" /> с параметрами по умолчанию.Если <see cref="T:System.Xml.XmlWriter" /> используется вместе с методом <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />, необходимо использовать свойство <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> для получения объекта <see cref="T:System.Xml.XmlWriterSettings" /> с верными параметрами.Это гарантирует правильность параметров выходных данных для объекта <see cref="T:System.Xml.XmlWriter" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder)">
      <summary>Создает новый экземпляр <see cref="T:System.Xml.XmlWriter" /> с использованием указанного <see cref="T:System.Text.StringBuilder" />.</summary>
      <returns>Объект <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">Класс <see cref="T:System.Text.StringBuilder" />, в который осуществляется запись.Содержимое, записанное методом <see cref="T:System.Xml.XmlWriter" />, добавляется в <see cref="T:System.Text.StringBuilder" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder,System.Xml.XmlWriterSettings)">
      <summary>Создает новый экземпляр <see cref="T:System.Xml.XmlWriter" /> с использованием объектов <see cref="T:System.Text.StringBuilder" /> и <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
      <returns>Объект <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">Класс <see cref="T:System.Text.StringBuilder" />, в который осуществляется запись.Содержимое, записанное методом <see cref="T:System.Xml.XmlWriter" />, добавляется в <see cref="T:System.Text.StringBuilder" />.</param>
      <param name="settings">Объект <see cref="T:System.Xml.XmlWriterSettings" />, использованный для настройки нового экземпляра<see cref="T:System.Xml.XmlWriter" />.Если значение равно null, используется <see cref="T:System.Xml.XmlWriterSettings" /> с параметрами по умолчанию.Если <see cref="T:System.Xml.XmlWriter" /> используется вместе с методом <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />, необходимо использовать свойство <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> для получения объекта <see cref="T:System.Xml.XmlWriterSettings" /> с верными параметрами.Это гарантирует правильность параметров выходных данных для объекта <see cref="T:System.Xml.XmlWriter" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter)">
      <summary>Создает новый экземпляр <see cref="T:System.Xml.XmlWriter" /> с использованием указанного объекта <see cref="T:System.Xml.XmlWriter" />.</summary>
      <returns>Возвращает объект <see cref="T:System.Xml.XmlWriter" />, являющийся оболочкой указанного объекта <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">Объект <see cref="T:System.Xml.XmlWriter" />, который следует использовать в качестве базового средства записи.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter,System.Xml.XmlWriterSettings)">
      <summary>Создает новый экземпляр <see cref="T:System.Xml.XmlWriter" /> с использованием указанных объектов <see cref="T:System.Xml.XmlWriter" /> и <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
      <returns>Возвращает объект <see cref="T:System.Xml.XmlWriter" />, являющийся оболочкой указанного объекта <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">Объект <see cref="T:System.Xml.XmlWriter" />, который следует использовать в качестве базового средства записи.</param>
      <param name="settings">Объект <see cref="T:System.Xml.XmlWriterSettings" />, использованный для настройки нового экземпляра<see cref="T:System.Xml.XmlWriter" />.Если значение равно null, используется <see cref="T:System.Xml.XmlWriterSettings" /> с параметрами по умолчанию.Если <see cref="T:System.Xml.XmlWriter" /> используется вместе с методом <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />, необходимо использовать свойство <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> для получения объекта <see cref="T:System.Xml.XmlWriterSettings" /> с верными параметрами.Это гарантирует правильность параметров выходных данных для объекта <see cref="T:System.Xml.XmlWriter" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose">
      <summary>Освобождает все ресурсы, используемые текущим экземпляром класса <see cref="T:System.Xml.XmlWriter" />.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose(System.Boolean)">
      <summary>Освобождает неуправляемые ресурсы, используемые объектом <see cref="T:System.Xml.XmlWriter" />, а при необходимости освобождает также управляемые ресурсы.</summary>
      <param name="disposing">Значение true позволяет освободить управляемые и неуправляемые ресурсы; значение false позволяет освободить только неуправляемые ресурсы.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Flush">
      <summary>Когда переопределено в производном классе, сохраняет в базовый поток содержимое буфера, а также сохраняет основной поток.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.FlushAsync">
      <summary>Асинхронно записывает в базовый поток содержимое буфера и сохраняет базовый поток.</summary>
      <returns>Задача, представляющая асинхронную операцию Flush.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.LookupPrefix(System.String)">
      <summary>Когда переопределено в производном классе, возвращает ближайший префикс, определенный в области видимости текущего пространства имен для URI пространства имен.</summary>
      <returns>Соответствующий префикс или значение null, если в текущей области отсутствует соответствующий URI пространства имен.</returns>
      <param name="ns">URI пространства имен, префикс которого нужно найти.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="ns" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.Settings">
      <summary>Получает объект <see cref="T:System.Xml.XmlWriterSettings" />, используемый для создания данного экземпляра <see cref="T:System.Xml.XmlWriter" />.</summary>
      <returns>Объект <see cref="T:System.Xml.XmlWriterSettings" />, используемый для создания этого экземпляра модуля записи.Если это средство записи не было создано с помощью метода <see cref="Overload:System.Xml.XmlWriter.Create" />, это свойство возвращает null.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributes(System.Xml.XmlReader,System.Boolean)">
      <summary>При переопределении в производном классе записывает все атрибуты, найденные в текущей позиции в объекте <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="reader">XmlReader, из которого происходит копирование атрибутов.</param>
      <param name="defattr">Значение true, чтобы скопировать атрибуты по умолчанию из XmlReader; в противном случае — значение false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null. </exception>
      <exception cref="T:System.Xml.XmlException">The reader is not positioned on an element, attribute or XmlDeclaration node. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributesAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>Асинхронно записывает все атрибуты, найденные в текущей позиции в объекте <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteAttributes.</returns>
      <param name="reader">XmlReader, из которого происходит копирование атрибутов.</param>
      <param name="defattr">Значение true, чтобы скопировать атрибуты по умолчанию из XmlReader; в противном случае — значение false.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String)">
      <summary>Когда переопределено в производном классе, записывает атрибут с указанным локальным именем и значением.</summary>
      <param name="localName">Локальное имя атрибута.</param>
      <param name="value">Значение атрибута.</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String)">
      <summary>Когда переопределено в производном классе, записывает атрибут с указанным локальным именем, URI пространства имен и значением.</summary>
      <param name="localName">Локальное имя атрибута.</param>
      <param name="ns">URI пространства имен, который связывается с атрибутом.</param>
      <param name="value">Значение атрибута.</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String,System.String)">
      <summary>Когда переопределено в производном классе, записывает атрибут с указанным префиксом, локальным именем, URI пространства имен и значением.</summary>
      <param name="prefix">Префикс пространства имен атрибута.</param>
      <param name="localName">Локальное имя атрибута.</param>
      <param name="ns">Универсальный код ресурса (URI) пространства имен атрибута.</param>
      <param name="value">Значение атрибута.</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.Xml.XmlException">The <paramref name="localName" /> or <paramref name="ns" /> is null. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeStringAsync(System.String,System.String,System.String,System.String)">
      <summary>Асинхронно записывает атрибут с заданным префиксом, локальным именем, универсальным кодом ресурса (URI) пространства имен и значением.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteAttributeString.</returns>
      <param name="prefix">Префикс пространства имен атрибута.</param>
      <param name="localName">Локальное имя атрибута.</param>
      <param name="ns">Универсальный код ресурса (URI) пространства имен атрибута.</param>
      <param name="value">Значение атрибута.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>Когда переопределено в производном классе, преобразует указанный набор двоичных байтов в кодировку Base64 и записывает получившийся текст.</summary>
      <param name="buffer">Кодируемый массив байтов.</param>
      <param name="index">Позиция в буфере, с которой начинается запись байтов.</param>
      <param name="count">Количество записываемых байтов.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Асинхронно преобразует указанный набор двоичных байтов в кодировку Base64 и записывает получившийся текст.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteBase64.</returns>
      <param name="buffer">Кодируемый массив байтов.</param>
      <param name="index">Позиция в буфере, с которой начинается запись байтов.</param>
      <param name="count">Количество записываемых байтов.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>При переопределении в производном классе преобразует указанный набор двоичных байтов как BinHex и выводит получившийся текст.</summary>
      <param name="buffer">Кодируемый массив байтов.</param>
      <param name="index">Позиция в буфере, с которой начинается запись байтов.</param>
      <param name="count">Количество записываемых байтов.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Асинхронно кодирует указанные двоичные байты как BinHex и выводит получившийся текст.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteBinHex.</returns>
      <param name="buffer">Кодируемый массив байтов.</param>
      <param name="index">Позиция в буфере, с которой начинается запись байтов.</param>
      <param name="count">Количество записываемых байтов.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCData(System.String)">
      <summary>Когда переопределено в производном классе, записывает блок &lt;![CDATA[...]]&gt;, содержащий заданный текст.</summary>
      <param name="text">Текст, записываемый в блок CDATA.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCDataAsync(System.String)">
      <summary>Асинхронно записывает блок &lt;![CDATA[...]]&gt;, содержащий заданный текст.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteCData.</returns>
      <param name="text">Текст, записываемый в блок CDATA.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntity(System.Char)">
      <summary>Когда переопределено в производном классе, вызывает создание сущности знака для указанного значения знака Юникода.</summary>
      <param name="ch">Знак Юникода, для которого создается сущность знака.</param>
      <exception cref="T:System.ArgumentException">The character is in the surrogate pair character range, 0xd800 - 0xdfff.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntityAsync(System.Char)">
      <summary>Асинхронно инициирует создание сущности знака для указанного значения знака Юникода.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteCharEntity.</returns>
      <param name="ch">Знак Юникода, для которого создается сущность знака.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteChars(System.Char[],System.Int32,System.Int32)">
      <summary>Когда переопределено в производном классе, записывает содержимое текстового буфера.</summary>
      <param name="buffer">Массив символов, содержащий текст для записи.</param>
      <param name="index">Позиция в буфере, с которой начинается запись текста.</param>
      <param name="count">Количество символов для записи.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />; the call results in surrogate pair characters being split or an invalid surrogate pair being written.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="buffer" /> parameter value is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharsAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Асинхронно записывает содержимое текстового буфера.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteChars.</returns>
      <param name="buffer">Массив символов, содержащий текст для записи.</param>
      <param name="index">Позиция в буфере, с которой начинается запись текста.</param>
      <param name="count">Количество символов для записи.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteComment(System.String)">
      <summary>Когда переопределено в производном классе, записывает примечание &lt;!--...--&gt;, содержащее заданный текст.</summary>
      <param name="text">Текст, записываемый в примечание.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well-formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCommentAsync(System.String)">
      <summary>Асинхронно записывает комментарий &lt;!--...--&gt;, содержащий заданный текст.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteComment.</returns>
      <param name="text">Текст, записываемый в примечание.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocType(System.String,System.String,System.String,System.String)">
      <summary>Когда переопределено в производном классе, записывает объявление DOCTYPE с указанным именем и дополнительными атрибутами.</summary>
      <param name="name">Имя DOCTYPE.Не должно быть пустым.</param>
      <param name="pubid">Если значение не равно нулю, записывается также PUBLIC "pubid" "sysid", где <paramref name="pubid" /> и <paramref name="sysid" /> заменяются значениями заданных аргументов.</param>
      <param name="sysid">Если параметр <paramref name="pubid" /> имеет значение null, а параметр <paramref name="sysid" /> не равен нулю, записывается SYSTEM "sysid", где <paramref name="sysid" /> замещается значением данного аргумента.</param>
      <param name="subset">Если не равно нулю, записывает [subset], где subset замещается значением данного аргумента.</param>
      <exception cref="T:System.InvalidOperationException">This method was called outside the prolog (after the root element). </exception>
      <exception cref="T:System.ArgumentException">The value for <paramref name="name" /> would result in invalid XML.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocTypeAsync(System.String,System.String,System.String,System.String)">
      <summary>Асинхронно записывает объявление DOCTYPE с указанным именем и дополнительными атрибутами.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteDocType.</returns>
      <param name="name">Имя DOCTYPE.Не должно быть пустым.</param>
      <param name="pubid">Если значение не равно нулю, записывается также PUBLIC "pubid" "sysid", где <paramref name="pubid" /> и <paramref name="sysid" /> заменяются значениями заданных аргументов.</param>
      <param name="sysid">Если параметр <paramref name="pubid" /> имеет значение null, а параметр <paramref name="sysid" /> не равен нулю, записывается SYSTEM "sysid", где <paramref name="sysid" /> замещается значением данного аргумента.</param>
      <param name="subset">Если не равно нулю, записывает [subset], где subset замещается значением данного аргумента.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String)">
      <summary>Записывает элемент с заданным локальным именем и значением.</summary>
      <param name="localName">Локальное имя элемента.</param>
      <param name="value">Значение элемента.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String)">
      <summary>Записывает элемент с заданным локальным именем, URI пространства имен и значением.</summary>
      <param name="localName">Локальное имя элемента.</param>
      <param name="ns">URI пространства имен, связываемый с элементом.</param>
      <param name="value">Значение элемента.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String,System.String)">
      <summary>Записывает элемент с заданным префиксом, локальным именем, универсальный кодом ресурса (URI) пространства имен и значением.</summary>
      <param name="prefix">Префикс элемента.</param>
      <param name="localName">Локальное имя элемента.</param>
      <param name="ns">Универсальный код ресурса (URI) пространства имен элемента.</param>
      <param name="value">Значение элемента.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementStringAsync(System.String,System.String,System.String,System.String)">
      <summary>Асинхронно записывает элемент с заданным префиксом, локальным именем, универсальным кодом ресурса (URI) пространства имен и значением.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteElementString.</returns>
      <param name="prefix">Префикс элемента.</param>
      <param name="localName">Локальное имя элемента.</param>
      <param name="ns">Универсальный код ресурса (URI) пространства имен элемента.</param>
      <param name="value">Значение элемента.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttribute">
      <summary>При переопределении в производном классе закрывает предыдущий вызов <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" />.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttributeAsync">
      <summary>Асинхронно закрывает предыдущий вызов <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" />.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteEndAttribute.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocument">
      <summary>Когда переопределено в производном классе, закрывает все открытые элементы и атрибуты, возвращая средство записи в начальное состояние.</summary>
      <exception cref="T:System.ArgumentException">The XML document is invalid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocumentAsync">
      <summary>Асинхронно закрывает все открытые элементы и атрибуты, возвращая средство записи в начальное состояние.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteEndDocument.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElement">
      <summary>Когда переопределено в производном классе, закрывает один элемент и извлекает из стека область видимости соответствующего пространства имен.</summary>
      <exception cref="T:System.InvalidOperationException">This results in an invalid XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElementAsync">
      <summary>Асинхронно закрывает один элемент и извлекает из стека область видимости соответствующего пространства имен.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteEndElement.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRef(System.String)">
      <summary>При переопределении в производном классе записывает ссылку на сущность в виде &amp;name;.</summary>
      <param name="name">Имя ссылки на сущность.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRefAsync(System.String)">
      <summary>Асинхронно записывает ссылку на сущность в виде &amp;name;.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteEntityRef.</returns>
      <param name="name">Имя ссылки на сущность.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElement">
      <summary>Когда переопределено в производном классе, закрывает один элемент и извлекает из стека область видимости соответствующего пространства имен.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElementAsync">
      <summary>Асинхронно закрывает один элемент и извлекает из стека область видимости соответствующего пространства имен.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteFullEndElement.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteName(System.String)">
      <summary>Когда переопределено в производном классе, записывает указанное имя, гарантируя его допустимость согласно рекомендации W3C по языку XML версии 1.0 (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name).</summary>
      <param name="name">Записываемое имя.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid XML name; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNameAsync(System.String)">
      <summary>Асинхронно записывает указанное имя, гарантируя его допустимость согласно рекомендации W3C по языку XML версии 1.0 (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name).</summary>
      <returns>Задача, представляющая асинхронную операцию WriteName.</returns>
      <param name="name">Записываемое имя.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmToken(System.String)">
      <summary>Когда переопределено в производном классе, записывает указанное имя, гарантируя допустимость NmToken согласно рекомендациям W3C по языку XML версии 1.0 (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name).</summary>
      <param name="name">Записываемое имя.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid NmToken; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmTokenAsync(System.String)">
      <summary>Асинхронно записывает указанное имя, гарантируя, что это допустимый NmToken, согласно рекомендации W3C по языку XML версии 1.0 (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name).</summary>
      <returns>Задача, представляющая асинхронную операцию WriteNmToken.</returns>
      <param name="name">Записываемое имя.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNode(System.Xml.XmlReader,System.Boolean)">
      <summary>Когда переопределено в производном классе, копирует все данные из средства чтения в средство записи и перемещает средство чтения к началу следующего элемента того же уровня.</summary>
      <param name="reader">Класс <see cref="T:System.Xml.XmlReader" />, из которого выполняется чтение.</param>
      <param name="defattr">Значение true, чтобы скопировать атрибуты по умолчанию из XmlReader; в противном случае — значение false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> contains invalid characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNodeAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>Асинхронно копирует все данные из средства чтения в средство записи и перемещает средство чтения к началу следующего элемента того же уровня.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteNode.</returns>
      <param name="reader">Класс <see cref="T:System.Xml.XmlReader" />, из которого выполняется чтение.</param>
      <param name="defattr">Значение true, чтобы скопировать атрибуты по умолчанию из XmlReader; в противном случае — значение false.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstruction(System.String,System.String)">
      <summary>При переопределении в производном классе считывает инструкцию обработки с пробелом между именем и текстом в следующем виде: &lt;?имя текст?&gt;.</summary>
      <param name="name">Имя инструкции по обработке.</param>
      <param name="text">Текст, включаемый в инструкцию по обработке.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.<paramref name="name" /> is either null or String.Empty.This method is being used to create an XML declaration after <see cref="M:System.Xml.XmlWriter.WriteStartDocument" /> has already been called. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstructionAsync(System.String,System.String)">
      <summary>Асинхронно записывает инструкцию обработки с пробелом между именем и текстом в следующем виде: &lt;?имя текст?&gt;.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteProcessingInstruction.</returns>
      <param name="name">Имя инструкции по обработке.</param>
      <param name="text">Текст, включаемый в инструкцию по обработке.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedName(System.String,System.String)">
      <summary>При переопределении в производном классе считывает полное имя пространства имен.Этот метод выполняет поиск префикса для пространства имен в его области.</summary>
      <param name="localName">Локальное имя для записи.</param>
      <param name="ns">URI пространства имен для имени.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="localName" /> is either null or String.Empty.<paramref name="localName" /> is not a valid name. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedNameAsync(System.String,System.String)">
      <summary>Асинхронно записывает полное имя пространства имен.Этот метод выполняет поиск префикса для пространства имен в его области.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteQualifiedName.</returns>
      <param name="localName">Локальное имя для записи.</param>
      <param name="ns">URI пространства имен для имени.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.Char[],System.Int32,System.Int32)">
      <summary>Когда переопределено в производном классе, вручную записывает из буфера символов необработанные данные для разметки .</summary>
      <param name="buffer">Массив символов, содержащий текст для записи.</param>
      <param name="index">Позиция в буфере, с которой начинается запись текста.</param>
      <param name="count">Количество символов для записи.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.String)">
      <summary>Когда переопределено в производном классе, вручную записывает из строки необработанные данные для разметки.</summary>
      <param name="data">Строка, содержащая текст для записи.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="data" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Асинхронно, вручную записывает для разметки необработанные данные из буфера символов.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteRaw.</returns>
      <param name="buffer">Массив символов, содержащий текст для записи.</param>
      <param name="index">Позиция в буфере, с которой начинается запись текста.</param>
      <param name="count">Количество символов для записи.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.String)">
      <summary>Асинхронно, вручную записывает необработанные данные для разметки.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteRaw.</returns>
      <param name="data">Строка, содержащая текст для записи.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String)">
      <summary>Записывает начало атрибута с заданным локальным именем.</summary>
      <param name="localName">Локальное имя атрибута.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)">
      <summary>Записывает начало атрибута с заданным локальным именем и URI пространства имен.</summary>
      <param name="localName">Локальное имя атрибута.</param>
      <param name="ns">Универсальный код ресурса (URI) пространства имен атрибута.</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String,System.String)">
      <summary>Когда переопределено в производном классе, записывает начало атрибута с указанным префиксом, локальным именем и URI пространства имен.</summary>
      <param name="prefix">Префикс пространства имен атрибута.</param>
      <param name="localName">Локальное имя атрибута.</param>
      <param name="ns">URI пространства имен атрибута.</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttributeAsync(System.String,System.String,System.String)">
      <summary>Асинхронно записывает начало атрибута с заданным префиксом, локальным именем и универсальным кодом ресурса (URI) пространства имен.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteStartAttribute.</returns>
      <param name="prefix">Префикс пространства имен атрибута.</param>
      <param name="localName">Локальное имя атрибута.</param>
      <param name="ns">URI пространства имен атрибута.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument">
      <summary>Когда переопределено в производном классе, записывает объявление XML с номером версии "1.0".</summary>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument(System.Boolean)">
      <summary>Когда переопределено в производном классе, записывает объявление XML с номером версии "1.0" и отдельным атрибутом.</summary>
      <param name="standalone">Если значение равно true, записывается "standalone=yes"; если false, записывается "standalone=no".</param>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync">
      <summary>Асинхронно записывает объявление XML с номером версии "1.0".</summary>
      <returns>Задача, представляющая асинхронную операцию WriteStartDocument.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync(System.Boolean)">
      <summary>Асинхронно записывает объявление XML с номером версии "1.0". и отдельным атрибутом.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteStartDocument.</returns>
      <param name="standalone">Если значение равно true, записывается "standalone=yes"; если false, записывается "standalone=no".</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String)">
      <summary>Когда переопределено в производном классе, записывает открывающий тег с указанным локальным именем.</summary>
      <param name="localName">Локальное имя элемента.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String)">
      <summary>Когда переопределено в производном классе, записывает указанный открывающий тег и связывает его с заданным пространством имен.</summary>
      <param name="localName">Локальное имя элемента.</param>
      <param name="ns">URI пространства имен, связываемый с элементом.Если пространство имен уже находится в области видимости и с ним связан префикс, средство записи автоматически запишет этот префикс.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String,System.String)">
      <summary>Когда переопределено в производном классе, записывает указанный открывающий тег и связывает его с заданным пространством имен и префиксом.</summary>
      <param name="prefix">Префикс пространства имен элемента.</param>
      <param name="localName">Локальное имя элемента.</param>
      <param name="ns">URI пространства имен, связываемый с элементом.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElementAsync(System.String,System.String,System.String)">
      <summary>Асинхронно записывает указанный открывающий тег и связывает его с заданным пространством имен и префиксом.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteStartElement.</returns>
      <param name="prefix">Префикс пространства имен элемента.</param>
      <param name="localName">Локальное имя элемента.</param>
      <param name="ns">URI пространства имен, связываемый с элементом.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.WriteState">
      <summary>Когда переопределено в производном классе, возвращает состояние средства записи.</summary>
      <returns>Одно из значений <see cref="T:System.Xml.WriteState" />.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteString(System.String)">
      <summary>Записывает заданное текстовое содержимое при переопределении в производном классе.</summary>
      <param name="text">Текст для записи.</param>
      <exception cref="T:System.ArgumentException">The text string contains an invalid surrogate pair.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStringAsync(System.String)">
      <summary>Асинхронно записывает заданное текстовое содержимое.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteString.</returns>
      <param name="text">Текст для записи.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntity(System.Char,System.Char)">
      <summary>Когда переопределено в производном классе, создает и записывает сущность символа-заместителя для пары символов-заместителей.</summary>
      <param name="lowChar">Младший заместитель.Значение должно быть в диапазоне от 0xDC00 до 0xDFFF.</param>
      <param name="highChar">Старший заместитель.Значение должно быть в диапазоне от 0xD800 до 0xDBFF.</param>
      <exception cref="T:System.ArgumentException">An invalid surrogate character pair was passed.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntityAsync(System.Char,System.Char)">
      <summary>Асинхронно создает и записывает сущность символа-заместителя для пары символов-заместителей.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteSurrogateCharEntity.</returns>
      <param name="lowChar">Младший заместитель.Значение должно быть в диапазоне от 0xDC00 до 0xDFFF.</param>
      <param name="highChar">Старший заместитель.Значение должно быть в диапазоне от 0xD800 до 0xDBFF.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Boolean)">
      <summary>Записывает значение <see cref="T:System.Boolean" />.</summary>
      <param name="value">Значение типа <see cref="T:System.Boolean" /> для записи.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.DateTimeOffset)">
      <summary>Записывает значение <see cref="T:System.DateTimeOffset" />.</summary>
      <param name="value">Значение типа <see cref="T:System.DateTimeOffset" /> для записи.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Decimal)">
      <summary>Записывает значение <see cref="T:System.Decimal" />.</summary>
      <param name="value">Значение типа <see cref="T:System.Decimal" /> для записи.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Double)">
      <summary>Записывает значение <see cref="T:System.Double" />.</summary>
      <param name="value">Значение типа <see cref="T:System.Double" /> для записи.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int32)">
      <summary>Записывает значение <see cref="T:System.Int32" />.</summary>
      <param name="value">Значение типа <see cref="T:System.Int32" /> для записи.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int64)">
      <summary>Записывает значение <see cref="T:System.Int64" />.</summary>
      <param name="value">Значение типа <see cref="T:System.Int64" /> для записи.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Object)">
      <summary>Записывает значение объекта.</summary>
      <param name="value">Значение объекта для записи.Примечание.   С выпуском платформы .NET Framework 3.5 этот метод принимает <see cref="T:System.DateTimeOffset" /> в качестве параметра.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Single)">
      <summary>Записывает число с плавающей запятой одиночной точности.</summary>
      <param name="value">Число с плавающей запятой одиночной точности для записи.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.String)">
      <summary>Записывает значение <see cref="T:System.String" />.</summary>
      <param name="value">Значение типа <see cref="T:System.String" /> для записи.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespace(System.String)">
      <summary>Когда переопределено в производном классе, записывает указанный символ-разделитель.</summary>
      <param name="ws">Строка символов-разделителей.</param>
      <exception cref="T:System.ArgumentException">The string contains non-white space characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespaceAsync(System.String)">
      <summary>Асинхронно записывает указанный символ-разделитель.</summary>
      <returns>Задача, представляющая асинхронную операцию WriteWhitespace.</returns>
      <param name="ws">Строка символов-разделителей.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlLang">
      <summary>При переопределении в производном классе получает текущую область действия xml:lang.</summary>
      <returns>Текущая ограниченная область действия xml:lang.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlSpace">
      <summary>При переопределении в производном классе возвращает класс <see cref="T:System.Xml.XmlSpace" />, предоставляющий текущую область xml:space.</summary>
      <returns>Объект XmlSpace, представляющий текущую область xml:space.Значение Значение NoneЗначение, задаваемое по умолчанию, если область xml:space отсутствует.DefaultТекущая область — xml:space=default.PreserveТекущая область — xml:space=preserve.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="T:System.Xml.XmlWriterSettings">
      <summary>Задает набор функций, которые должны поддерживаться объектом <see cref="T:System.Xml.XmlWriter" />, созданным с помощью метода <see cref="Overload:System.Xml.XmlWriter.Create" />.</summary>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Async">
      <summary>Получает или задает значение, указывающее, можно ли использовать асинхронные методы <see cref="T:System.Xml.XmlWriter" /> для конкретного экземпляра <see cref="T:System.Xml.XmlWriter" />.</summary>
      <returns>Значение true, если асинхронные методы можно использовать; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CheckCharacters">
      <summary>Получает или задает значение, указывающее, должно ли средство записи XML выполнять проверку на предмет соответствия всех в документе разделу "2.2 Символы" документа W3C Рекомендации по XML 1.0.</summary>
      <returns>Значение true для выполнения проверки символов; в противном случае — значение false.Значение по умолчанию — true.</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Clone">
      <summary>Создает копию экземпляра <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
      <returns>Точная копия объекта <see cref="T:System.Xml.XmlWriterSettings" />.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CloseOutput">
      <summary>Возвращает или задает значение, указывающее, следует ли объекту <see cref="T:System.Xml.XmlWriter" /> закрывать также и базовый поток или <see cref="T:System.IO.TextWriter" /> при вызове метода <see cref="M:System.Xml.XmlWriter.Close" />.</summary>
      <returns>Значение true, если следует закрыть базовый поток или <see cref="T:System.IO.TextWriter" />; в противном случае — значение false.Значение по умолчанию — false.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.ConformanceLevel">
      <summary>Возвращает или задает уровень соответствия, на предмет которого средство записи XML проверяет выходные данные XML.</summary>
      <returns>Одно из значений перечисления, указывающее уровень соответствия (документ, фрагмент или автоматическое обнаружение).Значение по умолчанию — <see cref="F:System.Xml.ConformanceLevel.Document" />.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Encoding">
      <summary>Возвращает или задает тип используемой кодировки текста.</summary>
      <returns>Используемая кодировка текста.Значение по умолчанию — Encoding.UTF8.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Indent">
      <summary>Возвращает или задает значение, указывающее, следует ли использовать отступ для элементов.</summary>
      <returns>Значение true, если необходимо записывать отдельные элементы в новых строках с отступом; в противном случае — значение false.Значение по умолчанию — false.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.IndentChars">
      <summary>Возвращает или задает строку символов, используемую для отступов.Этот параметр используется, если значение свойства <see cref="P:System.Xml.XmlWriterSettings.Indent" /> равно true.</summary>
      <returns>Строка символов, используемая для отступов.Может принять любое строковое значение.Однако в целях обеспечения корректности XML-кода необходимо использовать только допустимые символы-разделители: символы пробела, табуляции, возврата каретки или перевода строки.По умолчанию - два пробела.</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.IndentChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NamespaceHandling">
      <summary>Получает или задает значение, указывающие, должен ли объект <see cref="T:System.Xml.XmlWriter" /> при записи содержимого XML удалять дубликаты объявлений пространств имен.По умолчанию средство записи выводит все объявления пространства имен, присутствующие в его сопоставителе пространства имен.</summary>
      <returns>Перечисление <see cref="T:System.Xml.NamespaceHandling" />, которое указывает, нужно ли удалять дубликаты объявлений пространств имен в объекте <see cref="T:System.Xml.XmlWriter" />.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineChars">
      <summary>Возвращает или задает строку символов, используемую для разрыва строк.</summary>
      <returns>Строка символов, используемая для разрыва строк.Может принять любое строковое значение.Однако в целях обеспечения корректности XML-кода необходимо использовать только допустимые символы-разделители: символы пробела, табуляции, возврата каретки или перевода строки.Значение по умолчанию — \r\n (возврат каретки, новая строка).</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineHandling">
      <summary>Возвращает или задает значение, указывающее, следует ли осуществлять нормализацию разрывов строк в выходных данных.</summary>
      <returns>Одно из значений <see cref="T:System.Xml.NewLineHandling" />.Значение по умолчанию — <see cref="F:System.Xml.NewLineHandling.Replace" />.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineOnAttributes">
      <summary>Возвращает или задает значение, указывающее, следует ли записывать атрибуты на новой строке.</summary>
      <returns>Значение true, если необходимо записывать атрибуты в отдельные строки; в противном случае — значение false.Значение по умолчанию — false.ПримечаниеЭтот параметр ни на что не влияет, если значение свойства <see cref="P:System.Xml.XmlWriterSettings.Indent" /> равно false.Если значение объекта <see cref="P:System.Xml.XmlWriterSettings.NewLineOnAttributes" /> равно true, каждому атрибуту предшествует новая строка и дополнительный уровень отступа.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.OmitXmlDeclaration">
      <summary>Возвращает или задает значение, определяющее, следует ли опустить XML-объявление.</summary>
      <returns>Значение true, если необходимо пропустить XML-объявление; в противном случае — значение false.Значением по умолчанию является false; XML-объявление записывается.</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Reset">
      <summary>Повторно загружает значения по умолчанию для элементов класса параметров.</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.WriteEndDocumentOnClose">
      <summary>Получает или задает значение, указывающее, добавляет ли <see cref="T:System.Xml.XmlWriter" /> закрывающие теги ко всем незакрытым тегам элементов при вызове метода <see cref="M:System.Xml.XmlWriter.Close" />.</summary>
      <returns>Значение true, если все незакрытые теги элементов будут закрыты; в противном случае — значение false.Значение по умолчанию — true.</returns>
    </member>
    <member name="T:System.Xml.Schema.XmlSchema">
      <summary>Встроенное представление схемы XML, как указано в спецификациях консорциума W3C Схема XML. Часть 1: структуры и Схема XML. Часть 2: типы данных.</summary>
    </member>
    <member name="T:System.Xml.Schema.XmlSchemaForm">
      <summary>Указывает, требуется ли префикс пространства имен для атрибутов или элементов.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.None">
      <summary>Форма элемента и атрибута не указана в схеме.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Qualified">
      <summary>Для элементов и атрибутов необходим префикс пространства имен.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Unqualified">
      <summary>Префикс пространства имен для элементов и атрибутов не требуется.</summary>
    </member>
    <member name="T:System.Xml.Serialization.IXmlSerializable">
      <summary>Предоставляет пользовательский формат для сериализации и десериализации XML.</summary>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.GetSchema">
      <summary>Этот метод является зарезервированным, и его не следует использовать.При реализации интерфейса IXmlSerializable этот метод должен возвращать значение null (Nothing в Visual Basic), а если необходимо указать пользовательскую схему, то вместо использования метода следует применить <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" /> к классу.</summary>
      <returns>
        <see cref="T:System.Xml.Schema.XmlSchema" />, описывающая представление XML объекта, полученного из метода <see cref="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)" /> и включенного в метод <see cref="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)">
      <summary>Создает объект из представления XML.</summary>
      <param name="reader">Поток <see cref="T:System.Xml.XmlReader" />, из которого выполняется десериализация объекта. </param>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)">
      <summary>Преобразует объект в представление XML.</summary>
      <param name="writer">Поток <see cref="T:System.Xml.XmlWriter" />, в который выполняется сериализация объекта. </param>
    </member>
    <member name="T:System.Xml.Serialization.XmlSchemaProviderAttribute">
      <summary>При применении к типу сохраняет имя статического метода типа, возвращающего схему XML и <see cref="T:System.Xml.XmlQualifiedName" /> (или <see cref="T:System.Xml.Schema.XmlSchemaType" /> для анонимных типов), управляющих сериализацией типа.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaProviderAttribute.#ctor(System.String)">
      <summary>Инициализация нового экземпляра  класса <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" />, принимая имя статического метода, предоставляющего схему XML типа.</summary>
      <param name="methodName">Имя статического метода, который должен быть реализован.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.IsAny">
      <summary>Получает или задает значение, определяющее, является ли целевой класс подстановочным классом, или содержит ли схема для класса только элемент xs:any.</summary>
      <returns>true, если класс является подстановочным знаком, или схема содержит только элемент xs:any, в противном случае false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.MethodName">
      <summary>Получает имя статического метода, предоставляющего схему XML типа, и имя его типа данных схемы XML.</summary>
      <returns>Имя метода, вызываемого инфраструктурой XML, для возврата схемы XML.</returns>
    </member>
  </members>
</doc>