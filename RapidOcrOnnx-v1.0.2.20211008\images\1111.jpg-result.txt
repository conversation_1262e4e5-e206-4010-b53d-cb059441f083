=====Input Params=====
numThread(12),padding(0),max<PERSON>ide<PERSON><PERSON>(1024),boxScoreThresh(0.500000),boxThresh(0.300000),unClipRatio(1.500000),doAngle(1),mostAngle(1)
=====Init Models=====
--- Init DbNet ---
--- Init AngleNet ---
--- Init CrnnNet ---
Init Models Success!
=====Start detect=====
ScaleParam(sw:1702,sh:1276,dw:1024,dh:736,0.601645,0.576802)
---------- step: dbNet getTextBoxes ----------
dbNetTime(101.113500ms)
TextBox[0](+padding)[score(0.669932),[x: 83, y: 1], [x: 127, y: 1], [x: 127, y: 19], [x: 83, y: 19]]
TextBox[1](+padding)[score(0.633185),[x: 176, y: 3], [x: 202, y: 3], [x: 202, y: 17], [x: 176, y: 17]]
TextBox[2](+padding)[score(0.661675),[x: 1547, y: 0], [x: 1587, y: 0], [x: 1587, y: 26], [x: 1547, y: 26]]
TextBox[3](+padding)[score(0.547497),[x: 1392, y: 1], [x: 1462, y: 1], [x: 1462, y: 26], [x: 1392, y: 26]]
TextBox[4](+padding)[score(0.625431),[x: 79, y: 29], [x: 147, y: 29], [x: 147, y: 60], [x: 79, y: 60]]
TextBox[5](+padding)[score(0.587798),[x: 134, y: 29], [x: 234, y: 29], [x: 234, y: 60], [x: 134, y: 60]]
TextBox[6](+padding)[score(0.593666),[x: 292, y: 27], [x: 681, y: 33], [x: 681, y: 62], [x: 292, y: 57]]
TextBox[7](+padding)[score(0.573943),[x: 16, y: 32], [x: 79, y: 32], [x: 79, y: 57], [x: 16, y: 57]]
TextBox[8](+padding)[score(0.613326),[x: 671, y: 36], [x: 797, y: 36], [x: 797, y: 60], [x: 671, y: 60]]
TextBox[9](+padding)[score(0.670306),[x: 826, y: 38], [x: 867, y: 38], [x: 867, y: 58], [x: 826, y: 58]]
TextBox[10](+padding)[score(0.707494),[x: 892, y: 34], [x: 1048, y: 34], [x: 1048, y: 64], [x: 892, y: 64]]
TextBox[11](+padding)[score(0.574879),[x: 1060, y: 31], [x: 1645, y: 38], [x: 1645, y: 72], [x: 1060, y: 65]]
TextBox[12](+padding)[score(0.649660),[x: 1638, y: 41], [x: 1695, y: 45], [x: 1693, y: 73], [x: 1636, y: 68]]
TextBox[13](+padding)[score(0.587815),[x: 1234, y: 90], [x: 1336, y: 90], [x: 1336, y: 126], [x: 1234, y: 126]]
TextBox[14](+padding)[score(0.629378),[x: 1387, y: 91], [x: 1525, y: 91], [x: 1525, y: 123], [x: 1387, y: 123]]
TextBox[15](+padding)[score(0.600001),[x: 1420, y: 114], [x: 1524, y: 110], [x: 1526, y: 146], [x: 1422, y: 151]]
TextBox[16](+padding)[score(0.590254),[x: 1243, y: 117], [x: 1333, y: 117], [x: 1333, y: 149], [x: 1243, y: 149]]
TextBox[17](+padding)[score(0.551176),[x: 1238, y: 136], [x: 1331, y: 136], [x: 1331, y: 182], [x: 1238, y: 182]]
TextBox[18](+padding)[score(0.731291),[x: 1442, y: 150], [x: 1522, y: 150], [x: 1522, y: 176], [x: 1442, y: 176]]
TextBox[19](+padding)[score(0.653822),[x: 1369, y: 178], [x: 1484, y: 178], [x: 1484, y: 209], [x: 1369, y: 209]]
TextBox[20](+padding)[score(0.547076),[x: 1484, y: 187], [x: 1515, y: 187], [x: 1515, y: 206], [x: 1484, y: 206]]
TextBox[21](+padding)[score(0.590744),[x: 1447, y: 305], [x: 1484, y: 305], [x: 1484, y: 334], [x: 1447, y: 334]]
TextBox[22](+padding)[score(0.713584),[x: 1380, y: 347], [x: 1518, y: 342], [x: 1519, y: 379], [x: 1381, y: 383]]
TextBox[23](+padding)[score(0.691334),[x: 1307, y: 979], [x: 1506, y: 985], [x: 1505, y: 1028], [x: 1306, y: 1022]]
TextBox[24](+padding)[score(0.799571),[x: 1308, y: 1035], [x: 1504, y: 1035], [x: 1504, y: 1071], [x: 1308, y: 1071]]
TextBox[25](+padding)[score(0.680490),[x: 1302, y: 1076], [x: 1504, y: 1084], [x: 1503, y: 1130], [x: 1300, y: 1122]]
TextBox[26](+padding)[score(0.713324),[x: 1303, y: 1130], [x: 1501, y: 1135], [x: 1500, y: 1177], [x: 1302, y: 1171]]
TextBox[27](+padding)[score(0.583476),[x: 23, y: 1185], [x: 296, y: 1194], [x: 295, y: 1224], [x: 22, y: 1215]]
TextBox[28](+padding)[score(0.700377),[x: 30, y: 1223], [x: 178, y: 1227], [x: 177, y: 1257], [x: 29, y: 1253]]
TextBox[29](+padding)[score(0.711170),[x: 171, y: 1229], [x: 379, y: 1231], [x: 378, y: 1260], [x: 171, y: 1258]]
---------- step: drawTextBoxes ----------
---------- step: angleNet getAngles ----------
angle[0][index(0), score(0.706950), time(3.722800ms)]
angle[1][index(0), score(0.970762), time(2.089400ms)]
angle[2][index(0), score(0.746068), time(2.132100ms)]
angle[3][index(0), score(0.693302), time(9.753100ms)]
angle[4][index(0), score(0.985905), time(1.915600ms)]
angle[5][index(0), score(0.852014), time(1.840400ms)]
angle[6][index(0), score(1.000000), time(4.726500ms)]
angle[7][index(0), score(0.889821), time(2.178300ms)]
angle[8][index(0), score(1.000000), time(2.128900ms)]
angle[9][index(0), score(0.746207), time(3.862200ms)]
angle[10][index(0), score(1.000000), time(2.706000ms)]
angle[11][index(0), score(0.999949), time(2.350800ms)]
angle[12][index(0), score(0.759531), time(2.490300ms)]
angle[13][index(0), score(0.676995), time(2.232800ms)]
angle[14][index(0), score(1.000000), time(2.598700ms)]
angle[15][index(0), score(0.949173), time(9.972300ms)]
angle[16][index(0), score(0.982576), time(2.160600ms)]
angle[17][index(0), score(0.815284), time(2.270200ms)]
angle[18][index(0), score(0.999966), time(7.070500ms)]
angle[19][index(0), score(0.999982), time(5.816800ms)]
angle[20][index(0), score(0.729086), time(3.727500ms)]
angle[21][index(0), score(0.597987), time(2.211000ms)]
angle[22][index(0), score(0.802353), time(8.244100ms)]
angle[23][index(0), score(1.000000), time(2.112900ms)]
angle[24][index(0), score(1.000000), time(7.120900ms)]
angle[25][index(0), score(1.000000), time(3.444200ms)]
angle[26][index(0), score(1.000000), time(4.099100ms)]
angle[27][index(0), score(1.000000), time(2.644300ms)]
angle[28][index(0), score(1.000000), time(5.952000ms)]
angle[29][index(0), score(1.000000), time(2.025600ms)]
---------- step: crnnNet getTextLine ----------
textLine[0](院面)
textScores[0]{0.0502621 ,0.10494}
crnnTime[0](5.959200ms)
textLine[1](机)
textScores[1]{0.202144}
crnnTime[1](17.434500ms)
textLine[2](C)
textScores[2]{0.224402}
crnnTime[2](7.613100ms)
textLine[3](之)
textScores[3]{0.357706}
crnnTime[3](5.485400ms)
textLine[4](解除)
textScores[4]{0.998804 ,0.994239}
crnnTime[4](9.300500ms)
textLine[5](重登录)
textScores[5]{0.999172 ,0.998261 ,0.997377}
crnnTime[5](3.612000ms)
textLine[6](超声测量危急值上报快速预约)
textScores[6]{0.962423 ,0.999337 ,0.99913 ,0.976614 ,0.998041 ,0.993898 ,0.998613 ,0.996394 ,0.994547 ,0.998958 ,0.998773 ,0.995664 ,0.998073}
crnnTime[6](31.265400ms)
textLine[7](日档)
textScores[7]{0.231067 ,0.552578}
crnnTime[7](3.498000ms)
textLine[8](新建检查)
textScores[8]{0.999513 ,0.999035 ,0.996833 ,0.895274}
crnnTime[8](5.576300ms)
textLine[9](CA)
textScores[9]{0.999206 ,0.995824}
crnnTime[9](4.805200ms)
textLine[10](云医平台查看)
textScores[10]{0.998445 ,0.999595 ,0.999471 ,0.994224 ,0.995273 ,0.968874}
crnnTime[10](7.535700ms)
textLine[11](登录信息核查闭环查看危急值上报报告评分)
textScores[11]{0.999801 ,0.995014 ,0.999816 ,0.998656 ,0.994585 ,0.999713 ,0.900978 ,0.989274 ,0.998943 ,0.987395 ,0.998862 ,0.987041 ,0.998495 ,0.999653 ,0.99837 ,0.998554 ,0.999308 ,0.999145 ,0.999726}
crnnTime[11](18.058400ms)
textLine[12](质控)
textScores[12]{0.999504 ,0.990005}
crnnTime[12](4.210900ms)
textLine[13](TIs0.8)
textScores[13]{0.993982 ,0.642967 ,0.981229 ,0.975889 ,0.984683 ,0.998126}
crnnTime[13](4.747200ms)
textLine[14](2025/04/10)
textScores[14]{0.999313 ,0.999076 ,0.999371 ,0.999228 ,0.986279 ,0.997601 ,0.999369 ,0.967766 ,0.998705 ,0.995434}
crnnTime[14](12.858000ms)
textLine[15](14:05:06)
textScores[15]{0.998056 ,0.974962 ,0.656373 ,0.996625 ,0.993504 ,0.627539 ,0.993488 ,0.973083}
crnnTime[15](33.887200ms)
textLine[16](TIb0.8)
textScores[16]{0.996172 ,0.819659 ,0.992674 ,0.920136 ,0.97185 ,0.935117}
crnnTime[16](4.001500ms)
textLine[17](MI1.2)
textScores[17]{0.988667 ,0.477324 ,0.997503 ,0.691564 ,0.991115}
crnnTime[17](2.690300ms)
textLine[18](C1-6-D)
textScores[18]{0.987734 ,0.998093 ,0.973191 ,0.998013 ,0.964325 ,0.995716}
crnnTime[18](20.973100ms)
textLine[19](31H2/13.)
textScores[19]{0.98497 ,0.995987 ,0.972923 ,0.741556 ,0.995602 ,0.994358 ,0.984454 ,0.981877}
crnnTime[19](8.451800ms)
textLine[20](cn)
textScores[20]{0.687967 ,0.830866}
crnnTime[20](14.471100ms)
textLine[21](A)
textScores[21]{0.235011}
crnnTime[21](1.989800ms)
textLine[22](SR2/CRI)
textScores[22]{0.993074 ,0.953848 ,0.531889 ,0.938092 ,0.961827 ,0.667038 ,0.497451}
crnnTime[22](10.561800ms)
textLine[23](AC19.92cm)
textScores[23]{0.992162 ,0.988526 ,0.998205 ,0.999623 ,0.994434 ,0.994843 ,0.99838 ,0.994904 ,0.999052}
crnnTime[23](9.353400ms)
textLine[24](GA24w4d)
textScores[24]{0.997951 ,0.98733 ,0.988038 ,0.989214 ,0.85882 ,0.981645 ,0.947954}
crnnTime[24](26.782400ms)
textLine[25](EFW673g)
textScores[25]{0.99278 ,0.979946 ,0.986169 ,0.992906 ,0.999234 ,0.999827 ,0.726251}
crnnTime[25](5.026000ms)
textLine[26](GA24wOd)
textScores[26]{0.996114 ,0.978193 ,0.99033 ,0.987299 ,0.858919 ,0.621389 ,0.985259}
crnnTime[26](8.635500ms)
textLine[27](胎儿胸部：双肺可见。)
textScores[27]{0.92492 ,0.711683 ,0.987597 ,0.945985 ,0.985557 ,0.991221 ,0.918251 ,0.968673 ,0.908099 ,0.815584}
crnnTime[27](11.043600ms)
textLine[28](胎儿心脏：)
textScores[28]{0.970552 ,0.937785 ,0.997544 ,0.929858 ,0.723179}
crnnTime[28](70.839900ms)
textLine[29](四腔心切面显示)
textScores[29]{0.998871 ,0.986898 ,0.999807 ,0.99778 ,0.999451 ,0.999663 ,0.999378}
crnnTime[29](8.356200ms)
=====End detect=====
FullDetectTime(286.828800ms)
院面
机
C
之
解除
重登录
超声测量危急值上报快速预约
日档
新建检查
CA
云医平台查看
登录信息核查闭环查看危急值上报报告评分
质控
TIs0.8
2025/04/10
14:05:06
TIb0.8
MI1.2
C1-6-D
31H2/13.
cn
A
SR2/CRI
AC19.92cm
GA24w4d
EFW673g
GA24wOd
胎儿胸部：双肺可见。
胎儿心脏：
四腔心切面显示

