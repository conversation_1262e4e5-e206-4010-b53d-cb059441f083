﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.ReaderWriter</name>
  </assembly>
  <members>
    <member name="T:System.Xml.ConformanceLevel">
      <summary>Especifica el número de comprobaciones de entrada o de salida que realizan los objetos <see cref="T:System.Xml.XmlReader" /> y <see cref="T:System.Xml.XmlWriter" />.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Auto">
      <summary>Los objetos <see cref="T:System.Xml.XmlReader" /> o <see cref="T:System.Xml.XmlWriter" /> detectan automáticamente si se debe realizar la comprobación del documento o fragmento y lleva a cabo la comprobación correspondiente.Si está ajustando otro objeto <see cref="T:System.Xml.XmlReader" /> o <see cref="T:System.Xml.XmlWriter" />, el objeto externo no lleva a cabo ninguna comprobación de conformidad adicional.La comprobación de conformidad se deja al objeto subyacente.Vea las propiedades <see cref="P:System.Xml.XmlReaderSettings.ConformanceLevel" /> y <see cref="P:System.Xml.XmlWriterSettings.ConformanceLevel" /> para obtener más información sobre cómo se determina el nivel de cumplimiento.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Document">
      <summary>Los datos XML cumplen con las reglas de un documento XML 1.0 con el formato correcto, tal como define W3C.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Fragment">
      <summary>Los datos XML son un fragmento XML con el formato correcto, tal como define W3C.</summary>
    </member>
    <member name="T:System.Xml.DtdProcessing">
      <summary>Especifica las opciones para procesar DTD.La clase <see cref="T:System.Xml.XmlReaderSettings" /> utiliza la enumeración <see cref="T:System.Xml.DtdProcessing" />.</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Ignore">
      <summary>Hace que se omita el elemento DOCTYPE.No se procesa ninguna DTD.</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Prohibit">
      <summary>Especifica que cuando se encuentre una DTD, se produzca una excepción <see cref="T:System.Xml.XmlException" /> con un mensaje que indique que se prohíbe el uso de esa DTD.Éste es el comportamiento predeterminado.</summary>
    </member>
    <member name="T:System.Xml.IXmlLineInfo">
      <summary>Proporciona una interfaz que permite a una clase devolver información de línea y de posición.</summary>
    </member>
    <member name="M:System.Xml.IXmlLineInfo.HasLineInfo">
      <summary>Obtiene un valor que indica si la clase puede devolver información de línea.</summary>
      <returns>Es true si se pueden proporcionar <see cref="P:System.Xml.IXmlLineInfo.LineNumber" /> y <see cref="P:System.Xml.IXmlLineInfo.LinePosition" />; en caso contrario, es false.</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LineNumber">
      <summary>Obtiene el número de línea actual.</summary>
      <returns>Número de línea actual o 0 si no hay información de línea disponible (por ejemplo, <see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" /> devuelve false).</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LinePosition">
      <summary>Obtiene la posición de línea actual.</summary>
      <returns>Posición de línea actual o 0 si no hay información de línea disponible (por ejemplo, <see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" /> devuelve false).</returns>
    </member>
    <member name="T:System.Xml.IXmlNamespaceResolver">
      <summary>Proporciona acceso de solo lectura a un conjunto de asignaciones de prefijos y espacios de nombres.</summary>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>Obtiene una colección de asignaciones de prefijos y espacios de nombres que están actualmente en el ámbito.</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> que contiene los espacios de nombres que hay actualmente en el ámbito.</returns>
      <param name="scope">Valor <see cref="T:System.Xml.XmlNamespaceScope" /> que especifica el tipo de nodos de espacio de nombres que se va a devolver.</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupNamespace(System.String)">
      <summary>Obtiene el URI del espacio de nombres asignado al prefijo especificado.</summary>
      <returns>El espacio de nombres del URI que está asignado al prefijo; es null si el prefijo no está asignado a ningún espacio de nombres de URI.</returns>
      <param name="prefix">Prefijo cuyo URI de espacio de nombres se desea buscar.</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupPrefix(System.String)">
      <summary>Obtiene el prefijo asignado al URI del espacio de nombres especificado.</summary>
      <returns>Prefijo asignado al URI del espacio de nombres; es null si este URI no está asignado a ningún prefijo.</returns>
      <param name="namespaceName">URI de espacio de nombres cuyo prefijo se desea buscar.</param>
    </member>
    <member name="T:System.Xml.NamespaceHandling">
      <summary>Especifica si se van a quitar las declaraciones de espacio de nombres duplicadas en <see cref="T:System.Xml.XmlWriter" />. </summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.Default">
      <summary>Especifica que no se quitarán las declaraciones de espacio de nombres duplicadas.</summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.OmitDuplicates">
      <summary>Especifica que se quitarán las declaraciones de espacio de nombres duplicadas.Para poder quitar el espacio de nombres duplicado, el prefijo y el espacio de nombres deben coincidir.</summary>
    </member>
    <member name="T:System.Xml.NameTable">
      <summary>Implementa <see cref="T:System.Xml.XmlNameTable" /> de un único subproceso.</summary>
    </member>
    <member name="M:System.Xml.NameTable.#ctor">
      <summary>Inicializa una nueva instancia de la clase NameTable.</summary>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>Subdivide la cadena especificada y la agrega a NameTable.</summary>
      <returns>Cadena subdividida o cadena existente si ya está en NameTable.Si <paramref name="len" /> es cero, se devuelve String.Empty.</returns>
      <param name="key">Matriz de caracteres que contiene la cadena que se va a agregar. </param>
      <param name="start">Índice de base cero de la matriz que especifica el primer carácter de la cadena. </param>
      <param name="len">Número de caracteres de la cadena. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />O bien <paramref name="start" /> &gt;= <paramref name="key" />.Length O bien <paramref name="len" /> &gt;= <paramref name="key" />.Length Las condiciones anteriores no hacen que se produzca una excepción si <paramref name="len" /> = 0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.String)">
      <summary>Subdivide la cadena especificada y la agrega a NameTable.</summary>
      <returns>Cadena subdividida o cadena existente si ya está en NameTable.</returns>
      <param name="key">Cadena que se va a agregar. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> es null. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>Obtiene la cadena subdividida que contiene los mismos caracteres que el intervalo de caracteres especificado en una matriz determinada.</summary>
      <returns>Cadena subdividida o null si la cadena no se ha subdividido todavía.Si <paramref name="len" /> es cero, se devuelve String.Empty.</returns>
      <param name="key">Matriz de caracteres que contiene el nombre que se va a buscar. </param>
      <param name="start">Índice de base cero de la matriz que especifica el primer carácter del nombre. </param>
      <param name="len">Número de caracteres del nombre. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />O bien <paramref name="start" /> &gt;= <paramref name="key" />.Length O bien <paramref name="len" /> &gt;= <paramref name="key" />.Length Las condiciones anteriores no hacen que se produzca una excepción si <paramref name="len" /> = 0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.String)">
      <summary>Obtiene la cadena subdividida con el valor especificado.</summary>
      <returns>Objeto de cadena subdividida o null si la cadena no se ha subdividido todavía.</returns>
      <param name="value">Nombre que se va a buscar. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> es null. </exception>
    </member>
    <member name="T:System.Xml.NewLineHandling">
      <summary>Especifica cómo controlar los saltos de línea.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Entitize">
      <summary>Los nuevos caracteres de la línea tienen entidades.Esta configuración conserva todos los caracteres cuando el resultado se lee mediante un <see cref="T:System.Xml.XmlReader" /> de normalización.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.None">
      <summary>Los nuevos caracteres de línea no se modifican.El resultado es igual que la entrada.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Replace">
      <summary>Los nuevos caracteres de línea se reemplazan para coincidir con el carácter especificado en la propiedad <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" />.</summary>
    </member>
    <member name="T:System.Xml.ReadState">
      <summary>Especifica el estado del lector.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Closed">
      <summary>Se ha llamado al método <see cref="M:System.Xml.XmlReader.Close" />.</summary>
    </member>
    <member name="F:System.Xml.ReadState.EndOfFile">
      <summary>Se ha llegado al final del archivo correctamente.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Error">
      <summary>Se ha producido un error que impide que continúe la operación de lectura.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Initial">
      <summary>No se ha llamado al método Read.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Interactive">
      <summary>Se ha llamado al método Read.Se puede llamar a otros métodos en el lector.</summary>
    </member>
    <member name="T:System.Xml.WriteState">
      <summary>Especifica el estado de <see cref="T:System.Xml.XmlWriter" />.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Attribute">
      <summary>Indica que se escribe un valor de atributo.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Closed">
      <summary>Indica que se ha llamado al método <see cref="M:System.Xml.XmlWriter.Close" />.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Content">
      <summary>Indica que se está escribiendo contenido del elemento.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Element">
      <summary>Indica que se está escribiendo una etiqueta de apertura de elemento.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Error">
      <summary>Se ha iniciado una excepción que ha dejado <see cref="T:System.Xml.XmlWriter" /> en un estado no válido.Puede llamar al método <see cref="M:System.Xml.XmlWriter.Close" /> para poner <see cref="T:System.Xml.XmlWriter" /> en el estado <see cref="F:System.Xml.WriteState.Closed" />.Cualquier otra llamada al método <see cref="T:System.Xml.XmlWriter" /> hará que se inicie una excepción <see cref="T:System.InvalidOperationException" />.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Prolog">
      <summary>Indica que se escribe el prólogo.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Start">
      <summary>Indica que todavía no se ha llamado a un método Write.</summary>
    </member>
    <member name="T:System.Xml.XmlConvert">
      <summary>Codifica y descodifica nombres XML y proporciona métodos de conversión entre tipos de Common Language Runtime y tipos de lenguajes de definición de esquema XML (XSD).Cuando se convierten tipos de datos, los valores devueltos no dependen de la configuración regional.</summary>
    </member>
    <member name="M:System.Xml.XmlConvert.DecodeName(System.String)">
      <summary>Descodifica un nombre.Este método hace lo contrario que los métodos <see cref="M:System.Xml.XmlConvert.EncodeName(System.String)" /> y <see cref="M:System.Xml.XmlConvert.EncodeLocalName(System.String)" />.</summary>
      <returns>Nombre descodificado.</returns>
      <param name="name">Nombre que se va a transformar. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeLocalName(System.String)">
      <summary>Convierte el nombre en un nombre XML local válido.</summary>
      <returns>Nombre codificado.</returns>
      <param name="name">Nombre que se va a codificar. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeName(System.String)">
      <summary>Convierte el nombre en un nombre XML válido.</summary>
      <returns>Devuelve el nombre con los caracteres no válidos sustituidos por una cadena de escape.</returns>
      <param name="name">Nombre que se va a convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeNmToken(System.String)">
      <summary>Comprueba que el nombre es válido de acuerdo con la especificación XML.</summary>
      <returns>Nombre codificado.</returns>
      <param name="name">Nombre que se va a codificar. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToBoolean(System.String)">
      <summary>Convierte <see cref="T:System.String" /> en un <see cref="T:System.Boolean" /> equivalente.</summary>
      <returns>Valor Boolean; es decir, true o false.</returns>
      <param name="s">Cadena que se va a convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> does not represent a Boolean value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToByte(System.String)">
      <summary>Convierte <see cref="T:System.String" /> en un <see cref="T:System.Byte" /> equivalente.</summary>
      <returns>Valor Byte equivalente de la cadena.</returns>
      <param name="s">Cadena que se va a convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToChar(System.String)">
      <summary>Convierte <see cref="T:System.String" /> en un <see cref="T:System.Char" /> equivalente.</summary>
      <returns>Char que representa el carácter único.</returns>
      <param name="s">Cadena que contiene un carácter único que se va a convertir. </param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="s" /> parameter is null. </exception>
      <exception cref="T:System.FormatException">The <paramref name="s" /> parameter contains more than one character. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTime(System.String,System.Xml.XmlDateTimeSerializationMode)">
      <summary>Convierte <see cref="T:System.String" /> en un <see cref="T:System.DateTime" /> mediante el <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> especificado.</summary>
      <returns>
        <see cref="T:System.DateTime" /> equivalente de la <see cref="T:System.String" />.</returns>
      <param name="s">Valor <see cref="T:System.String" /> que se va a convertir.</param>
      <param name="dateTimeOption">Uno de los valores de <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> que especifican si la fecha se debe pasar a la hora local o mantenerse como hora universal coordinada (UTC), si se trata de una fecha de UTC.</param>
      <exception cref="T:System.NullReferenceException">
        <paramref name="s" /> is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="dateTimeOption" /> value is null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is an empty string or is not in a valid format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String)">
      <summary>Convierte la <see cref="T:System.String" /> proporcionada en un equivalente de <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>Equivalente de <see cref="T:System.DateTimeOffset" /> de la cadena proporcionada.</returns>
      <param name="s">Cadena que se va a convertir.Nota   La cadena debe ajustarse a un subconjunto de la recomendación del Consorcio W3C relativa al tipo XML dateTime.Para obtener más información, consulte http://www.w3.org/TR/xmlschema-2/#dateTime.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The argument passed to this method is outside the range of allowable values.For information about allowable values, see <see cref="T:System.DateTimeOffset" />.</exception>
      <exception cref="T:System.FormatException">The argument passed to this method does not conform to a subset of the W3C Recommendations for the XML dateTime type.For more information see http://www.w3.org/TR/xmlschema-2/#dateTime.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String)">
      <summary>Convierte la <see cref="T:System.String" /> proporcionada en un equivalente de <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>Equivalente de <see cref="T:System.DateTimeOffset" /> de la cadena proporcionada.</returns>
      <param name="s">Cadena que se va a convertir.</param>
      <param name="format">Formato desde el que se convierte <paramref name="s" />.El parámetro de formato puede ser cualquier subconjunto de la recomendación del Consorcio W3C relativa al tipo XML dateTime.(Para obtener más información, consulte http://www.w3.org/TR/xmlschema-2/#dateTime). La cadena <paramref name="s" /> se valida comparándola con este formato.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> or <paramref name="format" /> is an empty string or is not in the specified format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String[])">
      <summary>Convierte la <see cref="T:System.String" /> proporcionada en un equivalente de <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>Equivalente de <see cref="T:System.DateTimeOffset" /> de la cadena proporcionada.</returns>
      <param name="s">Cadena que se va a convertir.</param>
      <param name="formats">Matriz de formatos a partir de los cuales puede convertirse <paramref name="s" />.Cada formato de <paramref name="formats" /> puede ser cualquier subconjunto de la recomendación del Consorcio W3C relativa al tipo XML dateTime.(Para obtener más información, consulte http://www.w3.org/TR/xmlschema-2/#dateTime). La cadena <paramref name="s" /> se valida comparándola con uno de estos formatos.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDecimal(System.String)">
      <summary>Convierte el <see cref="T:System.String" /> en un <see cref="T:System.Decimal" /> equivalente.</summary>
      <returns>Decimal equivalente de la cadena.</returns>
      <param name="s">Cadena que se va a convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Decimal.MinValue" /> or greater than <see cref="F:System.Decimal.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDouble(System.String)">
      <summary>Convierte el <see cref="T:System.String" /> en un <see cref="T:System.Double" /> equivalente.</summary>
      <returns>Double equivalente de la cadena.</returns>
      <param name="s">Cadena que se va a convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Double.MinValue" /> or greater than <see cref="F:System.Double.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToGuid(System.String)">
      <summary>Convierte el <see cref="T:System.String" /> en un <see cref="T:System.Guid" /> equivalente.</summary>
      <returns>Guid equivalente de la cadena.</returns>
      <param name="s">Cadena que se va a convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt16(System.String)">
      <summary>Convierte el <see cref="T:System.String" /> en un <see cref="T:System.Int16" /> equivalente.</summary>
      <returns>Int16 equivalente de la cadena.</returns>
      <param name="s">Cadena que se va a convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int16.MinValue" /> or greater than <see cref="F:System.Int16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt32(System.String)">
      <summary>Convierte el <see cref="T:System.String" /> en un <see cref="T:System.Int32" /> equivalente.</summary>
      <returns>Int32 equivalente de la cadena.</returns>
      <param name="s">Cadena que se va a convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int32.MinValue" /> or greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt64(System.String)">
      <summary>Convierte el <see cref="T:System.String" /> en un <see cref="T:System.Int64" /> equivalente.</summary>
      <returns>Int64 equivalente de la cadena.</returns>
      <param name="s">Cadena que se va a convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int64.MinValue" /> or greater than <see cref="F:System.Int64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSByte(System.String)">
      <summary>Convierte el <see cref="T:System.String" /> en un <see cref="T:System.SByte" /> equivalente.</summary>
      <returns>SByte equivalente de la cadena.</returns>
      <param name="s">Cadena que se va a convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.SByte.MinValue" /> or greater than <see cref="F:System.SByte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSingle(System.String)">
      <summary>Convierte el <see cref="T:System.String" /> en un <see cref="T:System.Single" /> equivalente.</summary>
      <returns>Single equivalente de la cadena.</returns>
      <param name="s">Cadena que se va a convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Single.MinValue" /> or greater than <see cref="F:System.Single.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Boolean)">
      <summary>Convierte la clase <see cref="T:System.Boolean" /> en una clase <see cref="T:System.String" />.</summary>
      <returns>Representación de cadena de Boolean; es decir, "true" o "false".</returns>
      <param name="value">Valor que se va a convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Byte)">
      <summary>Convierte la clase <see cref="T:System.Byte" /> en una clase <see cref="T:System.String" />.</summary>
      <returns>Representación de cadena de Byte.</returns>
      <param name="value">Valor que se va a convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Char)">
      <summary>Convierte la clase <see cref="T:System.Char" /> en una clase <see cref="T:System.String" />.</summary>
      <returns>Representación de cadena de Char.</returns>
      <param name="value">Valor que se va a convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTime,System.Xml.XmlDateTimeSerializationMode)">
      <summary>Convierte <see cref="T:System.DateTime" /> en <see cref="T:System.String" /> mediante el <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> especificado.</summary>
      <returns>
        <see cref="T:System.String" /> equivalente de la <see cref="T:System.DateTime" />.</returns>
      <param name="value">Valor <see cref="T:System.DateTime" /> que se va a convertir.</param>
      <param name="dateTimeOption">Uno de los valores de <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> que especifica cómo tratar el valor <see cref="T:System.DateTime" />.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="dateTimeOption" /> value is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> or <paramref name="dateTimeOption" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset)">
      <summary>Convierte el <see cref="T:System.DateTimeOffset" /> proporcionado en una <see cref="T:System.String" />.</summary>
      <returns>Representación de tipo <see cref="T:System.String" /> del <see cref="T:System.DateTimeOffset" /> proporcionado.</returns>
      <param name="value">
        <see cref="T:System.DateTimeOffset" /> que va a convertirse.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset,System.String)">
      <summary>Convierte el <see cref="T:System.DateTimeOffset" /> proporcionado en una <see cref="T:System.String" /> con el formato especificado.</summary>
      <returns>Representación <see cref="T:System.String" /> con el formato especificado del <see cref="T:System.DateTimeOffset" /> proporcionado.</returns>
      <param name="value">
        <see cref="T:System.DateTimeOffset" /> que va a convertirse.</param>
      <param name="format">Formato al que se convierte <paramref name="s" />.El parámetro de formato puede ser cualquier subconjunto de la recomendación del Consorcio W3C relativa al tipo XML dateTime.(Para obtener más información, consulte http://www.w3.org/TR/xmlschema-2/#dateTime).</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Decimal)">
      <summary>Convierte la clase <see cref="T:System.Decimal" /> en una clase <see cref="T:System.String" />.</summary>
      <returns>Representación de cadena de Decimal.</returns>
      <param name="value">Valor que se va a convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Double)">
      <summary>Convierte la clase <see cref="T:System.Double" /> en una clase <see cref="T:System.String" />.</summary>
      <returns>Representación de cadena de Double.</returns>
      <param name="value">Valor que se va a convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Guid)">
      <summary>Convierte la clase <see cref="T:System.Guid" /> en una clase <see cref="T:System.String" />.</summary>
      <returns>Representación de cadena de Guid.</returns>
      <param name="value">Valor que se va a convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int16)">
      <summary>Convierte la clase <see cref="T:System.Int16" /> en una clase <see cref="T:System.String" />.</summary>
      <returns>Representación de cadena de Int16.</returns>
      <param name="value">Valor que se va a convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int32)">
      <summary>Convierte la clase <see cref="T:System.Int32" /> en una clase <see cref="T:System.String" />.</summary>
      <returns>Representación de cadena de Int32.</returns>
      <param name="value">Valor que se va a convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int64)">
      <summary>Convierte la clase <see cref="T:System.Int64" /> en una clase <see cref="T:System.String" />.</summary>
      <returns>Representación de cadena de Int64.</returns>
      <param name="value">Valor que se va a convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.SByte)">
      <summary>Convierte la clase <see cref="T:System.SByte" /> en una clase <see cref="T:System.String" />.</summary>
      <returns>Representación de cadena de SByte.</returns>
      <param name="value">Valor que se va a convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Single)">
      <summary>Convierte la clase <see cref="T:System.Single" /> en una clase <see cref="T:System.String" />.</summary>
      <returns>Representación de cadena de Single.</returns>
      <param name="value">Valor que se va a convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.TimeSpan)">
      <summary>Convierte la clase <see cref="T:System.TimeSpan" /> en una clase <see cref="T:System.String" />.</summary>
      <returns>Representación de cadena de TimeSpan.</returns>
      <param name="value">Valor que se va a convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt16)">
      <summary>Convierte la clase <see cref="T:System.UInt16" /> en una clase <see cref="T:System.String" />.</summary>
      <returns>Representación de cadena de UInt16.</returns>
      <param name="value">Valor que se va a convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt32)">
      <summary>Convierte la clase <see cref="T:System.UInt32" /> en una clase <see cref="T:System.String" />.</summary>
      <returns>Representación de cadena de UInt32.</returns>
      <param name="value">Valor que se va a convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt64)">
      <summary>Convierte la clase <see cref="T:System.UInt64" /> en una clase <see cref="T:System.String" />.</summary>
      <returns>Representación de cadena de UInt64.</returns>
      <param name="value">Valor que se va a convertir. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToTimeSpan(System.String)">
      <summary>Convierte <see cref="T:System.String" /> en un <see cref="T:System.TimeSpan" /> equivalente.</summary>
      <returns>TimeSpan equivalente de la cadena.</returns>
      <param name="s">Cadena que se va a convertir.El formato de cadena debe cumplir la recomendación sobre la duración del Consorcio W3C "XML Schema Part 2: Datatypes".</param>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in correct format to represent a TimeSpan value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt16(System.String)">
      <summary>Convierte <see cref="T:System.String" /> en un <see cref="T:System.UInt16" /> equivalente.</summary>
      <returns>UInt16 equivalente de la cadena.</returns>
      <param name="s">Cadena que se va a convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt16.MinValue" /> or greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt32(System.String)">
      <summary>Convierte <see cref="T:System.String" /> en un valor <see cref="T:System.UInt32" /> equivalente.</summary>
      <returns>UInt32 equivalente de la cadena.</returns>
      <param name="s">Cadena que se va a convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt32.MinValue" /> or greater than <see cref="F:System.UInt32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt64(System.String)">
      <summary>Convierte <see cref="T:System.String" /> en un valor <see cref="T:System.UInt64" /> equivalente.</summary>
      <returns>UInt64 equivalente de la cadena.</returns>
      <param name="s">Cadena que se va a convertir. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt64.MinValue" /> or greater than <see cref="F:System.UInt64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyName(System.String)">
      <summary>Comprueba que el nombre sea válido de acuerdo con la recomendación sobre el lenguaje de marcado extensible del Consorcio W3C.</summary>
      <returns>Nombre, si es un nombre XML válido.</returns>
      <param name="name">Nombre que se va a comprobar. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid XML name. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNCName(System.String)">
      <summary>Comprueba que el nombre sea un NCName válido de acuerdo con la recomendación sobre el lenguaje de marcado extensible del Consorcio W3C.NCName es un nombre que no puede contener un carácter de dos puntos.</summary>
      <returns>Nombre, si es un nombre NCName válido.</returns>
      <param name="name">Nombre que se va a comprobar. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid non-colon name. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNMTOKEN(System.String)">
      <summary>Comprueba que la cadena es un NMTOKEN válido según la recomendación "XML Schema Part 2: Datatypes" del esquema XML del Consorcio W3C.</summary>
      <returns>Token del nombre, si es un NMTOKEN válido.</returns>
      <param name="name">La cadena que desea comprobar.</param>
      <exception cref="T:System.Xml.XmlException">The string is not a valid name token.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyPublicId(System.String)">
      <summary>Devuelve la instancia de cadena pasada si todos los caracteres del argumento de cadena son caracteres de identificadores públicos válidos.</summary>
      <returns>Devuelve la cadena pasada si todos los caracteres del argumento son caracteres de identificadores públicos válidos.</returns>
      <param name="publicId">
        <see cref="T:System.String" /> que contiene el identificador que se va a validar.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyWhitespace(System.String)">
      <summary>Devuelve la instancia de cadena pasada si todos los caracteres del argumento de cadena son caracteres de espacio en blanco válidos. </summary>
      <returns>Devuelve la instancia de cadena pasada si todos los caracteres del argumento de cadena son caracteres de espacio en blanco válidos; en caso contrario, null.</returns>
      <param name="content">
        <see cref="T:System.String" /> que se va a comprobar.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyXmlChars(System.String)">
      <summary>Devuelve la cadena que se pasa si todos los caracteres y pares de caracteres suplentes de un argumento de la cadena son caracteres XML válidos, en caso contrario se produce una XmlException con información sobre el primer carácter no válido encontrado. </summary>
      <returns>Devuelve la cadena que se pasa si todos los caracteres y pares de caracteres suplentes de un argumento de la cadena son caracteres XML válidos, en caso contrario se produce una XmlException con información sobre el primer carácter no válido encontrado.</returns>
      <param name="content">
        <see cref="T:System.String" /> que contiene los caracteres que se van a comprobar.</param>
    </member>
    <member name="T:System.Xml.XmlDateTimeSerializationMode">
      <summary>Especifica cómo tratar el valor de tiempo al realizar una conversión entre una cadena y <see cref="T:System.DateTime" />.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Local">
      <summary>Se trata como hora local.Si el objeto <see cref="T:System.DateTime" /> representa la hora universal coordinada (UTC), se convierte a la hora local.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.RoundtripKind">
      <summary>La información de la zona horaria se debe conservar al realizar la conversión.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Unspecified">
      <summary>Se trata como hora local si <see cref="T:System.DateTime" /> se convierte en cadena.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Utc">
      <summary>Se trata como UTC.Si el objeto <see cref="T:System.DateTime" /> representa una hora local, se convierte en UTC.</summary>
    </member>
    <member name="T:System.Xml.XmlException">
      <summary>Devuelve información detallada sobre la última excepción.</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor">
      <summary>Inicializa una nueva instancia de la clase XmlException.</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase XmlException con el mensaje de error especificado.</summary>
      <param name="message">Descripción de error. </param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase XmlException.</summary>
      <param name="message">Descripción de la condición de error. </param>
      <param name="innerException">
        <see cref="T:System.Exception" /> que inició XmlException, en caso de que exista.Este valor puede ser null.</param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception,System.Int32,System.Int32)">
      <summary>Inicializa una nueva instancia de la clase XmlException con el mensaje, la excepción interna, el número de línea y la posición de línea especificados.</summary>
      <param name="message">Descripción de error. </param>
      <param name="innerException">La excepción que es la causa de la excepción actual.Este valor puede ser null.</param>
      <param name="lineNumber">Número de línea que indica dónde se produjo el error. </param>
      <param name="linePosition">Posición de línea que indica dónde se produjo el error. </param>
    </member>
    <member name="P:System.Xml.XmlException.LineNumber">
      <summary>Obtiene el número de línea que indica dónde se produjo el error.</summary>
      <returns>Número de línea que indica dónde se produjo el error.</returns>
    </member>
    <member name="P:System.Xml.XmlException.LinePosition">
      <summary>Obtiene la posición de línea que indica dónde se produjo el error.</summary>
      <returns>Posición de línea que indica dónde se produjo el error.</returns>
    </member>
    <member name="P:System.Xml.XmlException.Message">
      <summary>Obtiene un mensaje que describe la excepción actual.</summary>
      <returns>Mensaje de error que explica la razón de la excepción.</returns>
    </member>
    <member name="T:System.Xml.XmlNamespaceManager">
      <summary>Resuelve, agrega y quita espacios de nombres en una colección y proporciona la administración del ámbito de estos espacios de nombres. </summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.#ctor(System.Xml.XmlNameTable)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Xml.XmlNamespaceManager" /> con el objeto <see cref="T:System.Xml.XmlNameTable" /> especificado.</summary>
      <param name="nameTable">Objeto <see cref="T:System.Xml.XmlNameTable" /> que se va a usar. </param>
      <exception cref="T:System.NullReferenceException">null is passed to the constructor </exception>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.AddNamespace(System.String,System.String)">
      <summary>Agrega el espacio de nombres especificado a la colección.</summary>
      <param name="prefix">Prefijo que se va a asociar al espacio de nombres que se agrega.Use String.Empty para agregar un espacio de nombres predeterminado.NotaSi se usa <see cref="T:System.Xml.XmlNamespaceManager" /> para resolver los espacios de nombres en una expresión XPath (XML Path Language), se ha de especificar un prefijo.Si una expresión XPath no incluye un prefijo, se supone que el identificador uniforme de recursos (URI) del espacio de nombres corresponde al espacio de nombres vacío.Para más información sobre las expresiones XPath y <see cref="T:System.Xml.XmlNamespaceManager" />, vea los métodos <see cref="M:System.Xml.XmlNode.SelectNodes(System.String)" /> y <see cref="M:System.Xml.XPath.XPathExpression.SetContext(System.Xml.XmlNamespaceManager)" />.</param>
      <param name="uri">Espacio de nombres que se va a agregar. </param>
      <exception cref="T:System.ArgumentException">The value for <paramref name="prefix" /> is "xml" or "xmlns". </exception>
      <exception cref="T:System.ArgumentNullException">The value for <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.DefaultNamespace">
      <summary>Obtiene el identificador URI de espacio de nombres del espacio de nombres predeterminado.</summary>
      <returns>Devuelve el identificador URI de espacio de nombres del espacio de nombres predeterminado, o String.Empty si no hay espacio de nombres predeterminado.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetEnumerator">
      <summary>Devuelve un enumerador que se usará para recorrer en iteración los espacios de nombres de <see cref="T:System.Xml.XmlNamespaceManager" />.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> que contiene los prefijos almacenados por <see cref="T:System.Xml.XmlNamespaceManager" />.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>Obtiene una colección de nombres de espacios de nombres por clave de prefijo que se puede usar para enumerar los espacios de nombres que actualmente se encuentran en el ámbito.</summary>
      <returns>Colección de espacios de nombres y prefijos que se encuentran actualmente en el ámbito.</returns>
      <param name="scope">Valor de enumeración que especifica el tipo de nodos de espacio de nombres que se va a devolver.</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.HasNamespace(System.String)">
      <summary>Obtiene un valor que indica si el prefijo proporcionado tiene un espacio de nombres definido para el ámbito que se ha insertado.</summary>
      <returns>true si se ha definido un espacio de nombres; en caso contrario, false.</returns>
      <param name="prefix">Prefijo del espacio de nombres que se desea buscar. </param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupNamespace(System.String)">
      <summary>Obtiene el identificador URI de espacio de nombres del prefijo especificado.</summary>
      <returns>Devuelve el identificador URI de espacio de nombres de <paramref name="prefix" /> o null si no se ha asignado un espacio de nombres.La cadena devuelta está subdividida.Para más información sobre cadenas subdivididas, vea la clase <see cref="T:System.Xml.XmlNameTable" />.</returns>
      <param name="prefix">Prefijo cuyo identificador URI de espacio de nombres se desea resolver.Para hacer coincidir el espacio de nombres predeterminado, pase String.Empty.</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupPrefix(System.String)">
      <summary>Busca el prefijo declarado para el identificador URI de espacio de nombres especificado.</summary>
      <returns>Prefijo que coincide.Si no hay ningún prefijo asignado, el método devuelve String.Empty.Si se proporciona un valor nulo, se devuelve null.</returns>
      <param name="uri">Espacio de nombres que se va a resolver para el prefijo. </param>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.NameTable">
      <summary>Obtiene el objeto <see cref="T:System.Xml.XmlNameTable" /> asociado a este objeto.</summary>
      <returns>
        <see cref="T:System.Xml.XmlNameTable" /> que usa este objeto.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PopScope">
      <summary>Extrae un ámbito de espacio de nombres de la pila.</summary>
      <returns>true si quedan ámbitos de espacio de nombres en la pila; false si no quedan espacios de nombres para extraer.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PushScope">
      <summary>Inserta un ámbito de espacio de nombres en la pila.</summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.RemoveNamespace(System.String,System.String)">
      <summary>Quita el espacio de nombres dado del prefijo especificado.</summary>
      <param name="prefix">Prefijo del espacio de nombres. </param>
      <param name="uri">Espacio de nombres que se va a quitar del prefijo especificado.El espacio de nombres quitado pertenece al ámbito de espacio de nombres actual.Los espacios de nombres que no pertenecen al ámbito actual no se tienen en cuenta.</param>
      <exception cref="T:System.ArgumentNullException">The value of <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="T:System.Xml.XmlNamespaceScope">
      <summary>Define el ámbito del espacio de nombres.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.All">
      <summary>Todos los espacios de nombres definidos en el ámbito del nodo actual.Esto incluye el espacio de nombres xmlns:xml que siempre se declara de manera implícita.No está definido el orden de los espacios de nombres que se devuelven.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.ExcludeXml">
      <summary>Todos los espacios de nombres definidos en el ámbito del nodo actual, excluido el espacio de nombres xmlns:xml, que siempre se declara implícitamente.No está definido el orden de los espacios de nombres que se devuelven.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.Local">
      <summary>Todos los espacios de nombres definidos localmente en el nodo actual.</summary>
    </member>
    <member name="T:System.Xml.XmlNameTable">
      <summary>Tabla de objetos en forma de cadena subdividida.</summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Xml.XmlNameTable" />. </summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>Cuando se invalida en una clase derivada, subdivide la cadena especificada y la agrega a XmlNameTable.</summary>
      <returns>Cadena subdividida nueva o cadena existente si ya hay una.Si la longitud es cero, se devuelve String.Empty.</returns>
      <param name="array">Matriz de caracteres que contiene el nombre que se va a agregar. </param>
      <param name="offset">Índice de base cero de la matriz que especifica el primer carácter del nombre. </param>
      <param name="length">Número de caracteres del nombre. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />.O bien <paramref name="offset" /> &gt;= <paramref name="array" /> .Length O bien <paramref name="length" /> &gt;= <paramref name="array" /> .Length Las condiciones anteriores no hacen que se produzca una excepción si <paramref name="length" /> = 0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.String)">
      <summary>Cuando se invalida en una clase derivada, subdivide la cadena especificada y la agrega a XmlNameTable.</summary>
      <returns>Cadena subdividida nueva o cadena existente si ya hay una.</returns>
      <param name="array">Nombre que se va a agregar. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> es null. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>Cuando se invalida en una clase derivada, se obtiene la cadena subdividida que contiene los mismos caracteres que el intervalo de caracteres especificado en una matriz determinada.</summary>
      <returns>Cadena subdividida o null si la cadena no se ha subdividido todavía.Si <paramref name="length" /> es cero, se devuelve String.Empty.</returns>
      <param name="array">Matriz de caracteres que contiene el nombre que se va a buscar. </param>
      <param name="offset">Índice de base cero de la matriz que especifica el primer carácter del nombre. </param>
      <param name="length">Número de caracteres del nombre. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />.O bien <paramref name="offset" /> &gt;= <paramref name="array" /> .Length O bien <paramref name="length" /> &gt;= <paramref name="array" /> .Length Las condiciones anteriores no hacen que se produzca una excepción si <paramref name="length" /> = 0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.String)">
      <summary>Cuando se invalida en una clase derivada, obtiene la cadena subdividida que contiene el mismo valor que la cadena especificada.</summary>
      <returns>Cadena subdividida o null si la cadena no se ha subdividido todavía.</returns>
      <param name="array">Nombre que se va a buscar. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> es null. </exception>
    </member>
    <member name="T:System.Xml.XmlNodeType">
      <summary>Especifica el tipo de nodo.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Attribute">
      <summary>Atributo (por ejemplo, id='123').</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.CDATA">
      <summary>Sección CDATA (por ejemplo, &lt;![CDATA[my escaped text]]&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Comment">
      <summary>Comentario (por ejemplo, &lt;!-- my comment --&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Document">
      <summary>Objeto de documento que, como raíz del árbol de documentos, proporciona acceso a todo el documento XML.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentFragment">
      <summary>Fragmento de documento.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentType">
      <summary>declaración de tipos de documento, indicada por la siguiente etiqueta (por ejemplo, &lt;!DOCTYPE...&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Element">
      <summary>Elemento (por ejemplo, &lt;item&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndElement">
      <summary>Etiqueta de elemento final (por ejemplo, &lt;/item&gt;) .</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndEntity">
      <summary>Se devuelve cuando XmlReader alcanza el final del reemplazo de entidad como resultado de una llamada al método <see cref="M:System.Xml.XmlReader.ResolveEntity" />.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Entity">
      <summary>Declaración de entidad (por ejemplo, &lt;!ENTITY...&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EntityReference">
      <summary>Referencia a una entidad (por ejemplo, &amp;num;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.None">
      <summary>
        <see cref="T:System.Xml.XmlReader" /> devuelve este valor si no se ha llamado al método Read.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Notation">
      <summary>Notación en la declaración de tipos de documento (por ejemplo, &lt;!NOTATION...&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.ProcessingInstruction">
      <summary>Instrucción de procesamiento (por ejemplo, &lt;?pi test?&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.SignificantWhitespace">
      <summary>Espacio en blanco entre marcas en un modelo de contenido mixto o espacio en blanco dentro del ámbito de xml:space="preserve".</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Text">
      <summary>Contenido de texto de un nodo.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Whitespace">
      <summary>Espacio en blanco entre marcas.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.XmlDeclaration">
      <summary>Declaración XML (por ejemplo, &lt;?xml version='1.0'?&gt; ).</summary>
    </member>
    <member name="T:System.Xml.XmlParserContext">
      <summary>Proporciona toda la información de contexto que necesita el objeto <see cref="T:System.Xml.XmlReader" /> para analizar un fragmento de XML.</summary>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace)">
      <summary>Inicializa una nueva instancia de la clase XmlParserContext con los valores <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, URI base, xml:lang, xml:space y tipo de documento especificados.</summary>
      <param name="nt">
        <see cref="T:System.Xml.XmlNameTable" /> que se va a utilizar para subdividir cadenas.Si el valor es null, se utilizará la tabla de nombres usada para construir <paramref name="nsMgr" />.Para obtener más información sobre las cadenas subdivididas, vea <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">
        <see cref="T:System.Xml.XmlNamespaceManager" /> que se va a utilizar para buscar información sobre los espacios de nombres o null. </param>
      <param name="docTypeName">Nombre de la declaración de tipos de documento. </param>
      <param name="pubId">Identificador público. </param>
      <param name="sysId">Identificador de sistema. </param>
      <param name="internalSubset">Subconjunto DTD interno.El subconjunto DTD se usa para la resolución de entidades, no para la validación de documentos.</param>
      <param name="baseURI">Identificador URI base del fragmento de XML (la ubicación desde la que se cargó el fragmento). </param>
      <param name="xmlLang">Ámbito de xml:lang. </param>
      <param name="xmlSpace">Valor de <see cref="T:System.Xml.XmlSpace" /> que indica el ámbito de xml:space. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> no es el mismo XmlNameTable utilizado para construir <paramref name="nsMgr" />. </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>Inicializa una nueva instancia de la clase XmlParserContext con los valores <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, URI base, xml:lang, xml:space, codificación y tipo de documento especificados.</summary>
      <param name="nt">
        <see cref="T:System.Xml.XmlNameTable" /> que se va a utilizar para subdividir cadenas.Si el valor es null, se utilizará la tabla de nombres usada para construir <paramref name="nsMgr" />.Para obtener más información sobre las cadenas subdivididas, vea <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">
        <see cref="T:System.Xml.XmlNamespaceManager" /> que se va a utilizar para buscar información sobre los espacios de nombres o null. </param>
      <param name="docTypeName">Nombre de la declaración de tipos de documento. </param>
      <param name="pubId">Identificador público. </param>
      <param name="sysId">Identificador de sistema. </param>
      <param name="internalSubset">Subconjunto DTD interno.DTD se usa para la resolución de entidades, no para la validación de documentos.</param>
      <param name="baseURI">Identificador URI base del fragmento de XML (la ubicación desde la que se cargó el fragmento). </param>
      <param name="xmlLang">Ámbito de xml:lang. </param>
      <param name="xmlSpace">Valor de <see cref="T:System.Xml.XmlSpace" /> que indica el ámbito de xml:space. </param>
      <param name="enc">Objeto <see cref="T:System.Text.Encoding" /> que indica el valor de codificación. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> no es el mismo XmlNameTable utilizado para construir <paramref name="nsMgr" />. </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace)">
      <summary>Inicializa una nueva instancia de la clase XmlParserContext con los valores <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, xml:lang y xml:space especificados.</summary>
      <param name="nt">
        <see cref="T:System.Xml.XmlNameTable" /> que se va a utilizar para subdividir cadenas.Si el valor es null, se utilizará la tabla de nombres usada para construir <paramref name="nsMgr" />.Para obtener más información sobre las cadenas subdivididas, vea <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">
        <see cref="T:System.Xml.XmlNamespaceManager" /> que se va a utilizar para buscar información sobre los espacios de nombres o null. </param>
      <param name="xmlLang">Ámbito de xml:lang. </param>
      <param name="xmlSpace">Valor de <see cref="T:System.Xml.XmlSpace" /> que indica el ámbito de xml:space. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> no es el mismo XmlNameTable utilizado para construir <paramref name="nsMgr" />. </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>Inicializa una nueva instancia de la clase XmlParserContext con los valores <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, xml:lang y xml:space especificados y codificación.</summary>
      <param name="nt">
        <see cref="T:System.Xml.XmlNameTable" /> que se va a utilizar para subdividir cadenas.Si el valor es null, se utilizará la tabla de nombres usada para construir <paramref name="nsMgr" />.Para obtener más información sobre cadenas subdivididas, vea <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">
        <see cref="T:System.Xml.XmlNamespaceManager" /> que se va a utilizar para buscar información sobre los espacios de nombres o null. </param>
      <param name="xmlLang">Ámbito de xml:lang. </param>
      <param name="xmlSpace">Valor de <see cref="T:System.Xml.XmlSpace" /> que indica el ámbito de xml:space. </param>
      <param name="enc">Objeto <see cref="T:System.Text.Encoding" /> que indica el valor de codificación. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> no es el mismo XmlNameTable utilizado para construir <paramref name="nsMgr" />. </exception>
    </member>
    <member name="P:System.Xml.XmlParserContext.BaseURI">
      <summary>Obtiene o establece el identificador URI base.</summary>
      <returns>Identificador URI base para resolver el archivo DTD.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.DocTypeName">
      <summary>Obtiene o establece el nombre de la declaración de tipos de documento.</summary>
      <returns>Nombre de la declaración de tipos de documento.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.Encoding">
      <summary>Obtiene o establece el tipo de codificación.</summary>
      <returns>Objeto <see cref="T:System.Text.Encoding" /> que indica el tipo de codificación.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.InternalSubset">
      <summary>Obtiene o establece el subconjunto DTD interno.</summary>
      <returns>Subconjunto DTD interno.Por ejemplo, esta propiedad devuelve todo lo que se encuentra entre los corchetes &lt;!DOCTYPE doc [...]&gt;.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NamespaceManager">
      <summary>Obtiene o establece el objeto <see cref="T:System.Xml.XmlNamespaceManager" />.</summary>
      <returns>XmlNamespaceManager.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NameTable">
      <summary>Obtiene el objeto <see cref="T:System.Xml.XmlNameTable" /> que se va a utilizar para subdividir cadenas.Para obtener más información sobre cadenas subdivididas, vea <see cref="T:System.Xml.XmlNameTable" />.</summary>
      <returns>XmlNameTable.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.PublicId">
      <summary>Obtiene o establece el identificador público.</summary>
      <returns>Identificador público.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.SystemId">
      <summary>Obtiene o establece el identificador de sistema.</summary>
      <returns>Identificador de sistema.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlLang">
      <summary>Obtiene o establece el ámbito de xml:lang actual.</summary>
      <returns>Ámbito de xml:lang actual.Si en el ámbito no hay ningún xml:lang, se devuelve String.Empty.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlSpace">
      <summary>Obtiene o establece el ámbito de xml:space actual.</summary>
      <returns>Valor de <see cref="T:System.Xml.XmlSpace" /> que indica el ámbito de xml:space.</returns>
    </member>
    <member name="T:System.Xml.XmlQualifiedName">
      <summary>Representa un nombre XML completo.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Xml.XmlQualifiedName" /> con el nombre especificado.</summary>
      <param name="name">Nombre local que se va a utilizar como nombre del objeto <see cref="T:System.Xml.XmlQualifiedName" />. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Xml.XmlQualifiedName" /> con el nombre y el espacio de nombres especificados.</summary>
      <param name="name">Nombre local que se va a utilizar como nombre del objeto <see cref="T:System.Xml.XmlQualifiedName" />. </param>
      <param name="ns">Espacio de nombres para el objeto <see cref="T:System.Xml.XmlQualifiedName" />. </param>
    </member>
    <member name="F:System.Xml.XmlQualifiedName.Empty">
      <summary>Proporciona un <see cref="T:System.Xml.XmlQualifiedName" /> vacío.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.Equals(System.Object)">
      <summary>Determina si el objeto <see cref="T:System.Xml.XmlQualifiedName" /> especificado es igual al objeto <see cref="T:System.Xml.XmlQualifiedName" /> actual. </summary>
      <returns>Es true si los dos son un objeto de la misma instancia; en caso contrario, es false.</returns>
      <param name="other">Estructura <see cref="T:System.Xml.XmlQualifiedName" /> que se va comparar. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.GetHashCode">
      <summary>Devuelve el código hash del <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Código hash de este objeto.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.IsEmpty">
      <summary>Obtiene un valor que indica si el objeto <see cref="T:System.Xml.XmlQualifiedName" /> está vacío.</summary>
      <returns>true si el nombre y el espacio de nombres corresponden a cadenas vacías; en caso contrario, false.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Name">
      <summary>Obtiene una representación de cadena del nombre completo de <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Representación de cadena del nombre completo o de String.Empty si no hay un nombre que esté definido para el objeto.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Namespace">
      <summary>Obtiene una representación de cadena del espacio de nombres de <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Representación de cadena del espacio de nombres o de String.Empty si no hay un espacio de nombres que esté definido para el objeto.</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Equality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>Compara dos objetos <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Es true si los dos objetos tienen los mismos valores de nombre y de espacio de nombres; en caso contrario, es false.</returns>
      <param name="a">
        <see cref="T:System.Xml.XmlQualifiedName" /> que se va a comparar. </param>
      <param name="b">
        <see cref="T:System.Xml.XmlQualifiedName" /> que se va a comparar. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Inequality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>Compara dos objetos <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Es true si los valores de nombre y de espacio de nombres de los dos objetos se diferencian en algo; en caso contrario, es false.</returns>
      <param name="a">
        <see cref="T:System.Xml.XmlQualifiedName" /> que se va a comparar. </param>
      <param name="b">
        <see cref="T:System.Xml.XmlQualifiedName" /> que se va a comparar. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString">
      <summary>Devuelve el valor de cadena de <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Valor de cadena de <see cref="T:System.Xml.XmlQualifiedName" /> con el formato de namespace:localname.Si el objeto no tiene un espacio de nombres definido, el método sólo devuelve el nombre local.</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString(System.String,System.String)">
      <summary>Devuelve el valor de cadena de <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>Valor de cadena de <see cref="T:System.Xml.XmlQualifiedName" /> con el formato de namespace:localname.Si el objeto no tiene un espacio de nombres definido, el método sólo devuelve el nombre local.</returns>
      <param name="name">Nombre del objeto. </param>
      <param name="ns">Espacio de nombres del objeto. </param>
    </member>
    <member name="T:System.Xml.XmlReader">
      <summary>Representa un lector que proporciona acceso rápido a datos XML, sin almacenamiento en caché y con desplazamiento solo hacia delante.Para examinar el código fuente de .NET Framework para este tipo, consulte el fuente de referencia de.</summary>
    </member>
    <member name="M:System.Xml.XmlReader.#ctor">
      <summary>Inicializa una nueva instancia de la clase XmlReader.</summary>
    </member>
    <member name="P:System.Xml.XmlReader.AttributeCount">
      <summary>Cuando se invalida en una clase derivada, obtiene el número de atributos en el nodo actual.</summary>
      <returns>Número de atributos del nodo actual.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.BaseURI">
      <summary>Cuando se invalida en una clase derivada, obtiene el identificador URI base del nodo actual.</summary>
      <returns>Identificador URI base del nodo actual.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadBinaryContent">
      <summary>Obtiene un valor que indica si <see cref="T:System.Xml.XmlReader" /> implementa los métodos de lectura de contenido binario.</summary>
      <returns>Es true si se implementan los métodos de lectura de contenido binario; en caso contrario, es false.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadValueChunk">
      <summary>Obtiene un valor que indica si <see cref="T:System.Xml.XmlReader" /> implementa el método <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" />.</summary>
      <returns>Es true si <see cref="T:System.Xml.XmlReader" /> implementa el método <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" />; en caso contrario, es false.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanResolveEntity">
      <summary>Obtiene un valor que indica si este lector puede analizar y resolver entidades.</summary>
      <returns>Es true si el lector puede analizar y resolver entidades; en caso contrario, es false.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream)">
      <summary>Crea un nuevo <see cref="T:System.Xml.XmlReader" /> utilizando la secuencia especificada con la configuración predeterminada de la instancia.</summary>
      <returns>Objeto que se utiliza para leer los datos XML en la secuencia.</returns>
      <param name="input">Flujo que contiene los datos XML.<see cref="T:System.Xml.XmlReader" /> examina los primeros bytes de la secuencia buscando una marca de orden de bytes u otro signo de codificación.Cuando se especifica la codificación, esta se usa para seguir leyendo el flujo, y el procesamiento continúa analizando la entrada como un flujo de caracteres (Unicode).</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="input" /> es null.</exception>
      <exception cref="T:System.Security.SecurityException">El <see cref="T:System.Xml.XmlReader" /> no tiene los permisos suficientes para tener acceso a la ubicación de los datos XML.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings)">
      <summary>Crea un nuevo <see cref="T:System.Xml.XmlReader" /> instancia con la configuración y la secuencia especificada.</summary>
      <returns>Objeto que se utiliza para leer los datos XML en la secuencia.</returns>
      <param name="input">Flujo que contiene los datos XML.<see cref="T:System.Xml.XmlReader" /> examina los primeros bytes de la secuencia buscando una marca de orden de bytes u otro signo de codificación.Cuando se especifica la codificación, esta se usa para seguir leyendo el flujo, y el procesamiento continúa analizando la entrada como un flujo de caracteres (Unicode).</param>
      <param name="settings">La configuración para el nuevo <see cref="T:System.Xml.XmlReader" /> instancia.Este valor puede ser null.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="input" /> es null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>Crea un nuevo <see cref="T:System.Xml.XmlReader" /> instancia utilizando la información de secuencia, la configuración y el contexto para el análisis.</summary>
      <returns>Objeto que se utiliza para leer los datos XML en la secuencia.</returns>
      <param name="input">Flujo que contiene los datos XML. <see cref="T:System.Xml.XmlReader" /> examina los primeros bytes de la secuencia buscando una marca de orden de bytes u otro signo de codificación.Cuando se especifica la codificación, esta se usa para seguir leyendo el flujo, y el procesamiento continúa analizando la entrada como un flujo de caracteres (Unicode).</param>
      <param name="settings">La configuración para el nuevo <see cref="T:System.Xml.XmlReader" /> instancia.Este valor puede ser null.</param>
      <param name="inputContext">La información de contexto requerida para analizar el fragmento XML.La información de contexto puede incluir el objeto <see cref="T:System.Xml.XmlNameTable" /> que se va a utilizar, la codificación, el ámbito del espacio de nombres, el ámbito actual de xml:lang y xml:space, el URI base y la definición de tipo de documento.Este valor puede ser null.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="input" /> es null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader)">
      <summary>Crea un nuevo <see cref="T:System.Xml.XmlReader" /> instancia mediante el lector de texto especificado.</summary>
      <returns>Objeto que se utiliza para leer los datos XML en la secuencia.</returns>
      <param name="input">El lector de texto desde el que se va a leer los datos XML.Un lector de texto devuelve una secuencia de caracteres Unicode, por lo que la codificación especificada en la declaración XML no se utiliza el lector XML para descodificar la secuencia de datos.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="input" /> es null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings)">
      <summary>Crea un nuevo <see cref="T:System.Xml.XmlReader" /> instancia mediante el lector de texto especificado y la configuración.</summary>
      <returns>Objeto que se utiliza para leer los datos XML en la secuencia.</returns>
      <param name="input">El lector de texto desde el que se va a leer los datos XML.Un lector de texto devuelve una secuencia de caracteres Unicode, por lo que la codificación especificada en la declaración XML no se usa el lector XML para descodificar la secuencia de datos.</param>
      <param name="settings">La configuración para el nuevo <see cref="T:System.Xml.XmlReader" />.Este valor puede ser null.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="input" /> es null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>Crea un nuevo <see cref="T:System.Xml.XmlReader" /> instancia utilizando la información de lector, la configuración y el contexto de texto especificado para el análisis.</summary>
      <returns>Objeto que se utiliza para leer los datos XML en la secuencia.</returns>
      <param name="input">El lector de texto desde el que se va a leer los datos XML.Un lector de texto devuelve una secuencia de caracteres Unicode, por lo que la codificación especificada en la declaración XML no se usa el lector XML para descodificar la secuencia de datos.</param>
      <param name="settings">La configuración para el nuevo <see cref="T:System.Xml.XmlReader" /> instancia.Este valor puede ser null.</param>
      <param name="inputContext">La información de contexto requerida para analizar el fragmento XML.La información de contexto puede incluir el objeto <see cref="T:System.Xml.XmlNameTable" /> que se va a utilizar, la codificación, el ámbito del espacio de nombres, el ámbito actual de xml:lang y xml:space, el URI base y la definición de tipo de documento.Este valor puede ser null.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="input" /> es null.</exception>
      <exception cref="T:System.ArgumentException">Tanto la propiedad <see cref="P:System.Xml.XmlReaderSettings.NameTable" /> como la propiedad <see cref="P:System.Xml.XmlParserContext.NameTable" /> contienen valores.Sólo se puede establecer y utilizar una de estas propiedades NameTable.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String)">
      <summary>Crea una nueva instancia de <see cref="T:System.Xml.XmlReader" /> con el URI especificado.</summary>
      <returns>Objeto que se utiliza para leer los datos XML en la secuencia.</returns>
      <param name="inputUri">El URI para el archivo que contiene los datos XML.La clase <see cref="T:System.Xml.XmlUrlResolver" /> se utiliza para convertir la ruta de acceso en una representación de datos canónicos.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="inputUri" /> es null.</exception>
      <exception cref="T:System.Security.SecurityException">El <see cref="T:System.Xml.XmlReader" /> no tiene los permisos suficientes para tener acceso a la ubicación de los datos XML.</exception>
      <exception cref="T:System.IO.FileNotFoundException">El archivo que identifica el URI no existe.</exception>
      <exception cref="T:System.UriFormatException">En las API de .NET para aplicaciones de la Tienda Windows o en la Biblioteca de clases portable, encuentre la excepción de la clase base, <see cref="T:System.FormatException" />, en su lugar.El formato del URI no es correcto.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String,System.Xml.XmlReaderSettings)">
      <summary>Crea un nuevo <see cref="T:System.Xml.XmlReader" /> instancia usando el URI especificado y la configuración.</summary>
      <returns>Objeto que se utiliza para leer los datos XML en la secuencia.</returns>
      <param name="inputUri">URI del archivo que contiene los datos XML.El objeto <see cref="T:System.Xml.XmlResolver" /> del objeto <see cref="T:System.Xml.XmlReaderSettings" /> se utiliza para convertir la ruta de acceso en una representación de datos canónicos.Si <see cref="P:System.Xml.XmlReaderSettings.XmlResolver" /> es null, se utiliza un nuevo objeto <see cref="T:System.Xml.XmlUrlResolver" />.</param>
      <param name="settings">La configuración para el nuevo <see cref="T:System.Xml.XmlReader" /> instancia.Este valor puede ser null.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="inputUri" /> es null.</exception>
      <exception cref="T:System.IO.FileNotFoundException">No se encuentra el archivo especificado por el URI.</exception>
      <exception cref="T:System.UriFormatException">En las API de .NET para aplicaciones de la Tienda Windows o en la Biblioteca de clases portable, encuentre la excepción de la clase base, <see cref="T:System.FormatException" />, en su lugar.El formato del URI no es correcto.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.Xml.XmlReader,System.Xml.XmlReaderSettings)">
      <summary>Crea un nuevo <see cref="T:System.Xml.XmlReader" /> instancia utilizando el lector XML especificado y la configuración.</summary>
      <returns>Un objeto que se ajusta alrededor especificado <see cref="T:System.Xml.XmlReader" /> objeto.</returns>
      <param name="reader">El objeto que desea utilizar como lector XML subyacente.</param>
      <param name="settings">La configuración para el nuevo <see cref="T:System.Xml.XmlReader" /> instancia.El nivel de conformidad del objeto <see cref="T:System.Xml.XmlReaderSettings" /> debe coincidir con el del lector subyacente o establecerse en <see cref="F:System.Xml.ConformanceLevel.Auto" />.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="reader" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">Si el objeto <see cref="T:System.Xml.XmlReaderSettings" /> especifica un nivel de conformidad que no es coherente con nivel de conformidad del lector subyacente.o bienEl objeto <see cref="T:System.Xml.XmlReader" /> subyacente está en un estado de <see cref="F:System.Xml.ReadState.Error" /> o <see cref="F:System.Xml.ReadState.Closed" />.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Depth">
      <summary>Cuando se invalida en una clase derivada, obtiene la profundidad del nodo actual en el documento XML.</summary>
      <returns>Profundidad del nodo actual en el documento XML.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose">
      <summary>Libera todos los recursos usados por la instancia actual de la clase <see cref="T:System.Xml.XmlReader" />.</summary>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que usa <see cref="T:System.Xml.XmlReader" /> y, de forma opcional, libera los recursos administrados.</summary>
      <param name="disposing">truepara liberar los recursos administrados y no administrados; false para liberar únicamente los recursos no administrados.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.EOF">
      <summary>Cuando se invalida en una clase derivada, obtiene un valor que indica si el lector está situado al final del flujo.</summary>
      <returns>Es true si el lector está situado al final de la secuencia; en caso contrario, es false.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.Int32)">
      <summary>Cuando se invalida en una clase derivada, obtiene el valor del atributo con el índice especificado.</summary>
      <returns>Valor del atributo especificado.Este método no desplaza el lector.</returns>
      <param name="i">Índice del atributo.El índice está basado en cero.El primer atributo tiene índice 0.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="i" /> está fuera del intervalo.Debe ser no negativo y menor que el tamaño de la colección de atributos.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String)">
      <summary>Cuando se invalida en una clase derivada, obtiene el valor del atributo con la propiedad <see cref="P:System.Xml.XmlReader.Name" /> especificada.</summary>
      <returns>Valor del atributo especificado.Si no se encuentra el atributo o el valor es String.Empty, se devuelve null.</returns>
      <param name="name">Nombre completo del atributo.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" />is null.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String,System.String)">
      <summary>Cuando se invalida en una clase derivada, obtiene el valor del atributo con las propiedades <see cref="P:System.Xml.XmlReader.LocalName" /> y <see cref="P:System.Xml.XmlReader.NamespaceURI" /> especificadas.</summary>
      <returns>Valor del atributo especificado.Si no se encuentra el atributo o el valor es String.Empty, se devuelve null.Este método no desplaza el lector.</returns>
      <param name="name">Nombre local del atributo.</param>
      <param name="namespaceURI">URI de espacio de nombres del atributo.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" />is null.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetValueAsync">
      <summary>Obtiene de forma asincrónica el valor del nodo actual.</summary>
      <returns>Valor del nodo actual.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.InvalidOperationException">Un método asincrónico de <see cref="T:System.Xml.XmlReader" /> se llamó sin establecer la marca <see cref="P:System.Xml.XmlReaderSettings.Async" /> en true.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Establezca XmlReaderSettings.Async en true si desea usar métodos asincrónicos”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasAttributes">
      <summary>Obtiene un valor que indica si el nodo actual tiene algún atributo.</summary>
      <returns>Es true si el nodo actual tiene atributos; en caso contrario, es false.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasValue">
      <summary>Cuando se invalida en una clase derivada, obtiene un valor que indica si el nodo actual puede tener una propiedad <see cref="P:System.Xml.XmlReader.Value" />.</summary>
      <returns>Es true si el nodo en el que está situado actualmente el lector puede tener un Value; en caso contrario, es false.Si es false, el nodo tiene un valor de String.Empty.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsDefault">
      <summary>Cuando se reemplaza en una clase derivada, obtiene un valor que indica si el nodo actual es un atributo generado a partir del valor predeterminado definido en la DTD o el esquema.</summary>
      <returns>Es true si el nodo actual es un atributo cuyo valor fue generado a partir del valor predeterminado definido en la DTD o el esquema; es false si el valor de atributo se estableció explícitamente.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsEmptyElement">
      <summary>Cuando se invalida en una clase derivada, obtiene un valor que indica si el nodo actual es un elemento vacío (por ejemplo, &lt;MyElement/&gt;).</summary>
      <returns>Es true si el nodo actual es un elemento (<see cref="P:System.Xml.XmlReader.NodeType" /> es igual a XmlNodeType.Element) que termina en /&gt;; en caso contrario, es false.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsName(System.String)">
      <summary>Devuelve un valor que indica si el argumento de cadena es un nombre XML válido.</summary>
      <returns>Es true si el nombre es válido; en caso contrario, es false.</returns>
      <param name="str">Nombre que se va a validar.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="str" /> es null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsNameToken(System.String)">
      <summary>Devuelve un valor que indica si el argumento de cadena es un token de nombre XML válido.</summary>
      <returns>Es true si es un token de nombre válido; en caso contrario, es false.</returns>
      <param name="str">Token de nombre que se va a validar.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="str" /> es null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement">
      <summary>Llama al método <see cref="M:System.Xml.XmlReader.MoveToContent" /> y comprueba si el nodo de contenido actual es una etiqueta de apertura o una etiqueta de elemento vacío.</summary>
      <returns>Es true si <see cref="M:System.Xml.XmlReader.MoveToContent" /> encuentra una etiqueta de apertura o una etiqueta de elemento vacío; es false si se encuentra un tipo de nodo que no sea XmlNodeType.Element.</returns>
      <exception cref="T:System.Xml.XmlException">Se detecta XML incorrecto en el flujo de entrada.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String)">
      <summary>Llama al método <see cref="M:System.Xml.XmlReader.MoveToContent" /> y comprueba si el nodo de contenido actual es una etiqueta de apertura o una etiqueta de elemento vacío y si la propiedad <see cref="P:System.Xml.XmlReader.Name" /> del elemento encontrado coincide con el argumento especificado.</summary>
      <returns>true si el nodo resultante es un elemento y la propiedad Name coincide con la cadena especificada.false si se encuentra un tipo de nodo que no sea XmlNodeType.Element o si la propiedad Name del elemento no coincide con la cadena especificada.</returns>
      <param name="name">Cadena que se compara con la propiedad Name del elemento encontrado.</param>
      <exception cref="T:System.Xml.XmlException">Se detecta XML incorrecto en el flujo de entrada.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String,System.String)">
      <summary>Llama al método <see cref="M:System.Xml.XmlReader.MoveToContent" /> y comprueba si el nodo de contenido actual es una etiqueta de apertura o una etiqueta de elemento vacío y si las propiedades <see cref="P:System.Xml.XmlReader.LocalName" /> y <see cref="P:System.Xml.XmlReader.NamespaceURI" /> del elemento encontrado coinciden con las cadenas especificadas.</summary>
      <returns>true si el nodo resultante es un elemento.false si se encuentra un tipo de nodo que no sea XmlNodeType.Element o si las propiedades LocalName y NamespaceURI del elemento no coinciden con la cadena especificada.</returns>
      <param name="localname">Cadena con la que se compara la propiedad LocalName del elemento encontrado.</param>
      <param name="ns">Cadena con la que se compara la propiedad NamespaceURI del elemento encontrado.</param>
      <exception cref="T:System.Xml.XmlException">Se detecta XML incorrecto en el flujo de entrada.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.Int32)">
      <summary>Cuando se invalida en una clase derivada, obtiene el valor del atributo con el índice especificado.</summary>
      <returns>Valor del atributo especificado.</returns>
      <param name="i">Índice del atributo.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String)">
      <summary>Cuando se invalida en una clase derivada, obtiene el valor del atributo con la propiedad <see cref="P:System.Xml.XmlReader.Name" /> especificada.</summary>
      <returns>Valor del atributo especificado.Si no se encuentra el atributo, se devuelve null.</returns>
      <param name="name">Nombre completo del atributo.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String,System.String)">
      <summary>Cuando se invalida en una clase derivada, obtiene el valor del atributo con las propiedades <see cref="P:System.Xml.XmlReader.LocalName" /> y <see cref="P:System.Xml.XmlReader.NamespaceURI" /> especificadas.</summary>
      <returns>Valor del atributo especificado.Si no se encuentra el atributo, se devuelve null.</returns>
      <param name="name">Nombre local del atributo.</param>
      <param name="namespaceURI">URI de espacio de nombres del atributo.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.LocalName">
      <summary>Cuando se invalida en una clase derivada, obtiene el nombre local del nodo actual.</summary>
      <returns>Nombre del nodo actual sin prefijo.Por ejemplo, LocalName es book para el elemento &lt;bk:book&gt;.Para los tipos de nodo sin nombre (por ejemplo, Text, Comment, etc.), esta propiedad devuelve String.Empty.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.LookupNamespace(System.String)">
      <summary>Cuando se invalida en una clase derivada, resuelve un prefijo de espacio de nombres en el ámbito del elemento actual.</summary>
      <returns>Identificador URI de espacio de nombres al que se asigna el prefijo o null si no se encuentra ningún prefijo coincidente.</returns>
      <param name="prefix">Prefijo cuyo identificador URI de espacio de nombres se desea resolver.Para hacer coincidir el espacio de nombres predeterminado, pase una cadena vacía.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.Int32)">
      <summary>Cuando se invalida en una clase derivada, se desplaza al atributo con el índice especificado.</summary>
      <param name="i">Índice del atributo.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El parámetro tiene un valor negativo.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String)">
      <summary>Cuando se invalida en una clase derivada, se desplaza al atributo con la propiedad <see cref="P:System.Xml.XmlReader.Name" /> especificada.</summary>
      <returns>Es true si se encuentra el atributo; en caso contrario, es false.Si es false, no cambia la posición del lector.</returns>
      <param name="name">Nombre completo del atributo.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.ArgumentException">El parámetro es una cadena vacía.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String,System.String)">
      <summary>Cuando se invalida en una clase derivada, se desplaza al atributo con las propiedades <see cref="P:System.Xml.XmlReader.LocalName" /> y <see cref="P:System.Xml.XmlReader.NamespaceURI" /> especificadas.</summary>
      <returns>Es true si se encuentra el atributo; en caso contrario, es false.Si es false, no cambia la posición del lector.</returns>
      <param name="name">Nombre local del atributo.</param>
      <param name="ns">URI de espacio de nombres del atributo.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.ArgumentNullException">Ambos valores de parámetro son null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContent">
      <summary>Comprueba si el nodo actual es un nodo de contenido (texto sin espacios en blanco, CDATA, Element, EndElement, EntityReference o EndEntity).Si el nodo no es un nodo de contenido, el lector salta hasta el siguiente nodo de contenido o el final del archivo.Omite los siguientes tipos de nodo: ProcessingInstruction, DocumentType, Comment, Whitespace o SignificantWhitespace.</summary>
      <returns>
        <see cref="P:System.Xml.XmlReader.NodeType" /> del nodo actual encontrado por el método o XmlNodeType.None si el lector ha alcanzado el final del flujo de entrada.</returns>
      <exception cref="T:System.Xml.XmlException">XML incorrecto que se encuentra en el flujo de entrada.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContentAsync">
      <summary>De forma asincrónica comprueba si el nodo actual es un nodo de contenido.Si el nodo no es un nodo de contenido, el lector salta hasta el siguiente nodo de contenido o el final del archivo.</summary>
      <returns>
        <see cref="P:System.Xml.XmlReader.NodeType" /> del nodo actual encontrado por el método o XmlNodeType.None si el lector ha alcanzado el final del flujo de entrada.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.InvalidOperationException">Un método asincrónico de <see cref="T:System.Xml.XmlReader" /> se llamó sin establecer la marca <see cref="P:System.Xml.XmlReaderSettings.Async" /> en true.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Establezca XmlReaderSettings.Async en true si desea usar métodos asincrónicos”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToElement">
      <summary>Cuando se invalida en una clase derivada, se desplaza al elemento que contiene el nodo de atributo actual.</summary>
      <returns>Es true si el lector está situado en un atributo (el lector se desplaza hasta el elemento que posee el atributo); es false si el lector no está situado en un atributo (no cambia la posición del lector).</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToFirstAttribute">
      <summary>Cuando se invalida en una clase derivada, se desplaza hasta el primer atributo.</summary>
      <returns>Es true si existe un atributo (el lector se desplaza hasta el primer atributo); en caso contrario, es false (no cambia la posición del lector).</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToNextAttribute">
      <summary>Cuando se invalida en una clase derivada, se desplaza hasta el siguiente atributo.</summary>
      <returns>Es true si hay siguiente atributo; es false si no hay más atributos.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Name">
      <summary>Cuando se invalida en una clase derivada, obtiene el nombre completo del nodo actual.</summary>
      <returns>Nombre completo del nodo actual.Por ejemplo, Name es bk:book para el elemento &lt;bk:book&gt;.El nombre devuelto depende de la propiedad <see cref="P:System.Xml.XmlReader.NodeType" /> del nodo.Los siguientes tipos de nodo devuelven los valores que figuran en la lista.Todos los demás tipos de nodo devuelven una cadena vacía.Tipo de nodo Name AttributeNombre del atributo. DocumentTypeNombre del tipo de documento. ElementEl nombre de la etiqueta. EntityReferenceNombre de la entidad a la que se hace referencia. ProcessingInstructionDestino de la instrucción de procesamiento. XmlDeclarationCadena literal xml. </returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NamespaceURI">
      <summary>Cuando se invalida en una clase derivada, obtiene el identificador URI de espacio de nombres (según se define en la especificación relativa a espacios de nombres del Consorcio W3C) del nodo en el que está situado el lector.</summary>
      <returns>URI de espacio de nombres del nodo actual; en caso contrario, una cadena vacía.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NameTable">
      <summary>Cuando se invalida en una clase derivada, obtiene el objeto <see cref="T:System.Xml.XmlNameTable" /> que está asociado a esta implementación.</summary>
      <returns>XmlNameTable que permite obtener la versión subdividida de una cadena en el nodo.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NodeType">
      <summary>Cuando se invalida en una clase derivada, obtiene el tipo del nodo actual.</summary>
      <returns>Uno de los valores de enumeración que especifican el tipo del nodo actual.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Prefix">
      <summary>Cuando se invalida en una clase derivada, obtiene el prefijo de espacio de nombres asociado al nodo actual.</summary>
      <returns>Prefijo de espacio de nombres asociado al nodo actual.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Read">
      <summary>Cuando se invalida en una clase derivada, lee el siguiente nodo del flujo.</summary>
      <returns>trueSi el siguiente nodo se leyó correctamente; de lo contrario, false.</returns>
      <exception cref="T:System.Xml.XmlException">Se ha producido un error al analizar el fragmento de XML.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAsync">
      <summary>De forma asincrónica lee el nodo siguiente del flujo.</summary>
      <returns>Es true si se lee correctamente el siguiente nodo; es false si no hay más nodos para leer.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.InvalidOperationException">Un método asincrónico de <see cref="T:System.Xml.XmlReader" /> se llamó sin establecer la marca <see cref="P:System.Xml.XmlReaderSettings.Async" /> en true.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Establezca XmlReaderSettings.Async en true si desea usar métodos asincrónicos”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAttributeValue">
      <summary>Cuando se invalida en una clase derivada, analiza el valor de atributo en uno o varios nodos Text, EntityReference o EndEntity.</summary>
      <returns>Es true si hay nodos para devolver.Es false si el lector no está situado en un nodo de atributo cuando se realiza la llamada inicial o si se han leído todos los valores de atributo.Un atributo vacío, como misc="", devuelve true con un solo nodo cuyo valor es String.Empty.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Lee el contenido como objeto del tipo especificado.</summary>
      <returns>Contenido de texto concatenado o valor de atributo convertido en el tipo solicitado.</returns>
      <param name="returnType">Tipo del valor que se va a devolver.Nota   Con el lanzamiento de .NET Framework 3.5, el valor del parámetro <paramref name="returnType" /> ahora puede ser el tipo de <see cref="T:System.DateTimeOffset" />.</param>
      <param name="namespaceResolver">Objeto <see cref="T:System.Xml.IXmlNamespaceResolver" /> que se utiliza para resolver prefijos de espacios de nombres relacionados con la conversión de tipo.Por ejemplo, se puede utilizar al convertir un objeto <see cref="T:System.Xml.XmlQualifiedName" /> en xs:string.Este valor puede ser null.</param>
      <exception cref="T:System.FormatException">El formato del contenido no es correcto para el tipo de destino.</exception>
      <exception cref="T:System.InvalidCastException">La conversión intentada no es válida.</exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="returnType" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">El nodo actual no es un tipo de nodo compatible.Vea la siguiente tabla para obtener información detallada.</exception>
      <exception cref="T:System.OverflowException">Lea Decimal.MaxValue.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Lee asincrónicamente el contenido como objeto del tipo especificado.</summary>
      <returns>Contenido de texto concatenado o valor de atributo convertido en el tipo solicitado.</returns>
      <param name="returnType">Tipo del valor que se va a devolver.</param>
      <param name="namespaceResolver">Objeto <see cref="T:System.Xml.IXmlNamespaceResolver" /> que se utiliza para resolver prefijos de espacios de nombres relacionados con la conversión de tipo.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.InvalidOperationException">Un método asincrónico de <see cref="T:System.Xml.XmlReader" /> se llamó sin establecer la marca <see cref="P:System.Xml.XmlReaderSettings.Async" /> en true.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Establezca XmlReaderSettings.Async en true si desea usar métodos asincrónicos”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>Lee el contenido y devuelve los bytes binarios descodificados en Base64.</summary>
      <returns>Número de bytes escritos en el búfer.</returns>
      <param name="buffer">Búfer donde se va a copiar el texto resultante.Este valor no puede ser null.</param>
      <param name="index">Posición de desplazamiento en el búfer donde debe comenzar la copia del resultado.</param>
      <param name="count">Número máximo de bytes que se van a copiar en el búfer.El número real de bytes copiados se devuelve a partir de este método.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="buffer" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">El método <see cref="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)" /> no es compatible con el nodo actual.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El índice del búfer (index) o la suma del índice y el recuento (index + count) es mayor que el tamaño de búfer asignado.</exception>
      <exception cref="T:System.NotSupportedException">La implementación de <see cref="T:System.Xml.XmlReader" /> no admite este método.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Lee asincrónicamente el contenido y devuelve los bytes binarios descodificados en Base64.</summary>
      <returns>Número de bytes escritos en el búfer.</returns>
      <param name="buffer">Búfer donde se va a copiar el texto resultante.Este valor no puede ser null.</param>
      <param name="index">Posición de desplazamiento en el búfer donde debe comenzar la copia del resultado.</param>
      <param name="count">Número máximo de bytes que se van a copiar en el búfer.El número real de bytes copiados se devuelve a partir de este método.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.InvalidOperationException">Un método asincrónico de <see cref="T:System.Xml.XmlReader" /> se llamó sin establecer la marca <see cref="P:System.Xml.XmlReaderSettings.Async" /> en true.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Establezca XmlReaderSettings.Async en true si desea usar métodos asincrónicos”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>Lee el contenido y devuelve los bytes binarios descodificados de BinHex.</summary>
      <returns>Número de bytes escritos en el búfer.</returns>
      <param name="buffer">Búfer donde se va a copiar el texto resultante.Este valor no puede ser null.</param>
      <param name="index">Posición de desplazamiento en el búfer donde debe comenzar la copia del resultado.</param>
      <param name="count">Número máximo de bytes que se van a copiar en el búfer.El número real de bytes copiados se devuelve a partir de este método.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="buffer" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">El método <see cref="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)" /> no es compatible con el nodo actual.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El índice del búfer (index) o la suma del índice y el recuento (index + count) es mayor que el tamaño de búfer asignado.</exception>
      <exception cref="T:System.NotSupportedException">La implementación de <see cref="T:System.Xml.XmlReader" /> no admite este método.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Lee asincrónicamente el contenido y devuelve los bytes binarios descodificados de BinHex.</summary>
      <returns>Número de bytes escritos en el búfer.</returns>
      <param name="buffer">Búfer donde se va a copiar el texto resultante.Este valor no puede ser null.</param>
      <param name="index">Posición de desplazamiento en el búfer donde debe comenzar la copia del resultado.</param>
      <param name="count">Número máximo de bytes que se van a copiar en el búfer.El número real de bytes copiados se devuelve a partir de este método.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.InvalidOperationException">Un método asincrónico de <see cref="T:System.Xml.XmlReader" /> se llamó sin establecer la marca <see cref="P:System.Xml.XmlReaderSettings.Async" /> en true.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Establezca XmlReaderSettings.Async en true si desea usar métodos asincrónicos”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBoolean">
      <summary>Lee el contenido de texto en la posición actual como valor Boolean.</summary>
      <returns>El contenido del texto como objeto <see cref="T:System.Boolean" />.</returns>
      <exception cref="T:System.InvalidCastException">La conversión intentada no es válida.</exception>
      <exception cref="T:System.FormatException">El formato de la cadena no es válido.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDateTimeOffset">
      <summary>Lee el contenido de texto en la posición actual como un objeto <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>El contenido del texto como objeto <see cref="T:System.DateTimeOffset" />.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDecimal">
      <summary>Lee el contenido de texto en la posición actual como un objeto <see cref="T:System.Decimal" />.</summary>
      <returns>El contenido de texto en la posición actual como objeto <see cref="T:System.Decimal" />.</returns>
      <exception cref="T:System.InvalidCastException">La conversión intentada no es válida.</exception>
      <exception cref="T:System.FormatException">El formato de la cadena no es válido.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDouble">
      <summary>Lee el contenido de texto en la posición actual como número de punto flotante de precisión doble.</summary>
      <returns>El contenido de texto como número de punto flotante de precisión doble.</returns>
      <exception cref="T:System.InvalidCastException">La conversión intentada no es válida.</exception>
      <exception cref="T:System.FormatException">El formato de la cadena no es válido.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsFloat">
      <summary>Lee el contenido de texto en la posición actual como número de punto flotante de precisión sencilla.</summary>
      <returns>El contenido de texto en la posición actual como número de punto flotante de precisión sencilla.</returns>
      <exception cref="T:System.InvalidCastException">La conversión intentada no es válida.</exception>
      <exception cref="T:System.FormatException">El formato de la cadena no es válido.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsInt">
      <summary>Lee el contenido de texto en la posición actual como un entero de 32 bits con signo.</summary>
      <returns>El contenido de texto como entero de 32 bits con signo.</returns>
      <exception cref="T:System.InvalidCastException">La conversión intentada no es válida.</exception>
      <exception cref="T:System.FormatException">El formato de la cadena no es válido.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsLong">
      <summary>Lee el contenido de texto en la posición actual como un entero de 64 bits con signo.</summary>
      <returns>El contenido de texto como entero de 64 bits con signo.</returns>
      <exception cref="T:System.InvalidCastException">La conversión intentada no es válida.</exception>
      <exception cref="T:System.FormatException">El formato de la cadena no es válido.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObject">
      <summary>Lee el contenido de texto en la posición actual como <see cref="T:System.Object" />.</summary>
      <returns>El contenido de texto como el objeto de Common Language Runtime (CLR) más adecuado.</returns>
      <exception cref="T:System.InvalidCastException">La conversión intentada no es válida.</exception>
      <exception cref="T:System.FormatException">El formato de la cadena no es válido.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObjectAsync">
      <summary>Lee asincrónicamente el contenido de texto en la posición actual como un objeto <see cref="T:System.Object" />.</summary>
      <returns>El contenido de texto como el objeto de Common Language Runtime (CLR) más adecuado.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.InvalidOperationException">Un método asincrónico de <see cref="T:System.Xml.XmlReader" /> se llamó sin establecer la marca <see cref="P:System.Xml.XmlReaderSettings.Async" /> en true.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Establezca XmlReaderSettings.Async en true si desea usar métodos asincrónicos”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsString">
      <summary>Lee el contenido de texto en la posición actual como un objeto <see cref="T:System.String" />.</summary>
      <returns>El contenido del texto como objeto <see cref="T:System.String" />.</returns>
      <exception cref="T:System.InvalidCastException">La conversión intentada no es válida.</exception>
      <exception cref="T:System.FormatException">El formato de la cadena no es válido.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsStringAsync">
      <summary>Lee asincrónicamente el contenido de texto en la posición actual como un objeto <see cref="T:System.String" />.</summary>
      <returns>El contenido del texto como objeto <see cref="T:System.String" />.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.InvalidOperationException">Un método asincrónico de <see cref="T:System.Xml.XmlReader" /> se llamó sin establecer la marca <see cref="P:System.Xml.XmlReaderSettings.Async" /> en true.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Establezca XmlReaderSettings.Async en true si desea usar métodos asincrónicos”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Lee el contenido de los elementos como el tipo solicitado.</summary>
      <returns>Contenido de elementos convertido en el objeto con tipo solicitado.</returns>
      <param name="returnType">Tipo del valor que se va a devolver.Nota   Con el lanzamiento de .NET Framework 3.5, el valor del parámetro <paramref name="returnType" /> ahora puede ser el tipo de <see cref="T:System.DateTimeOffset" />.</param>
      <param name="namespaceResolver">Objeto <see cref="T:System.Xml.IXmlNamespaceResolver" /> que se utiliza para resolver prefijos de espacios de nombres relacionados con la conversión de tipo.</param>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.Xml.XmlReader" /> no está situado en ningún elemento.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento actual contiene elementos secundarios.o bienEl contenido del elemento no se puede convertir en el tipo solicitado.</exception>
      <exception cref="T:System.ArgumentNullException">Se llama al método con argumentos null.</exception>
      <exception cref="T:System.OverflowException">Lea Decimal.MaxValue.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver,System.String,System.String)">
      <summary>Comprueba que el nombre local especificado y el URI de espacio de nombres coinciden con los del elemento actual y, a continuación, lee el contenido de los elementos como el tipo solicitado.</summary>
      <returns>Contenido de elementos convertido en el objeto con tipo solicitado.</returns>
      <param name="returnType">Tipo del valor que se va a devolver.Nota   Con el lanzamiento de .NET Framework 3.5, el valor del parámetro <paramref name="returnType" /> ahora puede ser el tipo de <see cref="T:System.DateTimeOffset" />.</param>
      <param name="namespaceResolver">Objeto <see cref="T:System.Xml.IXmlNamespaceResolver" /> que se utiliza para resolver prefijos de espacios de nombres relacionados con la conversión de tipo.</param>
      <param name="localName">Nombre local del elemento.</param>
      <param name="namespaceURI">Identificador URI de espacio de nombres del elemento.</param>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.Xml.XmlReader" /> no está situado en ningún elemento.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento actual contiene elementos secundarios.o bienEl contenido del elemento no se puede convertir en el tipo solicitado.</exception>
      <exception cref="T:System.ArgumentNullException">Se llama al método con argumentos null.</exception>
      <exception cref="T:System.ArgumentException">El nombre local y el identificador URI del espacio de nombres especificados no coinciden con los del elemento que se está leyendo.</exception>
      <exception cref="T:System.OverflowException">Lea Decimal.MaxValue.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Lee asincrónicamente el contenido del elemento como el tipo solicitado.</summary>
      <returns>Contenido de elementos convertido en el objeto con tipo solicitado.</returns>
      <param name="returnType">Tipo del valor que se va a devolver.</param>
      <param name="namespaceResolver">Objeto <see cref="T:System.Xml.IXmlNamespaceResolver" /> que se utiliza para resolver prefijos de espacios de nombres relacionados con la conversión de tipo.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.InvalidOperationException">Un método asincrónico de <see cref="T:System.Xml.XmlReader" /> se llamó sin establecer la marca <see cref="P:System.Xml.XmlReaderSettings.Async" /> en true.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Establezca XmlReaderSettings.Async en true si desea usar métodos asincrónicos”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>Lee el elemento y descodifica el contenido de Base64.</summary>
      <returns>Número de bytes escritos en el búfer.</returns>
      <param name="buffer">Búfer donde se va a copiar el texto resultante.Este valor no puede ser null.</param>
      <param name="index">Posición de desplazamiento en el búfer donde debe comenzar la copia del resultado.</param>
      <param name="count">Número máximo de bytes que se van a copiar en el búfer.El número real de bytes copiados se devuelve a partir de este método.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="buffer" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">El nodo actual no es un nodo de elemento.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El índice del búfer (index) o la suma del índice y el recuento (index + count) es mayor que el tamaño de búfer asignado.</exception>
      <exception cref="T:System.NotSupportedException">La implementación de <see cref="T:System.Xml.XmlReader" /> no admite este método.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento contiene un contenido mixto.</exception>
      <exception cref="T:System.FormatException">El contenido no puede convertirse en el tipo solicitado.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Lee asincrónicamente el elemento y descodifica el contenido de Base64.</summary>
      <returns>Número de bytes escritos en el búfer.</returns>
      <param name="buffer">Búfer donde se va a copiar el texto resultante.Este valor no puede ser null.</param>
      <param name="index">Posición de desplazamiento en el búfer donde debe comenzar la copia del resultado.</param>
      <param name="count">Número máximo de bytes que se van a copiar en el búfer.El número real de bytes copiados se devuelve a partir de este método.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.InvalidOperationException">Un método asincrónico de <see cref="T:System.Xml.XmlReader" /> se llamó sin establecer la marca <see cref="P:System.Xml.XmlReaderSettings.Async" /> en true.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Establezca XmlReaderSettings.Async en true si desea usar métodos asincrónicos”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>Lee el elemento y descodifica el contenido de BinHex.</summary>
      <returns>Número de bytes escritos en el búfer.</returns>
      <param name="buffer">Búfer donde se va a copiar el texto resultante.Este valor no puede ser null.</param>
      <param name="index">Posición de desplazamiento en el búfer donde debe comenzar la copia del resultado.</param>
      <param name="count">Número máximo de bytes que se van a copiar en el búfer.El número real de bytes copiados se devuelve a partir de este método.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="buffer" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">El nodo actual no es un nodo de elemento.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El índice del búfer (index) o la suma del índice y el recuento (index + count) es mayor que el tamaño de búfer asignado.</exception>
      <exception cref="T:System.NotSupportedException">La implementación de <see cref="T:System.Xml.XmlReader" /> no admite este método.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento contiene un contenido mixto.</exception>
      <exception cref="T:System.FormatException">El contenido no puede convertirse en el tipo solicitado.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Lee asincrónicamente el elemento y descodifica el contenido de BinHex.</summary>
      <returns>Número de bytes escritos en el búfer.</returns>
      <param name="buffer">Búfer donde se va a copiar el texto resultante.Este valor no puede ser null.</param>
      <param name="index">Posición de desplazamiento en el búfer donde debe comenzar la copia del resultado.</param>
      <param name="count">Número máximo de bytes que se van a copiar en el búfer.El número real de bytes copiados se devuelve a partir de este método.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.InvalidOperationException">Un método asincrónico de <see cref="T:System.Xml.XmlReader" /> se llamó sin establecer la marca <see cref="P:System.Xml.XmlReaderSettings.Async" /> en true.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Establezca XmlReaderSettings.Async en true si desea usar métodos asincrónicos”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean">
      <summary>Lee el elemento actual y devuelve el contenido como un objeto <see cref="T:System.Boolean" />.</summary>
      <returns>Contenido de elemento como objeto <see cref="T:System.Boolean" />.</returns>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.Xml.XmlReader" /> no está situado en ningún elemento.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento actual contiene elementos secundarios.o bienEl contenido de elemento no puede convertirse en un objeto <see cref="T:System.Boolean" />.</exception>
      <exception cref="T:System.ArgumentNullException">Se llama al método con argumentos null.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean(System.String,System.String)">
      <summary>Comprueba que el nombre local especificado y el URI del espacio de nombres coinciden con los del elemento actual y, a continuación, lee este elemento y devuelve el contenido como objeto <see cref="T:System.Boolean" />.</summary>
      <returns>Contenido de elemento como objeto <see cref="T:System.Boolean" />.</returns>
      <param name="localName">Nombre local del elemento.</param>
      <param name="namespaceURI">Identificador URI de espacio de nombres del elemento.</param>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.Xml.XmlReader" /> no está situado en ningún elemento.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento actual contiene elementos secundarios.o bienEl contenido del elemento no se puede convertir en el tipo solicitado.</exception>
      <exception cref="T:System.ArgumentNullException">Se llama al método con argumentos null.</exception>
      <exception cref="T:System.ArgumentException">El nombre local y el identificador URI del espacio de nombres especificados no coinciden con los del elemento que se está leyendo.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal">
      <summary>Lee el elemento actual y devuelve el contenido como un objeto <see cref="T:System.Decimal" />.</summary>
      <returns>Contenido de elemento como objeto <see cref="T:System.Decimal" />.</returns>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.Xml.XmlReader" /> no está situado en ningún elemento.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento actual contiene elementos secundarios.o bienEl contenido de elemento no puede convertirse en <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.ArgumentNullException">Se llama al método con argumentos null.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal(System.String,System.String)">
      <summary>Comprueba que el nombre local especificado y el URI del espacio de nombres coinciden con los del elemento actual y, a continuación, lee este elemento y devuelve el contenido como objeto <see cref="T:System.Decimal" />.</summary>
      <returns>Contenido de elemento como objeto <see cref="T:System.Decimal" />.</returns>
      <param name="localName">Nombre local del elemento.</param>
      <param name="namespaceURI">Identificador URI de espacio de nombres del elemento.</param>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.Xml.XmlReader" /> no está situado en ningún elemento.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento actual contiene elementos secundarios.o bienEl contenido de elemento no puede convertirse en <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.ArgumentNullException">Se llama al método con argumentos null.</exception>
      <exception cref="T:System.ArgumentException">El nombre local y el identificador URI del espacio de nombres especificados no coinciden con los del elemento que se está leyendo.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble">
      <summary>Lee el elemento actual y devuelve el contenido como número de punto flotante de precisión doble.</summary>
      <returns>El contenido del elemento como número de punto flotante de precisión doble.</returns>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.Xml.XmlReader" /> no está situado en ningún elemento.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento actual contiene elementos secundarios.o bienEl contenido del elemento no se puede convertir en número de punto flotante de precisión doble.</exception>
      <exception cref="T:System.ArgumentNullException">Se llama al método con argumentos null.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble(System.String,System.String)">
      <summary>Comprueba que el nombre local especificado y el URI del espacio de nombres coinciden con los del elemento actual y, a continuación, lee este elemento y devuelve el contenido como número de punto flotante de precisión doble.</summary>
      <returns>El contenido del elemento como número de punto flotante de precisión doble.</returns>
      <param name="localName">Nombre local del elemento.</param>
      <param name="namespaceURI">Identificador URI de espacio de nombres del elemento.</param>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.Xml.XmlReader" /> no está situado en ningún elemento.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento actual contiene elementos secundarios.o bienEl contenido del elemento no se puede convertir en el tipo solicitado.</exception>
      <exception cref="T:System.ArgumentNullException">Se llama al método con argumentos null.</exception>
      <exception cref="T:System.ArgumentException">El nombre local y el identificador URI del espacio de nombres especificados no coinciden con los del elemento que se está leyendo.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat">
      <summary>Lee el elemento actual y devuelve el contenido como número de punto flotante de precisión sencilla.</summary>
      <returns>El contenido del elemento como número de punto flotante de precisión sencilla.</returns>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.Xml.XmlReader" /> no está situado en ningún elemento.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento actual contiene elementos secundarios.o bienEl contenido del elemento no se puede convertir en número de punto flotante de precisión sencilla.</exception>
      <exception cref="T:System.ArgumentNullException">Se llama al método con argumentos null.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat(System.String,System.String)">
      <summary>Comprueba que el nombre local especificado y el URI del espacio de nombres coinciden con los del elemento actual y, a continuación, lee este elemento y devuelve el contenido como número de punto flotante de precisión sencilla.</summary>
      <returns>El contenido del elemento como número de punto flotante de precisión sencilla.</returns>
      <param name="localName">Nombre local del elemento.</param>
      <param name="namespaceURI">Identificador URI de espacio de nombres del elemento.</param>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.Xml.XmlReader" /> no está situado en ningún elemento.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento actual contiene elementos secundarios.o bienEl contenido del elemento no se puede convertir en número de punto flotante de precisión sencilla.</exception>
      <exception cref="T:System.ArgumentNullException">Se llama al método con argumentos null.</exception>
      <exception cref="T:System.ArgumentException">El nombre local y el identificador URI del espacio de nombres especificados no coinciden con los del elemento que se está leyendo.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt">
      <summary>Lee el elemento actual y devuelve el contenido como un entero de 32 bits con signo.</summary>
      <returns>El elemento contiene un entero de 32 bits con signo.</returns>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.Xml.XmlReader" /> no está situado en ningún elemento.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento actual contiene elementos secundarios.o bienEl contenido del elemento no se puede convertir en un entero de 32 bits con signo.</exception>
      <exception cref="T:System.ArgumentNullException">Se llama al método con argumentos null.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt(System.String,System.String)">
      <summary>Comprueba que el nombre local especificado y el URI del espacio de nombres coinciden con los del elemento actual y, a continuación, lee el elemento actual y devuelve el contenido como entero de 32 bits con signo.</summary>
      <returns>El elemento contiene un entero de 32 bits con signo.</returns>
      <param name="localName">Nombre local del elemento.</param>
      <param name="namespaceURI">Identificador URI de espacio de nombres del elemento.</param>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.Xml.XmlReader" /> no está situado en ningún elemento.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento actual contiene elementos secundarios.o bienEl contenido del elemento no se puede convertir en un entero de 32 bits con signo.</exception>
      <exception cref="T:System.ArgumentNullException">Se llama al método con argumentos null.</exception>
      <exception cref="T:System.ArgumentException">El nombre local y el identificador URI del espacio de nombres especificados no coinciden con los del elemento que se está leyendo.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong">
      <summary>Lee el elemento actual y devuelve el contenido como un entero de 64 bits con signo.</summary>
      <returns>El elemento contiene un entero de 64 bits con signo.</returns>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.Xml.XmlReader" /> no está situado en ningún elemento.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento actual contiene elementos secundarios.o bienEl contenido del elemento no se puede convertir en un entero de 64 bits con signo.</exception>
      <exception cref="T:System.ArgumentNullException">Se llama al método con argumentos null.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong(System.String,System.String)">
      <summary>Comprueba que el nombre local especificado y el URI del espacio de nombres coinciden con los del elemento actual y, a continuación, lee el elemento actual y devuelve el contenido como entero de 64 bits con signo.</summary>
      <returns>El elemento contiene un entero de 64 bits con signo.</returns>
      <param name="localName">Nombre local del elemento.</param>
      <param name="namespaceURI">Identificador URI de espacio de nombres del elemento.</param>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.Xml.XmlReader" /> no está situado en ningún elemento.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento actual contiene elementos secundarios.o bienEl contenido del elemento no se puede convertir en un entero de 64 bits con signo.</exception>
      <exception cref="T:System.ArgumentNullException">Se llama al método con argumentos null.</exception>
      <exception cref="T:System.ArgumentException">El nombre local y el identificador URI del espacio de nombres especificados no coinciden con los del elemento que se está leyendo.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject">
      <summary>Lee el elemento actual y devuelve el contenido como objeto <see cref="T:System.Object" />.</summary>
      <returns>Objeto de Common Language Runtime (CLR) del tipo más adecuado al que se le ha aplicado la conversión boxing.La propiedad <see cref="P:System.Xml.XmlReader.ValueType" /> determina el tipo CLR adecuado.Si el contenido se escribe como tipo de lista, este método devuelve una matriz de objetos del tipo adecuado a los que se les ha aplicado la conversión boxing.</returns>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.Xml.XmlReader" /> no está situado en ningún elemento.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento actual contiene elementos secundarios.o bienEl contenido del elemento no se puede convertir en el tipo solicitado.</exception>
      <exception cref="T:System.ArgumentNullException">Se llama al método con argumentos null.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject(System.String,System.String)">
      <summary>Comprueba que el nombre local especificado y el URI del espacio de nombres coinciden con los del elemento actual y, a continuación, lee este elemento y devuelve el contenido como objeto <see cref="T:System.Object" />.</summary>
      <returns>Objeto de Common Language Runtime (CLR) del tipo más adecuado al que se le ha aplicado la conversión boxing.La propiedad <see cref="P:System.Xml.XmlReader.ValueType" /> determina el tipo CLR adecuado.Si el contenido se escribe como tipo de lista, este método devuelve una matriz de objetos del tipo adecuado a los que se les ha aplicado la conversión boxing.</returns>
      <param name="localName">Nombre local del elemento.</param>
      <param name="namespaceURI">Identificador URI de espacio de nombres del elemento.</param>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.Xml.XmlReader" /> no está situado en ningún elemento.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento actual contiene elementos secundarios.o bienEl contenido del elemento no se puede convertir en el tipo solicitado.</exception>
      <exception cref="T:System.ArgumentNullException">Se llama al método con argumentos null.</exception>
      <exception cref="T:System.ArgumentException">El nombre local y el identificador URI del espacio de nombres especificados no coinciden con los del elemento que se está leyendo.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObjectAsync">
      <summary>Lee asincrónicamente el elemento actual y devuelve el contenido como objeto <see cref="T:System.Object" />.</summary>
      <returns>Objeto de Common Language Runtime (CLR) del tipo más adecuado al que se le ha aplicado la conversión boxing.La propiedad <see cref="P:System.Xml.XmlReader.ValueType" /> determina el tipo CLR adecuado.Si el contenido se escribe como tipo de lista, este método devuelve una matriz de objetos del tipo adecuado a los que se les ha aplicado la conversión boxing.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.InvalidOperationException">Un método asincrónico de <see cref="T:System.Xml.XmlReader" /> se llamó sin establecer la marca <see cref="P:System.Xml.XmlReaderSettings.Async" /> en true.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Establezca XmlReaderSettings.Async en true si desea usar métodos asincrónicos”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString">
      <summary>Lee el elemento actual y devuelve el contenido como un objeto <see cref="T:System.String" />.</summary>
      <returns>Contenido de elemento como objeto <see cref="T:System.String" />.</returns>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.Xml.XmlReader" /> no está situado en ningún elemento.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento actual contiene elementos secundarios.o bienEl contenido de elemento no puede convertirse en un objeto <see cref="T:System.String" />.</exception>
      <exception cref="T:System.ArgumentNullException">Se llama al método con argumentos null.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString(System.String,System.String)">
      <summary>Comprueba que el nombre local especificado y el URI del espacio de nombres coinciden con los del elemento actual y, a continuación, lee este elemento y devuelve el contenido como objeto <see cref="T:System.String" />.</summary>
      <returns>Contenido de elemento como objeto <see cref="T:System.String" />.</returns>
      <param name="localName">Nombre local del elemento.</param>
      <param name="namespaceURI">Identificador URI de espacio de nombres del elemento.</param>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.Xml.XmlReader" /> no está situado en ningún elemento.</exception>
      <exception cref="T:System.Xml.XmlException">El elemento actual contiene elementos secundarios.o bienEl contenido de elemento no puede convertirse en un objeto <see cref="T:System.String" />.</exception>
      <exception cref="T:System.ArgumentNullException">Se llama al método con argumentos null.</exception>
      <exception cref="T:System.ArgumentException">El nombre local y el identificador URI del espacio de nombres especificados no coinciden con los del elemento que se está leyendo.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsStringAsync">
      <summary>Lee asincrónicamente el elemento actual y devuelve el contenido como un objeto <see cref="T:System.String" />.</summary>
      <returns>Contenido de elemento como objeto <see cref="T:System.String" />.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.InvalidOperationException">Un método asincrónico de <see cref="T:System.Xml.XmlReader" /> se llamó sin establecer la marca <see cref="P:System.Xml.XmlReaderSettings.Async" /> en true.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Establezca XmlReaderSettings.Async en true si desea usar métodos asincrónicos”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadEndElement">
      <summary>Comprueba si el nodo de contenido actual es una etiqueta de cierre y desplaza el lector hasta el siguiente nodo.</summary>
      <exception cref="T:System.Xml.XmlException">El nodo actual no es una etiqueta de cierre o si se encuentra XML incorrecto en el flujo de entrada.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXml">
      <summary>Cuando se invalida en una clase derivada, lee todo el contenido, incluido el marcado, como una cadena.</summary>
      <returns>Todo el contenido XML, incluido el marcado, del nodo actual.Si el nodo actual no tiene nodos secundarios, se devuelve una cadena vacía.Si el nodo actual no es un elemento ni un atributo, se devuelve una cadena vacía.</returns>
      <exception cref="T:System.Xml.XmlException">El fragmento de XML no está bien formado o se ha producido un error al analizarlo.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXmlAsync">
      <summary>De forma asincrónica lee todo el contenido, incluido el marcado, como una cadena.</summary>
      <returns>Todo el contenido XML, incluido el marcado, del nodo actual.Si el nodo actual no tiene nodos secundarios, se devuelve una cadena vacía.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.InvalidOperationException">Un método asincrónico de <see cref="T:System.Xml.XmlReader" /> se llamó sin establecer la marca <see cref="P:System.Xml.XmlReaderSettings.Async" /> en true.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Establezca XmlReaderSettings.Async en true si desea usar métodos asincrónicos”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXml">
      <summary>Cuando se invalida en una clase derivada, lee el contenido, incluido el marcado, que representa este nodo y todos sus nodos secundarios.</summary>
      <returns>Si el lector está situado en un nodo de elemento o de atributo, este método devuelve todo el contenido XML, incluido el marcado, del nodo actual y de todos sus nodos secundarios; en caso contrario, devuelve una cadena vacía.</returns>
      <exception cref="T:System.Xml.XmlException">El fragmento de XML no está bien formado o se ha producido un error al analizarlo.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXmlAsync">
      <summary>De forma asincrónica lee el contenido, incluido el marcado, que representa este nodo y todos sus elementos secundarios.</summary>
      <returns>Si el lector está situado en un nodo de elemento o de atributo, este método devuelve todo el contenido XML, incluido el marcado, del nodo actual y de todos sus nodos secundarios; en caso contrario, devuelve una cadena vacía.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.InvalidOperationException">Un método asincrónico de <see cref="T:System.Xml.XmlReader" /> se llamó sin establecer la marca <see cref="P:System.Xml.XmlReaderSettings.Async" /> en true.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Establezca XmlReaderSettings.Async en true si desea usar métodos asincrónicos”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement">
      <summary>Comprueba si el nodo actual es un elemento y hace avanzar el sistema de lectura hasta el siguiente nodo.</summary>
      <exception cref="T:System.Xml.XmlException">Se detectó XML incorrecto en el flujo de entrada.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String)">
      <summary>Comprueba si el nodo de contenido actual es un elemento con la propiedad <see cref="P:System.Xml.XmlReader.Name" /> especificada y desplaza el lector hasta el siguiente nodo.</summary>
      <param name="name">Nombre completo del elemento.</param>
      <exception cref="T:System.Xml.XmlException">Se detectó XML incorrecto en el flujo de entrada. o bien El objeto <see cref="P:System.Xml.XmlReader.Name" /> del elemento no coincide con el <paramref name="name" /> especificado.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String,System.String)">
      <summary>Comprueba si el nodo de contenido actual es un elemento con las propiedades <see cref="P:System.Xml.XmlReader.LocalName" /> y <see cref="P:System.Xml.XmlReader.NamespaceURI" /> especificadas y desplaza el lector hasta el siguiente nodo.</summary>
      <param name="localname">Nombre local del elemento.</param>
      <param name="ns">Identificador URI de espacio de nombres del elemento.</param>
      <exception cref="T:System.Xml.XmlException">Se detectó XML incorrecto en el flujo de entrada.o bienLas propiedades <see cref="P:System.Xml.XmlReader.LocalName" /> y <see cref="P:System.Xml.XmlReader.NamespaceURI" /> del elemento encontrado no coinciden con los argumentos especificados.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ReadState">
      <summary>Cuando se invalida en una clase derivada, obtiene el estado del lector.</summary>
      <returns>Uno de los valores de enumeración que especifica el estado del lector.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadSubtree">
      <summary>Devuelve una nueva instancia de XmlReader que se puede utilizar para leer el nodo actual y todos sus descendientes.</summary>
      <returns>Una nueva instancia de lector XML se establece en <see cref="F:System.Xml.ReadState.Initial" />.Llamar a la <see cref="M:System.Xml.XmlReader.Read" /> método coloca el nuevo sistema de lectura en el nodo que era actual antes de llamar a la <see cref="M:System.Xml.XmlReader.ReadSubtree" /> método.</returns>
      <exception cref="T:System.InvalidOperationException">El lector de XML no está situado en un elemento cuando se llama a este método.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String)">
      <summary>Hace avanzar el objeto <see cref="T:System.Xml.XmlReader" /> hasta al siguiente elemento descendiente con el nombre completo especificado.</summary>
      <returns>Es true si se encuentra un elemento descendiente; en caso contrario, es false.Si no se encuentra ningún elemento secundario relacionado, el objeto <see cref="T:System.Xml.XmlReader" /> se coloca en la etiqueta de cierre (<see cref="P:System.Xml.XmlReader.NodeType" /> es XmlNodeType.EndElement) del elemento.Si <see cref="T:System.Xml.XmlReader" /> no está en un elemento cuando se llama a <see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String)" />, este método devuelve false y la posición de <see cref="T:System.Xml.XmlReader" /> no cambia.</returns>
      <param name="name">Nombre completo del elemento al que se desea desplazar.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.ArgumentException">El parámetro es una cadena vacía.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)">
      <summary>Hace avanzar el objeto <see cref="T:System.Xml.XmlReader" /> hasta el siguiente elemento descendiente que tenga el URI de espacio de nombres y el nombre local especificados.</summary>
      <returns>Es true si se encuentra un elemento descendiente; en caso contrario, es false.Si no se encuentra ningún elemento secundario relacionado, el objeto <see cref="T:System.Xml.XmlReader" /> se coloca en la etiqueta de cierre (<see cref="P:System.Xml.XmlReader.NodeType" /> es XmlNodeType.EndElement) del elemento.Si <see cref="T:System.Xml.XmlReader" /> no está en un elemento cuando se llama a <see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)" />, este método devuelve false y la posición de <see cref="T:System.Xml.XmlReader" /> no cambia.</returns>
      <param name="localName">Nombre local del elemento al que se desea desplazar.</param>
      <param name="namespaceURI">URI del espacio de nombres del elemento al que se desea desplazar.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.ArgumentNullException">Ambos valores de parámetro son null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String)">
      <summary>Lee hasta que encuentra un elemento con el nombre completo especificado.</summary>
      <returns>Es true si se encuentra un elemento coincidente; de lo contrario, es false y el objeto <see cref="T:System.Xml.XmlReader" /> está en un estado de final de archivo.</returns>
      <param name="name">Nombre completo del elemento.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.ArgumentException">El parámetro es una cadena vacía.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String,System.String)">
      <summary>Lee hasta que encuentra un elemento con el nombre local y el URI de espacio de nombres especificados.</summary>
      <returns>Es true si se encuentra un elemento coincidente; de lo contrario, es false y el objeto <see cref="T:System.Xml.XmlReader" /> está en un estado de final de archivo.</returns>
      <param name="localName">Nombre local del elemento.</param>
      <param name="namespaceURI">Identificador URI de espacio de nombres del elemento.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.ArgumentNullException">Ambos valores de parámetro son null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String)">
      <summary>Hace avanzar el objeto XmlReader hasta al siguiente elemento relacionado con el nombre completo especificado.</summary>
      <returns>Es true si se encuentra un elemento relacionado; en caso contrario, es false.Si no se encuentra ningún elemento relacionado, el objeto XmlReader se coloca en la etiqueta de cierre (<see cref="P:System.Xml.XmlReader.NodeType" /> es XmlNodeType.EndElement) del elemento principal.</returns>
      <param name="name">Nombre completo del elemento relacionado al que se desea desplazar.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.ArgumentException">El parámetro es una cadena vacía.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String,System.String)">
      <summary>Hace avanzar el objeto XmlReader hasta el siguiente elemento relacionado que tenga el URI del espacio de nombres y el nombre local especificados.</summary>
      <returns>Es true si se encuentra un elemento relacionado; en caso contrario, es false.Si no se encuentra ningún elemento relacionado, el objeto XmlReader se coloca en la etiqueta de cierre (<see cref="P:System.Xml.XmlReader.NodeType" /> es XmlNodeType.EndElement) del elemento principal.</returns>
      <param name="localName">Nombre local del elemento relacionado al que se desea desplazar.</param>
      <param name="namespaceURI">URI del espacio de nombres del elemento relacionado al que se desea desplazar.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.ArgumentNullException">Ambos valores de parámetro son null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)">
      <summary>Lee grandes flujos de texto incrustados en un documento XML.</summary>
      <returns>Número de caracteres leídos en el búfer.Si no hay más contenido de texto, se devuelve el valor cero.</returns>
      <param name="buffer">Matriz de caracteres que sirve como búfer en el que se escribe el contenido de texto.Este valor no puede ser null.</param>
      <param name="index">Desplazamiento en el búfer en el que <see cref="T:System.Xml.XmlReader" /> puede empezar a copiar los resultados.</param>
      <param name="count">Número máximo de caracteres que se van a copiar en el búfer.El número real de caracteres copiados se devuelve desde este método.</param>
      <exception cref="T:System.InvalidOperationException">El nodo actual no tiene ningún valor (<see cref="P:System.Xml.XmlReader.HasValue" /> es false).</exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="buffer" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El índice del búfer (index) o la suma del índice y el recuento (index + count) es mayor que el tamaño de búfer asignado.</exception>
      <exception cref="T:System.NotSupportedException">La implementación de <see cref="T:System.Xml.XmlReader" /> no admite este método.</exception>
      <exception cref="T:System.Xml.XmlException">El formato de los datos XML no es correcto.</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunkAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Lee asincrónicamente grandes flujos de texto incrustados en un documento XML.</summary>
      <returns>Número de caracteres leídos en el búfer.Si no hay más contenido de texto, se devuelve el valor cero.</returns>
      <param name="buffer">Matriz de caracteres que sirve como búfer en el que se escribe el contenido de texto.Este valor no puede ser null.</param>
      <param name="index">Desplazamiento en el búfer en el que <see cref="T:System.Xml.XmlReader" /> puede empezar a copiar los resultados.</param>
      <param name="count">Número máximo de caracteres que se van a copiar en el búfer.El número real de caracteres copiados se devuelve desde este método.</param>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.InvalidOperationException">Un método asincrónico de <see cref="T:System.Xml.XmlReader" /> se llamó sin establecer la marca <see cref="P:System.Xml.XmlReaderSettings.Async" /> en true.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Establezca XmlReaderSettings.Async en true si desea usar métodos asincrónicos”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ResolveEntity">
      <summary>Cuando se invalida en una clase derivada, resuelve la referencia a entidad para los nodos EntityReference.</summary>
      <exception cref="T:System.InvalidOperationException">El lector no está situado en un nodo EntityReference; esta implementación del lector no puede resolver entidades (<see cref="P:System.Xml.XmlReader.CanResolveEntity" /> devuelve false).</exception>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Settings">
      <summary>Obtiene el objeto <see cref="T:System.Xml.XmlReaderSettings" /> que se usa para crear esta instancia de <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Objeto <see cref="T:System.Xml.XmlReaderSettings" /> utilizado para crear esta instancia del lector.Si este lector no se creó utilizando el método <see cref="Overload:System.Xml.XmlReader.Create" />, esta propiedad devuelve null.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Skip">
      <summary>Omite los nodos secundarios del nodo actual.</summary>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.SkipAsync">
      <summary>Omite de forma asincrónica los elementos secundarios del valor del nodo actual.</summary>
      <returns>Nodo actual.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
      <exception cref="T:System.InvalidOperationException">Un método asincrónico de <see cref="T:System.Xml.XmlReader" /> se llamó sin establecer la marca <see cref="P:System.Xml.XmlReaderSettings.Async" /> en true.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Establezca XmlReaderSettings.Async en true si desea usar métodos asincrónicos”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Value">
      <summary>Cuando se invalida en una clase derivada, obtiene el valor de texto del nodo actual.</summary>
      <returns>El valor devuelto depende de la propiedad <see cref="P:System.Xml.XmlReader.NodeType" /> del nodo.En la siguiente tabla se recogen los tipos de nodo que tienen un valor para devolver.Todos los demás tipos de nodo devuelven String.Empty.Tipo de nodo Valor AttributeValor del atributo. CDATAContenido de la sección CDATA. CommentContenido del comentario. DocumentTypeSubconjunto interno. ProcessingInstructionTodo el contenido, salvo el destino. SignificantWhitespaceEspacio en blanco entre marcas en un modelo de contenido mixto. TextEl contenido del nodo de texto. WhitespaceEspacio en blanco entre marcas. XmlDeclarationContenido de la declaración. </returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ValueType">
      <summary>Obtiene el tipo de Common Language Runtime (CLR) del nodo actual.</summary>
      <returns>Tipo de CLR correspondiente al valor con tipo del nodo.De manera predeterminada, es System.String.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlLang">
      <summary>Cuando se invalida en una clase derivada, obtiene el ámbito de xml:lang actual.</summary>
      <returns>Ámbito de xml:lang actual.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlSpace">
      <summary>Cuando se invalida en una clase derivada, obtiene el ámbito de xml:space actual.</summary>
      <returns>Uno de los valores de <see cref="T:System.Xml.XmlSpace" />.Si no existe ningún ámbito de xml:space, el valor predeterminado de esta propiedad será XmlSpace.None.</returns>
      <exception cref="T:System.InvalidOperationException">Un método de <see cref="T:System.Xml.XmlReader" /> se llamó antes de que una operación asincrónica anterior finalizara.En este caso, se produce una <see cref="T:System.InvalidOperationException" /> con el mensaje “Ya hay en curso una operación asincrónica”.</exception>
    </member>
    <member name="T:System.Xml.XmlReaderSettings">
      <summary>Especifica un conjunto de características compatibles en el objeto <see cref="T:System.Xml.XmlReader" /> creado mediante el método <see cref="Overload:System.Xml.XmlReader.Create" />. </summary>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Xml.XmlReaderSettings" />.</summary>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.Async">
      <summary>Obtiene o establece si los métodos asincrónicos de <see cref="T:System.Xml.XmlReader" /> se pueden usar en una instancia determinada de <see cref="T:System.Xml.XmlReader" /> .</summary>
      <returns>true si se pueden usar métodos asincrónicos; si no, false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CheckCharacters">
      <summary>Obtiene o establece un valor que indica si se va a realizar la comprobación de caracteres.</summary>
      <returns>Es true si se va a realizar la comprobación de caracteres; en caso contrario, es false.De manera predeterminada, es true.NotaSi <see cref="T:System.Xml.XmlReader" /> procesa datos de texto, siempre comprueba que los nombres XML y el contenido de texto son válidos, independientemente de la configuración de la propiedad.Al establecer la propiedad <see cref="P:System.Xml.XmlReaderSettings.CheckCharacters" /> en false, se desactiva la comprobación de caracteres en las referencias a entidades de caracteres.</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Clone">
      <summary>Crea una copia de la instancia de <see cref="T:System.Xml.XmlReaderSettings" />.</summary>
      <returns>Objeto <see cref="T:System.Xml.XmlReaderSettings" /> clonado.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CloseInput">
      <summary>Obtiene o establece un valor que indica si se debe cerrar la secuencia o el objeto <see cref="T:System.IO.TextReader" /> subyacente al cerrar el lector.</summary>
      <returns>Es true para cerrar la secuencia o <see cref="T:System.IO.TextReader" /> subyacente al cerrar el lector; en caso contrario, es false.De manera predeterminada, es false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.ConformanceLevel">
      <summary>Obtiene o establece el nivel de conformidad que cumplirá <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Uno de los valores de enumeración que especifica el nivel de cumplimiento que se aplicará el lector XML.De manera predeterminada, es <see cref="F:System.Xml.ConformanceLevel.Document" />.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.DtdProcessing">
      <summary>Obtiene o establece un valor que determine el procesamiento de DTD.</summary>
      <returns>Uno de los valores de enumeración que determina el procesamiento de DTD.De manera predeterminada, es <see cref="F:System.Xml.DtdProcessing.Prohibit" />.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreComments">
      <summary>Obtiene o establece un valor que indica si se van a omitir los comentarios.</summary>
      <returns>Es true para omitir los comentarios; en caso contrario, es false.De manera predeterminada, es false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreProcessingInstructions">
      <summary>Obtiene o establece un valor que indica si se van a omitir las instrucciones de procesamiento.</summary>
      <returns>Es true para omitir las instrucciones de procesamiento; en caso contrario, es false.De manera predeterminada, es false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreWhitespace">
      <summary>Obtiene o establece un valor que indica si se va a omitir el espacio en blanco no significativo.</summary>
      <returns>Es true para omitir el espacio en blanco; en caso contrario, es false.De manera predeterminada, es false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LineNumberOffset">
      <summary>Obtiene o establece el desplazamiento del número de línea del objeto <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Desplazamiento del número de línea.El valor predeterminado es 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LinePositionOffset">
      <summary>Obtiene o establece el desplazamiento de la posición de línea del objeto <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Desplazamiento de la posición de la línea.El valor predeterminado es 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersFromEntities">
      <summary>Obtiene o establece un valor que indica el número máximo de caracteres permitido en un documento que resulta de expandir las entidades.</summary>
      <returns>El número máximo de caracteres permitido de las entidades expandidas.El valor predeterminado es 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersInDocument">
      <summary>Obtiene o establece un valor que indica el número máximo permitido de caracteres en un documento XML.Un valor cero (0) significa que no existe ningún límite en el tamaño del documento XML.Un valor distinto de cero especifica el tamaño máximo, en caracteres.</summary>
      <returns>El número máximo de caracteres permitido en un documento XML.El valor predeterminado es 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.NameTable">
      <summary>Obtiene o establece el objeto <see cref="T:System.Xml.XmlNameTable" /> utilizado para las comparaciones de cadenas subdivididas.</summary>
      <returns>Objeto <see cref="T:System.Xml.XmlNameTable" /> que almacena todas las cadenas subdivididas que utilizan todas las instancias del objeto <see cref="T:System.Xml.XmlReader" /> creadas mediante este objeto <see cref="T:System.Xml.XmlReaderSettings" />.De manera predeterminada, es null.La instancia de <see cref="T:System.Xml.XmlReader" /> creada utilizará un nuevo objeto <see cref="T:System.Xml.NameTable" /> vacío si este valor es null.</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Reset">
      <summary>Restablece los miembros de la clase de configuración a sus valores predeterminados.</summary>
    </member>
    <member name="T:System.Xml.XmlSpace">
      <summary>Especifica el ámbito de xml:space actual.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Default">
      <summary>El ámbito de xml:space equivale a default.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.None">
      <summary>Sin ámbito de xml:space.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Preserve">
      <summary>El ámbito de xml:space equivale a preserve.</summary>
    </member>
    <member name="T:System.Xml.XmlWriter">
      <summary>Representa un sistema de escritura que constituye una manera rápida, no almacenada en caché y de solo avance para generar flujos o archivos que contienen datos XML.</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Xml.XmlWriter" />.</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream)">
      <summary>Crea una nueva instancia de <see cref="T:System.Xml.XmlWriter" /> mediante el flujo especificado.</summary>
      <returns>Un objeto <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">Flujo en el que desea escribir.<see cref="T:System.Xml.XmlWriter" /> escribe la sintaxis de texto de XML 1.0 y la anexa al flujo especificado.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream,System.Xml.XmlWriterSettings)">
      <summary>Crea una nueva instancia de <see cref="T:System.Xml.XmlWriter" /> mediante el flujo y el objeto <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
      <returns>Un objeto <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">Flujo en el que desea escribir.<see cref="T:System.Xml.XmlWriter" /> escribe la sintaxis de texto de XML 1.0 y la anexa al flujo especificado.</param>
      <param name="settings">Objeto <see cref="T:System.Xml.XmlWriterSettings" /> que se usa para configurar la nueva instancia de <see cref="T:System.Xml.XmlWriter" />.Si es null, se usa un objeto <see cref="T:System.Xml.XmlWriterSettings" /> con la configuración predeterminada.Si <see cref="T:System.Xml.XmlWriter" /> se está usando con el método <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />, debe usar la propiedad <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> para obtener un objeto <see cref="T:System.Xml.XmlWriterSettings" /> con la configuración correcta.Con ello se garantiza que el objeto <see cref="T:System.Xml.XmlWriter" /> creado tenga la configuración de resultados correcta.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter)">
      <summary>Crea una nueva instancia de <see cref="T:System.Xml.XmlWriter" /> mediante el objeto <see cref="T:System.IO.TextWriter" /> especificado.</summary>
      <returns>Un objeto <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">
        <see cref="T:System.IO.TextWriter" /> en el que se desea escribir.<see cref="T:System.Xml.XmlWriter" /> escribe la sintaxis de texto de XML 1.0 y la anexa al <see cref="T:System.IO.TextWriter" /> especificado.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter,System.Xml.XmlWriterSettings)">
      <summary>Crea una nueva instancia de <see cref="T:System.Xml.XmlWriter" /> mediante los objetos <see cref="T:System.IO.TextWriter" /> y <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
      <returns>Un objeto <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">
        <see cref="T:System.IO.TextWriter" /> en el que desea escribir.<see cref="T:System.Xml.XmlWriter" /> escribe la sintaxis de texto de XML 1.0 y la anexa al <see cref="T:System.IO.TextWriter" /> especificado.</param>
      <param name="settings">Objeto <see cref="T:System.Xml.XmlWriterSettings" /> que se usa para configurar la nueva instancia de <see cref="T:System.Xml.XmlWriter" />.Si es null, se usa un objeto <see cref="T:System.Xml.XmlWriterSettings" /> con la configuración predeterminada.Si <see cref="T:System.Xml.XmlWriter" /> se está usando con el método <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />, debe usar la propiedad <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> para obtener un objeto <see cref="T:System.Xml.XmlWriterSettings" /> con la configuración correcta.Con ello se garantiza que el objeto <see cref="T:System.Xml.XmlWriter" /> creado tenga la configuración de resultados correcta.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder)">
      <summary>Crea una nueva instancia de <see cref="T:System.Xml.XmlWriter" /> mediante el objeto <see cref="T:System.Text.StringBuilder" /> especificado.</summary>
      <returns>Un objeto <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">
        <see cref="T:System.Text.StringBuilder" /> en el que se va a escribir.El contenido que escribe <see cref="T:System.Xml.XmlWriter" /> se anexa a <see cref="T:System.Text.StringBuilder" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder,System.Xml.XmlWriterSettings)">
      <summary>Crea una nueva instancia de <see cref="T:System.Xml.XmlWriter" /> mediante los objetos <see cref="T:System.Text.StringBuilder" /> y <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
      <returns>Un objeto <see cref="T:System.Xml.XmlWriter" />.</returns>
      <param name="output">
        <see cref="T:System.Text.StringBuilder" /> en el que se va a escribir.El contenido que escribe <see cref="T:System.Xml.XmlWriter" /> se anexa a <see cref="T:System.Text.StringBuilder" />.</param>
      <param name="settings">Objeto <see cref="T:System.Xml.XmlWriterSettings" /> que se usa para configurar la nueva instancia de <see cref="T:System.Xml.XmlWriter" />.Si es null, se usa un objeto <see cref="T:System.Xml.XmlWriterSettings" /> con la configuración predeterminada.Si <see cref="T:System.Xml.XmlWriter" /> se está usando con el método <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />, debe usar la propiedad <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> para obtener un objeto <see cref="T:System.Xml.XmlWriterSettings" /> con la configuración correcta.Con ello se garantiza que el objeto <see cref="T:System.Xml.XmlWriter" /> creado tenga la configuración de resultados correcta.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter)">
      <summary>Crea una nueva instancia de <see cref="T:System.Xml.XmlWriter" /> mediante el objeto <see cref="T:System.Xml.XmlWriter" /> especificado.</summary>
      <returns>Objeto <see cref="T:System.Xml.XmlWriter" /> que contiene el objeto <see cref="T:System.Xml.XmlWriter" /> especificado.</returns>
      <param name="output">Objeto <see cref="T:System.Xml.XmlWriter" /> que desea usar como sistema de escritura subyacente.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter,System.Xml.XmlWriterSettings)">
      <summary>Crea una nueva instancia de <see cref="T:System.Xml.XmlWriter" /> mediante los objetos <see cref="T:System.Xml.XmlWriter" /> y <see cref="T:System.Xml.XmlWriterSettings" /> especificados.</summary>
      <returns>Objeto <see cref="T:System.Xml.XmlWriter" /> que contiene el objeto <see cref="T:System.Xml.XmlWriter" /> especificado.</returns>
      <param name="output">Objeto <see cref="T:System.Xml.XmlWriter" /> que desea usar como sistema de escritura subyacente.</param>
      <param name="settings">Objeto <see cref="T:System.Xml.XmlWriterSettings" /> que se usa para configurar la nueva instancia de <see cref="T:System.Xml.XmlWriter" />.Si es null, se usa un objeto <see cref="T:System.Xml.XmlWriterSettings" /> con la configuración predeterminada.Si <see cref="T:System.Xml.XmlWriter" /> se está usando con el método <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />, debe usar la propiedad <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> para obtener un objeto <see cref="T:System.Xml.XmlWriterSettings" /> con la configuración correcta.Con ello se garantiza que el objeto <see cref="T:System.Xml.XmlWriter" /> creado tenga la configuración de resultados correcta.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose">
      <summary>Libera todos los recursos usados por la instancia actual de la clase <see cref="T:System.Xml.XmlWriter" />.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que usa <see cref="T:System.Xml.XmlWriter" /> y libera los recursos administrados de forma opcional.</summary>
      <param name="disposing">Es true para liberar recursos tanto administrados como no administrados; es false para liberar únicamente recursos no administrados.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Flush">
      <summary>Cuando se invalida en una clase derivada, vuelca el contenido del búfer en los flujos subyacentes y también vuelca el flujo subyacente.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.FlushAsync">
      <summary>Vuelca asincrónicamente el contenido del búfer en los flujos subyacentes y también vuelca el flujo subyacente.</summary>
      <returns>Tarea que representa la operación Flush asincrónica.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.LookupPrefix(System.String)">
      <summary>Cuando se invalida en una clase derivada, devuelve el prefijo más próximo definido en el ámbito de espacio de nombres actual correspondiente al identificador URI de espacio de nombres.</summary>
      <returns>Prefijo coincidente o null si no se encuentra ningún identificador URI de espacio de nombres coincidente en el ámbito actual.</returns>
      <param name="ns">Identificador URI de espacio de nombres cuyo prefijo se desea buscar.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="ns" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.Settings">
      <summary>Obtiene el objeto <see cref="T:System.Xml.XmlWriterSettings" /> que se usa para crear esta instancia de <see cref="T:System.Xml.XmlWriter" />.</summary>
      <returns>Objeto <see cref="T:System.Xml.XmlWriterSettings" /> usado para crear esta instancia del sistema de escritura.Si este sistema de escritura no se creó usando el método <see cref="Overload:System.Xml.XmlWriter.Create" />, esta propiedad devuelve null.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributes(System.Xml.XmlReader,System.Boolean)">
      <summary>Cuando se invalida en una clase derivada, escribe todos los atributos que se encuentran en la posición actual en <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="reader">XmlReader del que se van a copiar los atributos.</param>
      <param name="defattr">Es true para copiar los atributos predeterminados de XmlReader; en caso contrario, es false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null. </exception>
      <exception cref="T:System.Xml.XmlException">The reader is not positioned on an element, attribute or XmlDeclaration node. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributesAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>De forma asincrónica escribe todos los atributos encontrados en la posición actual en <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>Tarea que representa la operación WriteAttributes asincrónica.</returns>
      <param name="reader">XmlReader del que se van a copiar los atributos.</param>
      <param name="defattr">Es true para copiar los atributos predeterminados de XmlReader; en caso contrario, es false.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String)">
      <summary>Cuando se invalida en una clase derivada, escribe el atributo con el valor y nombre local especificados.</summary>
      <param name="localName">Nombre local del atributo.</param>
      <param name="value">El valor del atributo.</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String)">
      <summary>Cuando se invalida en una clase derivada, escribe un atributo con el valor, nombre local e identificador URI del espacio de nombres especificados.</summary>
      <param name="localName">Nombre local del atributo.</param>
      <param name="ns">Identificador URI de espacio de nombres que se va asociar al atributo.</param>
      <param name="value">El valor del atributo.</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String,System.String)">
      <summary>Cuando se invalida en una clase derivada, escribe el atributo con el prefijo, el nombre local, el identificador URI de espacio de nombres y el valor especificados.</summary>
      <param name="prefix">Prefijo de espacio de nombres del atributo.</param>
      <param name="localName">Nombre local del atributo.</param>
      <param name="ns">URI de espacio de nombres del atributo.</param>
      <param name="value">El valor del atributo.</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.Xml.XmlException">The <paramref name="localName" /> or <paramref name="ns" /> is null. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeStringAsync(System.String,System.String,System.String,System.String)">
      <summary>Escribe asincrónicamente un atributo con el prefijo, el nombre local, el URI del espacio de nombres y el valor especificados.</summary>
      <returns>Tarea que representa la operación WriteAttributeString asincrónica.</returns>
      <param name="prefix">Prefijo de espacio de nombres del atributo.</param>
      <param name="localName">Nombre local del atributo.</param>
      <param name="ns">URI de espacio de nombres del atributo.</param>
      <param name="value">El valor del atributo.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>Cuando se invalida en una clase derivada, codifica los bytes binarios especificados en Base64 y escribe el texto resultante.</summary>
      <param name="buffer">Matriz de bytes que se va a codificar.</param>
      <param name="index">Posición en el búfer que indica el inicio de los bytes que se van a escribir.</param>
      <param name="count">Número de bytes que se van a escribir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Codifica asincrónicamente los bytes binarios especificados en Base64 y escribe el texto resultante.</summary>
      <returns>Tarea que representa la operación WriteBase64 asincrónica.</returns>
      <param name="buffer">Matriz de bytes que se va a codificar.</param>
      <param name="index">Posición en el búfer que indica el inicio de los bytes que se van a escribir.</param>
      <param name="count">Número de bytes que se van a escribir.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>Cuando se invalida en una clase derivada, codifica los bytes binarios especificados en BinHex y escribe el texto resultante.</summary>
      <param name="buffer">Matriz de bytes que se va a codificar.</param>
      <param name="index">Posición en el búfer que indica el inicio de los bytes que se van a escribir.</param>
      <param name="count">Número de bytes que se van a escribir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Codifica asincrónicamente los bytes binarios especificados como BinHex y escribe el texto resultante.</summary>
      <returns>Tarea que representa la operación WriteBinHex asincrónica.</returns>
      <param name="buffer">Matriz de bytes que se va a codificar.</param>
      <param name="index">Posición en el búfer que indica el inicio de los bytes que se van a escribir.</param>
      <param name="count">Número de bytes que se van a escribir.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCData(System.String)">
      <summary>Cuando se invalida en una clase derivada, escribe un bloque &lt;![CDATA[...]]&gt; que contiene el texto especificado.</summary>
      <param name="text">Texto que se va a colocar en el bloque CDATA.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCDataAsync(System.String)">
      <summary>Escribe asincrónicamente un bloque &lt;![CDATA[...]]&gt; que contiene el texto especificado.</summary>
      <returns>Tarea que representa la operación WriteCData asincrónica.</returns>
      <param name="text">Texto que se va a colocar en el bloque CDATA.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntity(System.Char)">
      <summary>Cuando se invalida en una clase derivada, impone la generación de una entidad de caracteres para el valor de carácter Unicode especificado.</summary>
      <param name="ch">Carácter Unicode para el que se va a generar una entidad de caracteres.</param>
      <exception cref="T:System.ArgumentException">The character is in the surrogate pair character range, 0xd800 - 0xdfff.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntityAsync(System.Char)">
      <summary>Impone asincrónicamente la generación de una entidad de caracteres para el valor de carácter Unicode especificado.</summary>
      <returns>Tarea que representa la operación WriteCharEntity asincrónica.</returns>
      <param name="ch">Carácter Unicode para el que se va a generar una entidad de caracteres.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteChars(System.Char[],System.Int32,System.Int32)">
      <summary>Cuando se invalida en una clase derivada, escribe texto en un búfer cada vez.</summary>
      <param name="buffer">Matriz de caracteres que contiene el texto que se va a escribir.</param>
      <param name="index">Posición en el búfer que indica el inicio del texto que se va a escribir.</param>
      <param name="count">Número de caracteres que se van a escribir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />; the call results in surrogate pair characters being split or an invalid surrogate pair being written.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="buffer" /> parameter value is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharsAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Escribe asincrónicamente texto en un búfer cada vez.</summary>
      <returns>Tarea que representa la operación WriteChars asincrónica.</returns>
      <param name="buffer">Matriz de caracteres que contiene el texto que se va a escribir.</param>
      <param name="index">Posición en el búfer que indica el inicio del texto que se va a escribir.</param>
      <param name="count">Número de caracteres que se van a escribir.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteComment(System.String)">
      <summary>Cuando se invalida en una clase derivada, escribe un comentario &lt;!--...--&gt; que contiene el texto especificado.</summary>
      <param name="text">Texto que se va a colocar en el comentario.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well-formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCommentAsync(System.String)">
      <summary>De forma asincrónica escribe un comentario &lt;!--...--&gt; que contiene el texto especificado.</summary>
      <returns>Tarea que representa la operación WriteComment asincrónica.</returns>
      <param name="text">Texto que se va a colocar en el comentario.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocType(System.String,System.String,System.String,System.String)">
      <summary>Cuando se invalida en una clase derivada, escribe la declaración DOCTYPE con el nombre y atributos opcionales especificados.</summary>
      <param name="name">Nombre de DOCTYPE.No puede estar vacío.</param>
      <param name="pubid">Si su valor no es nulo, también se escribe PUBLIC "pubid" "sysid", donde <paramref name="pubid" /> y <paramref name="sysid" /> se reemplazan por el valor de los argumentos especificados.</param>
      <param name="sysid">Si el valor de <paramref name="pubid" /> es null y el de <paramref name="sysid" /> no lo es, se escribe System "sysid", donde <paramref name="sysid" /> se reemplaza por el valor de este argumento.</param>
      <param name="subset">En caso de un valor no nulo, se escribe [subset], donde subset se reemplaza por el valor de este argumento.</param>
      <exception cref="T:System.InvalidOperationException">This method was called outside the prolog (after the root element). </exception>
      <exception cref="T:System.ArgumentException">The value for <paramref name="name" /> would result in invalid XML.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocTypeAsync(System.String,System.String,System.String,System.String)">
      <summary>Escribe asincrónicamente la declaración DOCTYPE con el nombre y los atributos opcionales especificados.</summary>
      <returns>Tarea que representa la operación WriteDocType asincrónica.</returns>
      <param name="name">Nombre de DOCTYPE.No puede estar vacío.</param>
      <param name="pubid">Si su valor no es nulo, también se escribe PUBLIC "pubid" "sysid", donde <paramref name="pubid" /> y <paramref name="sysid" /> se reemplazan por el valor de los argumentos especificados.</param>
      <param name="sysid">Si el valor de <paramref name="pubid" /> es null y el de <paramref name="sysid" /> no lo es, se escribe System "sysid", donde <paramref name="sysid" /> se reemplaza por el valor de este argumento.</param>
      <param name="subset">En caso de un valor no nulo, se escribe [subset], donde subset se reemplaza por el valor de este argumento.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String)">
      <summary>Escribe un elemento con el nombre local y el valor especificados.</summary>
      <param name="localName">Nombre local del elemento.</param>
      <param name="value">Valor del elemento.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String)">
      <summary>Escribe un elemento con el nombre local especificado, el URI de espacio de nombres y el valor.</summary>
      <param name="localName">Nombre local del elemento.</param>
      <param name="ns">Identificador URI de espacio de nombres que se va a asociar al elemento.</param>
      <param name="value">Valor del elemento.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String,System.String)">
      <summary>Escribe un elemento con el prefijo, nombre local, el URI de espacio de nombres y el valor especificados.</summary>
      <param name="prefix">Prefijo del elemento.</param>
      <param name="localName">Nombre local del elemento.</param>
      <param name="ns">Identificador URI de espacio de nombres del elemento.</param>
      <param name="value">Valor del elemento.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementStringAsync(System.String,System.String,System.String,System.String)">
      <summary>Escribe asincrónicamente un elemento con el nombre local, el URI de espacio de nombres, el valor y el prefijo especificados.</summary>
      <returns>Tarea que representa la operación WriteElementString asincrónica.</returns>
      <param name="prefix">Prefijo del elemento.</param>
      <param name="localName">Nombre local del elemento.</param>
      <param name="ns">Identificador URI de espacio de nombres del elemento.</param>
      <param name="value">Valor del elemento.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttribute">
      <summary>Cuando se invalida en una clase derivada, cierra la llamada a <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" /> anterior.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttributeAsync">
      <summary>Cierra de forma asincrónica la llamada anterior al método <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" />.</summary>
      <returns>Tarea que representa la operación WriteEndAttribute asincrónica.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocument">
      <summary>Cuando se invalida en una clase derivada, cierra todos los elementos o atributos abiertos y vuelve a colocar el sistema de escritura en el estado de inicio.</summary>
      <exception cref="T:System.ArgumentException">The XML document is invalid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocumentAsync">
      <summary>Cierra asincrónicamente todos los elementos o atributos abiertos y coloca de nuevo el sistema de escritura en el estado de inicio.</summary>
      <returns>Tarea que representa la operación WriteEndDocument asincrónica.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElement">
      <summary>Cuando se invalida en una clase derivada, cierra un elemento y extrae el ámbito de espacio de nombres correspondiente.</summary>
      <exception cref="T:System.InvalidOperationException">This results in an invalid XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElementAsync">
      <summary>Cierra asincrónicamente un elemento y extrae el correspondiente ámbito de espacio de nombres.</summary>
      <returns>Tarea que representa la operación WriteEndElement asincrónica.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRef(System.String)">
      <summary>Cuando se invalida en una clase derivada, escribe una referencia a entidad de la siguiente forma: &amp;name;.</summary>
      <param name="name">Nombre de la referencia a entidad.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRefAsync(System.String)">
      <summary>Escribe asincrónicamente una referencia a entidad de la siguiente manera: &amp;name;.</summary>
      <returns>Tarea que representa la operación WriteEntityRef asincrónica.</returns>
      <param name="name">Nombre de la referencia a entidad.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElement">
      <summary>Cuando se invalida en una clase derivada, cierra un elemento y extrae el ámbito de espacio de nombres correspondiente.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElementAsync">
      <summary>Cierra asincrónicamente un elemento y extrae el correspondiente ámbito de espacio de nombres.</summary>
      <returns>Tarea que representa la operación WriteFullEndElement asincrónica.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteName(System.String)">
      <summary>Cuando se invalida en una clase derivada, escribe el nombre especificado, comprobando que sea un nombre válido de acuerdo con la recomendación relativa a XML 1.0 del Consorcio W3C (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name).</summary>
      <param name="name">Nombre que se va a escribir.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid XML name; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNameAsync(System.String)">
      <summary>Escribe asincrónicamente el nombre especificado, asegurando que se trata de un nombre válido de acuerdo con la recomendación relativa a XML 1.0 del Consorcio W3C (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name).</summary>
      <returns>Tarea que representa la operación WriteName asincrónica.</returns>
      <param name="name">Nombre que se va a escribir.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmToken(System.String)">
      <summary>Cuando se invalida en una clase derivada, escribe el nombre especificado, comprobando que sea un NmToken válido de acuerdo con la recomendación relativa a XML 1.0 del Consorcio W3C (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name).</summary>
      <param name="name">Nombre que se va a escribir.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid NmToken; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmTokenAsync(System.String)">
      <summary>Escribe asincrónicamente el nombre especificado, asegurando que se trata de un NmToken válido de acuerdo con la recomendación relativa a XML 1.0 del Consorcio W3C (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name).</summary>
      <returns>Tarea que representa la operación WriteNmToken asincrónica.</returns>
      <param name="name">Nombre que se va a escribir.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNode(System.Xml.XmlReader,System.Boolean)">
      <summary>Cuando se invalida en una clase derivada, copia todo el contenido del lector en el sistema de escritura y desplaza el lector al inicio del siguiente nodo relacionado.</summary>
      <param name="reader">
        <see cref="T:System.Xml.XmlReader" /> desde el que se va a leer.</param>
      <param name="defattr">Es true para copiar los atributos predeterminados de XmlReader; en caso contrario, es false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> contains invalid characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNodeAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>Copia asincrónicamente todo el contenido del lector en el sistema de escritura y desplaza el lector al inicio del siguiente nodo relacionado.</summary>
      <returns>Tarea que representa la operación WriteNode asincrónica.</returns>
      <param name="reader">
        <see cref="T:System.Xml.XmlReader" /> desde el que se va a leer.</param>
      <param name="defattr">Es true para copiar los atributos predeterminados de XmlReader; en caso contrario, es false.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstruction(System.String,System.String)">
      <summary>Cuando se invalida en una clase derivada, escribe una instrucción de procesamiento con un espacio entre el nombre y el texto: &lt;?nombre texto?&gt;.</summary>
      <param name="name">Nombre de la instrucción de procesamiento.</param>
      <param name="text">Texto que se va a incluir en la instrucción de procesamiento.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.<paramref name="name" /> is either null or String.Empty.This method is being used to create an XML declaration after <see cref="M:System.Xml.XmlWriter.WriteStartDocument" /> has already been called. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstructionAsync(System.String,System.String)">
      <summary>Escribe de forma asincrónica una instrucción de procesamiento con un espacio entre el nombre y el texto: &lt;?nombre texto?&gt;.</summary>
      <returns>Tarea que representa la operación WriteProcessingInstruction asincrónica.</returns>
      <param name="name">Nombre de la instrucción de procesamiento.</param>
      <param name="text">Texto que se va a incluir en la instrucción de procesamiento.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedName(System.String,System.String)">
      <summary>Cuando se invalida en una clase derivada, escribe el nombre completo del espacio de nombres.Este método busca un prefijo que está en el ámbito del espacio de nombres especificado.</summary>
      <param name="localName">Nombre local que se va a escribir.</param>
      <param name="ns">Identificador URI de espacio de nombres del nombre.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="localName" /> is either null or String.Empty.<paramref name="localName" /> is not a valid name. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedNameAsync(System.String,System.String)">
      <summary>Escribe asincrónicamente el nombre completo del espacio de nombres.Este método busca un prefijo que está en el ámbito del espacio de nombres especificado.</summary>
      <returns>Tarea que representa la operación WriteQualifiedName asincrónica.</returns>
      <param name="localName">Nombre local que se va a escribir.</param>
      <param name="ns">Identificador URI de espacio de nombres del nombre.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.Char[],System.Int32,System.Int32)">
      <summary>Cuando se invalida en una clase derivada, escribe marcado sin formato manualmente desde un búfer de caracteres.</summary>
      <param name="buffer">Matriz de caracteres que contiene el texto que se va a escribir.</param>
      <param name="index">Posición en el búfer que indica el inicio del texto que se va a escribir.</param>
      <param name="count">Número de caracteres que se van a escribir.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.String)">
      <summary>Cuando se invalida en una clase derivada, escribe marcado sin formato manualmente desde una cadena.</summary>
      <param name="data">Cadena que contiene el texto que se va a escribir.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="data" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Escribe asincrónicamente el marcado sin formato de un búfer de caracteres.</summary>
      <returns>Tarea que representa la operación WriteRaw asincrónica.</returns>
      <param name="buffer">Matriz de caracteres que contiene el texto que se va a escribir.</param>
      <param name="index">Posición en el búfer que indica el inicio del texto que se va a escribir.</param>
      <param name="count">Número de caracteres que se van a escribir.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.String)">
      <summary>Escribe asincrónicamente el marcado sin formato de una cadena.</summary>
      <returns>Tarea que representa la operación WriteRaw asincrónica.</returns>
      <param name="data">Cadena que contiene el texto que se va a escribir.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String)">
      <summary>Escribe el inicio de un atributo con el nombre local especificado.</summary>
      <param name="localName">Nombre local del atributo.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)">
      <summary>Escribe el inicio de un atributo con el URI de espacio de nombres y el nombre local especificados.</summary>
      <param name="localName">Nombre local del atributo.</param>
      <param name="ns">URI de espacio de nombres del atributo.</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String,System.String)">
      <summary>Cuando se invalida en una clase derivada, escribe el inicio de un atributo con el prefijo, el nombre local y el URI de espacio de nombres especificados.</summary>
      <param name="prefix">Prefijo de espacio de nombres del atributo.</param>
      <param name="localName">Nombre local del atributo.</param>
      <param name="ns">Identificador URI de espacio de nombres del atributo.</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttributeAsync(System.String,System.String,System.String)">
      <summary>Escribe asincrónicamente el inicio de un atributo con el prefijo, URI de espacio de nombres y el nombre local especificados.</summary>
      <returns>Tarea que representa la operación WriteStartAttribute asincrónica.</returns>
      <param name="prefix">Prefijo de espacio de nombres del atributo.</param>
      <param name="localName">Nombre local del atributo.</param>
      <param name="ns">Identificador URI de espacio de nombres del atributo.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument">
      <summary>Cuando se invalida en una clase derivada, escribe la declaración XML con la versión "1.0".</summary>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument(System.Boolean)">
      <summary>Cuando se invalida en una clase derivada, escribe la declaración XML con la versión "1.0" y el atributo independiente.</summary>
      <param name="standalone">Si es true, escribirá "standalone=yes"; si es false, escribirá "standalone=no".</param>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync">
      <summary>Escribe asincrónicamente la declaración XML con la versión "1.0".</summary>
      <returns>Tarea que representa la operación WriteStartDocument asincrónica.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync(System.Boolean)">
      <summary>Escribe asincrónicamente la declaración XML con la versión "1.0" así como el atributo independiente.</summary>
      <returns>Tarea que representa la operación WriteStartDocument asincrónica.</returns>
      <param name="standalone">Si es true, escribirá "standalone=yes"; si es false, escribirá "standalone=no".</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String)">
      <summary>Cuando se invalida en una clase derivada, escribe una etiqueta de apertura con el nombre local especificado.</summary>
      <param name="localName">Nombre local del elemento.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String)">
      <summary>Cuando se invalida en una clase derivada, escribe la etiqueta de apertura especificada y la asocia al espacio de nombres especificado.</summary>
      <param name="localName">Nombre local del elemento.</param>
      <param name="ns">Identificador URI de espacio de nombres que se va a asociar al elemento.Si este espacio de nombres ya está en el ámbito y tiene asociado un prefijo, el sistema de escritura escribe automáticamente también dicho prefijo.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String,System.String)">
      <summary>Cuando se invalida en una clase derivada, escribe la etiqueta de apertura especificada y la asocia al espacio de nombres y prefijo especificados.</summary>
      <param name="prefix">Prefijo de espacio de nombres del elemento.</param>
      <param name="localName">Nombre local del elemento.</param>
      <param name="ns">Identificador URI de espacio de nombres que se va a asociar al elemento.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElementAsync(System.String,System.String,System.String)">
      <summary>Escribe asincrónicamente la etiqueta de apertura especificada y la asocia al espacio de nombres y al prefijo especificados.</summary>
      <returns>Tarea que representa la operación WriteStartElement asincrónica.</returns>
      <param name="prefix">Prefijo de espacio de nombres del elemento.</param>
      <param name="localName">Nombre local del elemento.</param>
      <param name="ns">Identificador URI de espacio de nombres que se va a asociar al elemento.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.WriteState">
      <summary>Cuando se invalida en una clase derivada, obtiene el estado del sistema de escritura.</summary>
      <returns>Uno de los valores de <see cref="T:System.Xml.WriteState" />.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteString(System.String)">
      <summary>Cuando se invalida en una clase derivada, escribe el contenido de texto especificado.</summary>
      <param name="text">Texto que se va a escribir.</param>
      <exception cref="T:System.ArgumentException">The text string contains an invalid surrogate pair.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStringAsync(System.String)">
      <summary>Escribe asincrónicamente el contenido de texto dado.</summary>
      <returns>Tarea que representa la operación WriteString asincrónica.</returns>
      <param name="text">Texto que se va a escribir.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntity(System.Char,System.Char)">
      <summary>Cuando se invalida en una clase derivada, genera y escribe la entidad de carácter suplente para el par de caracteres suplentes.</summary>
      <param name="lowChar">Suplente bajo.Debe ser un valor comprendido entre 0xDC00 y 0xDFFF.</param>
      <param name="highChar">Suplente alto.Debe ser un valor comprendido entre 0xD800 y 0xDBFF.</param>
      <exception cref="T:System.ArgumentException">An invalid surrogate character pair was passed.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntityAsync(System.Char,System.Char)">
      <summary>Genera y escribe asincrónicamente la entidad de carácter suplente del par de caracteres suplentes.</summary>
      <returns>Tarea que representa la operación WriteSurrogateCharEntity asincrónica.</returns>
      <param name="lowChar">Suplente bajo.Debe ser un valor comprendido entre 0xDC00 y 0xDFFF.</param>
      <param name="highChar">Suplente alto.Debe ser un valor comprendido entre 0xD800 y 0xDBFF.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Boolean)">
      <summary>Escribe un valor <see cref="T:System.Boolean" />.</summary>
      <param name="value">Valor <see cref="T:System.Boolean" /> que se va a escribir.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.DateTimeOffset)">
      <summary>Escribe un valor <see cref="T:System.DateTimeOffset" />.</summary>
      <param name="value">Valor <see cref="T:System.DateTimeOffset" /> que se va a escribir.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Decimal)">
      <summary>Escribe un valor <see cref="T:System.Decimal" />.</summary>
      <param name="value">Valor <see cref="T:System.Decimal" /> que se va a escribir.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Double)">
      <summary>Escribe un valor <see cref="T:System.Double" />.</summary>
      <param name="value">Valor <see cref="T:System.Double" /> que se va a escribir.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int32)">
      <summary>Escribe un valor <see cref="T:System.Int32" />.</summary>
      <param name="value">Valor <see cref="T:System.Int32" /> que se va a escribir.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int64)">
      <summary>Escribe un valor <see cref="T:System.Int64" />.</summary>
      <param name="value">Valor <see cref="T:System.Int64" /> que se va a escribir.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Object)">
      <summary>Escribe el valor del objeto.</summary>
      <param name="value">Valor del objeto que se va a escribir.Nota   Con el lanzamiento de .NET Framework 3.5, este método acepta <see cref="T:System.DateTimeOffset" /> como parámetro.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Single)">
      <summary>Escribe un número de punto flotante de precisión sencilla.</summary>
      <param name="value">El número de punto flotante de precisión sencilla que se va a escribir.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.String)">
      <summary>Escribe un valor <see cref="T:System.String" />.</summary>
      <param name="value">Valor <see cref="T:System.String" /> que se va a escribir.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespace(System.String)">
      <summary>Cuando se invalida en una clase derivada, escribe el espacio en blanco especificado.</summary>
      <param name="ws">Cadena de caracteres de espacio en blanco.</param>
      <exception cref="T:System.ArgumentException">The string contains non-white space characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespaceAsync(System.String)">
      <summary>Escribe asincrónicamente el espacio en blanco especificado.</summary>
      <returns>Tarea que representa la operación WriteWhitespace asincrónica.</returns>
      <param name="ws">Cadena de caracteres de espacio en blanco.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlLang">
      <summary>Cuando se invalida en una clase derivada, obtiene el ámbito de xml:lang actual.</summary>
      <returns>Ámbito de xml:lang actual.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlSpace">
      <summary>Cuando se invalida en una clase derivada, se obtiene un <see cref="T:System.Xml.XmlSpace" /> que representa el ámbito de xml:space actual.</summary>
      <returns>XmlSpace que representa el ámbito de xml:space actual.Valor Significado NoneEste es el valor predeterminado si no existe ningún ámbito de xml:space.DefaultEl ámbito actual es xml:space="default".PreserveEl ámbito actual es xml:space="preserve".</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="T:System.Xml.XmlWriterSettings">
      <summary>Especifica un conjunto de características compatibles en el objeto <see cref="T:System.Xml.XmlWriter" /> creado mediante el método <see cref="Overload:System.Xml.XmlWriter.Create" />.</summary>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Async">
      <summary>Obtiene o establece un valor que indica si los métodos asincrónicos <see cref="T:System.Xml.XmlWriter" /> se pueden usar en una instancia determinada de <see cref="T:System.Xml.XmlWriter" />.</summary>
      <returns>true si se pueden usar métodos asincrónicos; si no, false.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CheckCharacters">
      <summary>Obtiene o establece un valor que indica si el sistema de escritura XML debería comprobar y asegurarse de que todos los caracteres en el documento se ajustan a la sección "2.2 Characters" de la recomendación XML 1.0 del Consorcio W3C.</summary>
      <returns>true si se va a realizar la comprobación de caracteres; en caso contrario, false.De manera predeterminada, es true.</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Clone">
      <summary>Crea una copia de la instancia de <see cref="T:System.Xml.XmlWriterSettings" />.</summary>
      <returns>Objeto <see cref="T:System.Xml.XmlWriterSettings" /> clonado.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CloseOutput">
      <summary>Obtiene o establece un valor que indica si el objeto <see cref="T:System.Xml.XmlWriter" /> también debe cerrar el flujo subyacente o <see cref="T:System.IO.TextWriter" /> cuando se llama al método <see cref="M:System.Xml.XmlWriter.Close" />.</summary>
      <returns>true para cerrar también el flujo subyacente o <see cref="T:System.IO.TextWriter" />; en caso contrario, false.De manera predeterminada, es false.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.ConformanceLevel">
      <summary>Obtiene o establece el nivel de conformidad que el sistema de escritura XML comprueba para la salida XML.</summary>
      <returns>Uno de los valores de enumeración que especifica el nivel de conformidad (documento, fragmento o detección automática).De manera predeterminada, es <see cref="F:System.Xml.ConformanceLevel.Document" />.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Encoding">
      <summary>Obtiene o establece el tipo de codificación de texto que se va a usar.</summary>
      <returns>Codificación de texto que se va a usar.De manera predeterminada, es Encoding.UTF8.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Indent">
      <summary>Obtiene o establece un valor que indica si se va a aplicar sangría a los elementos.</summary>
      <returns>true para escribir elementos individuales en líneas nuevas y aplicar sangría; en caso contrario, false.De manera predeterminada, es false.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.IndentChars">
      <summary>Obtiene o establece la cadena de caracteres que se va a usar al aplicar sangría.Esta opción se usa cuando la propiedad <see cref="P:System.Xml.XmlWriterSettings.Indent" /> se establece en true.</summary>
      <returns>Cadena de caracteres que se va a usar al aplicar sangría.Se puede establecer en cualquier valor de cadena.Sin embargo, para garantizar la validez del contenido XML, debe especificar solo caracteres de espacio en blanco válidos, como caracteres de espacio, tabulaciones, retornos de carro y saltos de línea.El valor predeterminado es dos espacios.</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.IndentChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NamespaceHandling">
      <summary>Obtiene o establece un valor que indica si <see cref="T:System.Xml.XmlWriter" /> debe quitar declaraciones de espacio de nombres duplicadas al escribir contenido XML.El comportamiento predeterminado es que el sistema de escritura genere todas las declaraciones de espacio de nombres que se encuentran en la resolución de espacios de nombres del sistema de escritura.</summary>
      <returns>Enumeración <see cref="T:System.Xml.NamespaceHandling" /> usada para especificar si se van a quitar las declaraciones de espacio de nombres duplicadas en <see cref="T:System.Xml.XmlWriter" />.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineChars">
      <summary>Obtiene o establece la cadena de caracteres que se va a usar para los saltos de línea.</summary>
      <returns>Cadena de caracteres que se va a usar para los saltos de línea.Se puede establecer en cualquier valor de cadena.Sin embargo, para garantizar la validez del contenido XML, debe especificar solo caracteres de espacio en blanco válidos, como caracteres de espacio, tabulaciones, retornos de carro y saltos de línea.El valor predeterminado es \r\n (retorno de carro, nueva línea).</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineHandling">
      <summary>Obtiene o establece un valor que indica si se deben normalizar los saltos de línea en el resultado.</summary>
      <returns>Uno de los valores de <see cref="T:System.Xml.NewLineHandling" />.De manera predeterminada, es <see cref="F:System.Xml.NewLineHandling.Replace" />.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineOnAttributes">
      <summary>Obtiene o establece un valor que indica si los atributos se deben escribir en una nueva línea.</summary>
      <returns>true para escribir los atributos en líneas individuales; en caso contrario, false.De manera predeterminada, es false.NotaEsta configuración no se aplica cuando el valor de la propiedad <see cref="P:System.Xml.XmlWriterSettings.Indent" /> es false.Cuando <see cref="P:System.Xml.XmlWriterSettings.NewLineOnAttributes" /> se establece en true, a cada atributo le precede una nueva línea y un nivel adicional de sangría.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.OmitXmlDeclaration">
      <summary>Obtiene o establece un valor que indica si debe omitir una declaración XML.</summary>
      <returns>true para omitir la declaración XML; en caso contrario, false.El valor predeterminado es false, se escribe una declaración XML.</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Reset">
      <summary>Restablece los miembros de la clase de configuración a sus valores predeterminados.</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.WriteEndDocumentOnClose">
      <summary>Obtiene o establece un valor que indica si <see cref="T:System.Xml.XmlWriter" /> agregará etiquetas de cierre a todas las etiquetas de elementos sin cerrar cuando se llame al método <see cref="M:System.Xml.XmlWriter.Close" />.</summary>
      <returns>
            Es true si se cerrarán todas las etiquetas de elementos sin cerrar; si no, es false.El valor predeterminado es true.</returns>
    </member>
    <member name="T:System.Xml.Schema.XmlSchema">
      <summary>Una representación en memoria de un esquema XML según se indica en las especificaciones XML Schema Part 1: Structures y XML Schema Part 2: Datatypes de World Wide Web Consortium (W3C).</summary>
    </member>
    <member name="T:System.Xml.Schema.XmlSchemaForm">
      <summary>Indica si los atributos o los elementos deben calificarse con un espacio de nombres como prefijo.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.None">
      <summary>El formato de elemento y de atributo no se especifica en el esquema.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Qualified">
      <summary>Los atributos y los elementos deben estar calificados con el espacio de nombres como prefijo.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Unqualified">
      <summary>Los elementos y los atributos no deben estar calificados con el espacio de nombres como prefijo.</summary>
    </member>
    <member name="T:System.Xml.Serialization.IXmlSerializable">
      <summary>Proporciona formato personalizado para la serialización y deserialización XML.</summary>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.GetSchema">
      <summary>Este método se reserva y no debe utilizarse.Al implementar la interfaz IXmlSerializable, debe devolver null (Nothing en Visual Basic) desde este método y, en su lugar, si se requiere especificar un esquema personalizado, aplique <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" /> a la clase.</summary>
      <returns>Clase <see cref="T:System.Xml.Schema.XmlSchema" /> que describe la representación XML del objeto producido por el método <see cref="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)" /> y utilizado por el método <see cref="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)">
      <summary>Genera un objeto a partir de su representación XML.</summary>
      <param name="reader">Secuencia de <see cref="T:System.Xml.XmlReader" /> desde la que se deserializa el objeto. </param>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)">
      <summary>Convierte un objeto en su representación XML.</summary>
      <param name="writer">Secuencia de <see cref="T:System.Xml.XmlWriter" /> para la que se serializa el objeto. </param>
    </member>
    <member name="T:System.Xml.Serialization.XmlSchemaProviderAttribute">
      <summary>Cuando se aplica a un tipo, almacena el nombre de un método estático del tipo que devuelve un esquema XML y un <see cref="T:System.Xml.XmlQualifiedName" /> (o <see cref="T:System.Xml.Schema.XmlSchemaType" /> para los tipos anónimos) que controla la serialización del tipo.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaProviderAttribute.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" />, tomando el nombre del método estático que proporciona el esquema XML del tipo.</summary>
      <param name="methodName">El nombre del método estático que se debe implementar.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.IsAny">
      <summary>Obtiene o establece un valor que determina si la clase de destino es un carácter comodín o que el esquema para la clase contiene sólo un elemento xs:any.</summary>
      <returns>true, si la clase es un comodín, o si el esquema contiene sólo el elemento xs:any; de lo contrario, false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.MethodName">
      <summary>Obtiene el nombre del método estático que proporciona el esquema XML del tipo y el nombre de su tipo de datos de esquemas XML.</summary>
      <returns>Nombre del método que invoca la infraestructura de XML para devolver un esquema XML.</returns>
    </member>
  </members>
</doc>