﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net461</TargetFramework>
    <RootNamespace>OcrConsoleApp</RootNamespace>
    <AssemblyName>OcrConsoleApp</AssemblyName>
    <Platforms>AnyCPU;ARM64;x64</Platforms>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\OcrLib\OcrLib.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="Emgu.CV" />
    <Reference Include="Emgu.CV.UI" />
    <Reference Include="Emgu.CV.Bitmap" />
    <Reference Include="ClipperLib" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Properties\" />
  </ItemGroup>
</Project>
