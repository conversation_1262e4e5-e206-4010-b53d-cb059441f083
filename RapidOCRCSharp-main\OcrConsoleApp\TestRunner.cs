using System;
using System.IO;
using OcrLiteLib;
using System.Diagnostics;

namespace OcrConsoleApp
{
    /// <summary>
    /// 测试运行器，验证所有OCR功能
    /// </summary>
    public static class TestRunner
    {
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== OCR功能测试 ===");
            
            // 模型路径
            string modelsDir = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrOnnxForm\\models";
            string detPath = Path.Combine(modelsDir, "ch_PP-OCRv4_det_infer.onnx");
            string clsPath = Path.Combine(modelsDir, "ch_ppocr_mobile_v2.0_cls_infer.onnx");
            string recPath = Path.Combine(modelsDir, "ch_PP-OCRv4_rec_infer.onnx");
            string keysPath = Path.Combine(modelsDir, "ppocr_keys_v1.txt");
            
            // 检查模型文件
            if (!File.Exists(detPath) || !File.Exists(clsPath) || !File.Exists(recPath) || !File.Exists(keysPath))
            {
                Console.WriteLine("模型文件不存在，请检查路径:");
                Console.WriteLine($"  检测模型: {detPath}");
                Console.WriteLine($"  分类模型: {clsPath}");
                Console.WriteLine($"  识别模型: {recPath}");
                Console.WriteLine($"  字典文件: {keysPath}");
                return;
            }
            
            // 测试图片路径
            string testImagePath = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrConsoleApp\\images\\test.jpg";
            if (!File.Exists(testImagePath))
            {
                Console.WriteLine($"测试图片不存在: {testImagePath}");
                Console.WriteLine("请在images文件夹中放置测试图片");
                return;
            }
            
            // 开始测试
            TestBasicFunctionality(detPath, clsPath, recPath, keysPath, testImagePath);
            TestMemoryOptimization(detPath, clsPath, recPath, keysPath, testImagePath);
            TestPerformanceStats(detPath, clsPath, recPath, keysPath, testImagePath);
        }
        
        /// <summary>
        /// 测试基本功能
        /// </summary>
        static void TestBasicFunctionality(string detPath, string clsPath, string recPath, string keysPath, string testImagePath)
        {
            Console.WriteLine("\n=== 测试1: 基本功能 ===");
            
            try
            {
                using (var ocrService = new OcrServiceOptimized())
                {
                    Console.WriteLine("初始化模型...");
                    ocrService.InitModels(detPath, clsPath, recPath, keysPath, 1);
                    
                    Console.WriteLine("测试简单文本识别...");
                    string text = ocrService.DetectText(testImagePath);
                    Console.WriteLine($"识别结果: {text}");
                    
                    Console.WriteLine("测试详细信息获取...");
                    var textBlocks = ocrService.DetectTextBlocks(testImagePath);
                    Console.WriteLine($"识别到 {textBlocks.Count} 个文本块");
                    
                    foreach (var block in textBlocks)
                    {
                        Console.WriteLine($"  文本: {block.Text}");
                        Console.WriteLine($"  置信度: {block.BoxScore:F3}");
                    }
                    
                    Console.WriteLine("✓ 基本功能测试通过");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 基本功能测试失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 测试内存优化
        /// </summary>
        static void TestMemoryOptimization(string detPath, string clsPath, string recPath, string keysPath, string testImagePath)
        {
            Console.WriteLine("\n=== 测试2: 内存优化 ===");
            
            try
            {
                using (var ocrService = new OcrServiceOptimized())
                {
                    ocrService.InitModels(detPath, clsPath, recPath, keysPath, 1);
                    
                    var initialMemory = ocrService.GetDetailedMemoryInfo();
                    Console.WriteLine($"初始内存: {initialMemory}");
                    
                    // 连续识别测试
                    for (int i = 0; i < 10; i++)
                    {
                        var textBlocks = ocrService.DetectTextBlocks(testImagePath);
                        
                        if (i % 5 == 4)
                        {
                            var currentMemory = ocrService.GetDetailedMemoryInfo();
                            Console.WriteLine($"第{i + 1}次识别后: {currentMemory}");
                        }
                    }
                    
                    // 强制清理测试
                    Console.WriteLine("执行强制内存清理...");
                    ocrService.ForceMemoryCleanup();
                    
                    var finalMemory = ocrService.GetDetailedMemoryInfo();
                    Console.WriteLine($"清理后内存: {finalMemory}");
                    
                    Console.WriteLine("✓ 内存优化测试通过");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 内存优化测试失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 测试性能统计
        /// </summary>
        static void TestPerformanceStats(string detPath, string clsPath, string recPath, string keysPath, string testImagePath)
        {
            Console.WriteLine("\n=== 测试3: 性能统计 ===");
            
            try
            {
                using (var ocrService = new OcrServiceOptimized())
                {
                    ocrService.InitModels(detPath, clsPath, recPath, keysPath, 1);
                    
                    // 预热
                    ocrService.DetectTextBlocks(testImagePath);
                    
                    // 性能测试
                    Console.WriteLine("开始性能测试...");
                    var totalStopwatch = Stopwatch.StartNew();
                    
                    for (int i = 0; i < 5; i++)
                    {
                        OcrServiceOptimized.PerformanceStats stats;
                        var textBlocks = ocrService.DetectTextBlocks(testImagePath, out stats);
                        
                        Console.WriteLine($"第{i + 1}次识别:");
                        Console.WriteLine($"  总耗时: {stats.TotalDetectionTime}ms");
                        Console.WriteLine($"  - 预处理: {stats.PreprocessTime}ms");
                        Console.WriteLine($"  - 文本检测: {stats.DbNetTime}ms");
                        Console.WriteLine($"  - 角度分类: {stats.AngleNetTime}ms");
                        Console.WriteLine($"  - 文字识别: {stats.CrnnNetTime}ms");
                        Console.WriteLine($"  - 后处理: {stats.PostprocessTime}ms");
                        Console.WriteLine($"  文本块数: {stats.TextBlockCount}");
                    }
                    
                    totalStopwatch.Stop();
                    Console.WriteLine($"5次识别总耗时: {totalStopwatch.ElapsedMilliseconds}ms");
                    Console.WriteLine($"平均耗时: {totalStopwatch.ElapsedMilliseconds / 5}ms");
                    
                    Console.WriteLine("✓ 性能统计测试通过");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 性能统计测试失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 测试非托管内存管理
        /// </summary>
        public static void TestUnmanagedMemory()
        {
            Console.WriteLine("\n=== 测试4: 非托管内存管理 ===");
            
            try
            {
                var initialInfo = UnmanagedMemoryManager.GetMemoryInfo();
                Console.WriteLine($"初始状态: {initialInfo}");
                
                // 模拟内存使用
                Console.WriteLine("模拟内存分配...");
                byte[] largeArray = new byte[100 * 1024 * 1024]; // 100MB
                
                var afterAllocation = UnmanagedMemoryManager.GetMemoryInfo();
                Console.WriteLine($"分配后: {afterAllocation}");
                
                // 释放并清理
                largeArray = null;
                Console.WriteLine("执行智能内存清理...");
                UnmanagedMemoryManager.SmartMemoryCleanup(500); // 500MB阈值
                
                var afterCleanup = UnmanagedMemoryManager.GetMemoryInfo();
                Console.WriteLine($"清理后: {afterCleanup}");
                
                Console.WriteLine("✓ 非托管内存管理测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 非托管内存管理测试失败: {ex.Message}");
            }
        }
    }
}
