using Emgu.CV;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Drawing;

namespace OcrLiteLib
{
    /// <summary>
    /// Mat对象池，用于复用常用尺寸的Mat对象，减少内存分配和释放
    /// </summary>
    public class MatPool : IDisposable
    {
        private readonly ConcurrentDictionary<string, ConcurrentQueue<Mat>> _pools;
        private readonly object _lockObject = new object();
        private bool _disposed = false;
        private const int MAX_POOL_SIZE = 3; // 减少到每个尺寸最多缓存3个Mat对象
        private const int MAX_TOTAL_OBJECTS = 20; // 总共最多缓存20个Mat对象
        private int _totalObjects = 0;

        public MatPool()
        {
            _pools = new ConcurrentDictionary<string, ConcurrentQueue<Mat>>();
        }

        /// <summary>
        /// 获取指定尺寸的Mat对象
        /// </summary>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <param name="type">Mat类型</param>
        /// <returns>Mat对象</returns>
        public Mat GetMat(int width, int height, Emgu.CV.CvEnum.DepthType type = Emgu.CV.CvEnum.DepthType.Cv8U, int channels = 3)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(MatPool));

            string key = $"{width}x{height}x{type}x{channels}";
            
            if (_pools.TryGetValue(key, out ConcurrentQueue<Mat> queue) && queue.TryDequeue(out Mat mat))
            {
                // 重置Mat内容
                mat.SetTo(new Emgu.CV.Structure.MCvScalar(0));
                return mat;
            }

            // 如果池中没有可用的Mat，创建新的
            return new Mat(new Size(width, height), Emgu.CV.CvEnum.DepthType.Cv8U, channels);
        }

        /// <summary>
        /// 获取指定尺寸的Mat对象
        /// </summary>
        /// <param name="size">尺寸</param>
        /// <param name="type">Mat类型</param>
        /// <returns>Mat对象</returns>
        public Mat GetMat(Size size, Emgu.CV.CvEnum.DepthType type = Emgu.CV.CvEnum.DepthType.Cv8U, int channels = 3)
        {
            return GetMat(size.Width, size.Height, type, channels);
        }

        /// <summary>
        /// 归还Mat对象到池中
        /// </summary>
        /// <param name="mat">要归还的Mat对象</param>
        public void ReturnMat(Mat mat)
        {
            if (_disposed || mat == null || mat.IsEmpty)
            {
                mat?.Dispose();
                return;
            }

            lock (_lockObject)
            {
                // 检查总对象数量限制
                if (_totalObjects >= MAX_TOTAL_OBJECTS)
                {
                    mat.Dispose();
                    return;
                }

                string key = $"{mat.Width}x{mat.Height}x{mat.Depth}x{mat.NumberOfChannels}";
                var queue = _pools.GetOrAdd(key, _ => new ConcurrentQueue<Mat>());

                // 限制单个尺寸的池大小
                if (queue.Count < MAX_POOL_SIZE)
                {
                    queue.Enqueue(mat);
                    _totalObjects++;
                }
                else
                {
                    mat.Dispose(); // 如果池已满，直接释放
                }
            }
        }

        /// <summary>
        /// 清空所有池
        /// </summary>
        public void Clear()
        {
            lock (_lockObject)
            {
                foreach (var pool in _pools.Values)
                {
                    while (pool.TryDequeue(out Mat mat))
                    {
                        mat?.Dispose();
                    }
                }
                _pools.Clear();
                _totalObjects = 0;
            }
        }

        /// <summary>
        /// 强制清理池中的对象，释放内存
        /// </summary>
        public void ForceCleanup()
        {
            lock (_lockObject)
            {
                // 清理一半的对象
                foreach (var kvp in _pools)
                {
                    var queue = kvp.Value;
                    int removeCount = queue.Count / 2;
                    for (int i = 0; i < removeCount; i++)
                    {
                        if (queue.TryDequeue(out Mat mat))
                        {
                            mat?.Dispose();
                            _totalObjects--;
                        }
                    }
                }
            }

            // 强制垃圾回收
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
        }

        /// <summary>
        /// 获取池的统计信息
        /// </summary>
        /// <returns>统计信息字符串</returns>
        public string GetPoolStats()
        {
            var stats = new List<string>();
            foreach (var kvp in _pools)
            {
                stats.Add($"{kvp.Key}: {kvp.Value.Count} objects");
            }
            return string.Join(", ", stats);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    Clear();
                }
                _disposed = true;
            }
        }

        ~MatPool()
        {
            Dispose(false);
        }
    }

    /// <summary>
    /// 可自动归还的Mat包装器
    /// </summary>
    public class PooledMat : IDisposable
    {
        private readonly Mat _mat;
        private readonly MatPool _pool;
        private bool _disposed = false;

        public PooledMat(Mat mat, MatPool pool)
        {
            _mat = mat ?? throw new ArgumentNullException(nameof(mat));
            _pool = pool ?? throw new ArgumentNullException(nameof(pool));
        }

        public Mat Mat => _mat;

        // 隐式转换为Mat
        public static implicit operator Mat(PooledMat pooledMat)
        {
            return pooledMat._mat;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _pool?.ReturnMat(_mat);
                _disposed = true;
            }
        }
    }
}
