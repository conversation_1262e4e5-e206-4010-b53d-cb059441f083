﻿using Emgu.CV;
using Emgu.CV.CvEnum;
using Emgu.CV.Structure;
using Microsoft.ML.OnnxRuntime.Tensors;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;

namespace OcrLiteLib
{
    public class OcrUtils
    {
        // 静态缓冲区，避免重复分配
        private static byte[] _sharedBuffer = null;
        private static float[] _sharedFloatBuffer = null;
        private static readonly object _bufferLock = new object();

        public static Tensor<float> SubstractMeanNormalize(Mat src, float[] meanVals, float[] normVals)
        {
            int cols = src.Cols;
            int rows = src.Rows;
            int channels = src.NumberOfChannels;
            int totalPixels = rows * cols;
            int totalElements = totalPixels * channels;

            // 使用共享缓冲区，避免重复分配
            lock (_bufferLock)
            {
                // 确保缓冲区足够大
                int requiredByteSize = src.Total.ToInt32() * src.ElementSize;
                if (_sharedBuffer == null || _sharedBuffer.Length < requiredByteSize)
                {
                    _sharedBuffer = new byte[requiredByteSize];
                }

                if (_sharedFloatBuffer == null || _sharedFloatBuffer.Length < totalElements)
                {
                    _sharedFloatBuffer = new float[totalElements];
                }

                // 直接复制到共享缓冲区
                src.CopyTo(_sharedBuffer);

                // 预计算归一化参数
                float[] precomputedMean = new float[channels];
                float[] precomputedNorm = new float[channels];
                for (int ch = 0; ch < channels; ch++)
                {
                    precomputedMean[ch] = meanVals[ch] * normVals[ch];
                    precomputedNorm[ch] = normVals[ch];
                }

                // 优化的内存访问模式 - 按通道处理
                int srcIdx = 0;
                for (int r = 0; r < rows; r++)
                {
                    for (int c = 0; c < cols; c++)
                    {
                        int pixelIdx = r * cols + c;
                        for (int ch = 0; ch < channels; ch++)
                        {
                            var value = _sharedBuffer[srcIdx + ch];
                            _sharedFloatBuffer[ch * totalPixels + pixelIdx] = value * precomputedNorm[ch] - precomputedMean[ch];
                        }
                        srcIdx += channels;
                    }
                }

                // 创建Tensor，使用我们的数据
                return new DenseTensor<float>(_sharedFloatBuffer.AsSpan(0, totalElements).ToArray(), new[] { 1, channels, rows, cols });
            }
        }

        /// <summary>
        /// 清理共享缓冲区，释放内存
        /// </summary>
        public static void ClearSharedBuffers()
        {
            lock (_bufferLock)
            {
                _sharedBuffer = null;
                _sharedFloatBuffer = null;
                GC.Collect(); // 强制回收
            }
        }

        // 备用的安全版本，当unsafe代码不可用时使用
        public static Tensor<float> SubstractMeanNormalizeSafe(Mat src, float[] meanVals, float[] normVals)
        {
            int cols = src.Cols;
            int rows = src.Rows;
            int channels = src.NumberOfChannels;

            // 使用using确保Image对象被正确释放
            using (Image<Rgb, byte> srcImg = src.ToImage<Rgb, byte>())
            {
                byte[,,] imgData = srcImg.Data;
                Tensor<float> inputTensor = new DenseTensor<float>(new[] { 1, channels, rows, cols });

                // 优化内存访问模式，减少缓存未命中
                for (int ch = 0; ch < channels; ch++)
                {
                    float meanVal = meanVals[ch];
                    float normVal = normVals[ch];
                    for (int r = 0; r < rows; r++)
                    {
                        for (int c = 0; c < cols; c++)
                        {
                            var value = imgData[r, c, ch];
                            float data = (float)(value * normVal - meanVal * normVal);
                            inputTensor[0, ch, r, c] = data;
                        }
                    }
                }
                return inputTensor;
            }
        }

        public static Mat MakePadding(Mat src, int padding)
        {
            if (padding <= 0) return src;
            MCvScalar paddingScalar = new MCvScalar(255, 255, 255);
            Mat paddingSrc = new Mat();
            CvInvoke.CopyMakeBorder(src, paddingSrc, padding, padding, padding, padding, BorderType.Isolated, paddingScalar);
            return paddingSrc;
        }

        // 移除绘图和厚度计算功能，OCR服务不需要

        public static List<Mat> GetPartImages(Mat src, List<TextBox> textBoxes)
        {
            List<Mat> partImages = new List<Mat>(textBoxes.Count); // 预分配容量
            for (int i = 0; i < textBoxes.Count; ++i)
            {
                Mat partImg = GetRotateCropImage(src, textBoxes[i].Points);
                partImages.Add(partImg);
            }
            return partImages;
        }

        public static Mat GetRotateCropImage(Mat src, List<Point> box)
        {
            List<Point> points = new List<Point>();
            points.AddRange(box);

            int[] collectX = { box[0].X, box[1].X, box[2].X, box[3].X };
            int[] collectY = { box[0].Y, box[1].Y, box[2].Y, box[3].Y };
            int left = collectX.Min();
            int right = collectX.Max();
            int top = collectY.Min();
            int bottom = collectY.Max();

            Rectangle rect = new Rectangle(left, top, right - left, bottom - top);
            using (Mat imgCrop = new Mat(src, rect))
            {
                for (int i = 0; i < points.Count; i++)
                {
                    var pt = points[i];
                    pt.X -= left;
                    pt.Y -= top;
                    points[i] = pt;
                }

                int imgCropWidth = (int)(Math.Sqrt(Math.Pow(points[0].X - points[1].X, 2) +
                                            Math.Pow(points[0].Y - points[1].Y, 2)));
                int imgCropHeight = (int)(Math.Sqrt(Math.Pow(points[0].X - points[3].X, 2) +
                                             Math.Pow(points[0].Y - points[3].Y, 2)));

                var ptsDst0 = new PointF(0, 0);
                var ptsDst1 = new PointF(imgCropWidth, 0);
                var ptsDst2 = new PointF(imgCropWidth, imgCropHeight);
                var ptsDst3 = new PointF(0, imgCropHeight);

                PointF[] ptsDst = { ptsDst0, ptsDst1, ptsDst2, ptsDst3 };


                var ptsSrc0 = new PointF(points[0].X, points[0].Y);
                var ptsSrc1 = new PointF(points[1].X, points[1].Y);
                var ptsSrc2 = new PointF(points[2].X, points[2].Y);
                var ptsSrc3 = new PointF(points[3].X, points[3].Y);

                PointF[] ptsSrc = { ptsSrc0, ptsSrc1, ptsSrc2, ptsSrc3 };

                using (Mat M = CvInvoke.GetPerspectiveTransform(ptsSrc, ptsDst))
                {
                    using (Mat partImg = new Mat())
                    {
                        CvInvoke.WarpPerspective(imgCrop, partImg, M,
                                            new Size(imgCropWidth, imgCropHeight), Inter.Nearest, Warp.Default,
                                           BorderType.Replicate);
                        if (partImg.Rows >= partImg.Cols * 1.5)
                        {
                            using (Mat srcCopy = new Mat())
                            {
                                CvInvoke.Transpose(partImg, srcCopy);
                                CvInvoke.Flip(srcCopy, srcCopy, 0);
                                return srcCopy.Clone(); // 返回独立副本
                            }
                        }
                        else
                        {
                            return partImg.Clone(); // 返回独立 Mat，避免父对象被释放影响
                        }
                    }
                }
            }
        }

        public static Mat MatRotateClockWise180(Mat src)
        {
            CvInvoke.Flip(src, src, FlipType.Vertical);
            CvInvoke.Flip(src, src, FlipType.Horizontal);
            return src;
        }

        public static Mat MatRotateClockWise90(Mat src)
        {
            CvInvoke.Rotate(src, src, RotateFlags.Rotate90CounterClockwise);
            return src;
        }

    }
}

