using System;
using System.IO;
using OcrLiteLib;
using System.Diagnostics;

namespace OcrConsoleApp
{
    /// <summary>
    /// 简单的OCR测试类，演示如何使用优化的OCR服务
    /// </summary>
    public static class SimpleOcrTest
    {
        /// <summary>
        /// 运行简单的OCR测试
        /// </summary>
        public static void RunTest()
        {
            Console.WriteLine("=== 简单OCR测试 ===");
            
            // 模型路径
            string modelsDir = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrOnnxForm\\models";
            string detPath = Path.Combine(modelsDir, "ch_PP-OCRv4_det_infer.onnx");
            string clsPath = Path.Combine(modelsDir, "ch_ppocr_mobile_v2.0_cls_infer.onnx");
            string recPath = Path.Combine(modelsDir, "ch_PP-OCRv4_rec_infer.onnx");
            string keysPath = Path.Combine(modelsDir, "ppocr_keys_v1.txt");
            
            // 测试图片路径
            string testImagePath = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrConsoleApp\\images\\test.jpg";
            
            if (!File.Exists(testImagePath))
            {
                Console.WriteLine($"测试图片不存在: {testImagePath}");
                return;
            }
            
            // 使用优化的OCR服务
            using (var ocrService = new OcrServiceOptimized())
            {
                try
                {
                    Console.WriteLine("初始化模型...");
                    long beforeInit = ocrService.GetMemoryUsage();
                    ocrService.InitModels(detPath, clsPath, recPath, keysPath, 1);
                    long afterInit = ocrService.GetMemoryUsage();
                    Console.WriteLine($"模型初始化完成，内存增长: {FormatBytes(afterInit - beforeInit)}");
                    
                    var stopwatch = Stopwatch.StartNew();
                    
                    // 方式1: 只获取文本
                    Console.WriteLine("\n--- 方式1: 只获取文本 ---");
                    string text = ocrService.DetectText(testImagePath);
                    Console.WriteLine($"识别结果:\n{text}");
                    
                    // 方式2: 获取详细信息
                    Console.WriteLine("\n--- 方式2: 获取详细信息 ---");
                    var textBlocks = ocrService.DetectTextBlocks(testImagePath);
                    
                    stopwatch.Stop();
                    
                    Console.WriteLine($"识别到 {textBlocks.Count} 个文本块，耗时: {stopwatch.ElapsedMilliseconds}ms");
                    
                    foreach (var block in textBlocks)
                    {
                        Console.WriteLine($"文本: {block.Text}");
                        Console.WriteLine($"位置: [{block.BoxPoints[0].X},{block.BoxPoints[0].Y}] -> [{block.BoxPoints[2].X},{block.BoxPoints[2].Y}]");
                        Console.WriteLine($"文本框置信度: {block.BoxScore:F3}");
                        Console.WriteLine($"角度置信度: {block.AngleScore:F3}");
                        Console.WriteLine($"识别时间: {block.CrnnTime:F1}ms");
                        Console.WriteLine();
                    }
                    
                    Console.WriteLine($"当前内存使用: {FormatBytes(ocrService.GetMemoryUsage())}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"OCR处理出错: {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// 运行性能测试
        /// </summary>
        public static void RunPerformanceTest()
        {
            Console.WriteLine("=== OCR性能测试 ===");
            
            // 模型路径
            string modelsDir = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrOnnxForm\\models";
            string detPath = Path.Combine(modelsDir, "ch_PP-OCRv4_det_infer.onnx");
            string clsPath = Path.Combine(modelsDir, "ch_ppocr_mobile_v2.0_cls_infer.onnx");
            string recPath = Path.Combine(modelsDir, "ch_PP-OCRv4_rec_infer.onnx");
            string keysPath = Path.Combine(modelsDir, "ppocr_keys_v1.txt");
            
            // 测试图片路径
            string testImagePath = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrConsoleApp\\images\\test.jpg";
            
            if (!File.Exists(testImagePath))
            {
                Console.WriteLine($"测试图片不存在: {testImagePath}");
                return;
            }
            
            using (var ocrService = new OcrServiceOptimized())
            {
                try
                {
                    Console.WriteLine("初始化模型...");
                    ocrService.InitModels(detPath, clsPath, recPath, keysPath, 1);
                    
                    // 预热
                    Console.WriteLine("预热中...");
                    ocrService.DetectText(testImagePath);
                    
                    // 性能测试
                    int testCount = 10;
                    Console.WriteLine($"开始性能测试，连续识别 {testCount} 次...");
                    
                    var totalStopwatch = Stopwatch.StartNew();
                    long initialMemory = ocrService.GetMemoryUsage();
                    
                    for (int i = 0; i < testCount; i++)
                    {
                        var sw = Stopwatch.StartNew();
                        var textBlocks = ocrService.DetectTextBlocks(testImagePath);
                        sw.Stop();
                        
                        Console.WriteLine($"第 {i + 1} 次: {sw.ElapsedMilliseconds}ms, 识别到 {textBlocks.Count} 个文本块");
                        
                        // 每5次检查内存
                        if ((i + 1) % 5 == 0)
                        {
                            long currentMemory = ocrService.GetMemoryUsage();
                            Console.WriteLine($"  当前内存: {FormatBytes(currentMemory)}, 增长: {FormatBytes(currentMemory - initialMemory)}");
                            
                            // 检查并清理内存
                            ocrService.CheckAndCleanMemory(800);
                        }
                    }
                    
                    totalStopwatch.Stop();
                    
                    Console.WriteLine($"\n=== 性能测试结果 ===");
                    Console.WriteLine($"总耗时: {totalStopwatch.ElapsedMilliseconds}ms");
                    Console.WriteLine($"平均耗时: {totalStopwatch.ElapsedMilliseconds / testCount}ms");
                    Console.WriteLine($"初始内存: {FormatBytes(initialMemory)}");
                    Console.WriteLine($"最终内存: {FormatBytes(ocrService.GetMemoryUsage())}");
                    Console.WriteLine($"内存增长: {FormatBytes(ocrService.GetMemoryUsage() - initialMemory)}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"性能测试出错: {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// 运行内存压力测试
        /// </summary>
        public static void RunMemoryStressTest()
        {
            Console.WriteLine("=== 内存压力测试 ===");
            
            // 模型路径
            string modelsDir = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrOnnxForm\\models";
            string detPath = Path.Combine(modelsDir, "ch_PP-OCRv4_det_infer.onnx");
            string clsPath = Path.Combine(modelsDir, "ch_ppocr_mobile_v2.0_cls_infer.onnx");
            string recPath = Path.Combine(modelsDir, "ch_PP-OCRv4_rec_infer.onnx");
            string keysPath = Path.Combine(modelsDir, "ppocr_keys_v1.txt");
            
            // 测试图片路径
            string testImagePath = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrConsoleApp\\images\\test.jpg";
            
            if (!File.Exists(testImagePath))
            {
                Console.WriteLine($"测试图片不存在: {testImagePath}");
                return;
            }
            
            MemoryMonitor.StartMonitoring();
            
            using (var ocrService = new OcrServiceOptimized())
            {
                try
                {
                    Console.WriteLine("初始化模型...");
                    ocrService.InitModels(detPath, clsPath, recPath, keysPath, 1);
                    MemoryMonitor.Checkpoint("模型初始化完成");
                    
                    // 大量连续识别测试
                    int testCount = 100;
                    Console.WriteLine($"开始内存压力测试，连续识别 {testCount} 次...");
                    
                    for (int i = 0; i < testCount; i++)
                    {
                        var textBlocks = ocrService.DetectTextBlocks(testImagePath);
                        
                        if ((i + 1) % 20 == 0)
                        {
                            MemoryMonitor.Checkpoint($"完成 {i + 1} 次识别");
                            
                            // 检查内存泄漏
                            if (MemoryMonitor.CheckForMemoryLeak(100 * 1024 * 1024)) // 100MB阈值
                            {
                                Console.WriteLine($"警告: 在第 {i + 1} 次识别后检测到内存泄漏!");
                                ocrService.ForceMemoryCleanup();
                            }
                        }
                    }
                    
                    MemoryMonitor.CheckpointWithGC("压力测试完成");
                    Console.WriteLine("\n" + MemoryMonitor.GetDetailedReport());
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"内存压力测试出错: {ex.Message}");
                }
            }
        }
        
        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }
    }
}
