using System;
using System.IO;
using OcrLiteLib;
using System.Diagnostics;

namespace OcrConsoleApp
{
    /// <summary>
    /// 快速测试类，验证优化后的OCR功能
    /// </summary>
    public static class QuickTest
    {
        /// <summary>
        /// 运行快速测试
        /// </summary>
        public static void Run()
        {
            Console.WriteLine("=== 快速OCR测试 ===");
            
            // 模型路径
            string modelsDir = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrOnnxForm\\models";
            string detPath = Path.Combine(modelsDir, "ch_PP-OCRv4_det_infer.onnx");
            string clsPath = Path.Combine(modelsDir, "ch_ppocr_mobile_v2.0_cls_infer.onnx");
            string recPath = Path.Combine(modelsDir, "ch_PP-OCRv4_rec_infer.onnx");
            string keysPath = Path.Combine(modelsDir, "ppocr_keys_v1.txt");
            
            // 测试图片路径
            string testImagePath = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrConsoleApp\\images\\test.jpg";
            
            if (!File.Exists(testImagePath))
            {
                Console.WriteLine($"测试图片不存在: {testImagePath}");
                Console.WriteLine("请确保在images文件夹中有测试图片");
                return;
            }
            
            Console.WriteLine("开始内存优化...");
            
            // 内存优化设置
            try
            {
                System.Runtime.GCSettings.LatencyMode = System.Runtime.GCLatencyMode.Batch;
                System.Runtime.GCSettings.LargeObjectHeapCompactionMode = System.Runtime.GCLargeObjectHeapCompactionMode.CompactOnce;
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                Console.WriteLine($"优化完成，当前内存: {FormatBytes(GC.GetTotalMemory(false))}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"内存优化失败: {ex.Message}");
            }
            
            // 使用优化的OCR服务
            using (var ocrService = new OcrServiceOptimized())
            {
                try
                {
                    Console.WriteLine("\n开始初始化模型...");
                    long beforeInit = GC.GetTotalMemory(false);
                    var initStopwatch = Stopwatch.StartNew();
                    
                    ocrService.InitModels(detPath, clsPath, recPath, keysPath, 1);
                    
                    initStopwatch.Stop();
                    long afterInit = GC.GetTotalMemory(false);
                    
                    Console.WriteLine($"模型初始化完成:");
                    Console.WriteLine($"  耗时: {initStopwatch.ElapsedMilliseconds}ms");
                    Console.WriteLine($"  内存增长: {FormatBytes(afterInit - beforeInit)}");
                    Console.WriteLine($"  当前内存: {FormatBytes(afterInit)}");
                    
                    // 执行OCR识别
                    Console.WriteLine("\n开始OCR识别...");
                    var ocrStopwatch = Stopwatch.StartNew();

                    OcrServiceOptimized.PerformanceStats stats;
                    var textBlocks = ocrService.DetectTextBlocks(testImagePath, out stats);

                    ocrStopwatch.Stop();
                    
                    Console.WriteLine($"\nOCR识别完成:");
                    Console.WriteLine($"  总耗时: {stats.TotalDetectionTime}ms");
                    Console.WriteLine($"  - 预处理: {stats.PreprocessTime}ms");
                    Console.WriteLine($"  - 文本检测: {stats.DbNetTime}ms");
                    Console.WriteLine($"  - 角度分类: {stats.AngleNetTime}ms");
                    Console.WriteLine($"  - 文字识别: {stats.CrnnNetTime}ms");
                    Console.WriteLine($"  - 后处理: {stats.PostprocessTime}ms");
                    Console.WriteLine($"  内存变化: {FormatBytes(stats.MemoryAfter - stats.MemoryBefore)}");
                    Console.WriteLine($"  当前内存: {FormatBytes(stats.MemoryAfter)}");
                    Console.WriteLine($"  识别文本块数: {textBlocks.Count}");
                    
                    Console.WriteLine("\n识别结果:");
                    foreach (var block in textBlocks)
                    {
                        Console.WriteLine($"  文本: {block.Text}");
                        Console.WriteLine($"  位置: [{block.BoxPoints[0].X},{block.BoxPoints[0].Y}] -> [{block.BoxPoints[2].X},{block.BoxPoints[2].Y}]");
                        Console.WriteLine($"  置信度: {block.BoxScore:F3}");
                        Console.WriteLine();
                    }
                    
                    // 测试多次调用的内存稳定性
                    Console.WriteLine("测试内存稳定性（连续识别5次）...");
                    for (int i = 0; i < 5; i++)
                    {
                        long beforeCall = GC.GetTotalMemory(false);
                        var sw = Stopwatch.StartNew();
                        
                        var result = ocrService.DetectTextBlocks(testImagePath);
                        
                        sw.Stop();
                        long afterCall = GC.GetTotalMemory(false);
                        
                        Console.WriteLine($"  第{i + 1}次: {sw.ElapsedMilliseconds}ms, 内存变化: {FormatBytes(afterCall - beforeCall)}, 当前: {FormatBytes(afterCall)}");
                    }
                    
                    // 最终内存检查
                    ocrService.ForceMemoryCleanup();
                    long finalMemory = GC.GetTotalMemory(false);
                    Console.WriteLine($"\n最终内存清理后: {FormatBytes(finalMemory)}");
                    Console.WriteLine($"相比初始化后内存变化: {FormatBytes(finalMemory - afterInit)}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"OCR处理出错: {ex.Message}");
                    Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                }
            }
            
            // 最终垃圾回收
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            
            Console.WriteLine($"\n程序结束，最终内存: {FormatBytes(GC.GetTotalMemory(false))}");
        }
        
        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }
    }
}
