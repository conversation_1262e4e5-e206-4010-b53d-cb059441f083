﻿using Emgu.CV;
using Emgu.CV.CvEnum;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading;

namespace OcrLiteLib
{
    /// <summary>
    /// 专为OCR服务优化的类，移除所有不必要的功能
    /// </summary>
    public class OcrServiceOptimized : IDisposable
    {
        private DbNet dbNet;
        private AngleNet angleNet;
        private CrnnNet crnnNet;
        private bool disposed = false;
        private int _detectCount = 0;

        // 性能统计
        public class PerformanceStats
        {
            public long TotalDetectionTime { get; set; }
            public long DbNetTime { get; set; }
            public long AngleNetTime { get; set; }
            public long CrnnNetTime { get; set; }
            public long PreprocessTime { get; set; }
            public long PostprocessTime { get; set; }
            public int TextBlockCount { get; set; }
            public long MemoryBefore { get; set; }
            public long MemoryAfter { get; set; }
        }

        public OcrServiceOptimized()
        {
            dbNet = new DbNet();
            angleNet = new AngleNet();
            crnnNet = new CrnnNet();

            // 优化GC设置，减少频繁回收
            UnmanagedMemoryManager.OptimizeGCSettings();

            // 优化CPU设置
            OptimizeForPerformance();
        }

        /// <summary>
        /// 优化性能设置
        /// </summary>
        private void OptimizeForPerformance()
        {
            try
            {
                // 设置当前线程优先级
                Thread.CurrentThread.Priority = ThreadPriority.AboveNormal;

                // 建议ONNX Runtime使用更多线程
                Environment.SetEnvironmentVariable("OMP_NUM_THREADS", Environment.ProcessorCount.ToString());
                Environment.SetEnvironmentVariable("MKL_NUM_THREADS", Environment.ProcessorCount.ToString());
            }
            catch (Exception ex)
            {
                Console.WriteLine($"性能优化警告: {ex.Message}");
            }
        }

        ~OcrServiceOptimized()
        {
            Dispose(false);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing)
                {
                    dbNet?.Dispose();
                    angleNet?.Dispose();
                    crnnNet?.Dispose();

                    // 清理共享缓冲区
                    OcrUtils.ClearSharedBuffers();

                    // 强制释放非托管内存
                    UnmanagedMemoryManager.ForceReleaseUnmanagedMemory();

                    // 恢复GC设置
                    UnmanagedMemoryManager.RestoreGCSettings();
                }
                disposed = true;
            }
        }

        public void InitModels(string detPath, string clsPath, string recPath, string keysPath, int numThread)
        {
            try
            {
                Console.WriteLine("开始加载模型...");
                long beforeInit = GC.GetTotalMemory(false);

                Console.WriteLine("加载检测模型...");
                dbNet.InitModel(detPath, numThread);
                long afterDet = GC.GetTotalMemory(false);
                Console.WriteLine($"检测模型加载完成，内存增长: {FormatBytes(afterDet - beforeInit)}");

                Console.WriteLine("加载角度分类模型...");
                angleNet.InitModel(clsPath, numThread);
                long afterAngle = GC.GetTotalMemory(false);
                Console.WriteLine($"角度模型加载完成，内存增长: {FormatBytes(afterAngle - afterDet)}");

                Console.WriteLine("加载文字识别模型...");
                crnnNet.InitModel(recPath, keysPath, numThread);
                long afterCrnn = GC.GetTotalMemory(false);
                Console.WriteLine($"识别模型加载完成，内存增长: {FormatBytes(afterCrnn - afterAngle)}");

                Console.WriteLine($"所有模型加载完成，总内存增长: {FormatBytes(afterCrnn - beforeInit)}");

                // 模型加载后强制清理一次
                ForceMemoryCleanup();
                long afterCleanup = GC.GetTotalMemory(false);
                Console.WriteLine($"清理后内存: {FormatBytes(afterCleanup)}");
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message + ex.StackTrace);
                throw ex;
            }
        }

        /// <summary>
        /// 高性能OCR检测，只返回文本块信息
        /// </summary>
        /// <param name="img">图像路径</param>
        /// <returns>文本块列表</returns>
        public List<TextBlock> DetectTextBlocks(string img)
        {
            return DetectTextBlocks(img, 50, 1024, 0.5f, 0.3f, 2.0f, true, true);
        }

        /// <summary>
        /// 高性能OCR检测，返回文本块信息和性能统计
        /// </summary>
        /// <param name="img">图像路径</param>
        /// <param name="stats">性能统计信息</param>
        /// <returns>文本块列表</returns>
        public List<TextBlock> DetectTextBlocks(string img, out PerformanceStats stats)
        {
            return DetectTextBlocks(img, 50, 1024, 0.5f, 0.3f, 2.0f, true, true, out stats);
        }

        /// <summary>
        /// 高性能OCR检测，只返回文本块信息
        /// </summary>
        public List<TextBlock> DetectTextBlocks(string img, int padding, int maxSideLen, float boxScoreThresh, float boxThresh,
                              float unClipRatio, bool doAngle, bool mostAngle)
        {
            var stats = new PerformanceStats();
            return DetectTextBlocks(img, padding, maxSideLen, boxScoreThresh, boxThresh, unClipRatio, doAngle, mostAngle, out stats);
        }

        /// <summary>
        /// 高性能OCR检测，返回文本块信息和性能统计
        /// </summary>
        public List<TextBlock> DetectTextBlocks(string img, int padding, int maxSideLen, float boxScoreThresh, float boxThresh,
                              float unClipRatio, bool doAngle, bool mostAngle, out PerformanceStats stats)
        {
            stats = new PerformanceStats();
            var totalStopwatch = System.Diagnostics.Stopwatch.StartNew();
            stats.MemoryBefore = GC.GetTotalMemory(false);

            List<TextBlock> result;

            var preprocessStopwatch = System.Diagnostics.Stopwatch.StartNew();
            using (Mat originSrc = CvInvoke.Imread(img, ImreadModes.Color))
            {
                if (originSrc.IsEmpty)
                {
                    return new List<TextBlock>();
                }

                int originMaxSide = Math.Max(originSrc.Cols, originSrc.Rows);
                int resize = (maxSideLen <= 0 || maxSideLen > originMaxSide) ? originMaxSide : maxSideLen;
                resize += 2 * padding;

                Rectangle paddingRect = new Rectangle(padding, padding, originSrc.Cols, originSrc.Rows);
                using (Mat paddingSrc = OcrUtils.MakePadding(originSrc, padding))
                {
                    ScaleParam scale = ScaleParam.GetScaleParam(paddingSrc, resize);
                    preprocessStopwatch.Stop();
                    stats.PreprocessTime = preprocessStopwatch.ElapsedMilliseconds;

                    result = DetectTextBlocksOptimized(paddingSrc, paddingRect, scale, boxScoreThresh, boxThresh, unClipRatio, doAngle, mostAngle, stats);
                }
            }

            var postprocessStopwatch = System.Diagnostics.Stopwatch.StartNew();

            // 增加检测计数并智能内存管理
            _detectCount++;
            if (_detectCount % 20 == 0) // 减少到每20次检测才进行内存检查
            {
                UnmanagedMemoryManager.SmartMemoryCleanup(1500); // 1.5GB阈值
            }

            postprocessStopwatch.Stop();
            stats.PostprocessTime = postprocessStopwatch.ElapsedMilliseconds;

            totalStopwatch.Stop();
            stats.TotalDetectionTime = totalStopwatch.ElapsedMilliseconds;
            stats.MemoryAfter = GC.GetTotalMemory(false);
            stats.TextBlockCount = result.Count;

            return result;
        }

        /// <summary>
        /// 优化的文本检测核心方法
        /// </summary>
        private List<TextBlock> DetectTextBlocksOptimized(Mat src, Rectangle originRect, ScaleParam scale, float boxScoreThresh, float boxThresh,
                              float unClipRatio, bool doAngle, bool mostAngle, PerformanceStats stats)
        {
            // 获取文本框
            var dbNetStopwatch = System.Diagnostics.Stopwatch.StartNew();
            var textBoxes = dbNet.GetTextBoxes(src, scale, boxScoreThresh, boxThresh, unClipRatio);
            dbNetStopwatch.Stop();
            stats.DbNetTime = dbNetStopwatch.ElapsedMilliseconds;

            if (textBoxes.Count == 0)
            {
                return new List<TextBlock>();
            }

            // 获取文本区域图像
            List<Mat> partImages = OcrUtils.GetPartImages(src, textBoxes);
            List<Angle> angles = null;
            List<TextLine> textLines = null;

            try
            {
                // 角度检测
                var angleStopwatch = System.Diagnostics.Stopwatch.StartNew();
                angles = angleNet.GetAngles(partImages, doAngle, mostAngle);
                angleStopwatch.Stop();
                stats.AngleNetTime = angleStopwatch.ElapsedMilliseconds;

                // 旋转图像（就地处理，减少内存分配）
                for (int i = 0; i < partImages.Count; ++i)
                {
                    if (angles[i].Index == 1)
                    {
                        Mat original = partImages[i];
                        partImages[i] = OcrUtils.MatRotateClockWise180(original);
                        original.Dispose(); // 立即释放原图像
                    }
                }

                // 文本识别
                var crnnStopwatch = System.Diagnostics.Stopwatch.StartNew();
                textLines = crnnNet.GetTextLines(partImages);
                crnnStopwatch.Stop();
                stats.CrnnNetTime = crnnStopwatch.ElapsedMilliseconds;

                // 创建TextBlock列表
                List<TextBlock> textBlocks = new List<TextBlock>(textLines.Count);
                for (int i = 0; i < textLines.Count; ++i)
                {
                    // 调整坐标到原始图像坐标系
                    List<Point> adjustedPoints = new List<Point>(4);
                    foreach (var point in textBoxes[i].Points)
                    {
                        adjustedPoints.Add(new Point(
                            point.X - originRect.X, 
                            point.Y - originRect.Y
                        ));
                    }
                    
                    TextBlock textBlock = new TextBlock
                    {
                        BoxPoints = adjustedPoints,
                        BoxScore = textBoxes[i].Score,
                        AngleIndex = angles[i].Index,
                        AngleScore = angles[i].Score,
                        AngleTime = angles[i].Time,
                        Text = textLines[i].Text,
                        CharScores = textLines[i].CharScores,
                        CrnnTime = textLines[i].Time,
                        BlockTime = angles[i].Time + textLines[i].Time
                    };
                    textBlocks.Add(textBlock);
                }

                return textBlocks;
            }
            finally
            {
                // 确保所有Mat对象都被释放
                if (partImages != null)
                {
                    foreach (var mat in partImages)
                    {
                        mat?.Dispose();
                    }
                    partImages.Clear();
                }
                
                // 清理其他引用
                angles?.Clear();
                textLines?.Clear();
            }
        }

        /// <summary>
        /// 只返回文本字符串的简化方法
        /// </summary>
        /// <param name="img">图像路径</param>
        /// <returns>识别的文本</returns>
        public string DetectText(string img)
        {
            var textBlocks = DetectTextBlocks(img);
            if (textBlocks.Count == 0)
            {
                return string.Empty;
            }
            
            return string.Join("\n", textBlocks.Select(x => x.Text).Where(x => !string.IsNullOrWhiteSpace(x)));
        }

        /// <summary>
        /// 强制清理内存（包括非托管内存）
        /// </summary>
        public void ForceMemoryCleanup()
        {
            // 清理共享缓冲区
            OcrUtils.ClearSharedBuffers();

            // 强制释放非托管内存
            UnmanagedMemoryManager.ForceReleaseUnmanagedMemory();
        }

        /// <summary>
        /// 获取当前内存使用情况
        /// </summary>
        /// <returns>内存使用量（字节）</returns>
        public long GetMemoryUsage()
        {
            return UnmanagedMemoryManager.GetMemoryInfo().WorkingSet;
        }

        /// <summary>
        /// 获取详细内存信息
        /// </summary>
        /// <returns>内存信息</returns>
        public MemoryInfo GetDetailedMemoryInfo()
        {
            return UnmanagedMemoryManager.GetMemoryInfo();
        }

        /// <summary>
        /// 检查内存使用是否过高并自动清理
        /// </summary>
        /// <param name="thresholdMB">阈值（MB）</param>
        public void CheckAndCleanMemory(int thresholdMB = 800)
        {
            long currentMemory = GC.GetTotalMemory(false);
            long thresholdBytes = (long)thresholdMB * 1024 * 1024;

            if (currentMemory > thresholdBytes)
            {
                ForceMemoryCleanup();
            }
        }

        /// <summary>
        /// 格式化字节数为可读字符串
        /// </summary>
        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;

            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }

            return $"{number:n1} {suffixes[counter]}";
        }
    }
}
