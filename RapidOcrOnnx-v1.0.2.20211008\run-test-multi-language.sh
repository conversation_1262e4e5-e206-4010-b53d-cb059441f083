#!/usr/bin/env bash

sysOS=`uname -s`
NUM_THREADS=1
if [ $sysOS == "Darwin" ];then
    #echo "I'm MacOS"
    NUM_THREADS=$(sysctl -n hw.ncpu)
elif [ $sysOS == "Linux" ];then
    #echo "I'm Linux"
    NUM_THREADS=$(grep ^processor /proc/cpuinfo | wc -l)
else
    echo "Other OS: $sysOS"
fi

echo "Setting the Number of Threads=$NUM_THREADS Using an OpenMP Environment Variable"
set OMP_NUM_THREADS=$NUM_THREADS

echo "请选择det模型: 1)server, 2)mobile"
read -p "" DET_MODEL
if [ $DET_MODEL == 1 ]; then
    DET_MODEL="ch_ppocr_server_v2.0_det_infer.onnx"
elif [ $DET_MODEL == 2 ]; then
    DET_MODEL="ch_ppocr_mobile_v2.0_det_infer.onnx"
else
  echo -e "Input Error!"
fi

echo "请选择语言: 1)英文en, 2)法文french, 3)德文german(x), 4)韩文korean, 5)日文japan"
echo "请选择语言: 6)意大利文it, 7)西班牙文xi(x), 8)葡萄牙文pu, 9)俄罗斯文ru, 10)阿拉伯文ar"
echo "请选择语言: 11)印地文hi, 12)繁体中文cht, 13)维吾尔文ug, 14)波斯文fa, 15)乌尔都文ur"
echo "请选择语言: 16)塞尔维亚文(latin)rs, 17)欧西坦文oc, 18)马拉地文mr, 19)尼泊尔文ne, 20)塞尔维亚文(cyrillic)rsc"
echo "请选择语言: 21)保加利亚文bg, 22)乌克兰文uk, 23)白俄罗斯文be, 24)泰卢固文te, 25)卡纳达文ka, 26)泰米尔文ta"
read -p "" REC_LANG
if [ $REC_LANG == 1 ]; then
    REC_LANG=en
    REC_MODEL=en_number_mobile_v2.0_rec_infer.onnx
    KEYS=en_dict.txt
elif [ $REC_LANG == 2 ]; then
    REC_LANG=french
    REC_MODEL=french_mobile_v2.0_rec_infer.onnx
    KEYS=french_dict.txt
elif [ $REC_LANG == 3 ]; then
    REC_LANG=german
    REC_MODEL=german_mobile_v2.0_rec_infer.onnx
    KEYS=german_dict.txt
elif [ $REC_LANG == 4 ]; then
    REC_LANG=korean
    REC_MODEL=korean_mobile_v2.0_rec_infer.onnx
    KEYS=korean_dict.txt
elif [ $REC_LANG == 5 ]; then
    REC_LANG=japan
    REC_MODEL=japan_mobile_v2.0_rec_infer.onnx
    KEYS=japan_dict.txt
elif [ $REC_LANG == 6 ]; then
    REC_LANG=it
    REC_MODEL=it_mobile_v2.0_rec_infer.onnx
    KEYS=it_dict.txt
elif [ $REC_LANG == 7 ]; then
    REC_LANG=xi
    REC_MODEL=xi_mobile_v2.0_rec_infer.onnx
    KEYS=xi_dict.txt
elif [ $REC_LANG == 8 ]; then
    REC_LANG=pu
    REC_MODEL=pu_mobile_v2.0_rec_infer.onnx
    KEYS=pu_dict.txt
elif [ $REC_LANG == 9 ]; then
    REC_LANG=ru
    REC_MODEL=ru_mobile_v2.0_rec_infer.onnx
    KEYS=ru_dict.txt
elif [ $REC_LANG == 10 ]; then
    REC_LANG=ar
    REC_MODEL=ar_mobile_v2.0_rec_infer.onnx
    KEYS=ar_dict.txt
elif [ $REC_LANG == 11 ]; then
    REC_LANG=hi
    REC_MODEL=hi_mobile_v2.0_rec_infer.onnx
    KEYS=hi_dict.txt
elif [ $REC_LANG == 12 ]; then
    REC_LANG=chinese_cht
    REC_MODEL=chinese_cht_mobile_v2.0_rec_infer.onnx
    KEYS=chinese_cht_dict.txt
elif [ $REC_LANG == 13 ]; then
    REC_LANG=ug
    REC_MODEL=ug_mobile_v2.0_rec_infer.onnx
    KEYS=ug_dict.txt
elif [ $REC_LANG == 14 ]; then
    REC_LANG=fa
    REC_MODEL=fa_mobile_v2.0_rec_infer.onnx
    KEYS=fa_dict.txt
elif [ $REC_LANG == 15 ]; then
    REC_LANG=ur
    REC_MODEL=ur_mobile_v2.0_rec_infer.onnx
    KEYS=ur_dict.txt
elif [ $REC_LANG == 16 ]; then
    REC_LANG=rs
    REC_MODEL=rs_mobile_v2.0_rec_infer.onnx
    KEYS=rs_dict.txt
elif [ $REC_LANG == 17 ]; then
    REC_LANG=oc
    REC_MODEL=oc_mobile_v2.0_rec_infer.onnx
    KEYS=oc_dict.txt
elif [ $REC_LANG == 18 ]; then
    REC_LANG=mr
    REC_MODEL=mr_mobile_v2.0_rec_infer.onnx
    KEYS=mr_dict.txt
elif [ $REC_LANG == 19 ]; then
    REC_LANG=ne
    REC_MODEL=ne_mobile_v2.0_rec_infer.onnx
    KEYS=ne_dict.txt
elif [ $REC_LANG == 20 ]; then
    REC_LANG=rsc
    REC_MODEL=rsc_mobile_v2.0_rec_infer.onnx
    KEYS=rsc_dict.txt
elif [ $REC_LANG == 21 ]; then
    REC_LANG=bg
    REC_MODEL=bg_mobile_v2.0_rec_infer.onnx
    KEYS=bg_dict.txt
elif [ $REC_LANG == 22 ]; then
    REC_LANG=uk
    REC_MODEL=uk_mobile_v2.0_rec_infer.onnx
    KEYS=uk_dict.txt
elif [ $REC_LANG == 23 ]; then
    REC_LANG=be
    REC_MODEL=be_mobile_v2.0_rec_infer.onnx
    KEYS=be_dict.txt
elif [ $REC_LANG == 24 ]; then
    REC_LANG=te
    REC_MODEL=te_mobile_v2.0_rec_infer.onnx
    KEYS=te_dict.txt
elif [ $REC_LANG == 25 ]; then
    REC_LANG=ka
    REC_MODEL=ka_mobile_v2.0_rec_infer.onnx
    KEYS=ka_dict.txt
elif [ $REC_LANG == 26 ]; then
    REC_LANG=ta
    REC_MODEL=ta_mobile_v2.0_rec_infer.onnx
    KEYS=ta_dict.txt
else
  echo -e "Input Error!"
fi

TARGET_IMG=images/${REC_LANG}.jpg
if [ ! -f "$TARGET_IMG" ]; then
echo "找不到待识别的目标图片：${TARGET_IMG}，请打开本文件并编辑TARGET_IMG"
exit
fi

##### run test on MacOS or Linux
./${sysOS}/BaiPiaoOcrOnnx --version
./${sysOS}/BaiPiaoOcrOnnx --models models \
--det $DET_MODEL \
--cls ch_ppocr_mobile_v2.0_cls_infer.onnx \
--rec $REC_MODEL \
--keys $KEYS \
--image $TARGET_IMG \
--numThread $NUM_THREADS \
--padding 50 \
--maxSideLen 1024 \
--boxScoreThresh 0.5 \
--boxThresh 0.3 \
--unClipRatio 1.6 \
--doAngle 0 \
--mostAngle 0

#cls模型只能支持中文，所以此处关闭doAngle
