chcp 65001
:: Set Param
@ECHO OFF
@SETLOCAL
echo "Setting the Number of Threads=%NUMBER_OF_PROCESSORS% Using an OpenMP Environment Variable"
set OMP_NUM_THREADS=%NUMBER_OF_PROCESSORS%

:MainExec
echo "请输入测试选项并回车: 1)x64, 2)x86"
set /p flag=
if %flag% == 1 (set EXE_PATH=win-x64)^
else if %flag% == 2 (set EXE_PATH=win-x86)^
else (echo 输入错误！Input Error!)

echo "请选择det模型: 1)server, 2)mobile, 3)mobileV2"
set /p flag=
if %flag% == 1 (set DET_MODEL=ch_PP-OCRv4_det_server_infer.onnx)^
else if %flag% == 2 (set DET_MODEL=ch_ppocr_mobile_v2.0_det_infer.onnx)^
else if %flag% == 3 (set DET_MODEL=ch_PP-OCRv2_det_infer.onnx)^
else (echo 输入错误！Input Error!)

echo "请选择rec模型: 1)server, 2)mobile"
set /p flag=
if %flag% == 1 (set REC_MODEL=ch_PP-OCRv4_rec_server_infer.onnx)^
else if %flag% == 2 (set REC_MODEL=ch_ppocr_mobile_v2.0_rec_infer.onnx)^
else (echo 输入错误！Input Error!)

SET TARGET_IMG=images/1111.jpg
if not exist %TARGET_IMG% (
echo "找不到待识别的目标图片：%TARGET_IMG%，请打开本文件并编辑TARGET_IMG"
PAUSE
exit
)

:: run Windows
%EXE_PATH%\BaiPiaoOcrOnnx.exe --version
%EXE_PATH%\BaiPiaoOcrOnnx.exe --models models ^
--det %DET_MODEL% ^
--cls ch_ppocr_mobile_v2.0_cls_infer.onnx ^
--rec %REC_MODEL% ^
--keys ppocr_keys_v1.txt ^
--image %TARGET_IMG% ^
--numThread %NUMBER_OF_PROCESSORS% ^
--padding 0 ^
--maxSideLen 1024 ^
--boxScoreThresh 0.5 ^
--boxThresh 0.3 ^
--unClipRatio 1.5 ^
--doAngle 1 ^
--mostAngle 1

echo.
GOTO:MainExec

@ENDLOCAL
