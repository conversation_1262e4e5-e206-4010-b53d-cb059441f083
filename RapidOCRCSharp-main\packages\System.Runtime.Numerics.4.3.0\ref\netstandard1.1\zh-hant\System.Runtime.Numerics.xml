﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.Numerics</name>
  </assembly>
  <members>
    <member name="T:System.Numerics.BigInteger">
      <summary>表示任意大、帶正負號的整數。</summary>
    </member>
    <member name="M:System.Numerics.BigInteger.#ctor(System.Byte[])">
      <summary>使用位元組陣列中的值，初始化 <see cref="T:System.Numerics.BigInteger" /> 結構的新執行個體。</summary>
      <param name="value">位元組由小到大順序的位元組值陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.#ctor(System.Decimal)">
      <summary>使用 <see cref="T:System.Decimal" /> 值，初始化 <see cref="T:System.Numerics.BigInteger" /> 結構的新執行個體。</summary>
      <param name="value">十進位數字。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.#ctor(System.Double)">
      <summary>使用雙精確度浮點值，初始化 <see cref="T:System.Numerics.BigInteger" /> 結構的新執行個體。</summary>
      <param name="value">雙精確度浮點值。</param>
      <exception cref="T:System.OverflowException">The value of <paramref name="value" /> is <see cref="F:System.Double.NaN" />.-or-The value of <paramref name="value" /> is <see cref="F:System.Double.NegativeInfinity" />.-or-The value of <paramref name="value" /> is <see cref="F:System.Double.PositiveInfinity" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.#ctor(System.Int32)">
      <summary>使用 32 位元帶正負號的整數值，初始化 <see cref="T:System.Numerics.BigInteger" /> 結構的新執行個體。</summary>
      <param name="value">32 位元帶正負號的整數。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.#ctor(System.Int64)">
      <summary>使用 64 位元帶正負號的整數值，初始化 <see cref="T:System.Numerics.BigInteger" /> 結構的新執行個體。</summary>
      <param name="value">64 位元帶正負號的整數。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.#ctor(System.Single)">
      <summary>使用單精確度浮點值，初始化 <see cref="T:System.Numerics.BigInteger" /> 結構的新執行個體。</summary>
      <param name="value">單精確度浮點值。</param>
      <exception cref="T:System.OverflowException">The value of <paramref name="value" /> is <see cref="F:System.Single.NaN" />.-or-The value of <paramref name="value" /> is <see cref="F:System.Single.NegativeInfinity" />.-or-The value of <paramref name="value" /> is <see cref="F:System.Single.PositiveInfinity" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.#ctor(System.UInt32)">
      <summary>使用不帶正負號的 32 位元整數值，初始化 <see cref="T:System.Numerics.BigInteger" /> 結構的新執行個體。</summary>
      <param name="value">32 位元不帶正負號的整數值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.#ctor(System.UInt64)">
      <summary>使用不帶正負號的 64 位元整數值，初始化 <see cref="T:System.Numerics.BigInteger" /> 結構的新執行個體。</summary>
      <param name="value">不帶正負號的 64 位元整數。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.Abs(System.Numerics.BigInteger)">
      <summary>取得 <see cref="T:System.Numerics.BigInteger" /> 物件的絕對值。</summary>
      <returns>
        <paramref name="value" /> 的絕對值。</returns>
      <param name="value">數字。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.Add(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>兩個 <see cref="T:System.Numerics.BigInteger" /> 值相加，並傳回結果。</summary>
      <returns>
        <paramref name="left" /> 和 <paramref name="right" /> 的總和。</returns>
      <param name="left">要相加的第一個值。</param>
      <param name="right">要相加的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.Compare(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>比較兩個 <see cref="T:System.Numerics.BigInteger" /> 值並傳回整數，這個整數表示第一個值小於、等於或大於第二個值。</summary>
      <returns>帶正負號的整數，表示 <paramref name="left" /> 和 <paramref name="right" /> 的相對值，如下表所示。值條件小於零<paramref name="left" /> 小於 <paramref name="right" />。零<paramref name="left" /> 等於 <paramref name="right" />。大於零<paramref name="left" /> 大於 <paramref name="right" />。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.CompareTo(System.Int64)">
      <summary>比較這個執行個體與帶正負號的 64 位元整數，並且傳回整數，這個整數表示這個執行個體的值小於、等於或大於帶正負號 64 位元整數的值。</summary>
      <returns>帶正負號的整數值，表示這個執行個體與 <paramref name="other" /> 的關聯性，如下表所示。傳回值描述小於零目前的執行個體小於 <paramref name="other" />。零目前的執行個體等於 <paramref name="other" />。大於零目前的執行個體大於 <paramref name="other" />。</returns>
      <param name="other">要比較的帶正負號 64 位元整數。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.CompareTo(System.Numerics.BigInteger)">
      <summary>比較這個執行個體與第二個 <see cref="T:System.Numerics.BigInteger" /> ，並且傳回整數，這個整數表示這個執行個體的值小於、等於或大於指定之物件的值。</summary>
      <returns>帶正負號的整數值，表示這個執行個體與 <paramref name="other" /> 的關聯性，如下表所示。傳回值描述小於零目前的執行個體小於 <paramref name="other" />。零目前的執行個體等於 <paramref name="other" />。大於零目前的執行個體大於 <paramref name="other" />。</returns>
      <param name="other">要比較的物件。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.CompareTo(System.UInt64)">
      <summary>比較這個執行個體與不帶正負號的 64 位元整數，並且傳回整數，這個整數表示這個執行個體的值小於、等於或大於不帶正負號 64 位元整數的值。</summary>
      <returns>帶正負號的整數，表示這個執行個體與 <paramref name="other" /> 的相對值，如下表所示。傳回值描述小於零目前的執行個體小於 <paramref name="other" />。零目前的執行個體等於 <paramref name="other" />。大於零目前的執行個體大於 <paramref name="other" />。</returns>
      <param name="other">要比較的不帶正負號 64 位元整數。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.Divide(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>某個 <see cref="T:System.Numerics.BigInteger" /> 值除以另一個值，並且傳回結果。</summary>
      <returns>相除的商數。</returns>
      <param name="dividend">做為被除數的值。</param>
      <param name="divisor">做為除數的值。</param>
      <exception cref="T:System.DivideByZeroException">
        <paramref name="divisor" /> is 0 (zero).</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.DivRem(System.Numerics.BigInteger,System.Numerics.BigInteger,System.Numerics.BigInteger@)">
      <summary>某個 <see cref="T:System.Numerics.BigInteger" /> 值除以另一個值，傳回結果，並在輸出參數中傳回餘數。</summary>
      <returns>相除的商數。</returns>
      <param name="dividend">做為被除數的值。</param>
      <param name="divisor">做為除數的值。</param>
      <param name="remainder">當這個方法傳回時，會包含表示相除餘數的 <see cref="T:System.Numerics.BigInteger" /> 值。這個參數會以未初始化的狀態傳遞。</param>
      <exception cref="T:System.DivideByZeroException">
        <paramref name="divisor" /> is 0 (zero).</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Equals(System.Int64)">
      <summary>傳回值，這個值表示目前執行個體與帶正負號的 64 位元整數是否有相同的值。</summary>
      <returns>如果帶正負號的 64 位元整數與目前執行個體有相同的值，則為 true，否則為 false。</returns>
      <param name="other">要比較的帶正負號 64 位元整數值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.Equals(System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示目前執行個體與指定的 <see cref="T:System.Numerics.BigInteger" /> 物件是否有相同的值。</summary>
      <returns>如果這個 <see cref="T:System.Numerics.BigInteger" /> 物件與 <paramref name="other" /> 有相同的值，則為 true，否則為 false。</returns>
      <param name="other">要比較的物件。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.Equals(System.Object)">
      <summary>傳回值，這個值表示目前執行個體與指定的物件是否有相同的值。</summary>
      <returns>如果 <paramref name="obj" /> 參數為 <see cref="T:System.Numerics.BigInteger" /> 物件或能夠隱含轉換為 <see cref="T:System.Numerics.BigInteger" /> 值的類型，而且它的值等於目前 <see cref="T:System.Numerics.BigInteger" /> 物件的值，則為 true，否則為 false。</returns>
      <param name="obj">要比較的物件。 </param>
    </member>
    <member name="M:System.Numerics.BigInteger.Equals(System.UInt64)">
      <summary>傳回值，這個值表示目前執行個體與不帶正負號的 64 位元整數是否有相同的值。</summary>
      <returns>如果目前執行個體與不帶正負號的 64 位元整數有相同的值，則為 true，否則為 false。</returns>
      <param name="other">要比較的不帶正負號 64 位元整數。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.GetHashCode">
      <summary>傳回目前 <see cref="T:System.Numerics.BigInteger" /> 物件的雜湊碼。</summary>
      <returns>32 位元帶正負號的整數雜湊碼。</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.GreatestCommonDivisor(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>求兩個 <see cref="T:System.Numerics.BigInteger" /> 值的最大公因數。</summary>
      <returns>
        <paramref name="left" /> 和 <paramref name="right" /> 的最大公因數。</returns>
      <param name="left">第一個值。</param>
      <param name="right">第二個值。</param>
    </member>
    <member name="P:System.Numerics.BigInteger.IsEven">
      <summary>表示目前 <see cref="T:System.Numerics.BigInteger" /> 物件的值是否為偶數。</summary>
      <returns>如果 <see cref="T:System.Numerics.BigInteger" /> 物件的值為偶數，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Numerics.BigInteger.IsOne">
      <summary>表示目前 <see cref="T:System.Numerics.BigInteger" /> 物件的值是否為 <see cref="P:System.Numerics.BigInteger.One" />。</summary>
      <returns>如果 <see cref="T:System.Numerics.BigInteger" /> 物件的值為 <see cref="P:System.Numerics.BigInteger.One" />，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Numerics.BigInteger.IsPowerOfTwo">
      <summary>表示目前 <see cref="T:System.Numerics.BigInteger" /> 物件的值是否為二乘冪。</summary>
      <returns>如果 <see cref="T:System.Numerics.BigInteger" /> 物件的值為二乘冪，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Numerics.BigInteger.IsZero">
      <summary>表示目前 <see cref="T:System.Numerics.BigInteger" /> 物件的值是否為 <see cref="P:System.Numerics.BigInteger.Zero" />。</summary>
      <returns>如果 <see cref="T:System.Numerics.BigInteger" /> 物件的值為 <see cref="P:System.Numerics.BigInteger.Zero" />，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.Log(System.Numerics.BigInteger)">
      <summary>傳回指定數字的自然 (底數為 e) 對數。</summary>
      <returns>
        <paramref name="value" /> 的自然對數 (以 e 為底數)，如＜備註＞一節中的表格所示。</returns>
      <param name="value">要找出其對數的數字。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The natural log of <paramref name="value" /> is out of range of the <see cref="T:System.Double" /> data type.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Log(System.Numerics.BigInteger,System.Double)">
      <summary>傳回指定底數中指定數字的對數。</summary>
      <returns>
        <paramref name="value" /> 的以 <paramref name="baseValue" /> 為底數的對數，如＜備註＞一節中的表格所示。</returns>
      <param name="value">要找出其對數的數字。</param>
      <param name="baseValue">對數的底數。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The log of <paramref name="value" /> is out of range of the <see cref="T:System.Double" /> data type.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Log10(System.Numerics.BigInteger)">
      <summary>傳回指定數字的以 10 為底數的對數。</summary>
      <returns>
        <paramref name="value" /> 的以 10 為底數的對數，如＜備註＞一節中的表格所示。</returns>
      <param name="value">要找出其對數的數字。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The base 10 log of <paramref name="value" /> is out of range of the <see cref="T:System.Double" /> data type.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Max(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>傳回兩個 <see cref="T:System.Numerics.BigInteger" /> 值的較大值。</summary>
      <returns>
        <paramref name="left" /> 或 <paramref name="right" /> 參數 (取其較大者)。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.Min(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>傳回兩個 <see cref="T:System.Numerics.BigInteger" /> 值的較小值。</summary>
      <returns>
        <paramref name="left" /> 或 <paramref name="right" /> 參數 (取其較小者)。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="P:System.Numerics.BigInteger.MinusOne">
      <summary>取得表示數字負一 (-1) 的值。</summary>
      <returns>值為負一 (-1) 的整數。</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.ModPow(System.Numerics.BigInteger,System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>一個數目自乘至另一個數目的乘冪後，執行模數除法。</summary>
      <returns>
        <paramref name="value" />exponent 除以 <paramref name="modulus" /> 後的餘數。</returns>
      <param name="value">具有乘冪數 <paramref name="exponent" /> 的數字。</param>
      <param name="exponent">
        <paramref name="value" /> 的乘冪指數。</param>
      <param name="modulus">要除以具有乘冪數 <paramref name="exponent" /> 之 <paramref name="value" /> 的數字。</param>
      <exception cref="T:System.DivideByZeroException">
        <paramref name="modulus" /> is zero.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="exponent" /> is negative.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Multiply(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>傳回兩個 <see cref="T:System.Numerics.BigInteger" /> 值的乘積。</summary>
      <returns>
        <paramref name="left" /> 和 <paramref name="right" /> 參數的乘積。</returns>
      <param name="left">要相乘的第一個數字。</param>
      <param name="right">要相乘的第二個數字。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.Negate(System.Numerics.BigInteger)">
      <summary>將指定的 <see cref="T:System.Numerics.BigInteger" /> 值變換正負號。</summary>
      <returns>
        <paramref name="value" /> 參數乘以負一 (-1) 的結果。</returns>
      <param name="value">要變換正負號的值。</param>
    </member>
    <member name="P:System.Numerics.BigInteger.One">
      <summary>取得表示數字一 (1) 的值。</summary>
      <returns>值為一 (1) 的物件。</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Addition(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>兩個指定之 <see cref="T:System.Numerics.BigInteger" /> 物件的值相加。</summary>
      <returns>
        <paramref name="left" /> 和 <paramref name="right" /> 的總和。</returns>
      <param name="left">要相加的第一個值。</param>
      <param name="right">要相加的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_BitwiseAnd(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>對兩個 <see cref="T:System.Numerics.BigInteger" /> 值執行位元 And 運算。</summary>
      <returns>位元 And 運算的結果。</returns>
      <param name="left">第一個值。</param>
      <param name="right">第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_BitwiseOr(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>對兩個 <see cref="T:System.Numerics.BigInteger" /> 值執行位元 Or 運算。</summary>
      <returns>位元 Or 運算的結果。</returns>
      <param name="left">第一個值。</param>
      <param name="right">第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Decrement(System.Numerics.BigInteger)">
      <summary>
        <see cref="T:System.Numerics.BigInteger" /> 值遞減 1。</summary>
      <returns>
        <paramref name="value" /> 參數遞減 1 後的值。</returns>
      <param name="value">要遞減的值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Division(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>使用整數除法，將指定的 <see cref="T:System.Numerics.BigInteger" /> 值除以另一個指定的 <see cref="T:System.Numerics.BigInteger" /> 值。</summary>
      <returns>相除的整數結果。</returns>
      <param name="dividend">做為被除數的值。</param>
      <param name="divisor">做為除數的值。</param>
      <exception cref="T:System.DivideByZeroException">
        <paramref name="divisor" /> is 0 (zero).</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Equality(System.Int64,System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示帶正負號長整數值與 <see cref="T:System.Numerics.BigInteger" /> 值是否相等。</summary>
      <returns>如果 <paramref name="left" /> 與 <paramref name="right" /> 參數有相同的值，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Equality(System.Numerics.BigInteger,System.Int64)">
      <summary>傳回值，這個值表示 <see cref="T:System.Numerics.BigInteger" /> 值與帶正負號長整數值是否相等。</summary>
      <returns>如果 <paramref name="left" /> 與 <paramref name="right" /> 參數有相同的值，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Equality(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示兩個 <see cref="T:System.Numerics.BigInteger" /> 物件的值是否相等。</summary>
      <returns>如果 <paramref name="left" /> 與 <paramref name="right" /> 參數有相同的值，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Equality(System.Numerics.BigInteger,System.UInt64)">
      <summary>傳回值，這個值表示 <see cref="T:System.Numerics.BigInteger" /> 值與不帶正負號長整數值是否相等。</summary>
      <returns>如果 <paramref name="left" /> 與 <paramref name="right" /> 參數有相同的值，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Equality(System.UInt64,System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示不帶正負號長整數值與 <see cref="T:System.Numerics.BigInteger" /> 值是否相等。</summary>
      <returns>如果 <paramref name="left" /> 與 <paramref name="right" /> 參數有相同的值，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_ExclusiveOr(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>對兩個 <see cref="T:System.Numerics.BigInteger" /> 值執行位元互斥 Or (XOr) 運算。</summary>
      <returns>位元 Or 運算的結果。</returns>
      <param name="left">第一個值。</param>
      <param name="right">第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Decimal)~System.Numerics.BigInteger">
      <summary>定義從 <see cref="T:System.Decimal" /> 物件到 <see cref="T:System.Numerics.BigInteger" /> 值的明確轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為 <see cref="T:System.Numerics.BigInteger" /> 的值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Double)~System.Numerics.BigInteger">
      <summary>定義從 <see cref="T:System.Double" /> 值到 <see cref="T:System.Numerics.BigInteger" /> 值的明確轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為 <see cref="T:System.Numerics.BigInteger" /> 的值。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is <see cref="F:System.Double.NaN" />.-or-<paramref name="value" /> is <see cref="F:System.Double.PositiveInfinity" />.-or-<paramref name="value" /> is <see cref="F:System.Double.NegativeInfinity" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.Int16">
      <summary>定義從 <see cref="T:System.Numerics.BigInteger" /> 物件到 16 位元帶正負號整數值的明確轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為 16 位元帶正負號整數的值。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.Int16.MinValue" />.-or-<paramref name="value" /> is greater than <see cref="F:System.Int16.MaxValue" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.Decimal">
      <summary>定義從 <see cref="T:System.Numerics.BigInteger" /> 物件到 <see cref="T:System.Decimal" /> 值的明確轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為 <see cref="T:System.Decimal" /> 的值。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.Decimal.MinValue" />.-or-<paramref name="value" /> is greater than <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.Double">
      <summary>定義從 <see cref="T:System.Numerics.BigInteger" /> 物件到 <see cref="T:System.Double" /> 值的明確轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為 <see cref="T:System.Double" /> 的值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.Byte">
      <summary>定義從 <see cref="T:System.Numerics.BigInteger" /> 物件到不帶正負號位元組值的明確轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為 <see cref="T:System.Byte" /> 的值。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.Byte.MinValue" />. -or-<paramref name="value" /> is greater than <see cref="F:System.Byte.MaxValue" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.UInt64">
      <summary>定義從 <see cref="T:System.Numerics.BigInteger" /> 物件到不帶正負號 64 位元整數值的明確轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為不帶正負號 64 位元整數的值。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.UInt64.MinValue" />.-or-<paramref name="value" /> is greater than <see cref="F:System.UInt64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.Int32">
      <summary>定義從 <see cref="T:System.Numerics.BigInteger" /> 物件到 32 位元帶正負號整數值的明確轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為 32 位元帶正負號整數的值。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.Int32.MinValue" />.-or-<paramref name="value" /> is greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.SByte">
      <summary>定義從 <see cref="T:System.Numerics.BigInteger" /> 物件到帶正負號 8 位元值的明確轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為帶正負號 8 位元值的值。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.SByte.MinValue" />.-or-<paramref name="value" /> is greater than <see cref="F:System.SByte.MaxValue" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.Int64">
      <summary>定義從 <see cref="T:System.Numerics.BigInteger" /> 物件到 64 位元帶正負號整數值的明確轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為 64 位元帶正負號整數的值。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.Int64.MinValue" />.-or-<paramref name="value" /> is greater than <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.Single">
      <summary>定義從 <see cref="T:System.Numerics.BigInteger" /> 物件到單精確度浮點值的明確轉換。</summary>
      <returns>物件，包含最接近 <paramref name="value" /> 參數的可能表示。</returns>
      <param name="value">要轉換為單精確度浮點值的值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.UInt32">
      <summary>定義從 <see cref="T:System.Numerics.BigInteger" /> 物件到不帶正負號 32 位元整數值的明確轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為不帶正負號 32 位元整數的值。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.UInt32.MinValue" />.-or-<paramref name="value" /> is greater than <see cref="F:System.UInt32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.UInt16">
      <summary>定義從 <see cref="T:System.Numerics.BigInteger" /> 物件到不帶正負號 16 位元整數值的明確轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為不帶正負號 16 位元整數的值。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is less than <see cref="F:System.UInt16.MinValue" />.-or-<paramref name="value" /> is greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Single)~System.Numerics.BigInteger">
      <summary>定義從 <see cref="T:System.Single" /> 物件到 <see cref="T:System.Numerics.BigInteger" /> 值的明確轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為 <see cref="T:System.Numerics.BigInteger" /> 的值。</param>
      <exception cref="T:System.OverflowException">
        <paramref name="value" /> is <see cref="F:System.Single.NaN" />.-or-<paramref name="value" /> is <see cref="F:System.Single.PositiveInfinity" />.-or-<paramref name="value" /> is <see cref="F:System.Single.NegativeInfinity" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThan(System.Int64,System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示 64 位元帶正負號的整數是否大於 <see cref="T:System.Numerics.BigInteger" /> 值。</summary>
      <returns>如果 <paramref name="left" /> 大於 <paramref name="right" />，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThan(System.Numerics.BigInteger,System.Int64)">
      <summary>傳回值，這個值表示 <see cref="T:System.Numerics.BigInteger" /> 是否大於 64 位元帶正負號的整數值。</summary>
      <returns>如果 <paramref name="left" /> 大於 <paramref name="right" />，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThan(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示 <see cref="T:System.Numerics.BigInteger" /> 值是否大於另一個 <see cref="T:System.Numerics.BigInteger" /> 值。</summary>
      <returns>如果 <paramref name="left" /> 大於 <paramref name="right" />，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThan(System.Numerics.BigInteger,System.UInt64)">
      <summary>傳回值，這個值表示 <see cref="T:System.Numerics.BigInteger" /> 值是否大於 64 位元不帶正負號的整數。</summary>
      <returns>如果 <paramref name="left" /> 大於 <paramref name="right" />，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThan(System.UInt64,System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示 <see cref="T:System.Numerics.BigInteger" /> 值是否大於 64 位元不帶正負號的整數。</summary>
      <returns>如果 <paramref name="left" /> 大於 <paramref name="right" />，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThanOrEqual(System.Int64,System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示 64 位元帶正負號的整數是否大於或等於 <see cref="T:System.Numerics.BigInteger" /> 值。</summary>
      <returns>如果 <paramref name="left" /> 大於 <paramref name="right" />，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThanOrEqual(System.Numerics.BigInteger,System.Int64)">
      <summary>傳回值，這個值表示 <see cref="T:System.Numerics.BigInteger" /> 值是否大於或等於 64 位元帶正負號的整數值。</summary>
      <returns>如果 <paramref name="left" /> 大於 <paramref name="right" />，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThanOrEqual(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示 <see cref="T:System.Numerics.BigInteger" /> 值是否大於或等於另一個 <see cref="T:System.Numerics.BigInteger" /> 值。</summary>
      <returns>如果 <paramref name="left" /> 大於 <paramref name="right" />，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThanOrEqual(System.Numerics.BigInteger,System.UInt64)">
      <summary>傳回值，這個值表示 <see cref="T:System.Numerics.BigInteger" /> 值是否大於或等於 64 位元不帶正負號的整數值。</summary>
      <returns>如果 <paramref name="left" /> 大於 <paramref name="right" />，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThanOrEqual(System.UInt64,System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示 64 位元不帶正負號的整數是否大於或等於 <see cref="T:System.Numerics.BigInteger" /> 值。</summary>
      <returns>如果 <paramref name="left" /> 大於 <paramref name="right" />，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Implicit(System.Byte)~System.Numerics.BigInteger">
      <summary>定義從不帶正負號的位元組到 <see cref="T:System.Numerics.BigInteger" /> 值的隱含轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為 <see cref="T:System.Numerics.BigInteger" /> 的值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Implicit(System.Int16)~System.Numerics.BigInteger">
      <summary>定義從帶正負號的 16 位元整數到 <see cref="T:System.Numerics.BigInteger" /> 值的隱含轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為 <see cref="T:System.Numerics.BigInteger" /> 的值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Implicit(System.Int32)~System.Numerics.BigInteger">
      <summary>定義從帶正負號的 32 位元整數到 <see cref="T:System.Numerics.BigInteger" /> 值的隱含轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為 <see cref="T:System.Numerics.BigInteger" /> 的值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Implicit(System.Int64)~System.Numerics.BigInteger">
      <summary>定義從帶正負號的 64 位元整數到 <see cref="T:System.Numerics.BigInteger" /> 值的隱含轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為 <see cref="T:System.Numerics.BigInteger" /> 的值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Implicit(System.SByte)~System.Numerics.BigInteger">
      <summary>定義從 8 位元帶正負號的整數到 <see cref="T:System.Numerics.BigInteger" /> 值的隱含轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為 <see cref="T:System.Numerics.BigInteger" /> 的值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Implicit(System.UInt16)~System.Numerics.BigInteger">
      <summary>定義從 16 位元不帶正負號的整數到 <see cref="T:System.Numerics.BigInteger" /> 值的隱含轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為 <see cref="T:System.Numerics.BigInteger" /> 的值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Implicit(System.UInt32)~System.Numerics.BigInteger">
      <summary>定義從 32 位元不帶正負號的整數到 <see cref="T:System.Numerics.BigInteger" /> 值的隱含轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為 <see cref="T:System.Numerics.BigInteger" /> 的值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Implicit(System.UInt64)~System.Numerics.BigInteger">
      <summary>定義從 64 位元不帶正負號的整數到 <see cref="T:System.Numerics.BigInteger" /> 值的隱含轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數的值。</returns>
      <param name="value">要轉換為 <see cref="T:System.Numerics.BigInteger" /> 的值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Increment(System.Numerics.BigInteger)">
      <summary>
        <see cref="T:System.Numerics.BigInteger" /> 值遞增 1。</summary>
      <returns>
        <paramref name="value" /> 參數遞增 1 後的值。</returns>
      <param name="value">要遞增的值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Inequality(System.Int64,System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示 64 位元帶正負號的整數與 <see cref="T:System.Numerics.BigInteger" /> 值是否不相等。</summary>
      <returns>如果 <paramref name="left" /> 和 <paramref name="right" /> 不相等，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Inequality(System.Numerics.BigInteger,System.Int64)">
      <summary>傳回值，這個值表示 <see cref="T:System.Numerics.BigInteger" /> 值與 64 位元帶正負號的整數是否不相等。</summary>
      <returns>如果 <paramref name="left" /> 和 <paramref name="right" /> 不相等，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Inequality(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示兩個 <see cref="T:System.Numerics.BigInteger" /> 物件是否有不同的值。</summary>
      <returns>如果 <paramref name="left" /> 和 <paramref name="right" /> 不相等，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Inequality(System.Numerics.BigInteger,System.UInt64)">
      <summary>傳回值，這個值表示 <see cref="T:System.Numerics.BigInteger" /> 值與 64 位元不帶正負號的整數是否不相等。</summary>
      <returns>如果 <paramref name="left" /> 和 <paramref name="right" /> 不相等，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Inequality(System.UInt64,System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示 64 位元不帶正負號的整數與 <see cref="T:System.Numerics.BigInteger" /> 值是否不相等。</summary>
      <returns>如果 <paramref name="left" /> 和 <paramref name="right" /> 不相等，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LeftShift(System.Numerics.BigInteger,System.Int32)">
      <summary>將 <see cref="T:System.Numerics.BigInteger" /> 值向左移動指定的位元數。</summary>
      <returns>已經向左移動指定之位元數的值。</returns>
      <param name="value">要執行位元移位的值。</param>
      <param name="shift">
        <paramref name="value" /> 向左移位的位元數。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThan(System.Int64,System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示 64 位元帶正負號的整數是否小於 <see cref="T:System.Numerics.BigInteger" /> 值。</summary>
      <returns>如果 <paramref name="left" /> 小於 <paramref name="right" />，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThan(System.Numerics.BigInteger,System.Int64)">
      <summary>傳回值，這個值表示 <see cref="T:System.Numerics.BigInteger" /> 值是否小於 64 位元帶正負號的整數。</summary>
      <returns>如果 <paramref name="left" /> 小於 <paramref name="right" />，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThan(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示 <see cref="T:System.Numerics.BigInteger" /> 值是否小於另一個 <see cref="T:System.Numerics.BigInteger" /> 值。</summary>
      <returns>如果 <paramref name="left" /> 小於 <paramref name="right" />，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThan(System.Numerics.BigInteger,System.UInt64)">
      <summary>傳回值，這個值表示 <see cref="T:System.Numerics.BigInteger" /> 值是否小於 64 位元不帶正負號的整數。</summary>
      <returns>如果 <paramref name="left" /> 小於 <paramref name="right" />，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThan(System.UInt64,System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示 64 位元不帶正負號的整數是否小於 <see cref="T:System.Numerics.BigInteger" /> 值。</summary>
      <returns>如果 <paramref name="left" /> 小於 <paramref name="right" />，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThanOrEqual(System.Int64,System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示 64 位元帶正負號的整數是否小於或等於 <see cref="T:System.Numerics.BigInteger" /> 值。</summary>
      <returns>如果 <paramref name="left" /> 小於或等於 <paramref name="right" /> 則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThanOrEqual(System.Numerics.BigInteger,System.Int64)">
      <summary>傳回值，這個值表示 <see cref="T:System.Numerics.BigInteger" /> 值是否小於或等於 64 位元帶正負號的整數。</summary>
      <returns>如果 <paramref name="left" /> 小於或等於 <paramref name="right" /> 則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThanOrEqual(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示 <see cref="T:System.Numerics.BigInteger" /> 值是否小於或等於另一個 <see cref="T:System.Numerics.BigInteger" /> 值。</summary>
      <returns>如果 <paramref name="left" /> 小於或等於 <paramref name="right" /> 則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThanOrEqual(System.Numerics.BigInteger,System.UInt64)">
      <summary>傳回值，這個值表示 <see cref="T:System.Numerics.BigInteger" /> 值是否小於或等於 64 位元不帶正負號的整數。</summary>
      <returns>如果 <paramref name="left" /> 小於或等於 <paramref name="right" /> 則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThanOrEqual(System.UInt64,System.Numerics.BigInteger)">
      <summary>傳回值，這個值表示 64 位元不帶正負號的整數是否小於或等於 <see cref="T:System.Numerics.BigInteger" /> 值。</summary>
      <returns>如果 <paramref name="left" /> 小於或等於 <paramref name="right" /> 則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Modulus(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>傳回從兩個指定的 <see cref="T:System.Numerics.BigInteger" /> 值相除所得的餘數。</summary>
      <returns>相除所得到的餘數。</returns>
      <param name="dividend">做為被除數的值。</param>
      <param name="divisor">做為除數的值。</param>
      <exception cref="T:System.DivideByZeroException">
        <paramref name="divisor" /> is 0 (zero).</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Multiply(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>將兩個指定的 <see cref="T:System.Numerics.BigInteger" /> 值相乘。</summary>
      <returns>
        <paramref name="left" /> 和 <paramref name="right" /> 的乘積。</returns>
      <param name="left">要相乘的第一個值。</param>
      <param name="right">要相乘的第二個值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_OnesComplement(System.Numerics.BigInteger)">
      <summary>傳回 <see cref="T:System.Numerics.BigInteger" /> 值的位元一補數。</summary>
      <returns>
        <paramref name="value" /> 的位元一補數。</returns>
      <param name="value">整數值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_RightShift(System.Numerics.BigInteger,System.Int32)">
      <summary>將 <see cref="T:System.Numerics.BigInteger" /> 值向右移動指定的位元數。</summary>
      <returns>已經向右移動指定之位元數的值。</returns>
      <param name="value">要執行位元移位的值。</param>
      <param name="shift">
        <paramref name="value" /> 向右移位的位元數。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Subtraction(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>將某個 <see cref="T:System.Numerics.BigInteger" /> 值減去另一個 <see cref="T:System.Numerics.BigInteger" /> 值。</summary>
      <returns>
        <paramref name="left" /> 減去 <paramref name="right" /> 的結果。</returns>
      <param name="left">要被減的值 (被減數)。</param>
      <param name="right">要減去的值 (減數)。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_UnaryNegation(System.Numerics.BigInteger)">
      <summary>將指定的 BigInteger 值變換正負號。</summary>
      <returns>
        <paramref name="value" /> 參數乘以負一 (-1) 的結果。</returns>
      <param name="value">要變換正負號的值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.op_UnaryPlus(System.Numerics.BigInteger)">
      <summary>傳回 <see cref="T:System.Numerics.BigInteger" /> 運算元的值。(運算元的正負號不會變更)。</summary>
      <returns>
        <paramref name="value" /> 運算元的值。</returns>
      <param name="value">整數值。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.Parse(System.String)">
      <summary>將數字的字串表示，轉換為其相等的 <see cref="T:System.Numerics.BigInteger" />。</summary>
      <returns>值，與以 <paramref name="value" /> 參數指定的數字相等。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in the correct format.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Parse(System.String,System.Globalization.NumberStyles)">
      <summary>將指定樣式中數字的字串表示轉換為其相等的 <see cref="T:System.Numerics.BigInteger" />。</summary>
      <returns>值，與以 <paramref name="value" /> 參數指定的數字相等。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="style">指定 <paramref name="value" /> 可以使用的格式之列舉值的位元組合。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="style" /> is not a <see cref="T:System.Globalization.NumberStyles" /> value.-or-<paramref name="style" /> includes the <see cref="F:System.Globalization.NumberStyles.AllowHexSpecifier" /> or <see cref="F:System.Globalization.NumberStyles.HexNumber" /> flag along with another value.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> does not comply with the input pattern specified by <see cref="T:System.Globalization.NumberStyles" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Parse(System.String,System.Globalization.NumberStyles,System.IFormatProvider)">
      <summary>將數字的字串表示 (使用指定樣式和特定文化特性的格式) 轉換為其相等的 <see cref="T:System.Numerics.BigInteger" />。</summary>
      <returns>值，與以 <paramref name="value" /> 參數指定的數字相等。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="style">指定 <paramref name="value" /> 可以使用的格式之列舉值的位元組合。</param>
      <param name="provider">提供關於 <paramref name="value" /> 的特定文化特性格式資訊的物件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="style" /> is not a <see cref="T:System.Globalization.NumberStyles" /> value.-or-<paramref name="style" /> includes the <see cref="F:System.Globalization.NumberStyles.AllowHexSpecifier" /> or <see cref="F:System.Globalization.NumberStyles.HexNumber" /> flag along with another value.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> does not comply with the input pattern specified by <paramref name="style" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Parse(System.String,System.IFormatProvider)">
      <summary>將使用指定特定文化特性格式之數字的字串表示轉換為其相等的 <see cref="T:System.Numerics.BigInteger" />。</summary>
      <returns>值，與以 <paramref name="value" /> 參數指定的數字相等。</returns>
      <param name="value">字串，包含要轉換的數字。</param>
      <param name="provider">提供關於 <paramref name="value" /> 的特定文化特性格式資訊的物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="value" /> is not in the correct format.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Pow(System.Numerics.BigInteger,System.Int32)">
      <summary>
        <see cref="T:System.Numerics.BigInteger" /> 值自乘至指定之值的乘冪。</summary>
      <returns>
        <paramref name="value" /> 自乘至 <paramref name="exponent" /> 乘冪的結果。</returns>
      <param name="value">具有乘冪數 <paramref name="exponent" /> 的數字。</param>
      <param name="exponent">
        <paramref name="value" /> 的乘冪指數。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of the <paramref name="exponent" /> parameter is negative.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Remainder(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>對兩個 <see cref="T:System.Numerics.BigInteger" /> 值執行整數除法運算，並傳回餘數。</summary>
      <returns>
        <paramref name="dividend" /> 除以 <paramref name="divisor" /> 後所留的餘數。</returns>
      <param name="dividend">做為被除數的值。</param>
      <param name="divisor">做為除數的值。</param>
      <exception cref="T:System.DivideByZeroException">
        <paramref name="divisor" /> is 0 (zero).</exception>
    </member>
    <member name="P:System.Numerics.BigInteger.Sign">
      <summary>取得數字，這個數字表示目前 <see cref="T:System.Numerics.BigInteger" /> 物件的正負號 (負數、正數或零)。</summary>
      <returns>數字，表示 <see cref="T:System.Numerics.BigInteger" /> 物件的正負號，如下表所示。數字描述-1這個物件的值為負數。0這個物件的值為 0 (零)。1這個物件的值為正數。</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.Subtract(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>某個 <see cref="T:System.Numerics.BigInteger" /> 值減去另一個值，並且傳回結果。</summary>
      <returns>
        <paramref name="left" /> 減去 <paramref name="right" /> 的結果。</returns>
      <param name="left">要被減的值 (被減數)。</param>
      <param name="right">要減去的值 (減數)。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.System#IComparable#CompareTo(System.Object)">
      <summary>將目前的執行個體與相同類型的另一個物件相比較，並傳回整數，這個整數表示目前的執行個體在排序次序中，位於另一個物件之前、之後或相同位置。</summary>
      <returns>帶正負號的整數，指示這個執行個體與 <paramref name="obj" /> 的相對順序。傳回值描述小於零這個執行個體在排序順序中會在 <paramref name="obj" /> 之前。零這個執行個體出現在排序順序中的位置和 <paramref name="obj" /> 相同。大於零這個執行個體在排序順序中會跟在 <paramref name="obj" /> 之後。-或- <paramref name="value" /> 為 null。 </returns>
      <param name="obj">與這個執行個體相比較的物件，或 null。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> is not a <see cref="T:System.Numerics.BigInteger" />. </exception>
    </member>
    <member name="M:System.Numerics.BigInteger.ToByteArray">
      <summary>將 <see cref="T:System.Numerics.BigInteger" /> 值轉換成位元組陣列。</summary>
      <returns>目前 <see cref="T:System.Numerics.BigInteger" /> 物件的值，已轉換為位元組陣列。</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.ToString">
      <summary>將目前 <see cref="T:System.Numerics.BigInteger" /> 物件的數值，轉換為其相等字串表示。</summary>
      <returns>目前 <see cref="T:System.Numerics.BigInteger" /> 值的字串表示。</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.ToString(System.IFormatProvider)">
      <summary>使用指定的文化特性特定格式資訊，將目前 <see cref="T:System.Numerics.BigInteger" /> 物件的數值轉換為其對等字串表示。</summary>
      <returns>目前 <see cref="T:System.Numerics.BigInteger" /> 值的字串表示，採用 <paramref name="provider" /> 參數所指定的格式。</returns>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
    </member>
    <member name="M:System.Numerics.BigInteger.ToString(System.String)">
      <summary>使用指定的格式，將目前 <see cref="T:System.Numerics.BigInteger" /> 物件的值，轉換為其相等字串表示。</summary>
      <returns>目前 <see cref="T:System.Numerics.BigInteger" /> 值的字串表示，採用 <paramref name="format" /> 參數所指定的格式。</returns>
      <param name="format">標準或自訂數值格式字串。</param>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> is not a valid format string.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.ToString(System.String,System.IFormatProvider)">
      <summary>使用指定的格式和特定文化特性的格式資訊，將目前 <see cref="T:System.Numerics.BigInteger" /> 物件的值，轉換為其相等的字串表示。</summary>
      <returns>目前 <see cref="T:System.Numerics.BigInteger" /> 值的字串表示 (如 <paramref name="format" /> 和 <paramref name="provider" /> 參數所指定)。</returns>
      <param name="format">標準或自訂數值格式字串。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> is not a valid format string.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.TryParse(System.String,System.Globalization.NumberStyles,System.IFormatProvider,System.Numerics.BigInteger@)">
      <summary>使用指定的文化特性特定格式資訊和格式樣式，將日期和時間的指定字串表示轉換為其對等的 <see cref="T:System.Numerics.BigInteger" />，並傳回值，這個值表示轉換是否成功。</summary>
      <returns>如果 <paramref name="value" /> 參數轉換成功，則為 true，否則為 false。</returns>
      <param name="value">數字的字串表示。這個字串使用 <paramref name="style" /> 指定的樣式來解譯。</param>
      <param name="style">列舉值的位元組合，表示 <paramref name="value" /> 中可以存在的樣式項目。一般會指定的值是 <see cref="F:System.Globalization.NumberStyles.Integer" />。</param>
      <param name="provider">物件，提供關於 <paramref name="value" /> 的特定文化特性格式資訊。</param>
      <param name="result">當這個方法傳回時，如果轉換成功，則會包含相當於 <paramref name="value" /> 中所含之數字的 <see cref="T:System.Numerics.BigInteger" />；如果轉換失敗則為 <see cref="P:System.Numerics.BigInteger.Zero" />。轉換失敗的狀況包括：如果 <paramref name="value" /> 參數為 null 或格式不符合 <paramref name="style" />。這個參數會以未初始化的狀態傳遞。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="style" /> is not a <see cref="T:System.Globalization.NumberStyles" /> value.-or-<paramref name="style" /> includes the <see cref="F:System.Globalization.NumberStyles.AllowHexSpecifier" /> or <see cref="F:System.Globalization.NumberStyles.HexNumber" /> flag along with another value. </exception>
    </member>
    <member name="M:System.Numerics.BigInteger.TryParse(System.String,System.Numerics.BigInteger@)">
      <summary>嘗試將數字的字串表示轉換成其相等的 <see cref="T:System.Numerics.BigInteger" />，並傳回一個值表示轉換是否成功。</summary>
      <returns>如果 <paramref name="value" /> 轉換成功，則為 true，否則為 false。</returns>
      <param name="value">數字的字串表示。</param>
      <param name="result">當這個方法傳回時，如果轉換成功，則會包含相當於 <paramref name="value" /> 中所含之數字的 <see cref="T:System.Numerics.BigInteger" />；如果轉換失敗則為零 (0)。轉換失敗的狀況包括：如果 <paramref name="value" /> 參數為 null 或不是正確的格式。這個參數會以未初始化的狀態傳遞。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null.</exception>
    </member>
    <member name="P:System.Numerics.BigInteger.Zero">
      <summary>取得表示數字 0 (零) 的值。</summary>
      <returns>值為 0 (零) 的整數。</returns>
    </member>
    <member name="T:System.Numerics.Complex">
      <summary>表示複數。</summary>
    </member>
    <member name="M:System.Numerics.Complex.#ctor(System.Double,System.Double)">
      <summary>使用指定的實數和虛數，初始化 <see cref="T:System.Numerics.Complex" /> 結構的新執行個體。</summary>
      <param name="real">複數的實數部分。</param>
      <param name="imaginary">複數的虛數部分。</param>
    </member>
    <member name="M:System.Numerics.Complex.Abs(System.Numerics.Complex)">
      <summary>取得複數的絕對值 (或範圍)。</summary>
      <returns>
        <paramref name="value" /> 的絕對值。</returns>
      <param name="value">複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Acos(System.Numerics.Complex)">
      <summary>傳回角度，這個角度是指定之複數的反餘弦值。</summary>
      <returns>反餘弦值為 <paramref name="value" /> 的角度 (以弧度為單位)。</returns>
      <param name="value">表示餘弦值的複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Add(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>兩個複數相加，並傳回結果。</summary>
      <returns>
        <paramref name="left" /> 和 <paramref name="right" /> 的總和。</returns>
      <param name="left">要相加的第一個複數。</param>
      <param name="right">要相加的第二個複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Asin(System.Numerics.Complex)">
      <summary>傳回角度，這個角度是指定之複數的反正弦值。</summary>
      <returns>反正弦值為 <paramref name="value" /> 的角度。</returns>
      <param name="value">複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Atan(System.Numerics.Complex)">
      <summary>傳回角度，這個角度是指定之複數的反正切值。</summary>
      <returns>反正切值為 <paramref name="value" /> 的角度。</returns>
      <param name="value">複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Conjugate(System.Numerics.Complex)">
      <summary>計算複數的共軛，並傳回結果。</summary>
      <returns>
        <paramref name="value" /> 的共軛。</returns>
      <param name="value">複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Cos(System.Numerics.Complex)">
      <summary>傳回指定複數的餘弦函數。</summary>
      <returns>
        <paramref name="value" /> 的餘弦函數。</returns>
      <param name="value">複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Cosh(System.Numerics.Complex)">
      <summary>傳回指定複數的雙曲餘弦。</summary>
      <returns>
        <paramref name="value" /> 的雙曲線餘弦函數。</returns>
      <param name="value">複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Divide(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>以某複數除以另一個複數，並傳回結果。</summary>
      <returns>相除的商數。</returns>
      <param name="dividend">要當做被除數的複數。</param>
      <param name="divisor">要當做除數的複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Equals(System.Numerics.Complex)">
      <summary>傳回值，這個值指出目前執行個體和指定複數是否有相同的值。</summary>
      <returns>如果這個複數和 <paramref name="value" /> 有相同的值則為 true，否則為 false。</returns>
      <param name="value">要比較的複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Equals(System.Object)">
      <summary>傳回值，這個值表示目前執行個體與指定的物件是否有相同的值。</summary>
      <returns>如果 <paramref name="obj" /> 參數為 <see cref="T:System.Numerics.Complex" /> 物件，或是可隱含轉換為 <see cref="T:System.Numerics.Complex" /> 物件的型別，且其值等於目前的 <see cref="T:System.Numerics.Complex" /> 物件，則為 true，否則為 false。</returns>
      <param name="obj">要比較的物件。</param>
    </member>
    <member name="M:System.Numerics.Complex.Exp(System.Numerics.Complex)">
      <summary>傳回乘至複數指定乘冪的 e。</summary>
      <returns>具有乘冪數 <paramref name="value" /> 的數字 e。</returns>
      <param name="value">指定乘冪的複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.FromPolarCoordinates(System.Double,System.Double)">
      <summary>由點的極座標建立複數。</summary>
      <returns>複數。</returns>
      <param name="magnitude">範圍，即從原點 (X 軸和 Y 軸的交點) 到複數點的距離。</param>
      <param name="phase">相位，即從直線到水平軸的角度 (以弧度為單位)。</param>
    </member>
    <member name="M:System.Numerics.Complex.GetHashCode">
      <summary>傳回目前 <see cref="T:System.Numerics.Complex" /> 物件的雜湊碼。</summary>
      <returns>32 位元帶正負號的整數雜湊碼。</returns>
    </member>
    <member name="P:System.Numerics.Complex.Imaginary">
      <summary>取得目前 <see cref="T:System.Numerics.Complex" /> 物件的虛數部分。</summary>
      <returns>複數的虛數部分。</returns>
    </member>
    <member name="F:System.Numerics.Complex.ImaginaryOne">
      <summary>在實數等於零且虛數等於一條件下，傳回新 <see cref="T:System.Numerics.Complex" /> 執行個體。</summary>
    </member>
    <member name="M:System.Numerics.Complex.Log(System.Numerics.Complex)">
      <summary>傳回指定複數的自然 (底數 e) 對數。</summary>
      <returns>
        <paramref name="value" /> 的自然對數 (底數 e)。</returns>
      <param name="value">複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Log(System.Numerics.Complex,System.Double)">
      <summary>傳回指定底數中指定複數的對數。</summary>
      <returns>底數 <paramref name="baseValue" /> 中的對數 <paramref name="value" />。</returns>
      <param name="value">複數。</param>
      <param name="baseValue">對數的底數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Log10(System.Numerics.Complex)">
      <summary>傳回指定複數底數為 10 的對數。</summary>
      <returns>底數為 10 的 <paramref name="value" /> 對數。</returns>
      <param name="value">複數。</param>
    </member>
    <member name="P:System.Numerics.Complex.Magnitude">
      <summary>取得複數的範圍 (或絕對值)。</summary>
      <returns>目前執行個體的範圍。</returns>
    </member>
    <member name="M:System.Numerics.Complex.Multiply(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>傳回兩個複數的乘積。</summary>
      <returns>
        <paramref name="left" /> 和 <paramref name="right" /> 參數的乘積。</returns>
      <param name="left">要相乘的第一個複數。</param>
      <param name="right">要相乘的第二個複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Negate(System.Numerics.Complex)">
      <summary>傳回指定之複數的加法反元素。</summary>
      <returns>
        <paramref name="value" /> 參數之 <see cref="P:System.Numerics.Complex.Real" /> 和 <see cref="P:System.Numerics.Complex.Imaginary" /> 部分乘以 -1 的結果。</returns>
      <param name="value">複數。</param>
    </member>
    <member name="F:System.Numerics.Complex.One">
      <summary>在實數等於一且虛數等於零條件下，傳回新 <see cref="T:System.Numerics.Complex" /> 執行個體。</summary>
    </member>
    <member name="M:System.Numerics.Complex.op_Addition(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>將兩個複數相加。</summary>
      <returns>
        <paramref name="left" /> 和 <paramref name="right" /> 的總和。</returns>
      <param name="left">要相加的第一個值。</param>
      <param name="right">要相加的第二個值。</param>
    </member>
    <member name="M:System.Numerics.Complex.op_Division(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>以某指定複數除以另一個指定複數。</summary>
      <returns>
        <paramref name="left" /> 除以 <paramref name="right" /> 的結果。</returns>
      <param name="left">做為被除數的值。</param>
      <param name="right">做為除數的值。</param>
    </member>
    <member name="M:System.Numerics.Complex.op_Equality(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>傳回值，這個值表示兩個複數是否相等。</summary>
      <returns>如果 <paramref name="left" /> 與 <paramref name="right" /> 參數有相同的值，則為 true，否則為 false。</returns>
      <param name="left">要比較的第一個複數。</param>
      <param name="right">要比較的第二個複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.op_Explicit(System.Decimal)~System.Numerics.Complex">
      <summary>定義從 <see cref="T:System.Decimal" /> 值到複數的明確轉換。</summary>
      <returns>實數部分等於 <paramref name="value" /> 及虛數部分等於零的複數。</returns>
      <param name="value">要轉換成複數的值。</param>
    </member>
    <member name="M:System.Numerics.Complex.op_Explicit(System.Numerics.BigInteger)~System.Numerics.Complex">
      <summary>定義從 <see cref="T:System.Numerics.BigInteger" /> 值到複數的明確轉換。</summary>
      <returns>實數部分等於 <paramref name="value" /> 及虛數部分等於零的複數。</returns>
      <param name="value">要轉換成複數的值。</param>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.Byte)~System.Numerics.Complex">
      <summary>定義從不帶正負號的位元組到複數的隱含轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數值 (做為其實數部分) 以及零 (做為其虛數部分)。</returns>
      <param name="value">要轉換成複數的值。</param>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.Double)~System.Numerics.Complex">
      <summary>定義從雙精確度浮點數到複數的隱含轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數值 (做為其實數部分) 以及零 (做為其虛數部分)。</returns>
      <param name="value">要轉換成複數的值。</param>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.Int16)~System.Numerics.Complex">
      <summary>定義從 16 位元帶正負號整數到複數的隱含轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數值 (做為其實數部分) 以及零 (做為其虛數部分)。</returns>
      <param name="value">要轉換成複數的值。</param>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.Int32)~System.Numerics.Complex">
      <summary>定義從 32 位元帶正負號整數到複數的隱含轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數值 (做為其實數部分) 以及零 (做為其虛數部分)。</returns>
      <param name="value">要轉換成複數的值。</param>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.Int64)~System.Numerics.Complex">
      <summary>定義從 64 位元帶正負號整數到複數的隱含轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數值 (做為其實數部分) 以及零 (做為其虛數部分)。</returns>
      <param name="value">要轉換成複數的值。</param>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.SByte)~System.Numerics.Complex">
      <summary>定義從帶正負號的位元組到複數的隱含轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數值 (做為其實數部分) 以及零 (做為其虛數部分)。</returns>
      <param name="value">要轉換成複數的值。</param>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.Single)~System.Numerics.Complex">
      <summary>定義從單精確度浮點數到複數的隱含轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數值 (做為其實數部分) 以及零 (做為其虛數部分)。</returns>
      <param name="value">要轉換成複數的值。</param>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.UInt16)~System.Numerics.Complex">
      <summary>定義從 16 位元不帶正負號整數到複數的隱含轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數值 (做為其實數部分) 以及零 (做為其虛數部分)。</returns>
      <param name="value">要轉換成複數的值。</param>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.UInt32)~System.Numerics.Complex">
      <summary>定義從 32 位元不帶正負號整數到複數的隱含轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數值 (做為其實數部分) 以及零 (做為其虛數部分)。</returns>
      <param name="value">要轉換成複數的值。</param>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.UInt64)~System.Numerics.Complex">
      <summary>定義從 64 位元不帶正負號整數到複數的隱含轉換。</summary>
      <returns>物件，包含 <paramref name="value" /> 參數值 (做為其實數部分) 以及零 (做為其虛數部分)。</returns>
      <param name="value">要轉換成複數的值。</param>
    </member>
    <member name="M:System.Numerics.Complex.op_Inequality(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>傳回值，這個值表示兩個複數是否不相等。</summary>
      <returns>true if <paramref name="left" /> and <paramref name="right" /> are not equal; otherwise, false.</returns>
      <param name="left">要比較的第一個值。</param>
      <param name="right">要比較的第二個值。</param>
    </member>
    <member name="M:System.Numerics.Complex.op_Multiply(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>乘上兩個指定的複數。</summary>
      <returns>
        <paramref name="left" /> 與 <paramref name="right" /> 的乘積。</returns>
      <param name="left">要相乘的第一個值。</param>
      <param name="right">要相乘的第二個值。</param>
    </member>
    <member name="M:System.Numerics.Complex.op_Subtraction(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>從另一個複數減去一個複數。</summary>
      <returns>
        <paramref name="left" /> 減去 <paramref name="right" /> 的結果。</returns>
      <param name="left">要被減的值 (被減數)。</param>
      <param name="right">要減去的值 (減數)。</param>
    </member>
    <member name="M:System.Numerics.Complex.op_UnaryNegation(System.Numerics.Complex)">
      <summary>傳回指定之複數的加法反元素。</summary>
      <returns>
        <paramref name="value" /> 參數之 <see cref="P:System.Numerics.Complex.Real" /> 和 <see cref="P:System.Numerics.Complex.Imaginary" /> 部分乘以 -1 的結果。</returns>
      <param name="value">要變換正負號的值。</param>
    </member>
    <member name="P:System.Numerics.Complex.Phase">
      <summary>取得複數的階段。</summary>
      <returns>複數的相位 (以弧度為單位)。</returns>
    </member>
    <member name="M:System.Numerics.Complex.Pow(System.Numerics.Complex,System.Double)">
      <summary>傳回指定之複數自乘至雙精確度浮點數指定之乘冪的結果。</summary>
      <returns>乘至乘冪 <paramref name="power" /> 的複數 <paramref name="value" />。</returns>
      <param name="value">傳回乘至乘冪的複數。</param>
      <param name="power">雙精確度浮點數，用來指定乘冪數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Pow(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>傳回指定的複數自乘至複數指定之乘冪的結果。</summary>
      <returns>乘至乘冪 <paramref name="power" /> 的複數 <paramref name="value" />。</returns>
      <param name="value">傳回乘至乘冪的複數。</param>
      <param name="power">指定乘冪的複數。</param>
    </member>
    <member name="P:System.Numerics.Complex.Real">
      <summary>取得目前 <see cref="T:System.Numerics.Complex" /> 物件的實數部分。</summary>
      <returns>複數的實數部分。</returns>
    </member>
    <member name="M:System.Numerics.Complex.Reciprocal(System.Numerics.Complex)">
      <summary>傳回複數的乘法逆元。</summary>
      <returns>
        <paramref name="value" /> 的倒數。</returns>
      <param name="value">複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Sin(System.Numerics.Complex)">
      <summary>傳回指定複數的正弦函數。</summary>
      <returns>
        <paramref name="value" /> 的正弦函數。</returns>
      <param name="value">複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Sinh(System.Numerics.Complex)">
      <summary>傳回指定複數的雙曲正弦。</summary>
      <returns>
        <paramref name="value" /> 的雙曲線正弦函數。</returns>
      <param name="value">複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Sqrt(System.Numerics.Complex)">
      <summary>傳回指定複數的平方根。</summary>
      <returns>
        <paramref name="value" /> 的平方根。</returns>
      <param name="value">複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Subtract(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>從某複數減去另一個複數，並傳回結果。</summary>
      <returns>
        <paramref name="left" /> 減去 <paramref name="right" /> 的結果。</returns>
      <param name="left">要被減的值 (被減數)。</param>
      <param name="right">要減去的值 (減數)。</param>
    </member>
    <member name="M:System.Numerics.Complex.Tan(System.Numerics.Complex)">
      <summary>傳回指定複數的正切函數。</summary>
      <returns>
        <paramref name="value" /> 的正切函數。</returns>
      <param name="value">複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.Tanh(System.Numerics.Complex)">
      <summary>傳回指定複數的雙曲正切。</summary>
      <returns>
        <paramref name="value" /> 的雙曲線正切函數。</returns>
      <param name="value">複數。</param>
    </member>
    <member name="M:System.Numerics.Complex.ToString">
      <summary>將目前複數的值轉換為直角座標形式 (Cartesian form) 的相等字串表示。</summary>
      <returns>採取直角座標形式 (Cartesian form) 之目前執行個體的字串表示。</returns>
    </member>
    <member name="M:System.Numerics.Complex.ToString(System.IFormatProvider)">
      <summary>使用指定文化特性格式資訊，將目前的複數值轉換為採取直角座標形式 (Cartesian form) 的相等字串表示。</summary>
      <returns>採取直角座標形式 (Cartesian form) 之目前執行個體的字串表示，如 <paramref name="provider" /> 所指定。</returns>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
    </member>
    <member name="M:System.Numerics.Complex.ToString(System.String)">
      <summary>使用實數及虛數格式的指定格式，將目前的複數值轉換為採取直角座標形式 (Cartesian form) 的相等字串表示。</summary>
      <returns>採取直角座標形式 (Cartesian form) 之目前執行個體的字串表示。</returns>
      <param name="format">標準或自訂數值格式字串。</param>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> 不是有效的格式字串。</exception>
    </member>
    <member name="M:System.Numerics.Complex.ToString(System.String,System.IFormatProvider)">
      <summary>使用指定格式以及文化特性格式資訊來組成實數及虛數，將目前的複數值轉換為採取直角座標形式 (Cartesian form) 的相等字串表示。</summary>
      <returns>採取直角座標形式 (Cartesian form) 之目前執行個體的字串表示，如 <paramref name="format" /> 和 <paramref name="provider" /> 所指定。</returns>
      <param name="format">標準或自訂數值格式字串。</param>
      <param name="provider">物件，提供特定文化特性格式資訊。</param>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> 不是有效的格式字串。</exception>
    </member>
    <member name="F:System.Numerics.Complex.Zero">
      <summary>在實數等於零且虛數等於零條件下，傳回新 <see cref="T:System.Numerics.Complex" /> 執行個體。</summary>
    </member>
  </members>
</doc>