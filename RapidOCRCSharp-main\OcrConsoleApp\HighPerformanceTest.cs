using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using OcrLiteLib;

namespace OcrConsoleApp
{
    /// <summary>
    /// 高性能测试，专门用于提高CPU利用率
    /// </summary>
    public static class HighPerformanceTest
    {
        /// <summary>
        /// 运行高性能并行测试
        /// </summary>
        public static async Task RunHighPerformanceTest()
        {
            Console.WriteLine("=== 高性能并行测试 ===");
            
            // 1. 全面CPU优化
            CpuOptimizer.OptimizeForHighPerformance();
            CpuOptimizer.DisplaySystemInfo();
            CpuOptimizer.StartCpuMonitoring();
            
            // 2. 模型路径
            string modelsDir = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrOnnxForm\\models";
            string detPath = Path.Combine(modelsDir, "ch_PP-OCRv4_det_infer.onnx");
            string clsPath = Path.Combine(modelsDir, "ch_ppocr_mobile_v2.0_cls_infer.onnx");
            string recPath = Path.Combine(modelsDir, "ch_PP-OCRv4_rec_infer.onnx");
            string keysPath = Path.Combine(modelsDir, "ppocr_keys_v1.txt");
            
            string imagesDir = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrConsoleApp\\images";
            
            if (!Directory.Exists(imagesDir))
            {
                Console.WriteLine($"图片目录不存在: {imagesDir}");
                return;
            }
            
            // 3. 获取测试图片
            var imageFiles = Directory.GetFiles(imagesDir, "*.jpg")
                .Concat(Directory.GetFiles(imagesDir, "*.png"))
                .Concat(Directory.GetFiles(imagesDir, "*.jpeg"))
                .ToArray();
                
            if (imageFiles.Length == 0)
            {
                Console.WriteLine("未找到测试图片");
                return;
            }
            
            // 4. 复制图片以增加工作负载
            var multipliedFiles = MultiplyWorkload(imageFiles, 100); // 复制到100张
            Console.WriteLine($"测试工作负载: {multipliedFiles.Length} 张图片");
            
            // 5. 询问并发数
            Console.Write($"请输入并发数 (建议: {Environment.ProcessorCount * 3}): ");
            string input = Console.ReadLine();
            int maxConcurrency = Environment.ProcessorCount * 3;
            if (!string.IsNullOrEmpty(input) && int.TryParse(input, out int userConcurrency))
            {
                maxConcurrency = Math.Max(1, userConcurrency);
            }
            
            Console.WriteLine($"使用并发数: {maxConcurrency}");
            
            try
            {
                // 6. 创建高性能处理器
                using (var processor = new ParallelBatchProcessor(detPath, clsPath, recPath, keysPath, 1, maxConcurrency))
                {
                    Console.WriteLine("预热OCR服务池...");
                    await processor.WarmupAsync();
                    
                    // 7. 开始高强度测试
                    Console.WriteLine("开始高性能测试，请观察CPU使用率...");
                    
                    var cpuMonitor = StartCpuUsageMonitor();
                    var stopwatch = Stopwatch.StartNew();
                    
                    var progress = new Progress<ProcessProgress>(p =>
                    {
                        if (p.CompletedCount % 20 == 0 || p.IsCompleted)
                        {
                            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 进度: {p.ProgressPercentage:F1}% ({p.CompletedCount}/{p.TotalCount})");
                        }
                    });
                    
                    var results = await processor.ProcessFilesAsync(multipliedFiles, progress: progress);
                    
                    stopwatch.Stop();
                    cpuMonitor.Stop();
                    
                    // 8. 显示结果
                    DisplayResults(results, stopwatch.ElapsedMilliseconds, maxConcurrency);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"高性能测试失败: {ex.Message}");
            }
            finally
            {
                // 恢复默认设置
                CpuOptimizer.RestoreDefaultSettings();
            }
        }
        
        /// <summary>
        /// 增加工作负载
        /// </summary>
        private static string[] MultiplyWorkload(string[] originalFiles, int targetCount)
        {
            if (originalFiles.Length == 0) return originalFiles;
            
            var multipliedFiles = new string[targetCount];
            for (int i = 0; i < targetCount; i++)
            {
                multipliedFiles[i] = originalFiles[i % originalFiles.Length];
            }
            
            return multipliedFiles;
        }
        
        /// <summary>
        /// 启动CPU使用率监控
        /// </summary>
        private static Stopwatch StartCpuUsageMonitor()
        {
            var stopwatch = Stopwatch.StartNew();
            
            Task.Run(async () =>
            {
                var cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                cpuCounter.NextValue(); // 第一次调用
                await Task.Delay(1000);
                
                while (stopwatch.IsRunning)
                {
                    try
                    {
                        float cpuUsage = cpuCounter.NextValue();
                        Console.WriteLine($"[CPU监控] 当前CPU使用率: {cpuUsage:F1}%");
                        
                        if (cpuUsage < 30)
                        {
                            Console.WriteLine("  ⚠️  CPU使用率较低，考虑增加并发数");
                        }
                        else if (cpuUsage > 95)
                        {
                            Console.WriteLine("  ⚠️  CPU使用率过高，可能影响系统响应");
                        }
                        else if (cpuUsage > 70)
                        {
                            Console.WriteLine("  ✅ CPU使用率良好");
                        }
                    }
                    catch
                    {
                        // 忽略监控错误
                    }
                    
                    await Task.Delay(3000); // 每3秒监控一次
                }
            });
            
            return stopwatch;
        }
        
        /// <summary>
        /// 显示测试结果
        /// </summary>
        private static void DisplayResults(System.Collections.Generic.List<ParallelProcessResult> results, 
                                         long totalTimeMs, int concurrency)
        {
            var successCount = results.Count(r => r.IsSuccess);
            var failureCount = results.Count(r => !r.IsSuccess);
            
            Console.WriteLine("\n=== 高性能测试结果 ===");
            Console.WriteLine($"并发数: {concurrency}");
            Console.WriteLine($"总文件数: {results.Count}");
            Console.WriteLine($"成功: {successCount}, 失败: {failureCount}");
            Console.WriteLine($"总耗时: {totalTimeMs}ms ({totalTimeMs / 1000.0:F1}s)");
            
            if (successCount > 0)
            {
                var avgTime = results.Where(r => r.IsSuccess).Average(r => r.ProcessingTimeMs);
                var throughput = successCount * 1000.0 / totalTimeMs;
                var efficiency = throughput / Environment.ProcessorCount;
                
                Console.WriteLine($"平均处理时间: {avgTime:F1}ms/张");
                Console.WriteLine($"吞吐量: {throughput:F1} 张/秒");
                Console.WriteLine($"CPU效率: {efficiency:F1} 张/秒/核心");
                
                // 性能评估
                if (throughput > Environment.ProcessorCount * 2)
                {
                    Console.WriteLine("🎉 性能优秀！CPU利用率很好");
                }
                else if (throughput > Environment.ProcessorCount)
                {
                    Console.WriteLine("👍 性能良好，还有优化空间");
                }
                else
                {
                    Console.WriteLine("⚠️  性能较低，建议检查瓶颈");
                }
                
                // 线程使用统计
                var threadStats = results.Where(r => r.IsSuccess)
                    .GroupBy(r => r.ThreadId)
                    .Select(g => new { ThreadId = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .Take(5);
                    
                Console.WriteLine("\n线程使用统计 (前5名):");
                foreach (var stat in threadStats)
                {
                    Console.WriteLine($"  线程 {stat.ThreadId}: 处理了 {stat.Count} 张图片");
                }
            }
            
            if (failureCount > 0)
            {
                Console.WriteLine($"\n失败原因统计:");
                var errorStats = results.Where(r => !r.IsSuccess)
                    .GroupBy(r => r.Error?.GetType().Name ?? "Unknown")
                    .Select(g => new { ErrorType = g.Key, Count = g.Count() });
                    
                foreach (var stat in errorStats)
                {
                    Console.WriteLine($"  {stat.ErrorType}: {stat.Count} 次");
                }
            }
        }
        
        /// <summary>
        /// CPU压力测试
        /// </summary>
        public static async Task RunCpuStressTest()
        {
            Console.WriteLine("=== CPU压力测试 ===");
            Console.WriteLine("这个测试将尽可能使用所有CPU资源");
            
            CpuOptimizer.OptimizeForHighPerformance();
            
            // 创建大量并发任务
            int taskCount = Environment.ProcessorCount * 4;
            Console.WriteLine($"创建 {taskCount} 个并发任务");
            
            var tasks = Enumerable.Range(0, taskCount).Select(async i =>
            {
                // 模拟CPU密集型工作
                await Task.Run(() =>
                {
                    var random = new Random(i);
                    double result = 0;
                    
                    for (int j = 0; j < 1000000; j++)
                    {
                        result += Math.Sin(random.NextDouble()) * Math.Cos(random.NextDouble());
                    }
                    
                    return result;
                });
            });
            
            var stopwatch = Stopwatch.StartNew();
            await Task.WhenAll(tasks);
            stopwatch.Stop();
            
            Console.WriteLine($"CPU压力测试完成，耗时: {stopwatch.ElapsedMilliseconds}ms");
            
            CpuOptimizer.RestoreDefaultSettings();
        }
    }
}
