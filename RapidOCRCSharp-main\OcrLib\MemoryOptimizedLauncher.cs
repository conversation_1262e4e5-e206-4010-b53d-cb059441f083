using System;
using System.Runtime;

namespace OcrLiteLib
{
    /// <summary>
    /// 内存优化启动器，在模型加载前进行内存优化设置
    /// </summary>
    public static class MemoryOptimizedLauncher
    {
        /// <summary>
        /// 初始化内存优化设置
        /// </summary>
        public static void InitializeMemoryOptimization()
        {
            Console.WriteLine("=== 初始化内存优化设置 ===");
            
            try
            {
                // 1. 设置垃圾回收模式
                Console.WriteLine("设置垃圾回收模式...");
                GCSettings.LatencyMode = GCLatencyMode.Batch; // 批处理模式，更激进的内存回收
                
                // 2. 设置大对象堆压缩模式
                Console.WriteLine("设置大对象堆压缩模式...");
                GCSettings.LargeObjectHeapCompactionMode = GCLargeObjectHeapCompactionMode.CompactOnce;
                
                // 3. 预先进行一次完整的垃圾回收
                Console.WriteLine("执行初始垃圾回收...");
                long beforeGC = GC.GetTotalMemory(false);
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                long afterGC = GC.GetTotalMemory(false);
                Console.WriteLine($"初始GC完成，释放内存: {FormatBytes(beforeGC - afterGC)}");
                
                // 4. 设置进程优先级（如果可能）
                try
                {
                    System.Diagnostics.Process.GetCurrentProcess().PriorityClass = 
                        System.Diagnostics.ProcessPriorityClass.High;
                    Console.WriteLine("设置进程优先级为高");
                }
                catch
                {
                    Console.WriteLine("无法设置进程优先级（权限不足）");
                }
                
                Console.WriteLine($"内存优化设置完成，当前内存: {FormatBytes(GC.GetTotalMemory(false))}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"内存优化设置失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 创建优化的OCR服务实例
        /// </summary>
        /// <returns>优化的OCR服务实例</returns>
        public static OcrServiceOptimized CreateOptimizedOcrService()
        {
            Console.WriteLine("\n=== 创建优化OCR服务 ===");
            
            // 在创建服务前再次清理内存
            long beforeCreate = GC.GetTotalMemory(false);
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            long afterClean = GC.GetTotalMemory(false);
            
            Console.WriteLine($"创建前内存清理，释放: {FormatBytes(beforeCreate - afterClean)}");
            Console.WriteLine($"开始创建OCR服务实例...");
            
            var ocrService = new OcrServiceOptimized();
            
            long afterCreate = GC.GetTotalMemory(false);
            Console.WriteLine($"OCR服务创建完成，内存增长: {FormatBytes(afterCreate - afterClean)}");
            
            return ocrService;
        }
        
        /// <summary>
        /// 优化的模型初始化
        /// </summary>
        public static void InitializeModelsOptimized(OcrServiceOptimized ocrService, 
            string detPath, string clsPath, string recPath, string keysPath, int numThread)
        {
            Console.WriteLine("\n=== 优化模型初始化 ===");
            
            long beforeInit = GC.GetTotalMemory(false);
            Console.WriteLine($"初始化前内存: {FormatBytes(beforeInit)}");
            
            // 分步骤初始化，每步后清理内存
            try
            {
                // 预热JIT编译器
                Console.WriteLine("预热JIT编译器...");
                WarmupJIT();
                
                // 初始化模型
                ocrService.InitModels(detPath, clsPath, recPath, keysPath, numThread);
                
                long afterInit = GC.GetTotalMemory(false);
                Console.WriteLine($"模型初始化完成，总内存增长: {FormatBytes(afterInit - beforeInit)}");
                
                // 初始化后的内存优化
                Console.WriteLine("执行初始化后内存优化...");
                PostInitializationOptimization();
                
                long afterOptim = GC.GetTotalMemory(false);
                Console.WriteLine($"优化后内存: {FormatBytes(afterOptim)}");
                Console.WriteLine($"优化释放内存: {FormatBytes(afterInit - afterOptim)}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"模型初始化失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 预热JIT编译器
        /// </summary>
        private static void WarmupJIT()
        {
            try
            {
                // 创建一些小对象来预热JIT
                for (int i = 0; i < 100; i++)
                {
                    var dummy = new byte[1024];
                }
                
                // 强制GC来清理预热对象
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }
            catch
            {
                // 忽略预热错误
            }
        }
        
        /// <summary>
        /// 初始化后的内存优化
        /// </summary>
        private static void PostInitializationOptimization()
        {
            try
            {
                // 1. 强制压缩大对象堆
                GCSettings.LargeObjectHeapCompactionMode = GCLargeObjectHeapCompactionMode.CompactOnce;
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                
                // 2. 尝试释放工作集内存（Windows特有）
                try
                {
                    var process = System.Diagnostics.Process.GetCurrentProcess();
                    // 这会提示操作系统可以回收一些工作集内存
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();
                }
                catch
                {
                    // 忽略平台特定的优化错误
                }
                
                // 3. 恢复正常的GC模式
                GCSettings.LatencyMode = GCLatencyMode.Interactive;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"后初始化优化警告: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 运行时内存监控和优化
        /// </summary>
        /// <param name="currentCount">当前处理计数</param>
        /// <param name="thresholdMB">内存阈值（MB）</param>
        public static void RuntimeMemoryOptimization(int currentCount, int thresholdMB = 1000)
        {
            // 每10次处理检查一次内存
            if (currentCount % 10 == 0)
            {
                long currentMemory = GC.GetTotalMemory(false);
                long thresholdBytes = (long)thresholdMB * 1024 * 1024;
                
                if (currentMemory > thresholdBytes)
                {
                    Console.WriteLine($"内存使用过高 ({FormatBytes(currentMemory)})，执行强制清理...");
                    
                    long beforeCleanup = currentMemory;
                    
                    // 强制垃圾回收
                    GCSettings.LargeObjectHeapCompactionMode = GCLargeObjectHeapCompactionMode.CompactOnce;
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();
                    
                    long afterCleanup = GC.GetTotalMemory(false);
                    Console.WriteLine($"强制清理完成，释放: {FormatBytes(beforeCleanup - afterCleanup)}，当前: {FormatBytes(afterCleanup)}");
                }
            }
        }
        
        /// <summary>
        /// 格式化字节数
        /// </summary>
        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }
    }
}
