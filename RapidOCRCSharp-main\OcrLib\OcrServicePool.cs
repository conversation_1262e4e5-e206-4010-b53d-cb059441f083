﻿using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;

namespace OcrLiteLib
{
    /// <summary>
    /// OCR服务池，支持并行处理
    /// </summary>
    public class OcrServicePool : IDisposable
    {
        private readonly ConcurrentQueue<OcrServiceOptimized> _servicePool;
        private readonly SemaphoreSlim _semaphore;
        private readonly int _maxPoolSize;
        private volatile bool _disposed = false;
        private int _currentPoolSize = 0;

        // 模型参数
        private readonly string _detPath;
        private readonly string _clsPath;
        private readonly string _recPath;
        private readonly string _keysPath;
        private readonly int _numThread;

        public OcrServicePool(string detPath, string clsPath, string recPath, string keysPath, 
                             int numThread = 1, int maxPoolSize = 4)
        {
            _detPath = detPath;
            _clsPath = clsPath;
            _recPath = recPath;
            _keysPath = keysPath;
            _numThread = numThread;
            _maxPoolSize = maxPoolSize;

            _servicePool = new ConcurrentQueue<OcrServiceOptimized>();
            _semaphore = new SemaphoreSlim(maxPoolSize, maxPoolSize);

            Console.WriteLine($"OCR服务池初始化，最大并发数: {maxPoolSize}");
        }

        /// <summary>
        /// 获取OCR服务实例
        /// </summary>
        public async Task<PooledOcrService> AcquireServiceAsync()
        {
            if (_disposed) throw new ObjectDisposedException(nameof(OcrServicePool));

            await _semaphore.WaitAsync();

            try
            {
                if (_servicePool.TryDequeue(out var service))
                {
                    return new PooledOcrService(service, this);
                }

                // 如果池中没有可用服务，创建新的
                if (_currentPoolSize < _maxPoolSize)
                {
                    service = CreateNewService();
                    Interlocked.Increment(ref _currentPoolSize);
                    Console.WriteLine($"创建新的OCR服务实例，当前池大小: {_currentPoolSize}");
                    return new PooledOcrService(service, this);
                }

                throw new InvalidOperationException("无法获取OCR服务实例");
            }
            catch
            {
                _semaphore.Release();
                throw;
            }
        }

        /// <summary>
        /// 归还OCR服务实例
        /// </summary>
        internal void ReturnService(OcrServiceOptimized service)
        {
            if (!_disposed && service != null)
            {
                _servicePool.Enqueue(service);
            }
            _semaphore.Release();
        }

        /// <summary>
        /// 创建新的OCR服务实例
        /// </summary>
        private OcrServiceOptimized CreateNewService()
        {
            var service = new OcrServiceOptimized();
            service.InitModels(_detPath, _clsPath, _recPath, _keysPath, _numThread);
            return service;
        }

        /// <summary>
        /// 预热服务池
        /// </summary>
        public async Task WarmupAsync(int warmupCount = -1)
        {
            if (warmupCount == -1) warmupCount = _maxPoolSize;
            warmupCount = Math.Min(warmupCount, _maxPoolSize);

            Console.WriteLine($"开始预热OCR服务池，预热数量: {warmupCount}");

            var warmupTasks = new Task[warmupCount];
            for (int i = 0; i < warmupCount; i++)
            {
                warmupTasks[i] = Task.Run(async () =>
                {
                    using (var pooledService = await AcquireServiceAsync())
                    {
                        // 预热完成，服务会自动归还到池中
                    }
                });
            }

            await Task.WhenAll(warmupTasks);
            Console.WriteLine("OCR服务池预热完成");
        }

        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;

            Console.WriteLine("正在释放OCR服务池...");

            // 释放所有服务实例
            while (_servicePool.TryDequeue(out var service))
            {
                service?.Dispose();
            }

            _semaphore?.Dispose();
            Console.WriteLine("OCR服务池已释放");
        }
    }

    /// <summary>
    /// 池化的OCR服务包装器
    /// </summary>
    public class PooledOcrService : IDisposable
    {
        private readonly OcrServiceOptimized _service;
        private readonly OcrServicePool _pool;
        private bool _disposed = false;

        internal PooledOcrService(OcrServiceOptimized service, OcrServicePool pool)
        {
            _service = service ?? throw new ArgumentNullException(nameof(service));
            _pool = pool ?? throw new ArgumentNullException(nameof(pool));
        }

        /// <summary>
        /// 获取OCR服务实例
        /// </summary>
        public OcrServiceOptimized Service => _service;

        /// <summary>
        /// 检测文本（简单版本）
        /// </summary>
        public string DetectText(string imagePath)
        {
            return _service.DetectText(imagePath);
        }

        /// <summary>
        /// 检测文本块（简单版本）
        /// </summary>
        public System.Collections.Generic.List<TextBlock> DetectTextBlocks(string imagePath)
        {
            return _service.DetectTextBlocks(imagePath);
        }

        /// <summary>
        /// 检测文本块（带性能统计）
        /// </summary>
        public System.Collections.Generic.List<TextBlock> DetectTextBlocks(string imagePath, out OcrServiceOptimized.PerformanceStats stats)
        {
            return _service.DetectTextBlocks(imagePath, out stats);
        }

        /// <summary>
        /// 检测文本块（完整参数）
        /// </summary>
        public System.Collections.Generic.List<TextBlock> DetectTextBlocks(string imagePath, int padding, int maxSideLen, 
            float boxScoreThresh, float boxThresh, float unClipRatio, bool doAngle, bool mostAngle)
        {
            return _service.DetectTextBlocks(imagePath, padding, maxSideLen, boxScoreThresh, boxThresh, unClipRatio, doAngle, mostAngle);
        }

        /// <summary>
        /// 检测文本块（完整参数+性能统计）
        /// </summary>
        public System.Collections.Generic.List<TextBlock> DetectTextBlocks(string imagePath, int padding, int maxSideLen, 
            float boxScoreThresh, float boxThresh, float unClipRatio, bool doAngle, bool mostAngle, 
            out OcrServiceOptimized.PerformanceStats stats)
        {
            return _service.DetectTextBlocks(imagePath, padding, maxSideLen, boxScoreThresh, boxThresh, unClipRatio, doAngle, mostAngle, out stats);
        }

        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;

            // 将服务归还到池中
            _pool.ReturnService(_service);
        }
    }

    /// <summary>
    /// 并行处理结果
    /// </summary>
    public class ParallelProcessResult
    {
        public string ImagePath { get; set; }
        public System.Collections.Generic.List<TextBlock> TextBlocks { get; set; }
        public OcrServiceOptimized.PerformanceStats Stats { get; set; }
        public Exception Error { get; set; }
        public bool IsSuccess => Error == null;
        public long ProcessingTimeMs { get; set; }
        public int ThreadId { get; set; }

        public override string ToString()
        {
            if (IsSuccess)
            {
                return $"[线程{ThreadId}] {System.IO.Path.GetFileName(ImagePath)}: {TextBlocks.Count}个文本块, {ProcessingTimeMs}ms";
            }
            else
            {
                return $"[线程{ThreadId}] {System.IO.Path.GetFileName(ImagePath)}: 失败 - {Error?.Message}";
            }
        }
    }
}
