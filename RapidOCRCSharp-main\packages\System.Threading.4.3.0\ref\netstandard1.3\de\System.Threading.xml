﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading</name>
  </assembly>
  <members>
    <member name="T:System.Threading.AbandonedMutexException">
      <summary>Die Ausnahme, die ausgelöst wird, wenn ein Thread ein <see cref="T:System.Threading.Mutex" />-Objekt abruft, das von einem anderen Thread abgebrochen wurde, indem das Objekt beim Beenden nicht freigegeben wurde.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.AbandonedMutexException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.AbandonedMutexException" />-Klasse mit Standardwerten.</summary>
    </member>
    <member name="M:System.Threading.AbandonedMutexException.#ctor(System.Int32,System.Threading.WaitHandle)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.AbandonedMutexException" />-Klasse mit einem festgelegten Index für den abgebrochenen Mutex (falls zutreffend) und einem <see cref="T:System.Threading.Mutex" />-Objekt, das den Mutex darstellt.</summary>
      <param name="location">Der Index des abgebrochenen Mutex im Array von WaitHandles, wenn die Ausnahme für die <see cref="Overload:System.Threading.WaitHandle.WaitAny" />-Methode ausgelöst wird, oder -1, wenn die Ausnahme für die <see cref="Overload:System.Threading.WaitHandle.WaitOne" />-Methode oder die <see cref="Overload:System.Threading.WaitHandle.WaitAll" />-Methode ausgelöst wird.</param>
      <param name="handle">Ein <see cref="T:System.Threading.Mutex" />-Objekt, das den abgebrochenen Mutex darstellt.</param>
    </member>
    <member name="M:System.Threading.AbandonedMutexException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.AbandonedMutexException" />-Klasse mit einer angegebenen Fehlermeldung.</summary>
      <param name="message">Eine Fehlermeldung, in der die Ursache der Ausnahme erklärt wird.</param>
    </member>
    <member name="M:System.Threading.AbandonedMutexException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.AbandonedMutexException" />-Klasse mit einer festgelegten Fehlermeldung und einer festgelegten inneren Ausnahme. </summary>
      <param name="message">Eine Fehlermeldung, in der die Ursache der Ausnahme erklärt wird.</param>
      <param name="inner">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="inner" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="M:System.Threading.AbandonedMutexException.#ctor(System.String,System.Exception,System.Int32,System.Threading.WaitHandle)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.AbandonedMutexException" />-Klasse mit einer festgelegten Fehlermeldung, der inneren Ausnahme, dem Index für den abgebrochenen Mutex (falls zutreffend) und einem <see cref="T:System.Threading.Mutex" />-Objekt, das den Mutex darstellt.</summary>
      <param name="message">Eine Fehlermeldung, in der die Ursache der Ausnahme erklärt wird.</param>
      <param name="inner">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="inner" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
      <param name="location">Der Index des abgebrochenen Mutex im Array von WaitHandles, wenn die Ausnahme für die <see cref="Overload:System.Threading.WaitHandle.WaitAny" />-Methode ausgelöst wird, oder -1, wenn die Ausnahme für die <see cref="Overload:System.Threading.WaitHandle.WaitOne" />-Methode oder die <see cref="Overload:System.Threading.WaitHandle.WaitAll" />-Methode ausgelöst wird.</param>
      <param name="handle">Ein <see cref="T:System.Threading.Mutex" />-Objekt, das den abgebrochenen Mutex darstellt.</param>
    </member>
    <member name="M:System.Threading.AbandonedMutexException.#ctor(System.String,System.Int32,System.Threading.WaitHandle)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.AbandonedMutexException" />-Klasse mit einer festgelegten Fehlermeldung, dem Index des abgebrochenen Mutex (falls zutreffend) und dem abgebrochenen Mutex. </summary>
      <param name="message">Eine Fehlermeldung, in der die Ursache der Ausnahme erklärt wird.</param>
      <param name="location">Der Index des abgebrochenen Mutex im Array von WaitHandles, wenn die Ausnahme für die <see cref="Overload:System.Threading.WaitHandle.WaitAny" />-Methode ausgelöst wird, oder -1, wenn die Ausnahme für die <see cref="Overload:System.Threading.WaitHandle.WaitOne" />-Methode oder die <see cref="Overload:System.Threading.WaitHandle.WaitAll" />-Methode ausgelöst wird.</param>
      <param name="handle">Ein <see cref="T:System.Threading.Mutex" />-Objekt, das den abgebrochenen Mutex darstellt.</param>
    </member>
    <member name="P:System.Threading.AbandonedMutexException.Mutex">
      <summary>Ruft den abgebrochenen Mutex ab, das die Ausnahme verursacht hat (falls bekannt).</summary>
      <returns>Ein <see cref="T:System.Threading.Mutex" />-Objekt, das den abgebrochenen Mutex darstellt, oder null, wenn der abgebrochene Mutex nicht bestimmt werden konnte.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.AbandonedMutexException.MutexIndex">
      <summary>Ruft den Index des abgebrochenen Mutex ab, der die Ausnahme verursacht hat (falls bekannt).</summary>
      <returns>Der Index des <see cref="T:System.Threading.Mutex" />-Objekts, das der abgebrochene Mutex darstellt, im Array von WaitHandles, die an die <see cref="Overload:System.Threading.WaitHandle.WaitAny" />-Methode übergeben wurden, oder -1, wenn der Index des abgebrochenen Mutex nicht bestimmt werden konnte.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.AsyncLocal`1">
      <summary>Stellt Umgebungsdaten dar, die für eine angegebene asynchrone Ablaufsteuerung lokal sind, wie etwa eine asynchrone Methode. </summary>
      <typeparam name="T">Der Typ der Umgebungsdaten. </typeparam>
    </member>
    <member name="M:System.Threading.AsyncLocal`1.#ctor">
      <summary>Instanziiert eine <see cref="T:System.Threading.AsyncLocal`1" />-Instanz, die keine Änderungsbenachrichtigungen empfängt. </summary>
    </member>
    <member name="M:System.Threading.AsyncLocal`1.#ctor(System.Action{System.Threading.AsyncLocalValueChangedArgs{`0}})">
      <summary>Instanziiert eine lokale <see cref="T:System.Threading.AsyncLocal`1" />-Instanz, die Änderungsbenachrichtigungen empfängt. </summary>
      <param name="valueChangedHandler">Der Delegat, der aufgerufen wird, wenn sich der aktuelle Wert auf einem beliebigen Thread ändert. </param>
    </member>
    <member name="P:System.Threading.AsyncLocal`1.Value">
      <summary>Ruft den Wert der Umgebungsdaten ab oder legt ihn fest. </summary>
      <returns>Der Wert der Umgebungsdaten. </returns>
    </member>
    <member name="T:System.Threading.AsyncLocalValueChangedArgs`1">
      <summary>Die Klasse, die <see cref="T:System.Threading.AsyncLocal`1" />-Instanzen, die sich für Änderungsbenachrichtigungen registrieren, Informationen über Datenänderungen zur Verfügung stellt. </summary>
      <typeparam name="T">Der Typ der Daten. </typeparam>
    </member>
    <member name="P:System.Threading.AsyncLocalValueChangedArgs`1.CurrentValue">
      <summary>Ruft den aktuellen Wert der Daten ab. </summary>
      <returns>Der aktuelle Wert der Daten. </returns>
    </member>
    <member name="P:System.Threading.AsyncLocalValueChangedArgs`1.PreviousValue">
      <summary>Ruft den vorherigen Wert der Daten ab.</summary>
      <returns>Der vorherige Wert der Daten. </returns>
    </member>
    <member name="P:System.Threading.AsyncLocalValueChangedArgs`1.ThreadContextChanged">
      <summary>Gibt einen Wert zurück, der angibt, ob sich der Wert aufgrund einer Änderung des Ausführungskontexts ändert. </summary>
      <returns>true, wenn sich der Wert aufgrund einer Änderung des Ausführungstexts ändert, andernfalls false. </returns>
    </member>
    <member name="T:System.Threading.AutoResetEvent">
      <summary>Benachrichtigt einen wartenden Thread über das Eintreten eines Ereignisses.Diese Klasse kann nicht vererbt werden.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.AutoResetEvent.#ctor(System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.AutoResetEvent" />-Klasse mit einem booleschen Wert, der angibt, ob der anfängliche Zustand auf „signalisiert“ festgelegt werden soll.</summary>
      <param name="initialState">true, wenn der anfängliche Zustand auf „signalisiert“ festgelegt werden soll. false, wenn der anfängliche Zustand auf „nicht signalisiert“ festgelegt werden soll. </param>
    </member>
    <member name="T:System.Threading.Barrier">
      <summary>Ermöglicht es mehreren Aufgaben, parallel über mehrere Phasen gemeinsam an einem Algorithmus zu arbeiten.</summary>
    </member>
    <member name="M:System.Threading.Barrier.#ctor(System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Barrier" />-Klasse.</summary>
      <param name="participantCount">Die Anzahl teilnehmender Threads.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="participantCount" /> ist kleiner als 0 oder größer als 32,767.</exception>
    </member>
    <member name="M:System.Threading.Barrier.#ctor(System.Int32,System.Action{System.Threading.Barrier})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Barrier" />-Klasse.</summary>
      <param name="participantCount">Die Anzahl teilnehmender Threads.</param>
      <param name="postPhaseAction">
        <see cref="T:System.Action`1" />, die nach jeder Phase ausgeführt wird. NULL (Nothing in Visual Basic) wird möglicherweise übergeben, um keine Aktion anzugeben.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="participantCount" /> ist kleiner als 0 oder größer als 32,767.</exception>
    </member>
    <member name="M:System.Threading.Barrier.AddParticipant">
      <summary>Benachrichtigt <see cref="T:System.Threading.Barrier" /> über das Vorhandensein eines weiteren Teilnehmers.</summary>
      <returns>Die Phasennummer der Grenze, an der die neuen Teilnehmer zuerst teilnehmen.</returns>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Einen Teilnehmer hinzuzufügen würde verursachen, dass die Teilnehmeranzahl der Barriere 32.767 überschreitet.– oder –Die Methode wurde aus einer Postphasenaktion aufgerufen.</exception>
    </member>
    <member name="M:System.Threading.Barrier.AddParticipants(System.Int32)">
      <summary>Benachrichtigt <see cref="T:System.Threading.Barrier" /> über das Vorhandensein weiterer Teilnehmer.</summary>
      <returns>Die Phasennummer der Grenze, an der die neuen Teilnehmer zuerst teilnehmen.</returns>
      <param name="participantCount">Die Anzahl zusätzlicher Teilnehmer, die der Grenze hinzugefügt werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="participantCount" /> ist kleiner als 0.– oder –<paramref name="participantCount" />-Teilnehmer hinzuzufügen würde verursachen, dass die Teilnehmeranzahl der Barriere 32.767 überschreitet.</exception>
      <exception cref="T:System.InvalidOperationException">Die Methode wurde aus einer Postphasenaktion aufgerufen.</exception>
    </member>
    <member name="P:System.Threading.Barrier.CurrentPhaseNumber">
      <summary>Ruft die Nummer der aktuellen Phase der Grenze ab.</summary>
      <returns>Gibt die Nummer der aktuellen Phase der Grenze zurück.</returns>
    </member>
    <member name="M:System.Threading.Barrier.Dispose">
      <summary>Gibt alle von der aktuellen Instanz der <see cref="T:System.Threading.Barrier" />-Klasse verwendeten Ressourcen frei.</summary>
      <exception cref="T:System.InvalidOperationException">Die Methode wurde aus einer Postphasenaktion aufgerufen.</exception>
    </member>
    <member name="M:System.Threading.Barrier.Dispose(System.Boolean)">
      <summary>Gibt die vom <see cref="T:System.Threading.Barrier" /> verwendeten nicht verwalteten Ressourcen und optional auch die verwalteten Ressourcen frei.</summary>
      <param name="disposing">True, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um nur nicht verwaltete Ressourcen freizugeben.</param>
    </member>
    <member name="P:System.Threading.Barrier.ParticipantCount">
      <summary>Ruft die Gesamtanzahl von Teilnehmern für die Grenze ab.</summary>
      <returns>Gibt die Gesamtanzahl von Teilnehmern für die Grenze zurück.</returns>
    </member>
    <member name="P:System.Threading.Barrier.ParticipantsRemaining">
      <summary>Ruft die Anzahl von Teilnehmern für die Grenze ab, die in der aktuellen Phase noch nicht signalisiert haben.</summary>
      <returns>Gibt die Anzahl von Teilnehmern für die Grenze zurück, die in der aktuellen Phase noch nicht signalisiert haben.</returns>
    </member>
    <member name="M:System.Threading.Barrier.RemoveParticipant">
      <summary>Benachrichtigt <see cref="T:System.Threading.Barrier" />, dass ein Teilnehmer nicht mehr vorhanden ist.</summary>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Die Barriere hat bereits 0 Teilnehmer.– oder –Die Methode wurde aus einer Postphasenaktion aufgerufen.</exception>
    </member>
    <member name="M:System.Threading.Barrier.RemoveParticipants(System.Int32)">
      <summary>Benachrichtigt <see cref="T:System.Threading.Barrier" /> über die geringere Anzahl von Teilnehmern.</summary>
      <param name="participantCount">Die Anzahl zusätzlicher Teilnehmer, die aus der Grenze entfernt werden sollen.</param>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="participantCount" /> ist kleiner als 0.</exception>
      <exception cref="T:System.InvalidOperationException">Die Barriere hat bereits 0 Teilnehmer.– oder –Die Methode wurde aus einer Postphasenaktion aufgerufen. – oder –aktuelle Teilnehmeranzahl ist kleiner als der angegebene participantCount</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die gesamte Teilnehmeranzahl ist kleiner als der angegebene<paramref name=" participantCount" /></exception>
    </member>
    <member name="M:System.Threading.Barrier.SignalAndWait">
      <summary>Signalisiert, dass ein Teilnehmer die Barriere erreicht hat und darauf wartet, dass alle anderen Teilnehmer die Barriere ebenfalls erreichen.</summary>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Die Methode wurde innerhalb einer Postphasenaktion aufgerufen, die Barriere hat derzeit 0 Teilnehmer, oder die Barriere wird von mehr Threads gemeldet als Teilnehmer registriert sind.</exception>
      <exception cref="T:System.Threading.BarrierPostPhaseException">Wenn eine Ausnahme aus der Post-Phasenaktion einer Grenze ausgelöst wird, nachdem alle teilnehmenden Threads SignalAndWait aufgerufen haben, wird die Ausnahme in einer BarrierPostPhaseException umbrochen und für alle teilnehmenden Threads ausgelöst.</exception>
    </member>
    <member name="M:System.Threading.Barrier.SignalAndWait(System.Int32)">
      <summary>Signalisiert, dass ein Teilnehmer die Barriere erreicht hat und darauf wartet, dass alle anderen Teilnehmer die Barriere ebenfalls erreichen. Dabei wird eine 32-Bit-Ganzzahl mit Vorzeichen zum Messen des Timeouts verwendet.</summary>
      <returns>wenn alle Teilnehmer die Grenze innerhalb der angegebenen Zeit erreicht haben, andernfalls false.</returns>
      <param name="millisecondsTimeout">Die Wartezeit in Millisekunden oder <see cref="F:System.Threading.Timeout.Infinite" /> (-1) für Warten ohne Timeout.</param>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> ist eine negative Zahl, aber nicht -1. Der Wert -1 gibt einen Endlostimeout an.</exception>
      <exception cref="T:System.InvalidOperationException">Die Methode wurde innerhalb einer Postphasenaktion aufgerufen, die Barriere hat derzeit 0 Teilnehmer, oder die Barriere wird von mehr Threads gemeldet als Teilnehmer registriert sind.</exception>
      <exception cref="T:System.Threading.BarrierPostPhaseException">Wenn eine Ausnahme aus der Post-Phasenaktion einer Grenze ausgelöst wird, nachdem alle teilnehmenden Threads SignalAndWait aufgerufen haben, wird die Ausnahme in einer BarrierPostPhaseException umbrochen und für alle teilnehmenden Threads ausgelöst.</exception>
    </member>
    <member name="M:System.Threading.Barrier.SignalAndWait(System.Int32,System.Threading.CancellationToken)">
      <summary>Signalisiert, dass ein Teilnehmer die Barriere erreicht hat und darauf wartet, dass alle anderen Teilnehmer die Barriere ebenfalls erreichen. Dabei wird eine 32-Bit-Ganzzahl mit Vorzeichen zum Messen des Timeouts verwendet und ein Abbruchtoken berücksichtigt.</summary>
      <returns>wenn alle Teilnehmer die Grenze innerhalb der angegebenen Zeit erreicht haben, andernfalls false.</returns>
      <param name="millisecondsTimeout">Die Wartezeit in Millisekunden oder <see cref="F:System.Threading.Timeout.Infinite" /> (-1) für Warten ohne Timeout.</param>
      <param name="cancellationToken">Das zu überwachende <see cref="T:System.Threading.CancellationToken" />.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> wurde abgebrochen.</exception>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> ist eine negative Zahl, aber nicht -1. Der Wert -1 gibt einen Endlostimeout an.</exception>
      <exception cref="T:System.InvalidOperationException">Die Methode wurde innerhalb einer Postphasenaktion aufgerufen, die Barriere hat derzeit 0 Teilnehmer, oder die Barriere wird von mehr Threads gemeldet als Teilnehmer registriert sind.</exception>
    </member>
    <member name="M:System.Threading.Barrier.SignalAndWait(System.Threading.CancellationToken)">
      <summary>Signalisiert, dass ein Teilnehmer die Barriere erreicht hat und darauf wartet, dass alle anderen Teilnehmer die Barriere erreichen. Dabei wird ein Abbruchtoken überwacht.</summary>
      <param name="cancellationToken">Das zu überwachende <see cref="T:System.Threading.CancellationToken" />.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> wurde abgebrochen.</exception>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Die Methode wurde innerhalb einer Postphasenaktion aufgerufen, die Barriere hat derzeit 0 Teilnehmer, oder die Barriere wird von mehr Threads gemeldet als Teilnehmer registriert sind.</exception>
    </member>
    <member name="M:System.Threading.Barrier.SignalAndWait(System.TimeSpan)">
      <summary>Signalisiert, dass ein Teilnehmer die Barriere erreicht hat und darauf wartet, dass alle anderen Teilnehmer die Barriere ebenfalls erreichen. Dabei wird das Zeitintervall mit einem <see cref="T:System.TimeSpan" />-Objekt gemessen.</summary>
      <returns>True, wenn alle anderen Teilnehmer die Grenze erreicht haben, andernfalls false.</returns>
      <param name="timeout">Eine <see cref="T:System.TimeSpan" />-Struktur, die die Anzahl der zu wartenden Millisekunden angibt, oder eine <see cref="T:System.TimeSpan" />-Struktur, die -1 Millisekunden zum unendlichen Warten angibt.</param>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> ist eine negative Zahl ungleich -1 Millisekunden, die ein unendliches Timeout darstellt, oder er ist größer als 32.767.</exception>
      <exception cref="T:System.InvalidOperationException">Die Methode wurde innerhalb einer Postphasenaktion aufgerufen, die Barriere hat derzeit 0 Teilnehmer, oder die Barriere wird von mehr Threads gemeldet als Teilnehmer registriert sind.</exception>
    </member>
    <member name="M:System.Threading.Barrier.SignalAndWait(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Signalisiert, dass ein Teilnehmer die Barriere erreicht hat und darauf wartet, dass alle anderen Teilnehmer die Barriere ebenfalls erreichen. Dabei wird das Zeitintervall mit einem <see cref="T:System.TimeSpan" />-Objekt gemessen und ein Abbruchtoken berücksichtigt.</summary>
      <returns>True, wenn alle anderen Teilnehmer die Grenze erreicht haben, andernfalls false.</returns>
      <param name="timeout">Eine <see cref="T:System.TimeSpan" />-Struktur, die die Anzahl der zu wartenden Millisekunden angibt, oder eine <see cref="T:System.TimeSpan" />-Struktur, die -1 Millisekunden zum unendlichen Warten angibt.</param>
      <param name="cancellationToken">Das zu überwachende <see cref="T:System.Threading.CancellationToken" />.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> wurde abgebrochen.</exception>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> ist eine negative Zahl, aber nicht -1 Millisekunde. Ein Wert von -1 Millisekunde gibt einen unendlichen Timeout an.</exception>
      <exception cref="T:System.InvalidOperationException">Die Methode wurde innerhalb einer Postphasenaktion aufgerufen, die Barriere hat derzeit 0 Teilnehmer, oder die Barriere wird von mehr Threads gemeldet als Teilnehmer registriert sind.</exception>
    </member>
    <member name="T:System.Threading.BarrierPostPhaseException">
      <summary>Die Ausnahme, die bei einem Fehler der Nachphasenaktion einer <see cref="T:System.Threading.Barrier" /> ausgelöst wird.</summary>
    </member>
    <member name="M:System.Threading.BarrierPostPhaseException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.BarrierPostPhaseException" />-Klasse mit einer vom System generierten Meldung, die den Fehler beschreibt.</summary>
    </member>
    <member name="M:System.Threading.BarrierPostPhaseException.#ctor(System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.BarrierPostPhaseException" />-Klasse mit der angegebenen internen Ausnahme.</summary>
      <param name="innerException">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.</param>
    </member>
    <member name="M:System.Threading.BarrierPostPhaseException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.BarrierPostPhaseException" />-Klasse mit einer angegebenen Meldung, die den Fehler beschreibt.</summary>
      <param name="message">Die Meldung, in der die Ausnahme beschrieben wirdDer Aufrufer dieses Konstruktors muss sicherstellen, dass diese Zeichenfolge für die aktuelle Systemkultur lokalisiert wurde.</param>
    </member>
    <member name="M:System.Threading.BarrierPostPhaseException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.BarrierPostPhaseException" />-Klasse mit einer angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Meldung, in der die Ausnahme beschrieben wirdDer Aufrufer dieses Konstruktors muss sicherstellen, dass diese Zeichenfolge für die aktuelle Systemkultur lokalisiert wurde.</param>
      <param name="innerException">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="innerException" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="T:System.Threading.ContextCallback">
      <summary>Stellt eine Methode dar, die in einem neuen Kontext aufgerufen werden muss.  </summary>
      <param name="state">Ein Objekt mit den Informationen, die von der Rückrufmethode bei jeder Ausführung verwendet werden.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.CountdownEvent">
      <summary>Stellt einen Synchronisierungsprimitiven dar, der signalisiert wird, wenn seine Anzahl 0 (null) erreicht.</summary>
    </member>
    <member name="M:System.Threading.CountdownEvent.#ctor(System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.CountdownEvent" />-Klasse mit der angegebenen Anzahl.</summary>
      <param name="initialCount">Die zum Festlegen von <see cref="T:System.Threading.CountdownEvent" /> ursprünglich erforderliche Anzahl von Signalen.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialCount" /> ist kleiner als 0.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.AddCount">
      <summary>Erhöht die aktuelle Anzahl von <see cref="T:System.Threading.CountdownEvent" /> um 1.</summary>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Die aktuelle Instanz ist bereits festgelegt.– oder –<see cref="P:System.Threading.CountdownEvent.CurrentCount" /> ist größer oder gleich <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.AddCount(System.Int32)">
      <summary>Erhöht die aktuelle Anzahl von <see cref="T:System.Threading.CountdownEvent" /> um einen angegebenen Wert.</summary>
      <param name="signalCount">Der Wert, um den <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> erhöht werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signalCount" /> ist kleiner oder gleich 0.</exception>
      <exception cref="T:System.InvalidOperationException">Die aktuelle Instanz ist bereits festgelegt.– oder –<see cref="P:System.Threading.CountdownEvent.CurrentCount" /> ist größer gleich <see cref="F:System.Int32.MaxValue" />, nach die Anzahl schrittweise durch <paramref name="signalCount." /> erhöht wird.</exception>
    </member>
    <member name="P:System.Threading.CountdownEvent.CurrentCount">
      <summary>Ruft die Anzahl verbleibender Signale ab, die zum Festlegen des Ereignisses erforderlich sind.</summary>
      <returns> Die Anzahl verbleibender Signale, die zum Festlegen des Ereignisses erforderlich sind.</returns>
    </member>
    <member name="M:System.Threading.CountdownEvent.Dispose">
      <summary>Gibt alle von der aktuellen Instanz der <see cref="T:System.Threading.CountdownEvent" />-Klasse verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Threading.CountdownEvent.Dispose(System.Boolean)">
      <summary>Gibt die vom <see cref="T:System.Threading.CountdownEvent" /> verwendeten nicht verwalteten Ressourcen und optional auch die verwalteten Ressourcen frei.</summary>
      <param name="disposing">True, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um nur nicht verwaltete Ressourcen freizugeben.</param>
    </member>
    <member name="P:System.Threading.CountdownEvent.InitialCount">
      <summary>Ruft die Anzahl von Signalen ab, die ursprünglich zum Festlegen des Ereignisses erforderlich waren.</summary>
      <returns> Die Anzahl von Signalen, die ursprünglich zum Festlegen des Ereignisses erforderlich waren.</returns>
    </member>
    <member name="P:System.Threading.CountdownEvent.IsSet">
      <summary>Bestimmt, ob das Ereignis festgelegt wurde.</summary>
      <returns>True, wenn das Ereignis festgelegt wurde, andernfalls false.</returns>
    </member>
    <member name="M:System.Threading.CountdownEvent.Reset">
      <summary>Setzt <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> auf den Wert von <see cref="P:System.Threading.CountdownEvent.InitialCount" /> zurück.</summary>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Reset(System.Int32)">
      <summary>Setzt die <see cref="P:System.Threading.CountdownEvent.InitialCount" />-Eigenschaft auf einen angegebenen Wert zurück.</summary>
      <param name="count">Die zum Festlegen von <see cref="T:System.Threading.CountdownEvent" /> erforderliche Anzahl von Signalen.</param>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> ist kleiner als 0.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Signal">
      <summary>Registriert ein Signal beim <see cref="T:System.Threading.CountdownEvent" /> und dekrementiert den Wert von <see cref="P:System.Threading.CountdownEvent.CurrentCount" />.</summary>
      <returns>True, wenn die Anzahl aufgrund des Signals 0 (null) erreicht hat und das Ereignis festgelegt wurde, andernfalls false.</returns>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Die aktuelle Instanz ist bereits festgelegt.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Signal(System.Int32)">
      <summary>Registriert mehrere Signale bei <see cref="T:System.Threading.CountdownEvent" /> und verringert den Wert von <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> um den angegebenen Wert.</summary>
      <returns>True, wenn die Anzahl aufgrund der Signale 0 (null) erreicht hat und das Ereignis festgelegt wurde, andernfalls false.</returns>
      <param name="signalCount">Die Anzahl zu registrierender Signale.</param>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signalCount" /> ist kleiner als 1.</exception>
      <exception cref="T:System.InvalidOperationException">Die aktuelle Instanz ist bereits festgelegt. -oder- <paramref name="signalCount" /> ist größer als <see cref="P:System.Threading.CountdownEvent.CurrentCount" />.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.TryAddCount">
      <summary>Versucht, <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> um eins zu inkrementieren.</summary>
      <returns>True, wenn die Anzahl erfolgreich erhöht wurde, andernfalls false.Wenn <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> bereits 0 (null) ist, gibt diese Methode false zurück.</returns>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> ist gleich <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.TryAddCount(System.Int32)">
      <summary>Versucht, <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> durch einen angegebenen Wert zu inkrementieren.</summary>
      <returns>True, wenn die Anzahl erfolgreich erhöht wurde, andernfalls false.Wenn <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> bereits 0 (null) ist, wird false zurückgegeben.</returns>
      <param name="signalCount">Der Wert, um den <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> erhöht werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signalCount" /> ist kleiner oder gleich 0.</exception>
      <exception cref="T:System.InvalidOperationException">Die aktuelle Instanz ist bereits festgelegt.– oder –<see cref="P:System.Threading.CountdownEvent.CurrentCount" /> + <paramref name="signalCount" /> ist gleich oder größer als <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Wait">
      <summary>Blockiert den aktuellen Thread, bis <see cref="T:System.Threading.CountdownEvent" /> festgelegt wird.</summary>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Wait(System.Int32)">
      <summary>Blockiert den aktuellen Thread, bis <see cref="T:System.Threading.CountdownEvent" /> festgelegt wird, wobei eine 32-Bit-Ganzzahl mit Vorzeichen zum Messen des Timeouts verwendet wird.</summary>
      <returns>True, wenn <see cref="T:System.Threading.CountdownEvent" /> festgelegt wurde, andernfalls false.</returns>
      <param name="millisecondsTimeout">Die Wartezeit in Millisekunden oder <see cref="F:System.Threading.Timeout.Infinite" /> (-1) für Warten ohne Timeout.</param>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> ist eine negative Zahl, aber nicht -1. Der Wert -1 gibt einen Endlostimeout an.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Wait(System.Int32,System.Threading.CancellationToken)">
      <summary>Blockiert den aktuellen Thread, bis <see cref="T:System.Threading.CountdownEvent" /> festgelegt wird, wobei eine 32-Bit-Ganzzahl mit Vorzeichen zum Messen des Timeouts verwendet und ein <see cref="T:System.Threading.CancellationToken" /> überwacht wird.</summary>
      <returns>True, wenn <see cref="T:System.Threading.CountdownEvent" /> festgelegt wurde, andernfalls false.</returns>
      <param name="millisecondsTimeout">Die Wartezeit in Millisekunden oder <see cref="F:System.Threading.Timeout.Infinite" /> (-1) für Warten ohne Timeout.</param>
      <param name="cancellationToken">Das zu überwachende <see cref="T:System.Threading.CancellationToken" />.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> wurde abgebrochen.</exception>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben. - Oder - Die <see cref="T:System.Threading.CancellationTokenSource" />, die <paramref name="cancellationToken" /> erstellte, wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> ist eine negative Zahl, aber nicht -1. Der Wert -1 gibt einen Endlostimeout an.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Wait(System.Threading.CancellationToken)">
      <summary>Blockiert den aktuellen Thread, bis <see cref="T:System.Threading.CountdownEvent" /> festgelegt wird, wobei ein <see cref="T:System.Threading.CancellationToken" /> überwacht wird.</summary>
      <param name="cancellationToken">Das zu überwachende <see cref="T:System.Threading.CancellationToken" />.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> wurde abgebrochen.</exception>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben. - Oder - Die <see cref="T:System.Threading.CancellationTokenSource" />, die <paramref name="cancellationToken" /> erstellte, wurde bereits freigegeben.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Wait(System.TimeSpan)">
      <summary>Blockiert den aktuellen Thread, bis <see cref="T:System.Threading.CountdownEvent" /> festgelegt wird, wobei ein <see cref="T:System.TimeSpan" /> zum Messen des Timeouts verwendet wird.</summary>
      <returns>True, wenn <see cref="T:System.Threading.CountdownEvent" /> festgelegt wurde, andernfalls false.</returns>
      <param name="timeout">Eine <see cref="T:System.TimeSpan" />-Struktur, die die Anzahl der zu wartenden Millisekunden angibt, oder eine <see cref="T:System.TimeSpan" />-Struktur, die -1 Millisekunden zum unendlichen Warten angibt.</param>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> ist eine negative Zahl ungleich  -1 Millisekunden, die ein unendliches Timeout darstellt, - oder - Timeout ist größer als <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Wait(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Blockiert den aktuellen Thread, bis <see cref="T:System.Threading.CountdownEvent" /> festgelegt wird, wobei ein <see cref="T:System.TimeSpan" /> zum Messen des Zeitintervalls verwendet und ein <see cref="T:System.Threading.CancellationToken" /> überwacht wird.</summary>
      <returns>True, wenn <see cref="T:System.Threading.CountdownEvent" /> festgelegt wurde, andernfalls false.</returns>
      <param name="timeout">Eine <see cref="T:System.TimeSpan" />-Struktur, die die Anzahl der zu wartenden Millisekunden angibt, oder eine <see cref="T:System.TimeSpan" />-Struktur, die -1 Millisekunden zum unendlichen Warten angibt.</param>
      <param name="cancellationToken">Das zu überwachende <see cref="T:System.Threading.CancellationToken" />.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> wurde abgebrochen.</exception>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben. - Oder - Die <see cref="T:System.Threading.CancellationTokenSource" />, die <paramref name="cancellationToken" /> erstellte, wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> ist eine negative Zahl ungleich  -1 Millisekunden, die ein unendliches Timeout darstellt, - oder - Timeout ist größer als <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="P:System.Threading.CountdownEvent.WaitHandle">
      <summary>Ruft ein <see cref="T:System.Threading.WaitHandle" /> ab, das verwendet wird, um auf das festzulegende Ereignis zu warten.</summary>
      <returns>Ein <see cref="T:System.Threading.WaitHandle" />, das verwendet wird, um auf das festzulegende Ereignis zu warten.</returns>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
    </member>
    <member name="T:System.Threading.EventResetMode">
      <summary>Gibt an, ob eine <see cref="T:System.Threading.EventWaitHandle" />-Klasse nach dem Empfangen eines Signals automatisch oder manuell zurückgesetzt wird.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Threading.EventResetMode.AutoReset">
      <summary>Bei Signalisierung wird die <see cref="T:System.Threading.EventWaitHandle" />-Methode automatisch nach der Freigabe eines einzigen Threads zurückgesetzt.Wenn sich keine Threads in der Warteschlange befinden, bleibt die <see cref="T:System.Threading.EventWaitHandle" />-Methode solange signalisiert, bis ein Thread blockiert wird. Sie wird zurückgesetzt, nachdem der Thread freigegeben wurde.</summary>
    </member>
    <member name="F:System.Threading.EventResetMode.ManualReset">
      <summary>Bei Signalisierung gibt die <see cref="T:System.Threading.EventWaitHandle" />-Methode alle wartenden Threads frei. Sie bleibt solange signalisiert, bis sie manuell zurückgesetzt wird.</summary>
    </member>
    <member name="T:System.Threading.EventWaitHandle">
      <summary>Stellt ein Threadsynchronisierungsereignis dar.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.EventWaitHandle.#ctor(System.Boolean,System.Threading.EventResetMode)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.EventWaitHandle" />-Klasse und gibt an, ob das WaitHandle anfänglich signalisiert ist und ob es automatisch oder manuell zurückgesetzt wird.</summary>
      <param name="initialState">true, wenn der anfängliche Zustand auf signalisiert festgelegt werden soll. false, wenn er auf nicht signalisiert festgelegt werden soll.</param>
      <param name="mode">Einer der <see cref="T:System.Threading.EventResetMode" />-Werte, die bestimmen, ob das Ereignis automatisch oder manuell zurückgesetzt wird.</param>
    </member>
    <member name="M:System.Threading.EventWaitHandle.#ctor(System.Boolean,System.Threading.EventResetMode,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.EventWaitHandle" />-Klasse, gibt an, ob das WaitHandle anfänglich signalisiert ist, wenn es als Ergebnis dieses Aufrufs erstellt wurde, ob es automatisch oder manuell zurückgesetzt wird, und gibt den Namen eines Systemsynchronisierungsereignisses an.</summary>
      <param name="initialState">true, um den anfänglichen Zustand auf signalisiert festzulegen, wenn das benannte Ereignis als Ergebnis dieses Aufrufs erstellt wird; false, um den Zustand auf nicht signalisiert festzulegen.</param>
      <param name="mode">Einer der <see cref="T:System.Threading.EventResetMode" />-Werte, die bestimmen, ob das Ereignis automatisch oder manuell zurückgesetzt wird.</param>
      <param name="name">Der Name eines systemweiten Synchronisierungsereignisses.</param>
      <exception cref="T:System.IO.IOException">Ein Win32-Fehler ist aufgetreten.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Das benannte Ereignis ist vorhanden und verfügt über Zugriffssteuerungssicherheit, aber der Benutzer verfügt nicht über <see cref="F:System.Security.AccessControl.EventWaitHandleRights.FullControl" />.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">Das benannte Ereignis kann nicht erstellt werden, möglicherweise weil ein WaitHandle eines anderen Typs denselben Namen hat.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> ist länger als 260 Zeichen.</exception>
    </member>
    <member name="M:System.Threading.EventWaitHandle.#ctor(System.Boolean,System.Threading.EventResetMode,System.String,System.Boolean@)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.EventWaitHandle" />-Klasse, gibt an, ob das WaitHandle anfänglich signalisiert ist, wenn es als Ergebnis dieses Aufrufs erstellt wurde, und ob es automatisch oder manuell zurückgesetzt wird, und gibt den Namen eines Systemsynchronisierungsereignisses und eine boolesche Variable an, deren Wert nach dem Aufruf angibt, ob das benannte Systemereignis erstellt wurde.</summary>
      <param name="initialState">true, um den anfänglichen Zustand auf signalisiert festzulegen, wenn das benannte Ereignis als Ergebnis dieses Aufrufs erstellt wird; false, um den Zustand auf nicht signalisiert festzulegen.</param>
      <param name="mode">Einer der <see cref="T:System.Threading.EventResetMode" />-Werte, die bestimmen, ob das Ereignis automatisch oder manuell zurückgesetzt wird.</param>
      <param name="name">Der Name eines systemweiten Synchronisierungsereignisses.</param>
      <param name="createdNew">Enthält nach dem Beenden dieser Methode den Wert true, wenn ein lokales Ereignis erstellt wurde (d. h., wenn <paramref name="name" /> gleich null oder eine leere Zeichenfolge ist) oder wenn das angegebene benannte Systemereignis erstellt wurde. Der Wert ist false, wenn das angegebene benannte Systemsereignis bereits vorhanden war.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <exception cref="T:System.IO.IOException">Ein Win32-Fehler ist aufgetreten.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Das benannte Ereignis ist vorhanden und verfügt über Zugriffssteuerungssicherheit, aber der Benutzer verfügt nicht über <see cref="F:System.Security.AccessControl.EventWaitHandleRights.FullControl" />.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">Das benannte Ereignis kann nicht erstellt werden, möglicherweise weil ein WaitHandle eines anderen Typs denselben Namen hat.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> ist länger als 260 Zeichen.</exception>
    </member>
    <member name="M:System.Threading.EventWaitHandle.OpenExisting(System.String)">
      <summary>Öffnet das bestimmte benannte Synchronisierungsereignis, wenn es bereits vorhanden ist.</summary>
      <returns>Ein Objekt, das das benannte Systemereignis darstellt.</returns>
      <param name="name">Der Name eines systemweiten Synchronisierungsereignisses, das zu öffnen ist.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> ist eine leere Zeichenfolge. - oder -<paramref name="name" /> ist länger als 260 Zeichen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> ist null.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">Das benannte Systemereignis ist nicht vorhanden.</exception>
      <exception cref="T:System.IO.IOException">Ein Win32-Fehler ist aufgetreten.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Das benannte Ereignis ist vorhanden, der Benutzer verfügt jedoch nicht über den nötigen Sicherheitszugriff, um es zu verwenden.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Threading.EventWaitHandle.Reset">
      <summary>Legt den Zustand des Ereignisses auf nicht signalisiert fest, sodass Threads blockiert werden.</summary>
      <returns>true, wenn die Operation erfolgreich ausgeführt wird, andernfalls false.</returns>
      <exception cref="T:System.ObjectDisposedException">Die <see cref="M:System.Threading.EventWaitHandle.Close" />-Methode wurde zuvor für dieses <see cref="T:System.Threading.EventWaitHandle" /> aufgerufen.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.EventWaitHandle.Set">
      <summary>Legt den Zustand des Ereignisses auf signalisiert fest und ermöglicht so einem oder mehreren wartenden Threads fortzufahren.</summary>
      <returns>true, wenn die Operation erfolgreich ausgeführt wird, andernfalls false.</returns>
      <exception cref="T:System.ObjectDisposedException">Die <see cref="M:System.Threading.EventWaitHandle.Close" />-Methode wurde zuvor für dieses <see cref="T:System.Threading.EventWaitHandle" /> aufgerufen.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.EventWaitHandle.TryOpenExisting(System.String,System.Threading.EventWaitHandle@)">
      <summary>Öffnet das bestimmte benannte Synchronisierungsereignis, wenn es bereits vorhanden ist, und gibt einen Wert zurück, der angibt, ob der Vorgang erfolgreich war.</summary>
      <returns>true, wenn das benannte Synchronisierungsereignis erfolgreich geöffnet wurde; andernfalls false.</returns>
      <param name="name">Der Name eines systemweiten Synchronisierungsereignisses, das zu öffnen ist.</param>
      <param name="result">Enthält nach Beenden der Methode ein <see cref="T:System.Threading.EventWaitHandle" />-Objekt, das das benannte Synchronisierungsereignis darstellt, wenn der Aufruf erfolgreich ausgeführt wurde, oder null, wenn der Aufruf fehlgeschlagen ist.Dieser Parameter wird nicht initialisiert behandelt.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> ist eine leere Zeichenfolge.- oder -<paramref name="name" /> ist länger als 260 Zeichen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> ist null.</exception>
      <exception cref="T:System.IO.IOException">Ein Win32-Fehler ist aufgetreten.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Das benannte Ereignis ist vorhanden, der Benutzer verfügt jedoch nicht über den gewünschten Sicherheitszugriff.</exception>
    </member>
    <member name="T:System.Threading.ExecutionContext">
      <summary>Verwaltet den Ausführungskontext für den aktuellen Thread.Diese Klasse kann nicht vererbt werden.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.ExecutionContext.Capture">
      <summary>Zeichnet den Ausführungskontext des aktuellen Threads auf.</summary>
      <returns>Ein <see cref="T:System.Threading.ExecutionContext" />-Objekt, das den Ausführungskontext für den aktuellen Thread darstellt.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.ExecutionContext.Run(System.Threading.ExecutionContext,System.Threading.ContextCallback,System.Object)">
      <summary>Führt für den aktuellen Thread eine Methode in einem angegebenen Ausführungskontext aus.</summary>
      <param name="executionContext">Der festzulegende <see cref="T:System.Threading.ExecutionContext" />.</param>
      <param name="callback">Ein <see cref="T:System.Threading.ContextCallback" />-Delegat, der die im bereitgestellten Ausführungskontext auszuführende Methode darstellt.</param>
      <param name="state">Das Objekt, das an die Rückrufmethode übergeben werden soll.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="executionContext" /> ist null.– oder –<paramref name="executionContext" /> wurde nicht durch einen Aufzeichnungsvorgang ermittelt. – oder –<paramref name="executionContext" /> wurde bereits als Argument für einen Aufruf von <see cref="M:System.Threading.ExecutionContext.Run(System.Threading.ExecutionContext,System.Threading.ContextCallback,System.Object)" /> verwendet.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="Infrastructure" />
      </PermissionSet>
    </member>
    <member name="T:System.Threading.Interlocked">
      <summary>Stellt atomare Operationen für Variablen bereit, die von mehreren Threads gemeinsam genutzt werden. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Add(System.Int32@,System.Int32)">
      <summary>Fügt in einer atomaren Operation zwei 32-Bit-Ganzzahlen hinzu und ersetzt die erste Ganzzahl durch die Summe.</summary>
      <returns>Der unter <paramref name="location1" /> gespeicherte neue Wert.</returns>
      <param name="location1">Eine Variable, die den ersten Wert enthält, der hinzugefügt werden soll.Die Summe der beiden Werte wird in <paramref name="location1" /> gespeichert.</param>
      <param name="value">Der Wert, der der Ganzzahl in <paramref name="location1" /> hinzugefügt werden soll.</param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Add(System.Int64@,System.Int64)">
      <summary>Fügt in einer atomaren Operation zwei 64-Bit-Ganzzahlen hinzu und ersetzt die erste Ganzzahl durch die Summe.</summary>
      <returns>Der unter <paramref name="location1" /> gespeicherte neue Wert.</returns>
      <param name="location1">Eine Variable, die den ersten Wert enthält, der hinzugefügt werden soll.Die Summe der beiden Werte wird in <paramref name="location1" /> gespeichert.</param>
      <param name="value">Der Wert, der der Ganzzahl in <paramref name="location1" /> hinzugefügt werden soll.</param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange(System.Double@,System.Double,System.Double)">
      <summary>Vergleicht zwei Gleitkommazahlen mit doppelter Genauigkeit hinsichtlich ihrer Gleichheit und ersetzt bei vorliegender Gleichheit den ersten Wert.</summary>
      <returns>Der ursprüngliche Wert in <paramref name="location1" />.</returns>
      <param name="location1">Das Ziel, dessen Wert mit <paramref name="comparand" /> verglichen und möglicherweise ersetzt wird. </param>
      <param name="value">Der Wert, der den Zielwert ersetzt, wenn der Vergleich Gleichheit ergibt. </param>
      <param name="comparand">Der Wert, der mit dem Wert in <paramref name="location1" /> verglichen wird. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange(System.Int32@,System.Int32,System.Int32)">
      <summary>Vergleicht zwei 32-Bit-Ganzzahlen mit Vorzeichen hinsichtlich ihrer Gleichheit und ersetzt bei vorliegender Gleichheit den ersten Wert.</summary>
      <returns>Der ursprüngliche Wert in <paramref name="location1" />.</returns>
      <param name="location1">Das Ziel, dessen Wert mit <paramref name="comparand" /> verglichen und möglicherweise ersetzt wird. </param>
      <param name="value">Der Wert, der den Zielwert ersetzt, wenn der Vergleich Gleichheit ergibt. </param>
      <param name="comparand">Der Wert, der mit dem Wert in <paramref name="location1" /> verglichen wird. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange(System.Int64@,System.Int64,System.Int64)">
      <summary>Vergleicht zwei 64-Bit-Ganzzahlen mit Vorzeichen hinsichtlich ihrer Gleichheit und ersetzt bei vorliegender Gleichheit den ersten Wert.</summary>
      <returns>Der ursprüngliche Wert in <paramref name="location1" />.</returns>
      <param name="location1">Das Ziel, dessen Wert mit <paramref name="comparand" /> verglichen und möglicherweise ersetzt wird. </param>
      <param name="value">Der Wert, der den Zielwert ersetzt, wenn der Vergleich Gleichheit ergibt. </param>
      <param name="comparand">Der Wert, der mit dem Wert in <paramref name="location1" /> verglichen wird. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange(System.IntPtr@,System.IntPtr,System.IntPtr)">
      <summary>Vergleicht zwei plattformspezifische Handles oder Zeiger hinsichtlich ihrer Gleichheit und ersetzt bei vorliegender Gleichheit den ersten.</summary>
      <returns>Der ursprüngliche Wert in <paramref name="location1" />.</returns>
      <param name="location1">Der Ziel-<see cref="T:System.IntPtr" />, dessen Wert mit dem Wert von <paramref name="comparand" /> verglichen und möglicherweise durch <paramref name="value" /> ersetzt wird. </param>
      <param name="value">Der <see cref="T:System.IntPtr" />, der den Zielwert ersetzt, wenn der Vergleich Gleichheit ergibt. </param>
      <param name="comparand">Der <see cref="T:System.IntPtr" />, der mit dem Wert in <paramref name="location1" /> verglichen wird. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange(System.Object@,System.Object,System.Object)">
      <summary>Vergleicht zwei Objekte hinsichtlich ihrer Verweisgleichheit und ersetzt bei vorliegender Gleichheit das erste Objekt.</summary>
      <returns>Der ursprüngliche Wert in <paramref name="location1" />.</returns>
      <param name="location1">Das Zielobjekt, das mit <paramref name="comparand" /> verglichen und möglicherweise ersetzt wird. </param>
      <param name="value">Das Objekt, das das Zielobjekt ersetzt, wenn beim Vergleich Gleichheit festgestellt wird. </param>
      <param name="comparand">Das Objekt, das mit dem Objekt in <paramref name="location1" /> verglichen wird. </param>
      <exception cref="T:System.ArgumentNullException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange(System.Single@,System.Single,System.Single)">
      <summary>Vergleicht zwei Gleitkommazahlen mit einfacher Genauigkeit hinsichtlich ihrer Gleichheit und ersetzt bei vorliegender Gleichheit den ersten Wert.</summary>
      <returns>Der ursprüngliche Wert in <paramref name="location1" />.</returns>
      <param name="location1">Das Ziel, dessen Wert mit <paramref name="comparand" /> verglichen und möglicherweise ersetzt wird. </param>
      <param name="value">Der Wert, der den Zielwert ersetzt, wenn der Vergleich Gleichheit ergibt. </param>
      <param name="comparand">Der Wert, der mit dem Wert in <paramref name="location1" /> verglichen wird. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange``1(``0@,``0,``0)">
      <summary>Vergleicht zwei Instanzen des angegebenen Referenztyps <paramref name="T" /> hinsichtlich ihrer Gleichheit und ersetzt bei vorliegender Gleichheit die erste.</summary>
      <returns>Der ursprüngliche Wert in <paramref name="location1" />.</returns>
      <param name="location1">Das Ziel, dessen Wert mit <paramref name="comparand" /> verglichen und möglicherweise ersetzt wird.Dies ist ein Verweisparameter (ref in C#, ByRef in Visual Basic).</param>
      <param name="value">Der Wert, der den Zielwert ersetzt, wenn der Vergleich Gleichheit ergibt. </param>
      <param name="comparand">Der Wert, der mit dem Wert in <paramref name="location1" /> verglichen wird. </param>
      <typeparam name="T">Der Typ, der für <paramref name="location1" />, <paramref name="value" /> und <paramref name="comparand" /> verwendet werden soll.Dieser Typ muss ein Referenztyp sein.</typeparam>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
    </member>
    <member name="M:System.Threading.Interlocked.Decrement(System.Int32@)">
      <summary>Dekrementiert den Wert einer angegebenen Variablen und speichert das Ergebnis in einer atomaren Operation.</summary>
      <returns>Der dekrementierte Wert.</returns>
      <param name="location">Die Variable, deren Wert dekrementiert werden soll. </param>
      <exception cref="T:System.ArgumentNullException">The address of <paramref name="location" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Decrement(System.Int64@)">
      <summary>Dekrementiert den Wert der angegebenen Variablen und speichert das Ergebnis in einer atomaren Operation.</summary>
      <returns>Der dekrementierte Wert.</returns>
      <param name="location">Die Variable, deren Wert dekrementiert werden soll. </param>
      <exception cref="T:System.ArgumentNullException">The address of <paramref name="location" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange(System.Double@,System.Double)">
      <summary>Legt in einer atomaren Operation eine Gleitkommazahl mit doppelter Genauigkeit auf einen angegebenen Wert fest und gibt den ursprünglichen Wert zurück.</summary>
      <returns>Der ursprüngliche Wert von <paramref name="location1" />.</returns>
      <param name="location1">Die Variable, die auf den angegebenen Wert festgelegt werden soll. </param>
      <param name="value">Der Wert, auf den der <paramref name="location1" />-Parameter festgelegt ist. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange(System.Int32@,System.Int32)">
      <summary>Legt eine 32-Bit-Ganzzahl mit Vorzeichen in einer atomaren Operation auf einen angegebenen Wert fest und gibt den ursprünglichen Wert zurück.</summary>
      <returns>Der ursprüngliche Wert von <paramref name="location1" />.</returns>
      <param name="location1">Die Variable, die auf den angegebenen Wert festgelegt werden soll. </param>
      <param name="value">Der Wert, auf den der <paramref name="location1" />-Parameter festgelegt ist. </param>
      <exception cref="T:System.ArgumentNullException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange(System.Int64@,System.Int64)">
      <summary>Legt eine 64-Bit-Ganzzahl mit Vorzeichen in einer atomaren Operation auf einen angegebenen Wert fest und gibt den ursprünglichen Wert zurück.</summary>
      <returns>Der ursprüngliche Wert von <paramref name="location1" />.</returns>
      <param name="location1">Die Variable, die auf den angegebenen Wert festgelegt werden soll. </param>
      <param name="value">Der Wert, auf den der <paramref name="location1" />-Parameter festgelegt ist. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange(System.IntPtr@,System.IntPtr)">
      <summary>Legt in einer atomaren Operation ein plattformspezifisches Handle bzw. einen plattformspezifischen Zeiger auf einen angegebenen Wert fest und gibt den ursprünglichen Wert zurück.</summary>
      <returns>Der ursprüngliche Wert von <paramref name="location1" />.</returns>
      <param name="location1">Die Variable, die auf den angegebenen Wert festgelegt werden soll. </param>
      <param name="value">Der Wert, auf den der <paramref name="location1" />-Parameter festgelegt ist. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange(System.Object@,System.Object)">
      <summary>Legt in einer atomaren Operation ein Objekt auf einen angegebenen Wert fest und gibt einen Verweis auf das ursprüngliche Objekt zurück.</summary>
      <returns>Der ursprüngliche Wert von <paramref name="location1" />.</returns>
      <param name="location1">Die Variable, die auf den angegebenen Wert festgelegt werden soll. </param>
      <param name="value">Der Wert, auf den der <paramref name="location1" />-Parameter festgelegt ist. </param>
      <exception cref="T:System.ArgumentNullException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange(System.Single@,System.Single)">
      <summary>Legt in einer atomaren Operation eine Gleitkommazahl mit einfacher Genauigkeit auf einen angegebenen Wert fest und gibt den ursprünglichen Wert zurück.</summary>
      <returns>Der ursprüngliche Wert von <paramref name="location1" />.</returns>
      <param name="location1">Die Variable, die auf den angegebenen Wert festgelegt werden soll. </param>
      <param name="value">Der Wert, auf den der <paramref name="location1" />-Parameter festgelegt ist. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange``1(``0@,``0)">
      <summary>Legt eine Variable vom angegebenen Typ <paramref name="T" /> in einer atomaren Operation auf einen angegebenen Wert fest und gibt den ursprünglichen Wert zurück.</summary>
      <returns>Der ursprüngliche Wert von <paramref name="location1" />.</returns>
      <param name="location1">Die Variable, die auf den angegebenen Wert festgelegt werden soll.Dies ist ein Verweisparameter (ref in C#, ByRef in Visual Basic).</param>
      <param name="value">Der Wert, auf den der <paramref name="location1" />-Parameter festgelegt ist. </param>
      <typeparam name="T">Der Typ, der für <paramref name="location1" /> und <paramref name="value" /> verwendet werden soll.Dieser Typ muss ein Referenztyp sein.</typeparam>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
    </member>
    <member name="M:System.Threading.Interlocked.Increment(System.Int32@)">
      <summary>Inkrementiert den Wert einer angegebenen Variablen und speichert das Ergebnis in einer atomaren Operation.</summary>
      <returns>Der inkrementierte Wert.</returns>
      <param name="location">Die Variable, deren Wert inkrementiert werden soll. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Increment(System.Int64@)">
      <summary>Inkrementiert den Wert einer angegebenen Variablen und speichert das Ergebnis in einer atomaren Operation.</summary>
      <returns>Der inkrementierte Wert.</returns>
      <param name="location">Die Variable, deren Wert inkrementiert werden soll. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.MemoryBarrier">
      <summary>Synchronisiert den Speicherzugriff wie folgt: Der Prozessor, der den aktuellen Thread ausführt, kann Anweisungen nicht so neu anordnen, dass Speicherzugriffe vor dem Aufruf von <see cref="M:System.Threading.Interlocked.MemoryBarrier" /> nach Speicherzugriffen ausgeführt werden, die nach dem Aufruf von <see cref="M:System.Threading.Interlocked.MemoryBarrier" /> erfolgen.</summary>
    </member>
    <member name="M:System.Threading.Interlocked.Read(System.Int64@)">
      <summary>Gibt einen 64-Bit-Wert zurück, der in einer atomaren Operation geladen wird.</summary>
      <returns>Der geladene Wert.</returns>
      <param name="location">Der zu ladende 64-Bit-Wert.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.LazyInitializer">
      <summary>Stellt verzögerte Initialisierungsroutinen bereit.</summary>
    </member>
    <member name="M:System.Threading.LazyInitializer.EnsureInitialized``1(``0@)">
      <summary>Initialisiert einen Zielverweistyp mit seinem Standardkonstruktor, wenn er noch nicht initialisiert wurde.</summary>
      <returns>Der initialisierte Verweis vom Typ <paramref name="T" />.</returns>
      <param name="target">Ein Verweis vom Typ <paramref name="T" />, der initialisiert werden soll, wenn er noch nicht initialisiert wurde.</param>
      <typeparam name="T">Der Typ des zu initialisierenden Verweises.</typeparam>
      <exception cref="T:System.MemberAccessException">Berechtigungen, auf den Konstruktor des Typs <paramref name="T" /> zuzugreifen, haben gefehlt.</exception>
      <exception cref="T:System.MissingMemberException">Der Typ <paramref name="T" /> besitzt keinen Standardkonstruktor.</exception>
    </member>
    <member name="M:System.Threading.LazyInitializer.EnsureInitialized``1(``0@,System.Boolean@,System.Object@)">
      <summary>Initialisiert einen Zielverweis- oder Werttyp mit seinem Standardkonstruktor, wenn er noch nicht initialisiert wurde.</summary>
      <returns>Der initialisierte Wert vom Typ <paramref name="T" />.</returns>
      <param name="target">Ein Verweis oder Wert vom Typ <paramref name="T" />, der initialisiert werden soll, wenn er noch nicht initialisiert wurde.</param>
      <param name="initialized">Ein Verweis auf einen booleschen Wert, der bestimmt, ob das Ziel bereits initialisiert wurde.</param>
      <param name="syncLock">Ein Verweis auf ein Objekt, das für die Initialisierung von <paramref name="target" /> als sich gegenseitig ausschließende Sperre verwendet wird.Wenn <paramref name="syncLock" />null ist, wird ein neues Objekt instanziiert.</param>
      <typeparam name="T">Der Typ des zu initialisierenden Verweises.</typeparam>
      <exception cref="T:System.MemberAccessException">Berechtigungen, auf den Konstruktor des Typs <paramref name="T" /> zuzugreifen, haben gefehlt.</exception>
      <exception cref="T:System.MissingMemberException">Der Typ <paramref name="T" /> besitzt keinen Standardkonstruktor.</exception>
    </member>
    <member name="M:System.Threading.LazyInitializer.EnsureInitialized``1(``0@,System.Boolean@,System.Object@,System.Func{``0})">
      <summary>Initialisiert einen Zielverweis- oder Werttyp mit einer angegebenen Funktion, wenn er noch nicht initialisiert wurde.</summary>
      <returns>Der initialisierte Wert vom Typ <paramref name="T" />.</returns>
      <param name="target">Ein Verweis oder Wert vom Typ <paramref name="T" />, der initialisiert werden soll, wenn er noch nicht initialisiert wurde.</param>
      <param name="initialized">Ein Verweis auf einen booleschen Wert, der bestimmt, ob das Ziel bereits initialisiert wurde.</param>
      <param name="syncLock">Ein Verweis auf ein Objekt, das für die Initialisierung von <paramref name="target" /> als sich gegenseitig ausschließende Sperre verwendet wird.Wenn <paramref name="syncLock" />null ist, wird ein neues Objekt instanziiert.</param>
      <param name="valueFactory">Die Funktion, die aufgerufen wird, um den Verweis oder den Wert zu initialisieren.</param>
      <typeparam name="T">Der Typ des zu initialisierenden Verweises.</typeparam>
      <exception cref="T:System.MemberAccessException">Berechtigungen, auf den Konstruktor des Typs <paramref name="T" /> zuzugreifen, haben gefehlt.</exception>
      <exception cref="T:System.MissingMemberException">Der Typ <paramref name="T" /> besitzt keinen Standardkonstruktor.</exception>
    </member>
    <member name="M:System.Threading.LazyInitializer.EnsureInitialized``1(``0@,System.Func{``0})">
      <summary>Initialisiert einen Zielverweistyp mit einer angegebenen Funktion, wenn er noch nicht initialisiert wurde.</summary>
      <returns>Der initialisierte Wert vom Typ <paramref name="T" />.</returns>
      <param name="target">Der Verweis vom Typ <paramref name="T" />, der initialisiert werden soll, wenn er noch nicht initialisiert wurde.</param>
      <param name="valueFactory">Die Funktion, die aufgerufen wird, um den Verweis zu initialisieren.</param>
      <typeparam name="T">Der Verweistyp des zu initialisierenden Verweises.</typeparam>
      <exception cref="T:System.MissingMemberException">Der Typ <paramref name="T" /> besitzt keinen Standardkonstruktor.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="valueFactory" /> gibt null (Nothing in Visual Basic) zurück.</exception>
    </member>
    <member name="T:System.Threading.LockRecursionException">
      <summary>Die Ausnahme, die ausgelöst wird, wenn die rekursive Anforderung einer Sperre nicht mit der Rekursionsrichtlinie der Sperre kompatibel ist.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.LockRecursionException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.LockRecursionException" />-Klasse mit einer vom System generierten Meldung, die den Fehler beschreibt.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.LockRecursionException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.LockRecursionException" />-Klasse mit einer angegebenen Meldung, die den Fehler beschreibt.</summary>
      <param name="message">Die Meldung, in der die Ausnahme beschrieben wirdDer Aufrufer dieses Konstruktors muss sicherstellen, dass diese Zeichenfolge für die aktuelle Systemkultur lokalisiert wurde.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.LockRecursionException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.LockRecursionException" />-Klasse mit einer angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Meldung, in der die Ausnahme beschrieben wirdDer Aufrufer dieses Konstruktors muss sicherstellen, dass diese Zeichenfolge für die aktuelle Systemkultur lokalisiert wurde.</param>
      <param name="innerException">Die Ausnahme, die die aktuelle Ausnahme verursacht hat.Wenn der <paramref name="innerException" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Threading.LockRecursionPolicy">
      <summary>Gibt an, ob eine Sperre mehrmals dem gleichen Thread zugewiesen werden kann.</summary>
    </member>
    <member name="F:System.Threading.LockRecursionPolicy.NoRecursion">
      <summary>Wenn ein Thread rekursiv versucht, eine Sperre zu erhalten, wird eine Ausnahme ausgelöst.Einige Klassen gestatten gewisse Rekursionen, wenn diese Einstellung aktiv ist.</summary>
    </member>
    <member name="F:System.Threading.LockRecursionPolicy.SupportsRecursion">
      <summary>Ein Thread kann rekursiv eine Sperre erhalten.Einige Klassen beschränken diese Möglichkeit einer rekursiven Zuweisung.</summary>
    </member>
    <member name="T:System.Threading.ManualResetEvent">
      <summary>Benachrichtigt einen oder mehrere wartende Threads über das Eintreten eines Ereignisses.Diese Klasse kann nicht vererbt werden.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.ManualResetEvent.#ctor(System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.ManualResetEvent" />-Klasse mit einem booleschen Wert, der angibt, ob der anfängliche Zustand auf signalisiert festgelegt werden soll.</summary>
      <param name="initialState">true, wenn der anfängliche Zustand auf signalisiert festgelegt werden soll, false, wenn der anfängliche Zustand auf nicht signalisiert festgelegt werden soll. </param>
    </member>
    <member name="T:System.Threading.ManualResetEventSlim">
      <summary>Stellt eine verschlankte Version von <see cref="T:System.Threading.ManualResetEvent" /> bereit.</summary>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.ManualResetEventSlim" />-Klasse mit dem Anfangszustand „nicht signalisiert“.</summary>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.#ctor(System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.ManualResetEventSlim" />-Klasse mit einem booleschen Wert, der angibt, ob der Anfangszustand auf „signalisiert“ festgelegt werden soll.</summary>
      <param name="initialState">True, um den Anfangszustand auf „signalisiert“ festzulegen, false um den Anfangszustand auf „nicht signalisiert“ festzulegen.</param>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.#ctor(System.Boolean,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.ManualResetEventSlim" />-Klasse mit einem booleschen Wert, der angibt, ob der Anfangszustand auf „signalisiert“ festgelegt werden soll, und einer festgelegten Spin-Anzahl.</summary>
      <param name="initialState">True, um den Anfangszustand auf "signalisiert" festzulegen, false um den Anfangszustand auf "nicht signalisiert" festzulegen.</param>
      <param name="spinCount">Die Anzahl von Spin-Wartevorgängen, die vor dem Fallback auf einen kernelbasierten Wartevorgang stattfinden.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="spinCount" /> is less than 0 or greater than the maximum allowed value.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Dispose">
      <summary>Gibt alle von der aktuellen Instanz der <see cref="T:System.Threading.ManualResetEventSlim" />-Klasse verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Dispose(System.Boolean)">
      <summary>Gibt die vom <see cref="T:System.Threading.ManualResetEventSlim" /> verwendeten nicht verwalteten Ressourcen und optional auch die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um nur nicht verwaltete Ressourcen freizugeben.</param>
    </member>
    <member name="P:System.Threading.ManualResetEventSlim.IsSet">
      <summary>Ruft einen Wert ab, der angibt, ob das Ereignis festgelegt wurde.</summary>
      <returns>True, wenn das Ereignis festgelegt wurde, andernfalls false.</returns>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Reset">
      <summary>Legt den Zustand des Ereignisses auf „nicht signalisiert“ fest, sodass Threads blockiert werden.</summary>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Set">
      <summary>Legt den Zustand des Ereignisses auf „signalisiert“ fest und ermöglicht so die weitere Ausführung eines oder mehrerer wartender Threads.</summary>
    </member>
    <member name="P:System.Threading.ManualResetEventSlim.SpinCount">
      <summary>Ruft die Anzahl von Spin-Wartevorgängen ab, die vor dem Fallback auf einen kernelbasierten Wartevorgang stattfinden.</summary>
      <returns>Gibt die Anzahl von Spin-Wartevorgängen zurück, die vor dem Fallback auf einen kernelbasierten Wartevorgang stattfinden.</returns>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Wait">
      <summary>Blockiert den aktuellen Thread, bis das aktuelle <see cref="T:System.Threading.ManualResetEventSlim" /> festgelegt wird.</summary>
      <exception cref="T:System.InvalidOperationException">The maximum number of waiters has been exceeded.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Wait(System.Int32)">
      <summary>Blockiert den aktuellen Thread, bis das aktuelle <see cref="T:System.Threading.ManualResetEventSlim" /> festgelegt wird, wobei eine 32-Bit-Ganzzahl mit Vorzeichen zum Messen des Zeitintervalls verwendet wird.</summary>
      <returns>true, wenn der <see cref="T:System.Threading.ManualResetEventSlim" /> festgelegt wurde, andernfalls false.</returns>
      <param name="millisecondsTimeout">Die Anzahl von Millisekunden, die gewartet wird, oder <see cref="F:System.Threading.Timeout.Infinite" /> (-1) für Warten ohne Timeout.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.InvalidOperationException">The maximum number of waiters has been exceeded.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Wait(System.Int32,System.Threading.CancellationToken)">
      <summary>Blockiert den aktuellen Thread, bis das aktuelle <see cref="T:System.Threading.ManualResetEventSlim" /> festgelegt wird, wobei eine 32-Bit-Ganzzahl mit Vorzeichen zum Messen des Zeitintervalls verwendet und ein <see cref="T:System.Threading.CancellationToken" /> überwacht wird.</summary>
      <returns>true, wenn der <see cref="T:System.Threading.ManualResetEventSlim" /> festgelegt wurde, andernfalls false.</returns>
      <param name="millisecondsTimeout">Die Anzahl von Millisekunden, die gewartet wird, oder <see cref="F:System.Threading.Timeout.Infinite" /> (-1) für Warten ohne Timeout.</param>
      <param name="cancellationToken">Das zu überwachende <see cref="T:System.Threading.CancellationToken" />.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.InvalidOperationException">The maximum number of waiters has been exceeded.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed or the <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Wait(System.Threading.CancellationToken)">
      <summary>Blockiert den aktuellen Thread, bis das aktuelle <see cref="T:System.Threading.ManualResetEventSlim" /> ein Signal empfängt, wobei ein <see cref="T:System.Threading.CancellationToken" /> überwacht wird.</summary>
      <param name="cancellationToken">Das zu überwachende <see cref="T:System.Threading.CancellationToken" />.</param>
      <exception cref="T:System.InvalidOperationException">The maximum number of waiters has been exceeded.</exception>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed or the <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Wait(System.TimeSpan)">
      <summary>Blockiert den aktuellen Thread, bis das aktuelle <see cref="T:System.Threading.ManualResetEventSlim" /> festgelegt wird, wobei ein <see cref="T:System.TimeSpan" />-Wert zum Messen des Zeitintervalls verwendet wird.</summary>
      <returns>true, wenn der <see cref="T:System.Threading.ManualResetEventSlim" /> festgelegt wurde, andernfalls false.</returns>
      <param name="timeout">Eine <see cref="T:System.TimeSpan" />-Struktur, die die Anzahl der zu wartenden Millisekunden angibt, oder eine <see cref="T:System.TimeSpan" />-Struktur, die -1 Millisekunden zum unendlichen Warten angibt.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-The number of milliseconds in <paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.InvalidOperationException">The maximum number of waiters has been exceeded.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Wait(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Blockiert den aktuellen Thread, bis das aktuelle <see cref="T:System.Threading.ManualResetEventSlim" /> festgelegt wird. Dabei wird ein <see cref="T:System.TimeSpan" />-Wert zum Messen des Zeitintervalls verwendet und ein <see cref="T:System.Threading.CancellationToken" /> überwacht.</summary>
      <returns>true, wenn der <see cref="T:System.Threading.ManualResetEventSlim" /> festgelegt wurde, andernfalls false.</returns>
      <param name="timeout">Eine <see cref="T:System.TimeSpan" />-Struktur, die die Anzahl der zu wartenden Millisekunden angibt, oder eine <see cref="T:System.TimeSpan" />-Struktur, die -1 Millisekunden zum unendlichen Warten angibt.</param>
      <param name="cancellationToken">Das zu überwachende <see cref="T:System.Threading.CancellationToken" />.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-The number of milliseconds in <paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.InvalidOperationException">The maximum number of waiters has been exceeded. </exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed or the <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.ManualResetEventSlim.WaitHandle">
      <summary>Ruft das zugrunde liegende <see cref="T:System.Threading.WaitHandle" />-Objekt für dieses <see cref="T:System.Threading.ManualResetEventSlim" /> ab.</summary>
      <returns>Das zugrunde liegende <see cref="T:System.Threading.WaitHandle" />-Ereignisobjekt für dieses <see cref="T:System.Threading.ManualResetEventSlim" />.</returns>
    </member>
    <member name="T:System.Threading.Monitor">
      <summary>Stellt einen Mechanismus bereit, der den Zugriff auf Objekte synchronisiert.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.Enter(System.Object)">
      <summary>Erhält eine exklusive Sperre für das angegebene Objekt.</summary>
      <param name="obj">Das Objekt, für das die Monitorsperre erhalten werden soll. </param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="obj" />-Parameter ist null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.Enter(System.Object,System.Boolean@)">
      <summary>Erhält eine exklusive Sperre für das angegebene Objekt und legt atomar einen Wert fest, der angibt, ob die Sperre angenommen wurde.</summary>
      <param name="obj">Das Objekt, auf das gewartet werden soll. </param>
      <param name="lockTaken">Das Ergebnis des Versuchs, die Sperre abzurufen, übergeben als Verweis.Die Eingabe muss false sein.Die Ausgabe ist true, wenn die Sperre abgerufen wurde. Andernfalls ist die Ausgabe false.Die Ausgabe wird auch dann festgelegt, wenn eine Ausnahme bei dem Versuch auftritt, die Sperre abzurufen.Hinweis   Wenn keine Ausnahme auftritt, ist die Ausgabe dieser Methode immer true.</param>
      <exception cref="T:System.ArgumentException">Die Eingabe für <paramref name="lockTaken" /> ist true.</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="obj" />-Parameter ist null. </exception>
    </member>
    <member name="M:System.Threading.Monitor.Exit(System.Object)">
      <summary>Hebt eine exklusive Sperre für das angegebene Objekt auf.</summary>
      <param name="obj">Das Objekt, dessen Sperre aufgehoben werden soll. </param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="obj" />-Parameter ist null. </exception>
      <exception cref="T:System.Threading.SynchronizationLockException">Der aktuelle Thread besitzt die Sperre für das angegebene Objekt nicht. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.IsEntered(System.Object)">
      <summary>Bestimmt, ob der aktuelle Thread die Sperre für das angegebene Objekt enthält. </summary>
      <returns>true, wenn der aktuelle Thread die Sperre für <paramref name="obj" /> enthält, andernfalls false.</returns>
      <param name="obj">Das zu überprüfende Objekt. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="obj" /> ist null. </exception>
    </member>
    <member name="M:System.Threading.Monitor.Pulse(System.Object)">
      <summary>Benachrichtigt einen Thread in der Warteschlange für abzuarbeitende Threads über eine Änderung am Zustand des gesperrten Objekts.</summary>
      <param name="obj">Das Objekt, auf das ein Thread wartet. </param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="obj" />-Parameter ist null. </exception>
      <exception cref="T:System.Threading.SynchronizationLockException">Der aufrufende Thread besitzt keine Sperre für das angegebene Objekt. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.PulseAll(System.Object)">
      <summary>Benachrichtigt alle wartenden Threads über eine Änderung am Zustand des Objekts.</summary>
      <param name="obj">Das Objekt, das den Impuls sendet. </param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="obj" />-Parameter ist null. </exception>
      <exception cref="T:System.Threading.SynchronizationLockException">Der aufrufende Thread besitzt keine Sperre für das angegebene Objekt. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.TryEnter(System.Object)">
      <summary>Versucht, eine exklusive Sperre für das angegebene Objekt zu erhalten.</summary>
      <returns>true, wenn der aktuelle Thread die Sperre erhält, andernfalls false.</returns>
      <param name="obj">Das Objekt, für das die Sperre erhalten werden soll. </param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="obj" />-Parameter ist null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.TryEnter(System.Object,System.Boolean@)">
      <summary>Versucht, eine exklusive Sperre für das angegebene Objekt zu erhalten, und legt atomar einen Wert fest, der angibt, ob die Sperre angenommen wurde.</summary>
      <param name="obj">Das Objekt, für das die Sperre erhalten werden soll. </param>
      <param name="lockTaken">Das Ergebnis des Versuchs, die Sperre abzurufen, übergeben als Verweis.Die Eingabe muss false sein.Die Ausgabe ist true, wenn die Sperre abgerufen wurde. Andernfalls ist die Ausgabe false.Die Ausgabe wird auch dann festgelegt, wenn eine Ausnahme bei dem Versuch auftritt, die Sperre abzurufen.</param>
      <exception cref="T:System.ArgumentException">Die Eingabe für <paramref name="lockTaken" /> ist true.</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="obj" />-Parameter ist null. </exception>
    </member>
    <member name="M:System.Threading.Monitor.TryEnter(System.Object,System.Int32)">
      <summary>Versucht über eine angegebene Anzahl von Millisekunden hinweg, eine exklusive Sperre für das angegebene Objekt zu erhalten.</summary>
      <returns>true, wenn der aktuelle Thread die Sperre erhält, andernfalls false.</returns>
      <param name="obj">Das Objekt, für das die Sperre erhalten werden soll. </param>
      <param name="millisecondsTimeout">Die Anzahl der Millisekunden, für die auf die Sperre gewartet werden soll. </param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="obj" />-Parameter ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> ist negativ und ungleich <see cref="F:System.Threading.Timeout.Infinite" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.TryEnter(System.Object,System.Int32,System.Boolean@)">
      <summary>Versucht für die angegebene Anzahl von Millisekunden, eine exklusive Sperre für das angegebene Objekt zu erhalten, und legt atomar einen Wert fest, der angibt, ob die Sperre angenommen wurde.</summary>
      <param name="obj">Das Objekt, für das die Sperre erhalten werden soll. </param>
      <param name="millisecondsTimeout">Die Anzahl der Millisekunden, für die auf die Sperre gewartet werden soll. </param>
      <param name="lockTaken">Das Ergebnis des Versuchs, die Sperre abzurufen, übergeben als Verweis.Die Eingabe muss false sein.Die Ausgabe ist true, wenn die Sperre abgerufen wurde. Andernfalls ist die Ausgabe false.Die Ausgabe wird auch dann festgelegt, wenn eine Ausnahme bei dem Versuch auftritt, die Sperre abzurufen.</param>
      <exception cref="T:System.ArgumentException">Die Eingabe für <paramref name="lockTaken" /> ist true.</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="obj" />-Parameter ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> ist negativ und ungleich <see cref="F:System.Threading.Timeout.Infinite" />. </exception>
    </member>
    <member name="M:System.Threading.Monitor.TryEnter(System.Object,System.TimeSpan)">
      <summary>Versucht über einen angegebenen Zeitraum hinweg, eine exklusive Sperre für das angegebene Objekt zu erhalten.</summary>
      <returns>true, wenn der aktuelle Thread die Sperre erhält, andernfalls false.</returns>
      <param name="obj">Das Objekt, für das die Sperre erhalten werden soll. </param>
      <param name="timeout">Eine <see cref="T:System.TimeSpan" />, die die Zeitspanne darstellt, für die auf die Sperre gewartet werden soll.Ein Wert von -1 Millisekunde gibt eine unbegrenzte Wartezeit an.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="obj" />-Parameter ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Wert von <paramref name="timeout" /> in Millisekunden ist negativ und ungleich <see cref="F:System.Threading.Timeout.Infinite" /> (-1 Millisekunde), oder er ist größer als <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.TryEnter(System.Object,System.TimeSpan,System.Boolean@)">
      <summary>Versucht für die angegebene Dauer, eine exklusive Sperre für das angegebene Objekt zu erhalten, und legt atomar einen Wert fest, der angibt, ob die Sperre angenommen wurde.</summary>
      <param name="obj">Das Objekt, für das die Sperre erhalten werden soll. </param>
      <param name="timeout">Die Zeitspanne, für die auf die Sperre gewartet werden soll.Ein Wert von -1 Millisekunde gibt eine unbegrenzte Wartezeit an.</param>
      <param name="lockTaken">Das Ergebnis des Versuchs, die Sperre abzurufen, übergeben als Verweis.Die Eingabe muss false sein.Die Ausgabe ist true, wenn die Sperre abgerufen wurde. Andernfalls ist die Ausgabe false.Die Ausgabe wird auch dann festgelegt, wenn eine Ausnahme bei dem Versuch auftritt, die Sperre abzurufen.</param>
      <exception cref="T:System.ArgumentException">Die Eingabe für <paramref name="lockTaken" /> ist true.</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="obj" />-Parameter ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Wert von <paramref name="timeout" /> in Millisekunden ist negativ und ungleich <see cref="F:System.Threading.Timeout.Infinite" /> (-1 Millisekunde), oder er ist größer als <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Threading.Monitor.Wait(System.Object)">
      <summary>Hebt die Sperre für ein Objekt auf und blockiert den aktuellen Thread, bis er die Sperre erneut erhält.</summary>
      <returns>true, wenn der Aufruf beendet wurde, weil der Aufrufer die Sperre für das angegebene Objekt erneut erhalten hat.Diese Methode wird nicht beendet, wenn die Sperre nicht erneut erhalten wird.</returns>
      <param name="obj">Das Objekt, auf das gewartet werden soll. </param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="obj" />-Parameter ist null. </exception>
      <exception cref="T:System.Threading.SynchronizationLockException">Der aufrufende Thread besitzt keine Sperre für das angegebene Objekt. </exception>
      <exception cref="T:System.Threading.ThreadInterruptedException">Der Thread, der Wait aufruft, wird später im Wartezustand unterbrochen.Dieser Fall tritt ein, wenn ein anderer Thread die <see cref="M:System.Threading.Thread.Interrupt" />-Methode dieses Threads aufruft.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.Wait(System.Object,System.Int32)">
      <summary>Hebt die Sperre für ein Objekt auf und blockiert den aktuellen Thread, bis er die Sperre erneut erhält.Wenn das angegebene Timeoutintervall abläuft, tritt der Thread in die Warteschlange für abgearbeitete Threads ein.</summary>
      <returns>true, wenn die Sperre erneut erhalten wurde, bevor die angegebene Zeitspanne verstrichen ist. false, wenn die Sperre erneut erhalten wurde, nachdem die angegebene Zeitspanne verstrichen ist.Die Methode wird erst beendet, wenn die Sperre erneut erhalten wurde.</returns>
      <param name="obj">Das Objekt, auf das gewartet werden soll. </param>
      <param name="millisecondsTimeout">Die Anzahl von Millisekunden, die gewartet wird, bevor der Thread in die Warteschlange für abgearbeitete Threads eintritt. </param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="obj" />-Parameter ist null. </exception>
      <exception cref="T:System.Threading.SynchronizationLockException">Der aufrufende Thread besitzt keine Sperre für das angegebene Objekt. </exception>
      <exception cref="T:System.Threading.ThreadInterruptedException">Der Thread, der Wait aufruft, wird später im Wartezustand unterbrochen.Dieser Fall tritt ein, wenn ein anderer Thread die <see cref="M:System.Threading.Thread.Interrupt" />-Methode dieses Threads aufruft.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Wert des <paramref name="millisecondsTimeout" />-Parameters ist negativ und ungleich <see cref="F:System.Threading.Timeout.Infinite" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.Wait(System.Object,System.TimeSpan)">
      <summary>Hebt die Sperre für ein Objekt auf und blockiert den aktuellen Thread, bis er die Sperre erneut erhält.Wenn das angegebene Timeoutintervall abläuft, tritt der Thread in die Warteschlange für abgearbeitete Threads ein.</summary>
      <returns>true, wenn die Sperre erneut erhalten wurde, bevor die angegebene Zeitspanne verstrichen ist. false, wenn die Sperre erneut erhalten wurde, nachdem die angegebene Zeitspanne verstrichen ist.Die Methode wird erst beendet, wenn die Sperre erneut erhalten wurde.</returns>
      <param name="obj">Das Objekt, auf das gewartet werden soll. </param>
      <param name="timeout">Ein <see cref="T:System.TimeSpan" />, der die Zeit angibt, die gewartet wird, bevor der Thread in die Warteschlange für abgearbeitete Threads eintritt. </param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="obj" />-Parameter ist null. </exception>
      <exception cref="T:System.Threading.SynchronizationLockException">Der aufrufende Thread besitzt keine Sperre für das angegebene Objekt. </exception>
      <exception cref="T:System.Threading.ThreadInterruptedException">Der Thread, der Wait aufruft, wird später im Wartezustand unterbrochen.Dieser Fall tritt ein, wenn ein anderer Thread die <see cref="M:System.Threading.Thread.Interrupt" />-Methode dieses Threads aufruft.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Wert des <paramref name="timeout" />-Parameters in Millisekunden ist negativ und stellt nicht <see cref="F:System.Threading.Timeout.Infinite" /> (-1 Millisekunde) dar, oder er ist größer als <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.Mutex">
      <summary>Ein primitiver Synchronisierungstyp, der auch für die prozessübergreifende Synchronisierung verwendet werden kann. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Mutex.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Mutex" />-Klasse mit Standardeigenschaften.</summary>
    </member>
    <member name="M:System.Threading.Mutex.#ctor(System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Mutex" />-Klasse mit einem booleschen Wert, der angibt, ob dem aufrufenden Thread der anfängliche Besitz des Mutex zugewiesen werden soll.</summary>
      <param name="initiallyOwned">true, um dem aufrufenden Thread den anfänglichen Besitz des Mutex zuzuweisen, andernfalls false. </param>
    </member>
    <member name="M:System.Threading.Mutex.#ctor(System.Boolean,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Mutex" />-Klasse mit einem booleschen Wert, der angibt, ob dem aufrufenden Thread der anfängliche Besitz des Mutex zugewiesen werden soll, sowie mit einer Zeichenfolge, die den Namen des Mutex darstellt.</summary>
      <param name="initiallyOwned">true, um dem aufrufenden Thread den anfänglichen Besitz des benannten Systemmutex zuzuweisen, wenn der benannte Systemmutex als Ergebnis dieses Aufrufs erstellt wird, andernfalls false. </param>
      <param name="name">Der Name des <see cref="T:System.Threading.Mutex" />.Bei einem Wert von null ist das <see cref="T:System.Threading.Mutex" /> unbenannt.</param>
      <exception cref="T:System.UnauthorizedAccessException">Der benannte Mutex ist vorhanden und verfügt über Zugriffssteuerungssicherheit, aber der Benutzer verfügt nicht über <see cref="F:System.Security.AccessControl.MutexRights.FullControl" />.</exception>
      <exception cref="T:System.IO.IOException">Ein Win32-Fehler ist aufgetreten.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">Der benannte Mutex kann nicht erstellt werden, möglicherweise weil ein WaitHandle eines anderen Typs denselben Namen hat.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> ist länger als 260 Zeichen.</exception>
    </member>
    <member name="M:System.Threading.Mutex.#ctor(System.Boolean,System.String,System.Boolean@)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Mutex" />-Klasse mit einem booleschen Wert, der angibt, ob dem aufrufenden Thread der anfängliche Besitz des Mutex zugewiesen werden soll, mit einer Zeichenfolge mit dem Namen des Mutex sowie mit einem booleschen Wert, der beim Beenden der Methode angibt, ob dem aufrufenden Thread der anfängliche Besitz des Mutex gewährt wurde.</summary>
      <param name="initiallyOwned">true, um dem aufrufenden Thread den anfänglichen Besitz des benannten Systemmutex zuzuweisen, wenn der benannte Systemmutex als Ergebnis dieses Aufrufs erstellt wird, andernfalls false. </param>
      <param name="name">Der Name des <see cref="T:System.Threading.Mutex" />.Bei einem Wert von null ist das <see cref="T:System.Threading.Mutex" /> unbenannt.</param>
      <param name="createdNew">Enthält nach dem Beenden dieser Methode einen booleschen Wert, der true ist, wenn ein lokaler Mutex erstellt wurde (d. h. wenn <paramref name="name" /> gleich null oder eine leere Zeichenfolge ist) oder wenn der angegebene benannte Systemmutex erstellt wurde. Der Wert ist false, wenn der angegebene benannte Systemmutex bereits vorhanden war.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <exception cref="T:System.UnauthorizedAccessException">Der benannte Mutex ist vorhanden und verfügt über Zugriffssteuerungssicherheit, aber der Benutzer verfügt nicht über <see cref="F:System.Security.AccessControl.MutexRights.FullControl" />.</exception>
      <exception cref="T:System.IO.IOException">Ein Win32-Fehler ist aufgetreten.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">Der benannte Mutex kann nicht erstellt werden, möglicherweise weil ein WaitHandle eines anderen Typs denselben Namen hat.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> ist länger als 260 Zeichen.</exception>
    </member>
    <member name="M:System.Threading.Mutex.OpenExisting(System.String)">
      <summary>Öffnet den bestimmten benannten Mutex, wenn er bereits vorhanden ist.</summary>
      <returns>Ein Objekt, das den benannten Systemmutex darstellt.</returns>
      <param name="name">Der Name des zu öffnenden Systemmutex.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> ist eine leere Zeichenfolge.- oder - <paramref name="name" /> ist länger als 260 Zeichen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> ist null.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">Der benannte Mutex ist nicht vorhanden.</exception>
      <exception cref="T:System.IO.IOException">Ein Win32-Fehler ist aufgetreten.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Der benannte Mutex ist vorhanden, der Benutzer verfügt jedoch nicht über den erforderlichen Sicherheitszugriff, um es zu verwenden.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Threading.Mutex.ReleaseMutex">
      <summary>Gibt das <see cref="T:System.Threading.Mutex" /> einmal frei.</summary>
      <exception cref="T:System.ApplicationException">Der aufrufende Thread ist nicht im Besitz des Mutex. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Mutex.TryOpenExisting(System.String,System.Threading.Mutex@)">
      <summary>Öffnet den bestimmten benannten Mutex, wenn er bereits vorhanden ist, und gibt einen Wert zurück, der angibt, ob der Vorgang erfolgreich war.</summary>
      <returns>true, wenn der benannte Mutex erfolgreich geöffnet wurde; andernfalls false.</returns>
      <param name="name">Der Name des zu öffnenden Systemmutex.</param>
      <param name="result">Enthält nach Beenden der Methode ein <see cref="T:System.Threading.Mutex" />-Objekt, das das benannte Mutex darstellt, wenn der Aufruf erfolgreich ausgeführt wurde, oder null, wenn der Aufruf fehlgeschlagen ist.Dieser Parameter wird nicht initialisiert behandelt.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> ist eine leere Zeichenfolge.- oder - <paramref name="name" /> ist länger als 260 Zeichen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> ist null.</exception>
      <exception cref="T:System.IO.IOException">Ein Win32-Fehler ist aufgetreten.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Der benannte Mutex ist vorhanden, der Benutzer verfügt jedoch nicht über den erforderlichen Sicherheitszugriff, um es zu verwenden.</exception>
    </member>
    <member name="T:System.Threading.ReaderWriterLockSlim">
      <summary>Stellt eine Sperre dar, mit der der Zugriff auf eine Ressource verwaltet wird. Mehrere Threads können hierbei Lesezugriff oder exklusiven Schreibzugriff erhalten.</summary>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.ReaderWriterLockSlim" />-Klasse mit Standardeigenschaftswerten.</summary>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.#ctor(System.Threading.LockRecursionPolicy)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.ReaderWriterLockSlim" />-Klasse unter Angabe der Rekursionsrichtlinie für die Sperre.</summary>
      <param name="recursionPolicy">Einer der Enumerationswerte, der die Rekursionsrichtlinie für die Sperre angibt. </param>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.CurrentReadCount">
      <summary>Ruft die Gesamtzahl von eindeutigen Threads ab, denen die Sperre im Lesemodus zugewiesen ist.</summary>
      <returns>Die Anzahl von eindeutigen Threads, denen die Sperre im Lesemodus zugewiesen ist.</returns>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.Dispose">
      <summary>Gibt alle von der aktuellen Instanz der <see cref="T:System.Threading.ReaderWriterLockSlim" />-Klasse verwendeten Ressourcen frei.</summary>
      <exception cref="T:System.Threading.SynchronizationLockException">
        <see cref="P:System.Threading.ReaderWriterLockSlim.WaitingReadCount" /> is greater than zero. -or-<see cref="P:System.Threading.ReaderWriterLockSlim.WaitingUpgradeCount" /> is greater than zero. -or-<see cref="P:System.Threading.ReaderWriterLockSlim.WaitingWriteCount" /> is greater than zero. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.EnterReadLock">
      <summary>Versucht, die Sperre im Lesemodus zu erhalten.</summary>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered read mode. -or-The current thread may not acquire the read lock when it already holds the write lock. -or-The recursion number would exceed the capacity of the counter.This limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.EnterUpgradeableReadLock">
      <summary>Versucht, die Sperre im erweiterbaren Modus zu erhalten.</summary>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock in any mode. -or-The current thread has entered read mode, so trying to enter upgradeable mode would create the possibility of a deadlock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.EnterWriteLock">
      <summary>Versucht, die Sperre im Schreibmodus zu erhalten.</summary>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock in any mode. -or-The current thread has entered read mode, so trying to enter the lock in write mode would create the possibility of a deadlock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.ExitReadLock">
      <summary>Verringert die Rekursionszahl für den Lesemodus und beendet den Lesemodus, wenn das Rekursionsergebnis 0 (null) ist.</summary>
      <exception cref="T:System.Threading.SynchronizationLockException">The current thread has not entered the lock in read mode. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.ExitUpgradeableReadLock">
      <summary>Verringert die Rekursionszahl für den erweiterbaren Modus und beendet den erweiterbaren Modus, wenn das Rekursionsergebnis 0 (null) ist.</summary>
      <exception cref="T:System.Threading.SynchronizationLockException">The current thread has not entered the lock in upgradeable mode.</exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.ExitWriteLock">
      <summary>Verringert die Rekursionszahl für den Schreibmodus und beendet den Schreibmodus, wenn das Rekursionsergebnis 0 (null) ist.</summary>
      <exception cref="T:System.Threading.SynchronizationLockException">The current thread has not entered the lock in write mode.</exception>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.IsReadLockHeld">
      <summary>Ruft einen Wert ab, der angibt, ob die Sperre dem aktuellen Thread im Lesemodus zugewiesen ist.</summary>
      <returns>true, wenn sich der aktuelle Thread im Lesemodus befindet, andernfalls false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.IsUpgradeableReadLockHeld">
      <summary>Ruft einen Wert ab, der angibt, ob die Sperre dem aktuellen Thread im erweiterbaren Modus zugewiesen ist. </summary>
      <returns>true, wenn sich der aktuelle Thread im erweiterbaren Modus befindet, andernfalls false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.IsWriteLockHeld">
      <summary>Ruft einen Wert ab, der angibt, ob die Sperre dem aktuellen Thread im Schreibmodus zugewiesen ist.</summary>
      <returns>true, wenn sich der aktuelle Thread im Schreibmodus befindet, andernfalls false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy">
      <summary>Ruft einen Wert ab, der die Rekursionsrichtlinie für das aktuelle <see cref="T:System.Threading.ReaderWriterLockSlim" />-Objekt angibt.</summary>
      <returns>Einer der Enumerationswerte, der die Rekursionsrichtlinie für die Sperre angibt.</returns>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.RecursiveReadCount">
      <summary>Ruft einen Wert ab, der als Indikator für eine Rekursion angibt, wie oft dem aktuellen Thread die Sperre im Lesemodus zugewiesen ist.</summary>
      <returns>0 (null), wenn sich der aktuelle Thread nicht im Lesemodus befindet, 1, wenn sich der Thread im Lesemodus befindet und diesen nicht rekursiv angefordert hat, oder n, wenn der Thread die Sperre n - 1 Mal rekursiv angefordert hat.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.RecursiveUpgradeCount">
      <summary>Ruft einen Wert ab, der als Indikator für eine Rekursion angibt, wie oft dem aktuellen Thread die Sperre im erweiterbaren Modus zugewiesen ist.</summary>
      <returns>0 (null), wenn sich der aktuelle Thread nicht im erweiterbaren Modus befindet, 1, wenn sich der Thread im erweiterbaren Modus befindet und diesen nicht rekursiv angefordert hat, oder n, wenn der Thread den erweiterbaren Modus n - 1 Mal rekursiv angefordert hat.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.RecursiveWriteCount">
      <summary>Ruft einen Wert ab, der als Indikator für eine Rekursion angibt, wie oft dem aktuellen Thread die Sperre im Schreibmodus zugewiesen ist.</summary>
      <returns>0 (null), wenn sich der aktuelle Thread nicht im Schreibmodus befindet, 1, wenn sich der Thread im Schreibmodus befindet und diesen nicht rekursiv angefordert hat, oder n, wenn der Thread den Schreibmodus n - 1 Mal rekursiv angefordert hat.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.TryEnterReadLock(System.Int32)">
      <summary>Versucht, die Sperre im Lesemodus zu erhalten. Optional wird ein ganzzahliger Timeout berücksichtigt.</summary>
      <returns>true, wenn der aufrufende Thread den Lesemodus erhalten hat, andernfalls false.</returns>
      <param name="millisecondsTimeout">Die Zeit in Millisekunden, die gewartet wird, oder -1 (<see cref="F:System.Threading.Timeout.Infinite" />), um unbegrenzt zu warten.</param>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="millisecondsTimeout" /> is negative, but it is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> (-1), which is the only negative value allowed. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.TryEnterReadLock(System.TimeSpan)">
      <summary>Versucht, die Sperre im Lesemodus zu erhalten. Optional wird ein Timeout berücksichtigt.</summary>
      <returns>true, wenn der aufrufende Thread den Lesemodus erhalten hat, andernfalls false.</returns>
      <param name="timeout">Das Zeitintervall bis zum Timeout, oder -1 Millisekunden, um unbegrenzt zu warten. </param>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="timeout" /> is negative, but it is not equal to -1 milliseconds, which is the only negative value allowed.-or-The value of <paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" /> milliseconds. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.TryEnterUpgradeableReadLock(System.Int32)">
      <summary>Versucht, die Sperre im erweiterbaren Modus zu erhalten. Optional wird ein Timeout berücksichtigt.</summary>
      <returns>true, wenn der aufrufende Thread den erweiterbaren Modus erhalten hat, andernfalls false.</returns>
      <param name="millisecondsTimeout">Die Zeit in Millisekunden, die gewartet wird, oder -1 (<see cref="F:System.Threading.Timeout.Infinite" />), um unbegrenzt zu warten.</param>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock. -or-The current thread initially entered the lock in read mode, and therefore trying to enter upgradeable mode would create the possibility of a deadlock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="millisecondsTimeout" /> is negative, but it is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> (-1), which is the only negative value allowed. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.TryEnterUpgradeableReadLock(System.TimeSpan)">
      <summary>Versucht, die Sperre im erweiterbaren Modus zu erhalten. Optional wird ein Timeout berücksichtigt.</summary>
      <returns>true, wenn der aufrufende Thread den erweiterbaren Modus erhalten hat, andernfalls false.</returns>
      <param name="timeout">Das Zeitintervall bis zum Timeout, oder -1 Millisekunden, um unbegrenzt zu warten.</param>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock. -or-The current thread initially entered the lock in read mode, and therefore trying to enter upgradeable mode would create the possibility of a deadlock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="timeout" /> is negative, but it is not equal to -1 milliseconds, which is the only negative value allowed.-or-The value of <paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" /> milliseconds. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.TryEnterWriteLock(System.Int32)">
      <summary>Versucht, die Sperre im Schreibmodus zu erhalten. Optional wird ein Timeout berücksichtigt.</summary>
      <returns>true, wenn der aufrufende Thread den Schreibmodus erhalten hat, andernfalls false.</returns>
      <param name="millisecondsTimeout">Die Zeit in Millisekunden, die gewartet wird, oder -1 (<see cref="F:System.Threading.Timeout.Infinite" />), um unbegrenzt zu warten.</param>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock. -or-The current thread initially entered the lock in read mode, and therefore trying to enter write mode would create the possibility of a deadlock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="millisecondsTimeout" /> is negative, but it is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> (-1), which is the only negative value allowed. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.TryEnterWriteLock(System.TimeSpan)">
      <summary>Versucht, die Sperre im Schreibmodus zu erhalten. Optional wird ein Timeout berücksichtigt.</summary>
      <returns>true, wenn der aufrufende Thread den Schreibmodus erhalten hat, andernfalls false.</returns>
      <param name="timeout">Das Zeitintervall bis zum Timeout, oder -1 Millisekunden, um unbegrenzt zu warten.</param>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock. -or-The current thread initially entered the lock in read mode, and therefore trying to enter write mode would create the possibility of a deadlock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="timeout" /> is negative, but it is not equal to -1 milliseconds, which is the only negative value allowed.-or-The value of <paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" /> milliseconds. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.WaitingReadCount">
      <summary>Ruft die Gesamtzahl von Threads ab, die auf eine Zuweisung der Sperre im Lesemodus warten.</summary>
      <returns>Die Gesamtzahl von Threads, die auf eine Zuweisung des Lesemodus warten.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.WaitingUpgradeCount">
      <summary>Ruft die Gesamtzahl von Threads ab, die auf eine Zuweisung der Sperre im erweiterbaren Modus warten.</summary>
      <returns>Die Gesamtzahl von Threads, die auf eine Zuweisung des erweiterbaren Modus warten.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.WaitingWriteCount">
      <summary>Ruft die Gesamtzahl von Threads ab, die auf eine Zuweisung der Sperre im Schreibmodus warten.</summary>
      <returns>Die Gesamtzahl von Threads, die auf eine Zuweisung des Schreibmodus warten.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Threading.Semaphore">
      <summary>Schränkt die Anzahl von Threads ein, die gleichzeitig auf eine Ressource oder einen Pool von Ressourcen zugreifen können. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Semaphore.#ctor(System.Int32,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Semaphore" />-Klasse und gibt die ursprüngliche Anzahl von Einträgen und die maximale Anzahl von gleichzeitigen Einträgen an. </summary>
      <param name="initialCount">Die anfängliche Anzahl von Anforderungen für das Semaphor, die gleichzeitig gewährt werden können. </param>
      <param name="maximumCount">Die maximale Anzahl von Anforderungen für das Semaphor, die gleichzeitig gewährt werden können. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="initialCount" /> ist größer als <paramref name="maximumCount" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maximumCount" /> ist kleiner als 1.- oder - <paramref name="initialCount" /> ist kleiner als 0.</exception>
    </member>
    <member name="M:System.Threading.Semaphore.#ctor(System.Int32,System.Int32,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Semaphore" />-Klasse, gibt die ursprüngliche Anzahl von Einträgen und die maximale Anzahl von gleichzeitigen Einträgen sowie optional den Namen eines Systemsemaphorobjekts an. </summary>
      <param name="initialCount">Die anfängliche Anzahl von Anforderungen für das Semaphor, die gleichzeitig gewährt werden können. </param>
      <param name="maximumCount">Die maximale Anzahl von Anforderungen für das Semaphor, die gleichzeitig gewährt werden können.</param>
      <param name="name">Der Name eines benannten Systemsemaphorobjekts.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="initialCount" /> ist größer als <paramref name="maximumCount" />.- oder - <paramref name="name" /> ist länger als 260 Zeichen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maximumCount" /> ist kleiner als 1.- oder - <paramref name="initialCount" /> ist kleiner als 0.</exception>
      <exception cref="T:System.IO.IOException">Ein Win32-Fehler ist aufgetreten.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Das benannte Semaphor ist vorhanden und verfügt über Zugriffssteuerungssicherheit, aber der Benutzer verfügt nicht über <see cref="F:System.Security.AccessControl.SemaphoreRights.FullControl" />.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">Das benannte Semaphor kann nicht erstellt werden, möglicherweise weil ein WaitHandle eines anderen Typs denselben Namen hat.</exception>
    </member>
    <member name="M:System.Threading.Semaphore.#ctor(System.Int32,System.Int32,System.String,System.Boolean@)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Semaphore" />-Klasse, gibt die ursprüngliche Anzahl von Einträgen und die maximale Anzahl von gleichzeitigen Einträgen sowie optional den Namen eines Systemsemaphorobjekts an, gibt eine Variable an, die einen Wert empfängt, der angibt, ob ein neues Systemsemaphor erstellt wurde.</summary>
      <param name="initialCount">Die ursprüngliche Anzahl von Anforderungen für das Semaphor, die gleichzeitig ausgeführt werden können. </param>
      <param name="maximumCount">Die maximale Anzahl von Anforderungen für das Semaphor, die gleichzeitig ausgeführt werden können.</param>
      <param name="name">Der Name eines benannten Systemsemaphorobjekts.</param>
      <param name="createdNew">Enthält nach dem Beenden dieser Methode den Wert true, wenn ein lokales Semaphor erstellt wurde (d. h., wenn <paramref name="name" /> gleich null oder eine leere Zeichenfolge ist) oder wenn das angegebene benannte Systemsemaphor erstellt wurde. Der Wert ist false, wenn das angegebene benannte Systemsemaphor bereits vorhanden war.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="initialCount" /> ist größer als <paramref name="maximumCount" />. - oder - <paramref name="name" /> ist länger als 260 Zeichen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maximumCount" /> ist kleiner als 1.- oder - <paramref name="initialCount" /> ist kleiner als 0.</exception>
      <exception cref="T:System.IO.IOException">Ein Win32-Fehler ist aufgetreten.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Das benannte Semaphor ist vorhanden und verfügt über Zugriffssteuerungssicherheit, aber der Benutzer verfügt nicht über <see cref="F:System.Security.AccessControl.SemaphoreRights.FullControl" />.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">Das benannte Semaphor kann nicht erstellt werden, möglicherweise weil ein WaitHandle eines anderen Typs denselben Namen hat.</exception>
    </member>
    <member name="M:System.Threading.Semaphore.OpenExisting(System.String)">
      <summary>Öffnet das angegebene benannte Semaphor, wenn es bereits vorhanden ist.</summary>
      <returns>Ein Objekt, das das benannte Systemsemaphor darstellt.</returns>
      <param name="name">Der Name des zu öffnenden Systemsemaphors.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> ist eine leere Zeichenfolge.- oder - <paramref name="name" /> ist länger als 260 Zeichen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> ist null.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">Das benannte Semaphor ist nicht vorhanden.</exception>
      <exception cref="T:System.IO.IOException">Ein Win32-Fehler ist aufgetreten.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Das benannte Semaphor ist vorhanden, der Benutzer verfügt jedoch nicht über den nötigen Sicherheitszugriff, um es zu verwenden. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Threading.Semaphore.Release">
      <summary>Beendet das Semaphor und gibt die vorherige Anzahl zurück.</summary>
      <returns>Die Anzahl für das Semaphor vor dem Aufruf der <see cref="Overload:System.Threading.Semaphore.Release" />-Methode. </returns>
      <exception cref="T:System.Threading.SemaphoreFullException">Die Anzahl für das Semaphor weist bereits den maximalen Wert auf.</exception>
      <exception cref="T:System.IO.IOException">Bei einem benannten Semaphor ist ein Win32-Fehler aufgetreten.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Das aktuelle Semaphor stellt ein benanntes Systemsemaphor dar. Der Benutzer verfügt jedoch nicht über <see cref="F:System.Security.AccessControl.SemaphoreRights.Modify" />.- oder - Das aktuelle Semaphor stellt ein benanntes Systemsemaphor dar, es wurde jedoch nicht mit <see cref="F:System.Security.AccessControl.SemaphoreRights.Modify" /> geöffnet.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Semaphore.Release(System.Int32)">
      <summary>Gibt das Semaphor eine festgelegte Anzahl von Malen frei und gibt die vorherige Anzahl zurück.</summary>
      <returns>Die Anzahl für das Semaphor vor dem Aufruf der <see cref="Overload:System.Threading.Semaphore.Release" />-Methode. </returns>
      <param name="releaseCount">Die Anzahl von Malen, die das Semaphor freigegeben werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="releaseCount" /> ist kleiner als 1.</exception>
      <exception cref="T:System.Threading.SemaphoreFullException">Die Anzahl für das Semaphor weist bereits den maximalen Wert auf.</exception>
      <exception cref="T:System.IO.IOException">Bei einem benannten Semaphor ist ein Win32-Fehler aufgetreten.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Das aktuelle Semaphor stellt ein benanntes Systemsemaphor dar. Der Benutzer verfügt jedoch nicht über <see cref="F:System.Security.AccessControl.SemaphoreRights.Modify" />-Rechte.- oder - Das aktuelle Semaphor stellt ein benanntes Systemsemaphor dar, es wurde jedoch nicht mit <see cref="F:System.Security.AccessControl.SemaphoreRights.Modify" />-Rechten geöffnet.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Semaphore.TryOpenExisting(System.String,System.Threading.Semaphore@)">
      <summary>Öffnet das angegebene benannte Semaphor, wenn es bereits vorhanden ist, und gibt einen Wert zurück, der angibt, ob der Vorgang erfolgreich war.</summary>
      <returns>true, wenn das benannte Semaphor erfolgreich geöffnet wurde; andernfalls false.</returns>
      <param name="name">Der Name des zu öffnenden Systemsemaphors.</param>
      <param name="result">Enthält nach Beenden der Methode ein <see cref="T:System.Threading.Semaphore" />-Objekt, das das benannte Semaphor darstellt, wenn der Aufruf erfolgreich ausgeführt wurde, oder null, wenn der Aufruf fehlgeschlagen ist.Dieser Parameter wird nicht initialisiert behandelt.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> ist eine leere Zeichenfolge.- oder - <paramref name="name" /> ist länger als 260 Zeichen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> ist null.</exception>
      <exception cref="T:System.IO.IOException">Ein Win32-Fehler ist aufgetreten.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Das benannte Semaphor ist vorhanden, der Benutzer verfügt jedoch nicht über den nötigen Sicherheitszugriff, um es zu verwenden. </exception>
    </member>
    <member name="T:System.Threading.SemaphoreFullException">
      <summary>Die Ausnahme, die ausgelöst wird, wenn die <see cref="Overload:System.Threading.Semaphore.Release" />-Methode für ein Semaphor aufgerufen wird, dessen Zähler bereits den Maximalwert aufweist. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.SemaphoreFullException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.SemaphoreFullException" />-Klasse mit Standardwerten.</summary>
    </member>
    <member name="M:System.Threading.SemaphoreFullException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.SemaphoreFullException" />-Klasse mit einer angegebenen Fehlermeldung.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird.</param>
    </member>
    <member name="M:System.Threading.SemaphoreFullException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.SemaphoreFullException" />-Klasse mit einer angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird.</param>
      <param name="innerException">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="innerException" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="T:System.Threading.SemaphoreSlim">
      <summary>Eine einfache Alternative zu <see cref="T:System.Threading.Semaphore" />, die die Anzahl der Threads beschränkt, die gleichzeitig auf eine Ressource oder einen Ressourcenpool zugreifen können.</summary>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.#ctor(System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.SemaphoreSlim" />-Klasse und gibt die ursprüngliche Anzahl von Anforderungen an, die gleichzeitig gewährt werden können.</summary>
      <param name="initialCount">Die anfängliche Anzahl von Anforderungen für das Semaphor, die gleichzeitig gewährt werden können.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialCount" /> ist kleiner als 0.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.#ctor(System.Int32,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.SemaphoreSlim" />-Klasse und gibt die ursprüngliche sowie die maximale Anzahl von Anforderungen an, die gleichzeitig gewährt werden können.</summary>
      <param name="initialCount">Die anfängliche Anzahl von Anforderungen für das Semaphor, die gleichzeitig gewährt werden können.</param>
      <param name="maxCount">Die maximale Anzahl von Anforderungen für das Semaphor, die gleichzeitig gewährt werden können.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialCount" /> ist kleiner als 0, oder <paramref name="initialCount" /> ist größer als <paramref name="maxCount" />, oder <paramref name="maxCount" /> ist kleiner gleich 0.</exception>
    </member>
    <member name="P:System.Threading.SemaphoreSlim.AvailableWaitHandle">
      <summary>Gibt ein <see cref="T:System.Threading.WaitHandle" /> zurück, das verwendet werden kann um auf die Semaphore zu warten.</summary>
      <returns>Ein <see cref="T:System.Threading.WaitHandle" />, das verwendet werden kann um auf die Semaphore zu warten.</returns>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Threading.SemaphoreSlim" /> wurde verworfen.</exception>
    </member>
    <member name="P:System.Threading.SemaphoreSlim.CurrentCount">
      <summary>Ruft die Anzahl der verbleibenden Threads ab, für die das Eintreten in das <see cref="T:System.Threading.SemaphoreSlim" />-Objekt zulässig ist. </summary>
      <returns>Die Anzahl der verbleibenden Threads, für die das Eintreten in das Semaphor zulässig ist.</returns>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Dispose">
      <summary>Gibt alle von der aktuellen Instanz der <see cref="T:System.Threading.SemaphoreSlim" />-Klasse verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Dispose(System.Boolean)">
      <summary>Gibt die von <see cref="T:System.Threading.SemaphoreSlim" /> verwendeten nicht verwalteten Ressourcen und optional die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben.</param>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Release">
      <summary>Gibt das <see cref="T:System.Threading.SemaphoreSlim" />-Objekt einmal frei.</summary>
      <returns>Die vorherige Anzahl von <see cref="T:System.Threading.SemaphoreSlim" />.</returns>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.Threading.SemaphoreFullException">Der <see cref="T:System.Threading.SemaphoreSlim" /> hat bereits seine maximale Größe erreicht.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Release(System.Int32)">
      <summary>Gibt das <see cref="T:System.Threading.SemaphoreSlim" />-Objekt eine festgelegte Anzahl von Malen frei.</summary>
      <returns>Die vorherige Anzahl von <see cref="T:System.Threading.SemaphoreSlim" />.</returns>
      <param name="releaseCount">Die Anzahl von Malen, die das Semaphor freigegeben werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="releaseCount" /> ist kleiner als 1.</exception>
      <exception cref="T:System.Threading.SemaphoreFullException">Der <see cref="T:System.Threading.SemaphoreSlim" /> hat bereits seine maximale Größe erreicht.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Wait">
      <summary>Blockiert den aktuellen Thread, bis er in <see cref="T:System.Threading.SemaphoreSlim" /> eintreten kann.</summary>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Wait(System.Int32)">
      <summary>Blockiert den aktuellen Thread, bis er in die Warteschlange von <see cref="T:System.Threading.SemaphoreSlim" /> eingereiht werden kann, wobei das Timeout mit einer 32-Bit-Ganzzahl mit Vorzeichen angegeben wird.</summary>
      <returns>true, wenn der aktuelle Thread erfolgreich in die Warteschlange von <see cref="T:System.Threading.SemaphoreSlim" /> eingereiht wurde, andernfalls false.</returns>
      <param name="millisecondsTimeout">Die Anzahl von Millisekunden, die gewartet wird, oder <see cref="F:System.Threading.Timeout.Infinite" /> (-1) für Warten ohne Timeout.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> ist eine negative Zahl, aber nicht -1. Der Wert -1 gibt einen Endlostimeout an.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Wait(System.Int32,System.Threading.CancellationToken)">
      <summary>Blockiert den aktuellen Thread, bis er in die Warteschlange von <see cref="T:System.Threading.SemaphoreSlim" /> eingereiht werden kann, wobei eine 32-Bit-Ganzzahl mit Vorzeichen zum Angeben des Timeouts verwendet und ein <see cref="T:System.Threading.CancellationToken" /> überwacht wird.</summary>
      <returns>true, wenn der aktuelle Thread erfolgreich in die Warteschlange von <see cref="T:System.Threading.SemaphoreSlim" /> eingereiht wurde, andernfalls false.</returns>
      <param name="millisecondsTimeout">Die Anzahl von Millisekunden, die gewartet wird, oder <see cref="F:System.Threading.Timeout.Infinite" /> (-1) für Warten ohne Timeout.</param>
      <param name="cancellationToken">Das zu überwachende <see cref="T:System.Threading.CancellationToken" />.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> wurde abgebrochen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> ist eine negative Zahl, aber nicht -1. Der Wert -1 gibt einen Endlostimeout an.</exception>
      <exception cref="T:System.ObjectDisposedException">Die <see cref="T:System.Threading.SemaphoreSlim" /> Instanz wurde freigegeben, oder die <see cref="T:System.Threading.CancellationTokenSource" /> erstellten <paramref name="cancellationToken" /> freigegeben wurde.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Wait(System.Threading.CancellationToken)">
      <summary>Blockiert den aktuellen Thread, bis er in die Warteschlange von <see cref="T:System.Threading.SemaphoreSlim" /> eingereiht werden kann, wobei ein <see cref="T:System.Threading.CancellationToken" /> überwacht wird.</summary>
      <param name="cancellationToken">Das zu überwachende <see cref="T:System.Threading.CancellationToken" />-Token.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> wurde abgebrochen.</exception>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.- oder - Die <see cref="T:System.Threading.CancellationTokenSource" /> erstellten<paramref name=" cancellationToken" /> bereits freigegeben wurde.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Wait(System.TimeSpan)">
      <summary>Blockiert den aktuellen Thread, bis er in die Warteschlange von <see cref="T:System.Threading.SemaphoreSlim" /> eingereiht werden kann, wobei ein <see cref="T:System.TimeSpan" /> zum Angeben des Timeouts verwendet wird.</summary>
      <returns>true, wenn der aktuelle Thread erfolgreich in die Warteschlange von <see cref="T:System.Threading.SemaphoreSlim" /> eingereiht wurde, andernfalls false.</returns>
      <param name="timeout">Eine <see cref="T:System.TimeSpan" />-Struktur, die die Anzahl der zu wartenden Millisekunden angibt, oder eine <see cref="T:System.TimeSpan" />-Struktur, die -1 Millisekunden zum unendlichen Warten angibt.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> ist eine negative Zahl ungleich  -1 Millisekunden, die ein unendliches Timeout darstellt, - oder - Timeout ist größer als <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.ObjectDisposedException">Die semaphoreSlim-Instanz wurde freigegeben<paramref name="." /></exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Wait(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Blockiert den aktuellen Thread, bis er in die Warteschlange von <see cref="T:System.Threading.SemaphoreSlim" /> eingereiht werden kann, wobei eine <see cref="T:System.TimeSpan" /> den Timeout angibt und ein <see cref="T:System.Threading.CancellationToken" /> überwacht wird.</summary>
      <returns>true, wenn der aktuelle Thread erfolgreich in die Warteschlange von <see cref="T:System.Threading.SemaphoreSlim" /> eingereiht wurde, andernfalls false.</returns>
      <param name="timeout">Eine <see cref="T:System.TimeSpan" />-Struktur, die die Anzahl der zu wartenden Millisekunden angibt, oder eine <see cref="T:System.TimeSpan" />-Struktur, die -1 Millisekunden zum unendlichen Warten angibt.</param>
      <param name="cancellationToken">Das zu überwachende <see cref="T:System.Threading.CancellationToken" />.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> wurde abgebrochen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> ist eine negative Zahl ungleich  -1 Millisekunden, die ein unendliches Timeout darstellt, - oder - Timeout ist größer als <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.ObjectDisposedException">Die semaphoreSlim-Instanz wurde freigegeben<paramref name="." /><paramref name="-or-" />Die <see cref="T:System.Threading.CancellationTokenSource" />, die <paramref name="cancellationToken" /> erstellt hat, wurde bereits freigegeben.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.WaitAsync">
      <summary>Wartet asynchron auf den Eintritt in <see cref="T:System.Threading.SemaphoreSlim" />. </summary>
      <returns>Eine Aufgabe, die abgeschlossen wird, wenn das Semaphor eingegeben wurde.</returns>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.WaitAsync(System.Int32)">
      <summary>Wartet asynchron auf den Zutritt zum <see cref="T:System.Threading.SemaphoreSlim" />, wobei eine 32-Bit-Ganzzahl mit Vorzeichen zum Messen des Zeitintervalls verwendet wird. </summary>
      <returns>Eine Aufgabe, die mit dem Ergebnis true abgeschlossen wird, wenn der aktuelle Thread erfolgreich in <see cref="T:System.Threading.SemaphoreSlim" /> gewechselt ist, andernfalls mit dem Ergebnis false.</returns>
      <param name="millisecondsTimeout">Die Anzahl von Millisekunden, die gewartet wird, oder <see cref="F:System.Threading.Timeout.Infinite" /> (-1) für Warten ohne Timeout.</param>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> ist eine negative Zahl, aber nicht -1. Der Wert -1 gibt einen Endlostimeout an.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.WaitAsync(System.Int32,System.Threading.CancellationToken)">
      <summary>Wartet asynchron auf den Zutritt zum <see cref="T:System.Threading.SemaphoreSlim" />, wobei eine 32-Bit-Ganzzahl mit Vorzeichen zum Messen des Zeitintervalls verwendet wird, während ein <see cref="T:System.Threading.CancellationToken" /> beobachtet wird. </summary>
      <returns>Eine Aufgabe, die mit dem Ergebnis true abgeschlossen wird, wenn der aktuelle Thread erfolgreich in <see cref="T:System.Threading.SemaphoreSlim" /> gewechselt ist, andernfalls mit dem Ergebnis false. </returns>
      <param name="millisecondsTimeout">Die Anzahl von Millisekunden, die gewartet wird, oder <see cref="F:System.Threading.Timeout.Infinite" /> (-1) für Warten ohne Timeout.</param>
      <param name="cancellationToken">Das zu überwachende <see cref="T:System.Threading.CancellationToken" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> ist eine negative Zahl, aber nicht -1. Der Wert -1 gibt einen Endlostimeout an. </exception>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben. </exception>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> wurde abgebrochen. </exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.WaitAsync(System.Threading.CancellationToken)">
      <summary>Wartet asynchron auf den Zutritt zum <see cref="T:System.Threading.SemaphoreSlim" />, während ein ein <see cref="T:System.Threading.CancellationToken" /> beobachtet wird. </summary>
      <returns>Eine Aufgabe, die abgeschlossen wird, wenn das Semaphor eingegeben wurde. </returns>
      <param name="cancellationToken">Das zu überwachende <see cref="T:System.Threading.CancellationToken" />-Token.</param>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> wurde abgebrochen. </exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.WaitAsync(System.TimeSpan)">
      <summary>Wartet asynchron auf den Zutritt zum <see cref="T:System.Threading.SemaphoreSlim" /> unter Verwendung einer <see cref="T:System.TimeSpan" /> zum Messen des Zeitintervalls.</summary>
      <returns>Eine Aufgabe, die mit dem Ergebnis true abgeschlossen wird, wenn der aktuelle Thread erfolgreich in <see cref="T:System.Threading.SemaphoreSlim" /> gewechselt ist, andernfalls mit dem Ergebnis false.</returns>
      <param name="timeout">Eine <see cref="T:System.TimeSpan" />-Struktur, die die Anzahl der zu wartenden Millisekunden angibt, oder eine <see cref="T:System.TimeSpan" />-Struktur, die -1 Millisekunden zum unendlichen Warten angibt.</param>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits freigegeben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> ist eine negative Zahl, aber nicht -1. Der Wert -1 gibt einen Endlostimeout an. - oder -  Timeout ist größer als <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.WaitAsync(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Wartet asynchron auf den Zutritt zum <see cref="T:System.Threading.SemaphoreSlim" /> unter Verwendung einer <see cref="T:System.TimeSpan" /> zum Messen des Zeitintervalls, während ein <see cref="T:System.Threading.CancellationToken" /> beobachtet wird.</summary>
      <returns>Eine Aufgabe, die mit dem Ergebnis true abgeschlossen wird, wenn der aktuelle Thread erfolgreich in <see cref="T:System.Threading.SemaphoreSlim" /> gewechselt ist, andernfalls mit dem Ergebnis false.</returns>
      <param name="timeout">Eine <see cref="T:System.TimeSpan" />-Struktur, die die Anzahl der zu wartenden Millisekunden angibt, oder eine <see cref="T:System.TimeSpan" />-Struktur, die -1 Millisekunden zum unendlichen Warten angibt.</param>
      <param name="cancellationToken">Das zu überwachende <see cref="T:System.Threading.CancellationToken" />-Token.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> ist eine negative Zahl, aber nicht -1. Der Wert -1 gibt einen Endlostimeout an.- oder - Timeout ist größer als <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> wurde abgebrochen. </exception>
    </member>
    <member name="T:System.Threading.SendOrPostCallback">
      <summary>Stellt eine Methode dar, die aufgerufen werden muss, wenn eine Nachricht an einen Synchronisierungskontext gesendet werden soll.  </summary>
      <param name="state">Das an den Delegaten übergebene Objekt.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Threading.SpinLock">
      <summary>Stellt einen sich gegenseitig ausschließenden Sperrprimitiven bereit, wobei ein Thread, der versucht, die Sperre abzurufen, wiederholt in einer Schleife wartet, bis die Sperre verfügbar wird.</summary>
    </member>
    <member name="M:System.Threading.SpinLock.#ctor(System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.SpinLock" />-Struktur mit der Option, Thread-IDs nachzuverfolgen, um das Debuggen zu vereinfachen.</summary>
      <param name="enableThreadOwnerTracking">Gibt an, ob Thread-IDs zu Debugzwecken erfasst und verwendet werden.</param>
    </member>
    <member name="M:System.Threading.SpinLock.Enter(System.Boolean@)">
      <summary>Ruft die Sperre zuverlässig ab, sodass <paramref name="lockTaken" /> auch bei einer Ausnahme innerhalb des Methodenaufrufs zuverlässig untersucht werden kann, um zu bestimmen, ob die Sperre abgerufen wurde.</summary>
      <param name="lockTaken">True, wenn die Sperre abgerufen wird, andernfalls false.<paramref name="lockTaken" /> muss vor dem Aufrufen dieser Methode mit false initialisiert werden.</param>
      <exception cref="T:System.ArgumentException">Das <paramref name="lockTaken" />-Argument muss vor dem Aufrufen von Enter mit false initialisiert werden.</exception>
      <exception cref="T:System.Threading.LockRecursionException">Die Threadbesitznachverfolgung wird aktiviert, und der aktuelle Thread hat diese Sperre bereits abgerufen.</exception>
    </member>
    <member name="M:System.Threading.SpinLock.Exit">
      <summary>Hebt die Sperre auf.</summary>
      <exception cref="T:System.Threading.SynchronizationLockException">Die Threadbesitznachverfolgung wird aktiviert, und der aktuelle Thread ist nicht Besitzer dieser Sperre.</exception>
    </member>
    <member name="M:System.Threading.SpinLock.Exit(System.Boolean)">
      <summary>Hebt die Sperre auf.</summary>
      <param name="useMemoryBarrier">Ein boolescher Wert, der angibt, ob eine Arbeitsspeicherumgrenzung ausgegeben werden soll, um den Beendigungsvorgang sofort für andere Threads zu veröffentlichen.</param>
      <exception cref="T:System.Threading.SynchronizationLockException">Die Threadbesitznachverfolgung wird aktiviert, und der aktuelle Thread ist nicht Besitzer dieser Sperre.</exception>
    </member>
    <member name="P:System.Threading.SpinLock.IsHeld">
      <summary>Ruft einen Wert ab, der angibt, ob die Sperre zurzeit von einem Thread verwendet wird.</summary>
      <returns>True, wenn die Sperre zurzeit von einem Thread verwendet wird, andernfalls false.</returns>
    </member>
    <member name="P:System.Threading.SpinLock.IsHeldByCurrentThread">
      <summary>Ruft einen Wert ab, der angibt, ob die Sperre vom aktuellen Thread verwendet wird.</summary>
      <returns>True, wenn die Sperre vom aktuellen Thread verwendet wird, andernfalls false.</returns>
      <exception cref="T:System.InvalidOperationException">Die Threadbesitznachverfolgung wird deaktiviert.</exception>
    </member>
    <member name="P:System.Threading.SpinLock.IsThreadOwnerTrackingEnabled">
      <summary>Ruft einen Wert ab, der angibt, ob die Threadbesitznachverfolgung für diese Instanz aktiviert ist.</summary>
      <returns>True, wenn die Threadbesitznachverfolgung für diese Instanz aktiviert ist, andernfalls false.</returns>
    </member>
    <member name="M:System.Threading.SpinLock.TryEnter(System.Boolean@)">
      <summary>Versucht, die Sperre zuverlässig abzurufen, sodass <paramref name="lockTaken" /> auch bei einer Ausnahme innerhalb des Methodenaufrufs zuverlässig untersucht werden kann, um zu bestimmen, ob die Sperre abgerufen wurde.</summary>
      <param name="lockTaken">True, wenn die Sperre abgerufen wird, andernfalls false.<paramref name="lockTaken" /> muss vor dem Aufrufen dieser Methode mit false initialisiert werden.</param>
      <exception cref="T:System.ArgumentException">Das <paramref name="lockTaken" />-Argument muss vor dem Aufrufen von TryEnter mit false initialisiert werden.</exception>
      <exception cref="T:System.Threading.LockRecursionException">Die Threadbesitznachverfolgung wird aktiviert, und der aktuelle Thread hat diese Sperre bereits abgerufen.</exception>
    </member>
    <member name="M:System.Threading.SpinLock.TryEnter(System.Int32,System.Boolean@)">
      <summary>Versucht, die Sperre zuverlässig abzurufen, sodass <paramref name="lockTaken" /> auch bei einer Ausnahme innerhalb des Methodenaufrufs zuverlässig untersucht werden kann, um zu bestimmen, ob die Sperre abgerufen wurde.</summary>
      <param name="millisecondsTimeout">Die Anzahl von Millisekunden, die gewartet wird, oder <see cref="F:System.Threading.Timeout.Infinite" /> (-1) für Warten ohne Timeout.</param>
      <param name="lockTaken">True, wenn die Sperre abgerufen wird, andernfalls false.<paramref name="lockTaken" /> muss vor dem Aufrufen dieser Methode mit false initialisiert werden.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> ist eine negative Zahl, aber nicht -1. Der Wert -1 gibt einen Endlostimeout an.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="lockTaken" />-Argument muss vor dem Aufrufen von TryEnter mit false initialisiert werden.</exception>
      <exception cref="T:System.Threading.LockRecursionException">Die Threadbesitznachverfolgung wird aktiviert, und der aktuelle Thread hat diese Sperre bereits abgerufen.</exception>
    </member>
    <member name="M:System.Threading.SpinLock.TryEnter(System.TimeSpan,System.Boolean@)">
      <summary>Versucht, die Sperre zuverlässig abzurufen, sodass <paramref name="lockTaken" /> auch bei einer Ausnahme innerhalb des Methodenaufrufs zuverlässig untersucht werden kann, um zu bestimmen, ob die Sperre abgerufen wurde.</summary>
      <param name="timeout">Eine <see cref="T:System.TimeSpan" />-Struktur, die die Anzahl der zu wartenden Millisekunden angibt, oder eine <see cref="T:System.TimeSpan" />-Struktur, die -1 Millisekunden zum unendlichen Warten angibt.</param>
      <param name="lockTaken">True, wenn die Sperre abgerufen wird, andernfalls false.<paramref name="lockTaken" /> muss vor dem Aufrufen dieser Methode mit false initialisiert werden.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> ist eine negative Zahl ungleich -1 Millisekunden, die ein unendliches Timeout darstellt, - oder - Timeout ist größer als <see cref="F:System.Int32.MaxValue" /> Millisekunden.</exception>
      <exception cref="T:System.ArgumentException">Das <paramref name="lockTaken" />-Argument muss vor dem Aufrufen von TryEnter mit false initialisiert werden.</exception>
      <exception cref="T:System.Threading.LockRecursionException">Die Threadbesitznachverfolgung wird aktiviert, und der aktuelle Thread hat diese Sperre bereits abgerufen.</exception>
    </member>
    <member name="T:System.Threading.SpinWait">
      <summary>Stellt Unterstützung für Spin-basierte Wartevorgänge bereit.</summary>
    </member>
    <member name="P:System.Threading.SpinWait.Count">
      <summary>Ruft die Anzahl von <see cref="M:System.Threading.SpinWait.SpinOnce" />-Aufrufen für diese Instanz ab.</summary>
      <returns>Gibt eine ganze Zahl zurück, die angibt, wie häufig <see cref="M:System.Threading.SpinWait.SpinOnce" /> für diese Instanz aufgerufen wurde.</returns>
    </member>
    <member name="P:System.Threading.SpinWait.NextSpinWillYield">
      <summary>Ruft einen Wert ab, der angibt, ob der nächste Aufruf von <see cref="M:System.Threading.SpinWait.SpinOnce" /> den Prozessor ergibt und einen erzwungenen Kontextwechsel auslöst.</summary>
      <returns>Gibt an, ob der nächste Aufruf von <see cref="M:System.Threading.SpinWait.SpinOnce" /> den Prozessor ergibt und einen erzwungenen Kontextwechsel auslöst.</returns>
    </member>
    <member name="M:System.Threading.SpinWait.Reset">
      <summary>Setzt die Spin-Anzahl zurück.</summary>
    </member>
    <member name="M:System.Threading.SpinWait.SpinOnce">
      <summary>Führt einen Spin-Vorgang aus.</summary>
    </member>
    <member name="M:System.Threading.SpinWait.SpinUntil(System.Func{System.Boolean})">
      <summary>Führt Spin-Vorgänge aus, bis die angegebene Bedingung erfüllt wird.</summary>
      <param name="condition">Ein Delegat, der immer wieder ausgeführt wird, bis true zurückgegeben wird.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="condition" />-Argument ist Null.</exception>
    </member>
    <member name="M:System.Threading.SpinWait.SpinUntil(System.Func{System.Boolean},System.Int32)">
      <summary>Führt Spin-Vorgänge aus, bis die angegebene Bedingung erfüllt wird oder das angegebene Timeout abgelaufen ist.</summary>
      <returns>True, wenn die Bedingung innerhalb des Timeouts erfüllt wird, andernfalls false.</returns>
      <param name="condition">Ein Delegat, der immer wieder ausgeführt wird, bis true zurückgegeben wird.</param>
      <param name="millisecondsTimeout">Die Anzahl von Millisekunden, die gewartet wird, oder <see cref="F:System.Threading.Timeout.Infinite" /> (-1) für Warten ohne Timeout.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="condition" />-Argument ist Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> ist eine negative Zahl, aber nicht -1. Der Wert -1 gibt einen Endlostimeout an.</exception>
    </member>
    <member name="M:System.Threading.SpinWait.SpinUntil(System.Func{System.Boolean},System.TimeSpan)">
      <summary>Führt Spin-Vorgänge aus, bis die angegebene Bedingung erfüllt wird oder das angegebene Timeout abgelaufen ist.</summary>
      <returns>True, wenn die Bedingung innerhalb des Timeouts erfüllt wird, andernfalls false.</returns>
      <param name="condition">Ein Delegat, der immer wieder ausgeführt wird, bis true zurückgegeben wird.</param>
      <param name="timeout">Ein <see cref="T:System.TimeSpan" />, das die Wartezeit in Millisekunden darstellt, oder ein TimeSpan-Wert, der -1 Millisekunden für Warten ohne Timeout darstellt.</param>
      <exception cref="T:System.ArgumentNullException">Das <paramref name="condition" />-Argument ist Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> ist eine negative Zahl ungleich  -1 Millisekunden, die ein unendliches Timeout darstellt, - oder - Timeout ist größer als <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="T:System.Threading.SynchronizationContext">
      <summary>Stellt die Grundfunktionen für die Weitergabe eines Synchronisierungskontexts in unterschiedlichen Synchronisierungsmodellen bereit. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.SynchronizationContext.#ctor">
      <summary>Erstellt eine neue Instanz der <see cref="T:System.Threading.SynchronizationContext" />-Klasse.</summary>
    </member>
    <member name="M:System.Threading.SynchronizationContext.CreateCopy">
      <summary>Erstellt beim Überschreiben in einer abgeleiteten Klasse eine Kopie des Synchronisierungskontexts.  </summary>
      <returns>Ein neues <see cref="T:System.Threading.SynchronizationContext" />-Objekt.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.SynchronizationContext.Current">
      <summary>Ruft den Synchronisierungskontext für den aktuellen Thread ab.</summary>
      <returns>Ein <see cref="T:System.Threading.SynchronizationContext" />-Objekt, das den aktuellen Synchronisierungskontext darstellt.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.SynchronizationContext.OperationCompleted">
      <summary>Antwortet beim Überschreiben in einer abgeleiteten Klasse auf die Benachrichtigung, dass ein Vorgang abgeschlossen wurde.</summary>
    </member>
    <member name="M:System.Threading.SynchronizationContext.OperationStarted">
      <summary>Antwortet beim Überschreiben in einer abgeleiteten Klasse auf die Benachrichtigung, dass ein Vorgang gestartet wurde.</summary>
    </member>
    <member name="M:System.Threading.SynchronizationContext.Post(System.Threading.SendOrPostCallback,System.Object)">
      <summary>Sendet beim Überschreiben in einer abgeleiteten Klasse eine asynchrone Meldung an einen Synchronisierungskontext.</summary>
      <param name="d">Der aufzurufende <see cref="T:System.Threading.SendOrPostCallback" />-Delegat.</param>
      <param name="state">Das an den Delegaten übergebene Objekt.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.SynchronizationContext.Send(System.Threading.SendOrPostCallback,System.Object)">
      <summary>Sendet beim Überschreiben in einer abgeleiteten Klasse eine synchrone Meldung an einen Synchronisierungskontext.</summary>
      <param name="d">Der aufzurufende <see cref="T:System.Threading.SendOrPostCallback" />-Delegat.</param>
      <param name="state">Das an den Delegaten übergebene Objekt. </param>
      <exception cref="T:System.NotSupportedException">The method was called in a Windows Store app.The implementation of <see cref="T:System.Threading.SynchronizationContext" /> for Windows Store apps does not support the <see cref="M:System.Threading.SynchronizationContext.Send(System.Threading.SendOrPostCallback,System.Object)" /> method.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.SynchronizationContext.SetSynchronizationContext(System.Threading.SynchronizationContext)">
      <summary>Legt den aktuellen Synchronisierungskontext fest.</summary>
      <param name="syncContext">Das festzulegende <see cref="T:System.Threading.SynchronizationContext" />-Objekt.</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence, ControlPolicy" />
      </PermissionSet>
    </member>
    <member name="T:System.Threading.SynchronizationLockException">
      <summary>Die Ausnahme, die ausgelöst wird, wenn der Aufrufer für eine Methode über eine Sperre für einen bestimmten Monitor verfügen muss und die Methode von einem Aufrufer aufgerufen wird, der nicht über diese Sperre verfügt.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.SynchronizationLockException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.SynchronizationLockException" />-Klasse mit Standardeigenschaften.</summary>
    </member>
    <member name="M:System.Threading.SynchronizationLockException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.SynchronizationLockException" />-Klasse mit einer angegebenen Fehlermeldung.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird. </param>
    </member>
    <member name="M:System.Threading.SynchronizationLockException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.SynchronizationLockException" />-Klasse mit einer angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird. </param>
      <param name="innerException">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="innerException" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="T:System.Threading.ThreadLocal`1">
      <summary>Stellt einen lokalen Datenspeicher eines Threads bereit.</summary>
      <typeparam name="T">Gibt den für jeden Thread gespeicherten Datentyp an.</typeparam>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.#ctor">
      <summary>Initialisiert die <see cref="T:System.Threading.ThreadLocal`1" />-Instanz.</summary>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.#ctor(System.Boolean)">
      <summary>Initialisiert die <see cref="T:System.Threading.ThreadLocal`1" />-Instanz.</summary>
      <param name="trackAllValues">Ob alle Werte, die für die Instanz festgelegt werden, verfolgt werden und über die <see cref="P:System.Threading.ThreadLocal`1.Values" />-Eigenschaft verfügbar gemacht sollen.</param>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.#ctor(System.Func{`0})">
      <summary>Initialisiert die <see cref="T:System.Threading.ThreadLocal`1" />-Instanz mit der angegebenen <paramref name="valueFactory" />-Funktion.</summary>
      <param name="valueFactory">Das <see cref="T:System.Func`1" />, das aufgerufen wird, um einen verzögert initialisierten Wert zu erzeugen, wenn versucht wird, <see cref="P:System.Threading.ThreadLocal`1.Value" /> ohne vorherige Initialisierung abzurufen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="valueFactory" /> ist ein NULL-Verweis (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.#ctor(System.Func{`0},System.Boolean)">
      <summary>Initialisiert die <see cref="T:System.Threading.ThreadLocal`1" />-Instanz mit der angegebenen <paramref name="valueFactory" />-Funktion.</summary>
      <param name="valueFactory">Das <see cref="T:System.Func`1" />, das aufgerufen wird, um einen verzögert initialisierten Wert zu erzeugen, wenn versucht wird, <see cref="P:System.Threading.ThreadLocal`1.Value" /> ohne vorherige Initialisierung abzurufen.</param>
      <param name="trackAllValues">Ob alle Werte, die für die Instanz festgelegt werden, verfolgt werden und über die <see cref="P:System.Threading.ThreadLocal`1.Values" />-Eigenschaft verfügbar gemacht sollen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="valueFactory" /> ist ein null-Verweis (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.Dispose">
      <summary>Gibt alle von der aktuellen Instanz der <see cref="T:System.Threading.ThreadLocal`1" />-Klasse verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.Dispose(System.Boolean)">
      <summary>Gibt die von dieser <see cref="T:System.Threading.ThreadLocal`1" />-Instanz verwendeten Ressourcen frei.</summary>
      <param name="disposing">Ein boolescher Wert, der angibt, ob diese Methode aufgrund eines Aufrufs von <see cref="M:System.Threading.ThreadLocal`1.Dispose" /> aufgerufen wird.</param>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.Finalize">
      <summary>Gibt die von dieser <see cref="T:System.Threading.ThreadLocal`1" />-Instanz verwendeten Ressourcen frei.</summary>
    </member>
    <member name="P:System.Threading.ThreadLocal`1.IsValueCreated">
      <summary>Ruft einen Wert ab, der angibt, ob <see cref="P:System.Threading.ThreadLocal`1.Value" /> für den aktuellen Thread initialisiert wurde.</summary>
      <returns>True, wenn <see cref="P:System.Threading.ThreadLocal`1.Value" /> erfolgreich im aktuellen Thread initialisiert wurde, andernfalls false.</returns>
      <exception cref="T:System.ObjectDisposedException">Die <see cref="T:System.Threading.ThreadLocal`1" />-Instanz wurde freigegeben.</exception>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.ToString">
      <summary>Erstellt eine Zeichenfolgendarstellung dieser Instanz für den aktuellen Thread und gibt sie zurück.</summary>
      <returns>Das Ergebnis des Aufrufs von <see cref="M:System.Object.ToString" /> für <see cref="P:System.Threading.ThreadLocal`1.Value" />.</returns>
      <exception cref="T:System.ObjectDisposedException">Die <see cref="T:System.Threading.ThreadLocal`1" />-Instanz wurde freigegeben.</exception>
      <exception cref="T:System.NullReferenceException">Der <see cref="P:System.Threading.ThreadLocal`1.Value" /> für den aktuellen Thread ist ein NULL-Verweis (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">Die Initialisierungsfunktion versuchte, auf <see cref="P:System.Threading.ThreadLocal`1.Value" /> rekursiv zu verweisen.</exception>
      <exception cref="T:System.MissingMemberException">Kein Standardkonstruktor wird bereitgestellt, und keine Wertfactory wird angegeben.</exception>
    </member>
    <member name="P:System.Threading.ThreadLocal`1.Value">
      <summary>Ruft den Wert dieser Instanz für den aktuellen Thread ab oder legt ihn fest.</summary>
      <returns>Gibt eine Instanz des Objekts zurück, für dessen Initialisierung dieser ThreadLocal zuständig ist.</returns>
      <exception cref="T:System.ObjectDisposedException">Die <see cref="T:System.Threading.ThreadLocal`1" />-Instanz wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Die Initialisierungsfunktion versuchte, auf <see cref="P:System.Threading.ThreadLocal`1.Value" /> rekursiv zu verweisen.</exception>
      <exception cref="T:System.MissingMemberException">Kein Standardkonstruktor wird bereitgestellt, und keine Wertfactory wird angegeben.</exception>
    </member>
    <member name="P:System.Threading.ThreadLocal`1.Values">
      <summary>Ruft eine Liste aller Werte ab, die aktuell von allen Threads, die auf diese Instanz zugegriffen haben, gespeichert werden.</summary>
      <returns>Eine Liste aller Werte, die aktuell von allen Threads, die auf diese Instanz zugegriffen haben, gespeichert sind.</returns>
      <exception cref="T:System.ObjectDisposedException">Die <see cref="T:System.Threading.ThreadLocal`1" />-Instanz wurde freigegeben.</exception>
    </member>
    <member name="T:System.Threading.Volatile">
      <summary>Enthält Methoden für die Durchführung von Vorgängen für flüchtigen Speicher.</summary>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Boolean@)">
      <summary>Liest den Wert des angegebenen Felds.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn nach dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht vor diese Methode verschoben werden.</summary>
      <returns>Der gelesene Wert.Dieser Wert entspricht dem letzten von einem Prozessor im Computer geschriebenen Wert, unabhängig von der Anzahl der Prozessoren und dem Zustand des Prozessorcaches.</returns>
      <param name="location">Das zu lesende Feld.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Byte@)">
      <summary>Liest den Wert des angegebenen Felds.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn nach dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht vor diese Methode verschoben werden.</summary>
      <returns>Der gelesene Wert.Dieser Wert entspricht dem letzten von einem Prozessor im Computer geschriebenen Wert, unabhängig von der Anzahl der Prozessoren und dem Zustand des Prozessorcaches.</returns>
      <param name="location">Das zu lesende Feld.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Double@)">
      <summary>Liest den Wert des angegebenen Felds.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn nach dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht vor diese Methode verschoben werden.</summary>
      <returns>Der gelesene Wert.Dieser Wert entspricht dem letzten von einem Prozessor im Computer geschriebenen Wert, unabhängig von der Anzahl der Prozessoren und dem Zustand des Prozessorcaches.</returns>
      <param name="location">Das zu lesende Feld.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Int16@)">
      <summary>Liest den Wert des angegebenen Felds.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn nach dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht vor diese Methode verschoben werden.</summary>
      <returns>Der gelesene Wert.Dieser Wert entspricht dem letzten von einem Prozessor im Computer geschriebenen Wert, unabhängig von der Anzahl der Prozessoren und dem Zustand des Prozessorcaches.</returns>
      <param name="location">Das zu lesende Feld.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Int32@)">
      <summary>Liest den Wert des angegebenen Felds.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn nach dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht vor diese Methode verschoben werden.</summary>
      <returns>Der gelesene Wert.Dieser Wert entspricht dem letzten von einem Prozessor im Computer geschriebenen Wert, unabhängig von der Anzahl der Prozessoren und dem Zustand des Prozessorcaches.</returns>
      <param name="location">Das zu lesende Feld.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Int64@)">
      <summary>Liest den Wert des angegebenen Felds.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn nach dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht vor diese Methode verschoben werden.</summary>
      <returns>Der gelesene Wert.Dieser Wert entspricht dem letzten von einem Prozessor im Computer geschriebenen Wert, unabhängig von der Anzahl der Prozessoren und dem Zustand des Prozessorcaches.</returns>
      <param name="location">Das zu lesende Feld.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.IntPtr@)">
      <summary>Liest den Wert des angegebenen Felds.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn nach dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht vor diese Methode verschoben werden.</summary>
      <returns>Der gelesene Wert.Dieser Wert entspricht dem letzten von einem Prozessor im Computer geschriebenen Wert, unabhängig von der Anzahl der Prozessoren und dem Zustand des Prozessorcaches.</returns>
      <param name="location">Das zu lesende Feld.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.SByte@)">
      <summary>Liest den Wert des angegebenen Felds.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn nach dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht vor diese Methode verschoben werden.</summary>
      <returns>Der gelesene Wert.Dieser Wert entspricht dem letzten von einem Prozessor im Computer geschriebenen Wert, unabhängig von der Anzahl der Prozessoren und dem Zustand des Prozessorcaches.</returns>
      <param name="location">Das zu lesende Feld.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Single@)">
      <summary>Liest den Wert des angegebenen Felds.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn nach dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht vor diese Methode verschoben werden.</summary>
      <returns>Der gelesene Wert.Dieser Wert entspricht dem letzten von einem Prozessor im Computer geschriebenen Wert, unabhängig von der Anzahl der Prozessoren und dem Zustand des Prozessorcaches.</returns>
      <param name="location">Das zu lesende Feld.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.UInt16@)">
      <summary>Liest den Wert des angegebenen Felds.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn nach dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht vor diese Methode verschoben werden.</summary>
      <returns>Der gelesene Wert.Dieser Wert entspricht dem letzten von einem Prozessor im Computer geschriebenen Wert, unabhängig von der Anzahl der Prozessoren und dem Zustand des Prozessorcaches.</returns>
      <param name="location">Das zu lesende Feld.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.UInt32@)">
      <summary>Liest den Wert des angegebenen Felds.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn nach dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht vor diese Methode verschoben werden.</summary>
      <returns>Der gelesene Wert.Dieser Wert entspricht dem letzten von einem Prozessor im Computer geschriebenen Wert, unabhängig von der Anzahl der Prozessoren und dem Zustand des Prozessorcaches.</returns>
      <param name="location">Das zu lesende Feld.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.UInt64@)">
      <summary>Liest den Wert des angegebenen Felds.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn nach dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht vor diese Methode verschoben werden.</summary>
      <returns>Der gelesene Wert.Dieser Wert entspricht dem letzten von einem Prozessor im Computer geschriebenen Wert, unabhängig von der Anzahl der Prozessoren und dem Zustand des Prozessorcaches.</returns>
      <param name="location">Das zu lesende Feld.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.UIntPtr@)">
      <summary>Liest den Wert des angegebenen Felds.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn nach dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht vor diese Methode verschoben werden.</summary>
      <returns>Der gelesene Wert.Dieser Wert entspricht dem letzten von einem Prozessor im Computer geschriebenen Wert, unabhängig von der Anzahl der Prozessoren und dem Zustand des Prozessorcaches.</returns>
      <param name="location">Das zu lesende Feld.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read``1(``0@)">
      <summary>Liest den Objektverweis aus dem angegebenen Feld.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn nach dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht vor diese Methode verschoben werden.</summary>
      <returns>Der Verweis auf <paramref name="T" />, der gelesen wurde.Dieser Verweis entspricht dem letzten von einem Prozessor im Computer geschriebenen Verweis, unabhängig von der Anzahl der Prozessoren und dem Zustand des Prozessorcaches.</returns>
      <param name="location">Das zu lesende Feld.</param>
      <typeparam name="T">Der Typ des zu lesenden Felds.Dabei muss es sich um einen Verweistyp und keinen Werttyp handeln.</typeparam>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Boolean@,System.Boolean)">
      <summary>Schreibt den angegebenen Wert in das angegebene Feld.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn vor dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht hinter diese Methode verschoben werden.</summary>
      <param name="location">Das Feld, in das der Wert geschrieben wird.</param>
      <param name="value">Der zu schreibende Wert.Der Wert wird sofort geschrieben, sodass er für alle Prozessoren im Computer sichtbar ist.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Byte@,System.Byte)">
      <summary>Schreibt den angegebenen Wert in das angegebene Feld.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn vor dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht hinter diese Methode verschoben werden.</summary>
      <param name="location">Das Feld, in das der Wert geschrieben wird.</param>
      <param name="value">Der zu schreibende Wert.Der Wert wird sofort geschrieben, sodass er für alle Prozessoren im Computer sichtbar ist.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Double@,System.Double)">
      <summary>Schreibt den angegebenen Wert in das angegebene Feld.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn vor dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht hinter diese Methode verschoben werden.</summary>
      <param name="location">Das Feld, in das der Wert geschrieben wird.</param>
      <param name="value">Der zu schreibende Wert.Der Wert wird sofort geschrieben, sodass er für alle Prozessoren im Computer sichtbar ist.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Int16@,System.Int16)">
      <summary>Schreibt den angegebenen Wert in das angegebene Feld.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn vor dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht hinter diese Methode verschoben werden.</summary>
      <param name="location">Das Feld, in das der Wert geschrieben wird.</param>
      <param name="value">Der zu schreibende Wert.Der Wert wird sofort geschrieben, sodass er für alle Prozessoren im Computer sichtbar ist.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Int32@,System.Int32)">
      <summary>Schreibt den angegebenen Wert in das angegebene Feld.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn vor dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht hinter diese Methode verschoben werden.</summary>
      <param name="location">Das Feld, in das der Wert geschrieben wird.</param>
      <param name="value">Der zu schreibende Wert.Der Wert wird sofort geschrieben, sodass er für alle Prozessoren im Computer sichtbar ist.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Int64@,System.Int64)">
      <summary>Schreibt den angegebenen Wert in das angegebene Feld.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn vor dieser Methode im Code ein Arbeitsspeichervorgang ausgeführt wird, kann dieser vom Prozessor nicht hinter diese Methode verschoben werden.</summary>
      <param name="location">Das Feld, in das der Wert geschrieben wird.</param>
      <param name="value">Der zu schreibende Wert.Der Wert wird sofort geschrieben, sodass er für alle Prozessoren im Computer sichtbar ist.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.IntPtr@,System.IntPtr)">
      <summary>Schreibt den angegebenen Wert in das angegebene Feld.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn vor dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht hinter diese Methode verschoben werden.</summary>
      <param name="location">Das Feld, in das der Wert geschrieben wird.</param>
      <param name="value">Der zu schreibende Wert.Der Wert wird sofort geschrieben, sodass er für alle Prozessoren im Computer sichtbar ist.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.SByte@,System.SByte)">
      <summary>Schreibt den angegebenen Wert in das angegebene Feld.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn vor dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht hinter diese Methode verschoben werden.</summary>
      <param name="location">Das Feld, in das der Wert geschrieben wird.</param>
      <param name="value">Der zu schreibende Wert.Der Wert wird sofort geschrieben, sodass er für alle Prozessoren im Computer sichtbar ist.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Single@,System.Single)">
      <summary>Schreibt den angegebenen Wert in das angegebene Feld.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn vor dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht hinter diese Methode verschoben werden.</summary>
      <param name="location">Das Feld, in das der Wert geschrieben wird.</param>
      <param name="value">Der zu schreibende Wert.Der Wert wird sofort geschrieben, sodass er für alle Prozessoren im Computer sichtbar ist.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.UInt16@,System.UInt16)">
      <summary>Schreibt den angegebenen Wert in das angegebene Feld.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn vor dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht hinter diese Methode verschoben werden.</summary>
      <param name="location">Das Feld, in das der Wert geschrieben wird.</param>
      <param name="value">Der zu schreibende Wert.Der Wert wird sofort geschrieben, sodass er für alle Prozessoren im Computer sichtbar ist.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.UInt32@,System.UInt32)">
      <summary>Schreibt den angegebenen Wert in das angegebene Feld.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn vor dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht hinter diese Methode verschoben werden.</summary>
      <param name="location">Das Feld, in das der Wert geschrieben wird.</param>
      <param name="value">Der zu schreibende Wert.Der Wert wird sofort geschrieben, sodass er für alle Prozessoren im Computer sichtbar ist.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.UInt64@,System.UInt64)">
      <summary>Schreibt den angegebenen Wert in das angegebene Feld.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn vor dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht hinter diese Methode verschoben werden.</summary>
      <param name="location">Das Feld, in das der Wert geschrieben wird.</param>
      <param name="value">Der zu schreibende Wert.Der Wert wird sofort geschrieben, sodass er für alle Prozessoren im Computer sichtbar ist.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.UIntPtr@,System.UIntPtr)">
      <summary>Schreibt den angegebenen Wert in das angegebene Feld.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn vor dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht hinter diese Methode verschoben werden.</summary>
      <param name="location">Das Feld, in das der Wert geschrieben wird.</param>
      <param name="value">Der zu schreibende Wert.Der Wert wird sofort geschrieben, sodass er für alle Prozessoren im Computer sichtbar ist.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write``1(``0@,``0)">
      <summary>Schreibt den angegebenen Objektverweis in das angegebene Feld.Auf Systemen, auf denen dies erforderlich ist, wird eine Arbeitsspeicherbarriere eingefügt, die verhindert, dass der Prozessor Arbeitsspeichervorgänge wie folgt neu anordnet: Wenn vor dieser Methode im Code ein Lese- oder Schreibvorgang ausgeführt wird, kann dieser vom Prozessor nicht hinter diese Methode verschoben werden.</summary>
      <param name="location">Das Feld, in das der Objektverweis geschrieben wird.</param>
      <param name="value">Der zu schreibende Objektverweis.Der Verweis wird sofort geschrieben, sodass er für alle Prozessoren im Computer sichtbar ist.</param>
      <typeparam name="T">Der Typ des zu schreibenden Felds.Dabei muss es sich um einen Verweistyp und keinen Werttyp handeln.</typeparam>
    </member>
    <member name="T:System.Threading.WaitHandleCannotBeOpenedException">
      <summary>Die Ausnahme, die ausgelöst wird, wenn versucht wird, einen nicht vorhandenen Systemmutex oder ein nicht vorhandenes Semaphor zu öffnen.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.WaitHandleCannotBeOpenedException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.WaitHandleCannotBeOpenedException" />-Klasse mit Standardwerten.</summary>
    </member>
    <member name="M:System.Threading.WaitHandleCannotBeOpenedException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.WaitHandleCannotBeOpenedException" />-Klasse mit einer angegebenen Fehlermeldung.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird.</param>
    </member>
    <member name="M:System.Threading.WaitHandleCannotBeOpenedException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.WaitHandleCannotBeOpenedException" />-Klasse mit einer angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird.</param>
      <param name="innerException">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="innerException" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
  </members>
</doc>