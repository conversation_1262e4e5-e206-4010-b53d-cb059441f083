<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Target Name="Microsoft_ML_OnnxRuntime_CheckPrerequisites" BeforeTargets="BeforeBuild">
    <!--
    Special case .NET Core portable applications.  When building a portable .NET Core app,
    the PlatformTarget is empty, and you don't know until runtime (i.e. which dotnet.exe)
    what processor architecture will be used.
  -->
    <Error Condition="('$(PlatformTarget)' != 'x64' AND '$(PlatformTarget)' != 'arm32' AND '$(PlatformTarget)' != 'arm64' AND '$(PlatformTarget)' != 'x86' AND '$(PlatformTarget)' != 'AnyCPU') AND
                      ('$(OutputType)' == 'Exe' OR '$(OutputType)'=='WinExe') AND
                      !('$(TargetFrameworkIdentifier)' == '.NETCoreApp' AND '$(PlatformTarget)' == '') AND
                       $([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) != 'ios' AND
                      '$(SuppressOnnxRuntimePlatformCompatibilityError)' != 'true'"
           Text="Microsoft.ML.OnnxRuntime only supports the AnyCPU, x64, arm32, arm64 and x86 platforms at this time."/>
  </Target>
</Project>
