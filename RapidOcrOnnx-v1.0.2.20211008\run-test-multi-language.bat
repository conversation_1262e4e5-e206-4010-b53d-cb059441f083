chcp 65001
:: Set Param
@ECHO OFF
@SETLOCAL
echo "Setting the Number of Threads=%NUMBER_OF_PROCESSORS% Using an OpenMP Environment Variable"
set OMP_NUM_THREADS=%NUMBER_OF_PROCESSORS%

:MainExec
echo "请输入测试选项并回车: 1)x64, 2)x86"
set /p flag=
if %flag% == 1 (set EXE_PATH=win-x64)^
else if %flag% == 2 (set EXE_PATH=win-x86)^
else (echo 输入错误！Input Error!)

echo "请选择det模型: 1)server, 2)mobile"
set /p flag=
if %flag% == 1 (set DET_MODEL=ch_ppocr_server_v2.0_det_infer.onnx)^
else if %flag% == 2 (set DET_MODEL=ch_ppocr_mobile_v2.0_det_infer.onnx)^
else (echo 输入错误！Input Error!)

echo "要正确显示系统必须安装对应语言的字体"
echo "请选择语言: 1)英文en, 2)法文french, 3)德文german(x), 4)韩文korean, 5)日文japan"
echo "请选择语言: 6)意大利文it, 7)西班牙文xi(x), 8)葡萄牙文pu, 9)俄罗斯文ru, 10)阿拉伯文ar"
echo "请选择语言: 11)印地文hi, 12)繁体中文cht, 13)维吾尔文ug, 14)波斯文fa, 15)乌尔都文ur"
echo "请选择语言: 16)塞尔维亚文(latin)rs, 17)欧西坦文oc, 18)马拉地文mr, 19)尼泊尔文ne, 20)塞尔维亚文(cyrillic)rsc"
echo "请选择语言: 21)保加利亚文bg, 22)乌克兰文uk, 23)白俄罗斯文be, 24)泰卢固文te, 25)卡纳达文ka, 26)泰米尔文ta"
set /p flag=
if %flag% == 1 (
	set REC_LANG=en
    set REC_MODEL=en_number_mobile_v2.0_rec_infer.onnx
    set KEYS=en_dict.txt
)^
else if %flag% == 2 (
    set REC_LANG=french
	set REC_MODEL=french_mobile_v2.0_rec_infer.onnx
    set KEYS=french_dict.txt
)^
else if %flag% == 3 (
    set REC_LANG=german
    set REC_MODEL=german_mobile_v2.0_rec_infer.onnx
    set KEYS=german_dict.txt
)^
else if %flag% == 4 (
    set REC_LANG=korean
    set REC_MODEL=korean_mobile_v2.0_rec_infer.onnx
    set KEYS=korean_dict.txt
)^
else if %flag% == 5 (
    set REC_LANG=japan
    set REC_MODEL=japan_mobile_v2.0_rec_infer.onnx
    set KEYS=japan_dict.txt
)^
else if %flag% == 6 (
    set REC_LANG=it
    set REC_MODEL=it_mobile_v2.0_rec_infer.onnx
    set KEYS=it_dict.txt
)^
else if %flag% == 7 (
    set REC_LANG=xi
    set REC_MODEL=xi_mobile_v2.0_rec_infer.onnx
    set KEYS=xi_dict.txt
)^
else if %flag% == 8 (
    set REC_LANG=pu
    set REC_MODEL=pu_mobile_v2.0_rec_infer.onnx
    set KEYS=pu_dict.txt
)^
else if %flag% == 9 (
    set REC_LANG=ru
    set REC_MODEL=ru_mobile_v2.0_rec_infer.onnx
    set KEYS=ru_dict.txt
)^
else if %flag% == 10 (
    set REC_LANG=ar
    set REC_MODEL=ar_mobile_v2.0_rec_infer.onnx
    set KEYS=ar_dict.txt
)^
else if %flag% == 11 (
    set REC_LANG=hi
    set REC_MODEL=hi_mobile_v2.0_rec_infer.onnx
    set KEYS=hi_dict.txt
)^
else if %flag% == 12 (
    set REC_LANG=chinese_cht
    set REC_MODEL=chinese_cht_mobile_v2.0_rec_infer.onnx
    set KEYS=chinese_cht_dict.txt
)^
else if %flag% == 13 (
    set REC_LANG=ug
    set REC_MODEL=ug_mobile_v2.0_rec_infer.onnx
    set KEYS=ug_dict.txt
)^
else if %flag% == 14 (
    set REC_LANG=fa
    set REC_MODEL=fa_mobile_v2.0_rec_infer.onnx
    set KEYS=fa_dict.txt
)^
else if %flag% == 15 (
    set REC_LANG=ur
    set REC_MODEL=ur_mobile_v2.0_rec_infer.onnx
    set KEYS=ur_dict.txt
)^
else if %flag% == 16 (
    set REC_LANG=rs
    set REC_MODEL=rs_mobile_v2.0_rec_infer.onnx
    set KEYS=rs_dict.txt
)^
else if %flag% == 17 (
    set REC_LANG=oc
    set REC_MODEL=oc_mobile_v2.0_rec_infer.onnx
    set KEYS=oc_dict.txt
)^
else if %flag% == 18 (
    set REC_LANG=mr
    set REC_MODEL=mr_mobile_v2.0_rec_infer.onnx
    set KEYS=mr_dict.txt
)^
else if %flag% == 19 (
    set REC_LANG=ne
    set REC_MODEL=ne_mobile_v2.0_rec_infer.onnx
    set KEYS=ne_dict.txt
)^
else if %flag% == 20 (
    set REC_LANG=rsc
    set REC_MODEL=rsc_mobile_v2.0_rec_infer.onnx
    set KEYS=rsc_dict.txt
)^
else if %flag% == 21 (
    set REC_LANG=bg
    set REC_MODEL=bg_mobile_v2.0_rec_infer.onnx
    set KEYS=bg_dict.txt
)^
else if %flag% == 22 (
    set REC_LANG=uk
    set REC_MODEL=uk_mobile_v2.0_rec_infer.onnx
    set KEYS=uk_dict.txt
)^
else if %flag% == 23 (
    set REC_LANG=be
    set REC_MODEL=be_mobile_v2.0_rec_infer.onnx
    set KEYS=be_dict.txt
)^
else if %flag% == 24 (
    set REC_LANG=te
    set REC_MODEL=te_mobile_v2.0_rec_infer.onnx
    set KEYS=te_dict.txt
)^
else if %flag% == 25 (
    set REC_LANG=ka
    set REC_MODEL=ka_mobile_v2.0_rec_infer.onnx
    set KEYS=ka_dict.txt
)^
else if %flag% == 26 (
    set REC_LANG=ta
    set REC_MODEL=ta_mobile_v2.0_rec_infer.onnx
    set KEYS=ta_dict.txt
)^
else (echo 输入错误！Input Error!)

SET TARGET_IMG=images/%REC_LANG%.jpg
if not exist %TARGET_IMG% (
echo "找不到待识别的目标图片：%TARGET_IMG%，请打开本文件并编辑TARGET_IMG"
PAUSE
exit
)

:: run Windows
%EXE_PATH%\BaiPiaoOcrOnnx.exe --version
%EXE_PATH%\BaiPiaoOcrOnnx.exe --models models ^
--det %DET_MODEL% ^
--cls ch_ppocr_mobile_v2.0_cls_infer.onnx ^
--rec %REC_MODEL% ^
--keys %KEYS% ^
--image %TARGET_IMG% ^
--numThread %NUMBER_OF_PROCESSORS% ^
--padding 50 ^
--maxSideLen 1024 ^
--boxScoreThresh 0.5 ^
--boxThresh 0.3 ^
--unClipRatio 1.5 ^
--doAngle 0 ^
--mostAngle 0

:: cls模型只能支持中文，所以此处关闭doAngle

echo.
GOTO:MainExec

@ENDLOCAL
