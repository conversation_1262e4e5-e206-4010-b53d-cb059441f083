using System;
using System.Diagnostics;
using System.Runtime;
using System.Threading;

namespace OcrLiteLib
{
    /// <summary>
    /// CPU优化器，提高CPU利用率和性能
    /// </summary>
    public static class CpuOptimizer
    {
        /// <summary>
        /// 优化CPU设置以提高性能
        /// </summary>
        public static void OptimizeForHighPerformance()
        {
            Console.WriteLine("=== CPU性能优化 ===");
            
            try
            {
                // 1. 设置进程优先级为高
                SetProcessPriority();
                
                // 2. 优化GC设置
                OptimizeGarbageCollection();
                
                // 3. 优化线程池
                OptimizeThreadPool();
                
                // 4. 设置处理器亲和性（如果需要）
                // OptimizeProcessorAffinity();
                
                Console.WriteLine("CPU性能优化完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"CPU优化失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 设置进程优先级
        /// </summary>
        private static void SetProcessPriority()
        {
            try
            {
                var currentProcess = Process.GetCurrentProcess();
                currentProcess.PriorityClass = ProcessPriorityClass.High;
                Console.WriteLine("进程优先级设置为: High");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置进程优先级失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 优化垃圾回收设置
        /// </summary>
        private static void OptimizeGarbageCollection()
        {
            try
            {
                // 设置为服务器GC模式（如果可用）
                if (GCSettings.IsServerGC)
                {
                    Console.WriteLine("已启用服务器GC模式");
                }
                else
                {
                    Console.WriteLine("使用工作站GC模式");
                }
                
                // 设置延迟模式为批处理，减少GC中断
                GCSettings.LatencyMode = GCLatencyMode.Batch;
                Console.WriteLine("GC延迟模式设置为: Batch");
                
                // 设置大对象堆压缩
                GCSettings.LargeObjectHeapCompactionMode = GCLargeObjectHeapCompactionMode.CompactOnce;
                Console.WriteLine("大对象堆压缩已启用");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GC优化失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 优化线程池设置
        /// </summary>
        private static void OptimizeThreadPool()
        {
            try
            {
                int workerThreads, completionPortThreads;
                ThreadPool.GetMinThreads(out workerThreads, out completionPortThreads);
                
                // 计算最优线程数
                int coreCount = Environment.ProcessorCount;
                int optimalWorkerThreads = Math.Max(coreCount * 3, 16); // 至少16个线程
                int optimalCompletionThreads = Math.Max(coreCount * 2, 8);
                
                // 设置最小线程数，减少线程创建延迟
                ThreadPool.SetMinThreads(optimalWorkerThreads, optimalCompletionThreads);
                
                // 设置最大线程数
                ThreadPool.GetMaxThreads(out workerThreads, out completionPortThreads);
                int maxWorkerThreads = Math.Max(coreCount * 6, 32);
                int maxCompletionThreads = Math.Max(coreCount * 3, 16);
                
                ThreadPool.SetMaxThreads(maxWorkerThreads, maxCompletionThreads);
                
                Console.WriteLine($"线程池优化:");
                Console.WriteLine($"  最小工作线程: {optimalWorkerThreads}");
                Console.WriteLine($"  最大工作线程: {maxWorkerThreads}");
                Console.WriteLine($"  最小完成端口线程: {optimalCompletionThreads}");
                Console.WriteLine($"  最大完成端口线程: {maxCompletionThreads}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"线程池优化失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 优化处理器亲和性（可选）
        /// </summary>
        private static void OptimizeProcessorAffinity()
        {
            try
            {
                var currentProcess = Process.GetCurrentProcess();
                int coreCount = Environment.ProcessorCount;
                
                // 使用所有可用的CPU核心
                long affinityMask = (1L << coreCount) - 1;
                currentProcess.ProcessorAffinity = new IntPtr(affinityMask);
                
                Console.WriteLine($"处理器亲和性设置为使用所有 {coreCount} 个核心");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置处理器亲和性失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 监控CPU使用率
        /// </summary>
        public static void StartCpuMonitoring()
        {
            var cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
            
            // 第一次调用返回0，需要等待
            cpuCounter.NextValue();
            Thread.Sleep(1000);
            
            Timer cpuTimer = new Timer(_ =>
            {
                try
                {
                    float cpuUsage = cpuCounter.NextValue();
                    if (cpuUsage < 50) // CPU使用率低于50%时提醒
                    {
                        Console.WriteLine($"[CPU监控] 当前CPU使用率: {cpuUsage:F1}% (较低)");
                    }
                    else if (cpuUsage > 90) // CPU使用率高于90%时警告
                    {
                        Console.WriteLine($"[CPU监控] 当前CPU使用率: {cpuUsage:F1}% (过高)");
                    }
                }
                catch
                {
                    // 忽略监控错误
                }
            }, null, TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(10));
        }
        
        /// <summary>
        /// 获取系统性能信息
        /// </summary>
        public static void DisplaySystemInfo()
        {
            Console.WriteLine("=== 系统性能信息 ===");
            Console.WriteLine($"CPU核心数: {Environment.ProcessorCount}");
            Console.WriteLine($"系统内存: {GC.GetTotalMemory(false) / 1024 / 1024} MB (托管)");
            
            try
            {
                using (var process = Process.GetCurrentProcess())
                {
                    Console.WriteLine($"工作集内存: {process.WorkingSet64 / 1024 / 1024} MB");
                    Console.WriteLine($"进程优先级: {process.PriorityClass}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取进程信息失败: {ex.Message}");
            }
            
            Console.WriteLine($"服务器GC: {GCSettings.IsServerGC}");
            Console.WriteLine($"GC延迟模式: {GCSettings.LatencyMode}");
            
            int workerThreads, completionPortThreads;
            ThreadPool.GetMinThreads(out workerThreads, out completionPortThreads);
            Console.WriteLine($"线程池最小线程: 工作线程={workerThreads}, 完成端口线程={completionPortThreads}");
            
            ThreadPool.GetMaxThreads(out workerThreads, out completionPortThreads);
            Console.WriteLine($"线程池最大线程: 工作线程={workerThreads}, 完成端口线程={completionPortThreads}");
        }
        
        /// <summary>
        /// 恢复默认设置
        /// </summary>
        public static void RestoreDefaultSettings()
        {
            try
            {
                // 恢复进程优先级
                var currentProcess = Process.GetCurrentProcess();
                currentProcess.PriorityClass = ProcessPriorityClass.Normal;
                
                // 恢复GC设置
                GCSettings.LatencyMode = GCLatencyMode.Interactive;
                
                Console.WriteLine("已恢复默认CPU设置");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"恢复默认设置失败: {ex.Message}");
            }
        }
    }
}
