using System;
using System.Diagnostics;
using System.Runtime.InteropServices;

namespace OcrLiteLib
{
    /// <summary>
    /// 非托管内存管理器，用于监控和释放非托管内存
    /// </summary>
    public static class UnmanagedMemoryManager
    {
        [DllImport("kernel32.dll")]
        private static extern bool SetProcessWorkingSetSize(IntPtr hProcess, IntPtr dwMinimumWorkingSetSize, IntPtr dwMaximumWorkingSetSize);

        [DllImport("kernel32.dll")]
        private static extern IntPtr GetCurrentProcess();

        [DllImport("kernel32.dll")]
        private static extern bool EmptyWorkingSet(IntPtr hProcess);

        /// <summary>
        /// 获取当前进程的内存使用情况
        /// </summary>
        public static MemoryInfo GetMemoryInfo()
        {
            var info = new MemoryInfo();
            
            try
            {
                using (var process = Process.GetCurrentProcess())
                {
                    info.WorkingSet = process.WorkingSet64;
                    info.PrivateMemory = process.PrivateMemorySize64;
                    info.VirtualMemory = process.VirtualMemorySize64;
                    info.ManagedMemory = GC.GetTotalMemory(false);
                    info.UnmanagedMemory = info.WorkingSet - info.ManagedMemory;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取内存信息失败: {ex.Message}");
            }
            
            return info;
        }

        /// <summary>
        /// 强制释放非托管内存
        /// </summary>
        public static void ForceReleaseUnmanagedMemory()
        {
            try
            {
                var beforeInfo = GetMemoryInfo();
                Console.WriteLine($"释放前: 工作集={FormatBytes(beforeInfo.WorkingSet)}, 非托管={FormatBytes(beforeInfo.UnmanagedMemory)}");

                // 1. 先进行托管内存GC
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                // 2. 尝试释放工作集内存
                IntPtr currentProcess = GetCurrentProcess();
                
                // 方法1: 设置工作集大小为-1，提示系统回收内存
                SetProcessWorkingSetSize(currentProcess, new IntPtr(-1), new IntPtr(-1));
                
                // 方法2: 清空工作集
                EmptyWorkingSet(currentProcess);

                // 3. 再次GC确保清理完成
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var afterInfo = GetMemoryInfo();
                Console.WriteLine($"释放后: 工作集={FormatBytes(afterInfo.WorkingSet)}, 非托管={FormatBytes(afterInfo.UnmanagedMemory)}");
                Console.WriteLine($"释放了: {FormatBytes(beforeInfo.WorkingSet - afterInfo.WorkingSet)}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"强制释放非托管内存失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 智能内存管理：根据内存使用情况决定清理策略
        /// </summary>
        /// <param name="thresholdMB">阈值（MB）</param>
        public static void SmartMemoryCleanup(int thresholdMB = 1500)
        {
            var info = GetMemoryInfo();
            long thresholdBytes = (long)thresholdMB * 1024 * 1024;

            if (info.WorkingSet > thresholdBytes)
            {
                Console.WriteLine($"内存使用过高 ({FormatBytes(info.WorkingSet)})，开始智能清理...");
                
                // 清理共享缓冲区
                OcrUtils.ClearSharedBuffers();
                
                // 强制释放非托管内存
                ForceReleaseUnmanagedMemory();
            }
            else if (info.UnmanagedMemory > thresholdBytes / 2)
            {
                Console.WriteLine($"非托管内存较高 ({FormatBytes(info.UnmanagedMemory)})，进行轻度清理...");
                
                // 只进行托管内存GC
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }
        }

        /// <summary>
        /// 减少GC频率的设置
        /// </summary>
        public static void OptimizeGCSettings()
        {
            try
            {
                // 设置为服务器GC模式（如果可用）
                System.Runtime.GCSettings.LatencyMode = System.Runtime.GCLatencyMode.Batch;
                
                // 设置大对象堆压缩模式
                System.Runtime.GCSettings.LargeObjectHeapCompactionMode = System.Runtime.GCLargeObjectHeapCompactionMode.CompactOnce;
                
                Console.WriteLine("GC设置已优化，减少频繁回收");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GC设置优化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 恢复默认GC设置
        /// </summary>
        public static void RestoreGCSettings()
        {
            try
            {
                System.Runtime.GCSettings.LatencyMode = System.Runtime.GCLatencyMode.Interactive;
                Console.WriteLine("GC设置已恢复为默认");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GC设置恢复失败: {ex.Message}");
            }
        }

        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }
    }

    /// <summary>
    /// 内存信息结构
    /// </summary>
    public class MemoryInfo
    {
        public long WorkingSet { get; set; }
        public long PrivateMemory { get; set; }
        public long VirtualMemory { get; set; }
        public long ManagedMemory { get; set; }
        public long UnmanagedMemory { get; set; }

        public override string ToString()
        {
            return $"工作集: {FormatBytes(WorkingSet)}, " +
                   $"托管: {FormatBytes(ManagedMemory)}, " +
                   $"非托管: {FormatBytes(UnmanagedMemory)}";
        }

        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }
    }

    /// <summary>
    /// 自动非托管内存管理作用域
    /// </summary>
    public class UnmanagedMemoryScope : IDisposable
    {
        private readonly string _scopeName;
        private readonly MemoryInfo _startInfo;
        private readonly bool _forceCleanup;

        public UnmanagedMemoryScope(string scopeName, bool forceCleanup = false)
        {
            _scopeName = scopeName;
            _forceCleanup = forceCleanup;
            _startInfo = UnmanagedMemoryManager.GetMemoryInfo();
            
            Console.WriteLine($"[UnmanagedScope] 进入 {_scopeName} - {_startInfo}");
        }

        public void Dispose()
        {
            var endInfo = UnmanagedMemoryManager.GetMemoryInfo();
            long workingSetDiff = endInfo.WorkingSet - _startInfo.WorkingSet;
            long unmanagedDiff = endInfo.UnmanagedMemory - _startInfo.UnmanagedMemory;
            
            Console.WriteLine($"[UnmanagedScope] 退出 {_scopeName} - {endInfo}");
            Console.WriteLine($"  工作集变化: {FormatBytes(workingSetDiff)}, 非托管变化: {FormatBytes(unmanagedDiff)}");
            
            if (_forceCleanup || workingSetDiff > 100 * 1024 * 1024) // 超过100MB变化时强制清理
            {
                UnmanagedMemoryManager.SmartMemoryCleanup();
            }
        }

        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = Math.Abs(bytes);
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            string sign = bytes < 0 ? "-" : "+";
            return $"{sign}{number:n1} {suffixes[counter]}";
        }
    }
}
