using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using OcrLiteLib;

namespace OcrConsoleApp
{
    /// <summary>
    /// 并行处理演示
    /// </summary>
    public static class ParallelDemo
    {
        /// <summary>
        /// 运行并行vs串行对比测试
        /// </summary>
        public static async Task RunComparisonTest()
        {
            Console.WriteLine("=== 并行 vs 串行 对比测试 ===");
            
            // 模型路径
            string modelsDir = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrOnnxForm\\models";
            string detPath = Path.Combine(modelsDir, "ch_PP-OCRv4_det_infer.onnx");
            string clsPath = Path.Combine(modelsDir, "ch_ppocr_mobile_v2.0_cls_infer.onnx");
            string recPath = Path.Combine(modelsDir, "ch_PP-OCRv4_rec_infer.onnx");
            string keysPath = Path.Combine(modelsDir, "ppocr_keys_v1.txt");
            
            string imagesDir = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrConsoleApp\\images";
            
            if (!Directory.Exists(imagesDir))
            {
                Console.WriteLine($"图片目录不存在: {imagesDir}");
                return;
            }
            
            var imageFiles = Directory.GetFiles(imagesDir, "*.jpg")
                .Concat(Directory.GetFiles(imagesDir, "*.png"))
                .Concat(Directory.GetFiles(imagesDir, "*.jpeg"))
                .Take(10) // 只测试前10张图片
                .ToArray();
                
            if (imageFiles.Length == 0)
            {
                Console.WriteLine("未找到测试图片");
                return;
            }
            
            Console.WriteLine($"找到 {imageFiles.Length} 张测试图片");
            
            // 串行测试
            Console.WriteLine("\n--- 串行处理测试 ---");
            var serialTime = await RunSerialTest(detPath, clsPath, recPath, keysPath, imageFiles);
            
            // 并行测试
            Console.WriteLine("\n--- 并行处理测试 ---");
            var parallelTime = await RunParallelTest(detPath, clsPath, recPath, keysPath, imageFiles);
            
            // 对比结果
            Console.WriteLine("\n=== 对比结果 ===");
            Console.WriteLine($"串行处理时间: {serialTime}ms ({serialTime / 1000.0:F1}s)");
            Console.WriteLine($"并行处理时间: {parallelTime}ms ({parallelTime / 1000.0:F1}s)");
            Console.WriteLine($"速度提升: {(double)serialTime / parallelTime:F2}x");
            Console.WriteLine($"效率提升: {(1 - (double)parallelTime / serialTime) * 100:F1}%");
        }
        
        /// <summary>
        /// 串行处理测试
        /// </summary>
        private static async Task<long> RunSerialTest(string detPath, string clsPath, string recPath, string keysPath, string[] imageFiles)
        {
            var stopwatch = Stopwatch.StartNew();
            int successCount = 0;
            
            using (var ocrService = new OcrServiceOptimized())
            {
                ocrService.InitModels(detPath, clsPath, recPath, keysPath, 1);
                
                foreach (var imagePath in imageFiles)
                {
                    try
                    {
                        var textBlocks = ocrService.DetectTextBlocks(imagePath);
                        successCount++;
                        Console.WriteLine($"  串行处理: {Path.GetFileName(imagePath)} - {textBlocks.Count} 个文本块");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"  串行处理失败: {Path.GetFileName(imagePath)} - {ex.Message}");
                    }
                }
            }
            
            stopwatch.Stop();
            Console.WriteLine($"串行处理完成: {successCount}/{imageFiles.Length} 成功");
            return stopwatch.ElapsedMilliseconds;
        }
        
        /// <summary>
        /// 并行处理测试
        /// </summary>
        private static async Task<long> RunParallelTest(string detPath, string clsPath, string recPath, string keysPath, string[] imageFiles)
        {
            var stopwatch = Stopwatch.StartNew();
            
            using (var processor = new ParallelBatchProcessor(detPath, clsPath, recPath, keysPath, 1, Environment.ProcessorCount))
            {
                await processor.WarmupAsync();
                
                var results = await processor.ProcessFilesAsync(imageFiles);
                
                var successCount = results.Count(r => r.IsSuccess);
                Console.WriteLine($"并行处理完成: {successCount}/{imageFiles.Length} 成功");
                
                foreach (var result in results)
                {
                    if (result.IsSuccess)
                    {
                        Console.WriteLine($"  并行处理: {Path.GetFileName(result.ImagePath)} - {result.TextBlocks.Count} 个文本块 (线程{result.ThreadId})");
                    }
                    else
                    {
                        Console.WriteLine($"  并行处理失败: {Path.GetFileName(result.ImagePath)} - {result.Error?.Message}");
                    }
                }
            }
            
            stopwatch.Stop();
            return stopwatch.ElapsedMilliseconds;
        }
        
        /// <summary>
        /// 简单的并行处理演示
        /// </summary>
        public static async Task RunSimpleDemo()
        {
            Console.WriteLine("=== 简单并行处理演示 ===");
            
            // 模型路径
            string modelsDir = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrOnnxForm\\models";
            string detPath = Path.Combine(modelsDir, "ch_PP-OCRv4_det_infer.onnx");
            string clsPath = Path.Combine(modelsDir, "ch_ppocr_mobile_v2.0_cls_infer.onnx");
            string recPath = Path.Combine(modelsDir, "ch_PP-OCRv4_rec_infer.onnx");
            string keysPath = Path.Combine(modelsDir, "ppocr_keys_v1.txt");
            
            string imagesDir = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrConsoleApp\\images";
            
            try
            {
                using (var processor = new ParallelBatchProcessor(detPath, clsPath, recPath, keysPath, 1, 4))
                {
                    Console.WriteLine("预热服务池...");
                    await processor.WarmupAsync();
                    
                    Console.WriteLine("开始并行处理...");
                    var progress = new Progress<ProcessProgress>(p =>
                    {
                        Console.WriteLine($"进度: {p.ProgressPercentage:F1}% - {p.CurrentFile}");
                    });
                    
                    var results = await processor.ProcessDirectoryAsync(imagesDir, progress: progress);
                    
                    Console.WriteLine($"\n处理完成！");
                    Console.WriteLine($"成功: {results.Count(r => r.IsSuccess)} 张");
                    Console.WriteLine($"失败: {results.Count(r => !r.IsSuccess)} 张");
                    
                    if (results.Any(r => r.IsSuccess))
                    {
                        var avgTime = results.Where(r => r.IsSuccess).Average(r => r.ProcessingTimeMs);
                        Console.WriteLine($"平均处理时间: {avgTime:F1}ms/张");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"演示失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 压力测试
        /// </summary>
        public static async Task RunStressTest()
        {
            Console.WriteLine("=== 并行处理压力测试 ===");
            
            // 模型路径
            string modelsDir = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrOnnxForm\\models";
            string detPath = Path.Combine(modelsDir, "ch_PP-OCRv4_det_infer.onnx");
            string clsPath = Path.Combine(modelsDir, "ch_ppocr_mobile_v2.0_cls_infer.onnx");
            string recPath = Path.Combine(modelsDir, "ch_PP-OCRv4_rec_infer.onnx");
            string keysPath = Path.Combine(modelsDir, "ppocr_keys_v1.txt");
            
            string imagesDir = "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrConsoleApp\\images";
            
            var imageFiles = Directory.GetFiles(imagesDir, "*.jpg")
                .Concat(Directory.GetFiles(imagesDir, "*.png"))
                .Take(1) // 只用一张图片
                .ToArray();
                
            if (imageFiles.Length == 0)
            {
                Console.WriteLine("未找到测试图片");
                return;
            }
            
            // 复制图片文件名，模拟大量图片
            var duplicatedFiles = Enumerable.Range(0, 50)
                .Select(i => imageFiles[0])
                .ToArray();
                
            Console.WriteLine($"压力测试: 处理 {duplicatedFiles.Length} 张图片（实际是同一张图片的重复）");
            
            try
            {
                using (var processor = new ParallelBatchProcessor(detPath, clsPath, recPath, keysPath, 1, Environment.ProcessorCount))
                {
                    await processor.WarmupAsync();
                    
                    var stopwatch = Stopwatch.StartNew();
                    var results = await processor.ProcessFilesAsync(duplicatedFiles);
                    stopwatch.Stop();
                    
                    var successCount = results.Count(r => r.IsSuccess);
                    Console.WriteLine($"\n压力测试完成！");
                    Console.WriteLine($"总耗时: {stopwatch.ElapsedMilliseconds}ms ({stopwatch.ElapsedMilliseconds / 1000.0:F1}s)");
                    Console.WriteLine($"成功: {successCount}/{duplicatedFiles.Length}");
                    Console.WriteLine($"吞吐量: {successCount * 1000.0 / stopwatch.ElapsedMilliseconds:F1} 张/秒");
                    
                    if (results.Any(r => r.IsSuccess))
                    {
                        var avgTime = results.Where(r => r.IsSuccess).Average(r => r.ProcessingTimeMs);
                        Console.WriteLine($"平均处理时间: {avgTime:F1}ms/张");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"压力测试失败: {ex.Message}");
            }
        }
    }
}
