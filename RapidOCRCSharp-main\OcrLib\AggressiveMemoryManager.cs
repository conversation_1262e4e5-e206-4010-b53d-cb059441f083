using System;
using System.Runtime;
using System.Runtime.InteropServices;

namespace OcrLiteLib
{
    /// <summary>
    /// 激进的内存管理器，用于强制释放内存
    /// </summary>
    public static class AggressiveMemoryManager
    {
        [DllImport("kernel32.dll")]
        private static extern bool SetProcessWorkingSetSize(IntPtr hProcess, IntPtr dwMinimumWorkingSetSize, IntPtr dwMaximumWorkingSetSize);

        [DllImport("kernel32.dll")]
        private static extern IntPtr GetCurrentProcess();

        /// <summary>
        /// 强制释放内存到操作系统
        /// </summary>
        public static void ForceMemoryRelease()
        {
            // 1. 强制垃圾回收
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            
            // 2. 压缩大对象堆
            GCSettings.LargeObjectHeapCompactionMode = GCLargeObjectHeapCompactionMode.CompactOnce;
            GC.Collect();
            
            // 3. 强制释放工作集内存到操作系统
            try
            {
                IntPtr currentProcess = GetCurrentProcess();
                SetProcessWorkingSetSize(currentProcess, new IntPtr(-1), new IntPtr(-1));
            }
            catch
            {
                // 忽略权限错误
            }
        }

        /// <summary>
        /// 获取当前进程的内存使用情况
        /// </summary>
        /// <returns>内存使用情况（字节）</returns>
        public static long GetCurrentMemoryUsage()
        {
            return GC.GetTotalMemory(false);
        }

        /// <summary>
        /// 获取工作集内存大小
        /// </summary>
        /// <returns>工作集内存大小（字节）</returns>
        public static long GetWorkingSetMemory()
        {
            try
            {
                using (var process = System.Diagnostics.Process.GetCurrentProcess())
                {
                    return process.WorkingSet64;
                }
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 检查内存使用是否过高
        /// </summary>
        /// <param name="thresholdMB">阈值（MB）</param>
        /// <returns>是否超过阈值</returns>
        public static bool IsMemoryUsageHigh(int thresholdMB = 1000)
        {
            long currentMemory = GetWorkingSetMemory();
            long thresholdBytes = (long)thresholdMB * 1024 * 1024;
            return currentMemory > thresholdBytes;
        }

        /// <summary>
        /// 智能内存管理：根据当前内存使用情况决定是否进行激进清理
        /// </summary>
        public static void SmartMemoryManagement()
        {
            long beforeMemory = GetWorkingSetMemory();
            
            if (IsMemoryUsageHigh(800)) // 超过800MB时进行激进清理
            {
                Console.WriteLine($"内存使用过高 ({FormatBytes(beforeMemory)})，开始激进清理...");
                ForceMemoryRelease();
                
                long afterMemory = GetWorkingSetMemory();
                long freed = beforeMemory - afterMemory;
                Console.WriteLine($"内存清理完成，释放了 {FormatBytes(freed)}，当前内存: {FormatBytes(afterMemory)}");
            }
            else if (IsMemoryUsageHigh(500)) // 超过500MB时进行常规清理
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }
        }

        /// <summary>
        /// 格式化字节数为可读字符串
        /// </summary>
        /// <param name="bytes">字节数</param>
        /// <returns>格式化后的字符串</returns>
        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }

        /// <summary>
        /// 设置垃圾回收模式为服务器模式（更激进的内存回收）
        /// </summary>
        public static void SetAggressiveGCMode()
        {
            // 设置为服务器垃圾回收模式
            GCSettings.LatencyMode = GCLatencyMode.Batch;
        }

        /// <summary>
        /// 恢复默认垃圾回收模式
        /// </summary>
        public static void RestoreDefaultGCMode()
        {
            GCSettings.LatencyMode = GCLatencyMode.Interactive;
        }
    }

    /// <summary>
    /// 自动内存管理作用域
    /// </summary>
    public class AutoMemoryScope : IDisposable
    {
        private readonly string _scopeName;
        private readonly long _startMemory;
        private readonly bool _forceCleanup;

        public AutoMemoryScope(string scopeName, bool forceCleanup = true)
        {
            _scopeName = scopeName;
            _forceCleanup = forceCleanup;
            _startMemory = AggressiveMemoryManager.GetWorkingSetMemory();
            
            Console.WriteLine($"[AutoMemory] 进入 {_scopeName} - 内存: {FormatBytes(_startMemory)}");
        }

        public void Dispose()
        {
            if (_forceCleanup)
            {
                AggressiveMemoryManager.SmartMemoryManagement();
            }
            
            long endMemory = AggressiveMemoryManager.GetWorkingSetMemory();
            long memoryDiff = endMemory - _startMemory;
            
            Console.WriteLine($"[AutoMemory] 退出 {_scopeName} - " +
                            $"内存: {FormatBytes(endMemory)}, " +
                            $"变化: {FormatBytes(memoryDiff)}");
        }

        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }
    }
}
