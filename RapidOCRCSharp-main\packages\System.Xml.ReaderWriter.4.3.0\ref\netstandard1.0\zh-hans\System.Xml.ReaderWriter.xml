﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.ReaderWriter</name>
  </assembly>
  <members>
    <member name="T:System.Xml.ConformanceLevel">
      <summary>指定 <see cref="T:System.Xml.XmlReader" /> 和 <see cref="T:System.Xml.XmlWriter" /> 对象执行的输入或输出检查的量。</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Auto">
      <summary>
        <see cref="T:System.Xml.XmlReader" /> 或 <see cref="T:System.Xml.XmlWriter" /> 对象自动检测是否应该执行文档级别或片段级别检查，并执行相应的检查。如果你正在包装另一个 <see cref="T:System.Xml.XmlReader" /> 或 <see cref="T:System.Xml.XmlWriter" /> 对象，外层对象不进行任何附加一致性检查。一致性检查只针对基础对象。有关如何确定符合性级别，请参见 <see cref="P:System.Xml.XmlReaderSettings.ConformanceLevel" /> 和 <see cref="P:System.Xml.XmlWriterSettings.ConformanceLevel" /> 属性。</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Document">
      <summary>按 W3C 定义，XML 数据符合格式良好的 XML 1.0 文档 的规则。</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Fragment">
      <summary>按 W3C 定义，XML 数据为 格式良好的 XML 片段。</summary>
    </member>
    <member name="T:System.Xml.DtdProcessing">
      <summary>指定用于处理 DTD 的选项。<see cref="T:System.Xml.DtdProcessing" /> 枚举由 <see cref="T:System.Xml.XmlReaderSettings" /> 类使用。</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Ignore">
      <summary>将导致忽略 DOCTYPE 元素。将不发生任何 DTD 处理。</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Prohibit">
      <summary>指定在遇到 DTD 时将引发 <see cref="T:System.Xml.XmlException" />，同时有消息指示禁用 DTD。这是默认行为。</summary>
    </member>
    <member name="T:System.Xml.IXmlLineInfo">
      <summary>提供一个接口，使类可以返回行和位置信息。</summary>
    </member>
    <member name="M:System.Xml.IXmlLineInfo.HasLineInfo">
      <summary>获取一个值，该值指示该类是否可返回行信息。</summary>
      <returns>如果可以提供 <see cref="P:System.Xml.IXmlLineInfo.LineNumber" /> 和 <see cref="P:System.Xml.IXmlLineInfo.LinePosition" />，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LineNumber">
      <summary>获取当前行号。</summary>
      <returns>当前行号；如果没有行信息可用（例如 <see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" /> 返回 false），则为 0。</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LinePosition">
      <summary>获取当前行位置。</summary>
      <returns>当前行位置；如果没有行信息可用（例如 <see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" /> 返回 false），则为 0。</returns>
    </member>
    <member name="T:System.Xml.IXmlNamespaceResolver">
      <summary>提供对一组前缀和命名空间映射的只读访问。</summary>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>获取当前在范围内的已定义前缀/命名空间映射的集合。</summary>
      <returns>一个 <see cref="T:System.Collections.IDictionary" />，包含当前在范围内的命名空间。</returns>
      <param name="scope">一个 <see cref="T:System.Xml.XmlNamespaceScope" /> 值，指定要返回的命名空间节点的类型。</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupNamespace(System.String)">
      <summary>获取映射到指定前缀的命名空间 URI。</summary>
      <returns>映射到前缀的命名空间 URI；如果前缀未映射到命名空间 URI，则为 null。</returns>
      <param name="prefix">要查找其命名空间 URI 的前缀。</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupPrefix(System.String)">
      <summary>获取映射到指定命名空间 URI 的前缀。</summary>
      <returns>映射到命名空间 URI 的前缀；如果命名空间 URI 未映射到前缀，则为 null。</returns>
      <param name="namespaceName">要查找其前缀的命名空间 URI。</param>
    </member>
    <member name="T:System.Xml.NamespaceHandling">
      <summary>指定是否在 <see cref="T:System.Xml.XmlWriter" /> 中移除重复的命名空间声明。</summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.Default">
      <summary>指定将不移除重复的命名空间声明。</summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.OmitDuplicates">
      <summary>指定将移除重复的命名空间声明。对于要移除的重复命名空间，前缀和命名空间必须匹配。</summary>
    </member>
    <member name="T:System.Xml.NameTable">
      <summary>实现单线程 <see cref="T:System.Xml.XmlNameTable" />。</summary>
    </member>
    <member name="M:System.Xml.NameTable.#ctor">
      <summary>初始化 NameTable 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>将指定的字符串原子化，并将其添加到 NameTable。</summary>
      <returns>原子化字符串；如果 NameTable 中已存在字符串，则为现有字符串。如果 <paramref name="len" /> 为零，则返回 String.Empty。</returns>
      <param name="key">包含要添加字符串的字符数组。</param>
      <param name="start">数组中指定字符串第一个字符的从零开始的索引。</param>
      <param name="len">字符串中的字符数。</param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />- 或 -<paramref name="start" /> &gt;= <paramref name="key" />.Length- 或 -<paramref name="len" /> &gt;= <paramref name="key" />.Length如果 <paramref name="len" /> =0，则上述条件不会导致引发异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0。</exception>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.String)">
      <summary>将指定的字符串原子化，并将其添加到 NameTable。</summary>
      <returns>原子化字符串；如果 NameTable 中已存在字符串，则为现有字符串。</returns>
      <param name="key">要添加的字符串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> 为 null。</exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>获取包含相同字符（与给定数组中指定范围的字符相同）的原子化字符串。</summary>
      <returns>原子化字符串；如果字符串尚未原子化，则为 null。如果 <paramref name="len" /> 为零，则返回 String.Empty。</returns>
      <param name="key">包含要查找的名称的字符数组。</param>
      <param name="start">数组中指定名称第一个字符的从零开始的索引。</param>
      <param name="len">名称中的字符数。</param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />- 或 -<paramref name="start" /> &gt;= <paramref name="key" />.Length- 或 -<paramref name="len" /> &gt;= <paramref name="key" />.Length如果 <paramref name="len" /> =0，则上述条件不会导致引发异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0。</exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.String)">
      <summary>获取具有指定值的原子化字符串。</summary>
      <returns>原子化字符串对象；如果字符串尚未原子化，则为 null。</returns>
      <param name="value">要查找的名称。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 为 null。</exception>
    </member>
    <member name="T:System.Xml.NewLineHandling">
      <summary>指定如何处理分行符。</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Entitize">
      <summary>新行字符已实体化。当通过某个正常化 <see cref="T:System.Xml.XmlReader" /> 来读取输出时，此设置将保留所有字符。</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.None">
      <summary>新行字符未更改。输出与输入一样。</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Replace">
      <summary>替换新行字符才能与 <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" /> 属性中指定的字符匹配。</summary>
    </member>
    <member name="T:System.Xml.ReadState">
      <summary>指定读取器的状态。</summary>
    </member>
    <member name="F:System.Xml.ReadState.Closed">
      <summary>已调用 <see cref="M:System.Xml.XmlReader.Close" /> 方法。</summary>
    </member>
    <member name="F:System.Xml.ReadState.EndOfFile">
      <summary>已成功到达文件结尾。</summary>
    </member>
    <member name="F:System.Xml.ReadState.Error">
      <summary>出现错误，阻止读取操作继续进行。</summary>
    </member>
    <member name="F:System.Xml.ReadState.Initial">
      <summary>未调用 Read 方法。</summary>
    </member>
    <member name="F:System.Xml.ReadState.Interactive">
      <summary>已调用 Read 方法。可能对读取器调用了其他方法。</summary>
    </member>
    <member name="T:System.Xml.WriteState">
      <summary>指定 <see cref="T:System.Xml.XmlWriter" /> 的状态。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Attribute">
      <summary>指示正在写入特性值。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Closed">
      <summary>指示已调用 <see cref="M:System.Xml.XmlWriter.Close" /> 方法。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Content">
      <summary>指示正在写入元素内容。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Element">
      <summary>指示正在写入元素开始标记。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Error">
      <summary>已引发异常，使 <see cref="T:System.Xml.XmlWriter" /> 仍处于无效状态。可以调用 <see cref="M:System.Xml.XmlWriter.Close" /> 方法来将 <see cref="T:System.Xml.XmlWriter" /> 置于 <see cref="F:System.Xml.WriteState.Closed" /> 状态。任何其他 <see cref="T:System.Xml.XmlWriter" /> 方法调用都将导致 <see cref="T:System.InvalidOperationException" />。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Prolog">
      <summary>指示正在写入 Prolog。</summary>
    </member>
    <member name="F:System.Xml.WriteState.Start">
      <summary>指示尚未调用 Write 方法。</summary>
    </member>
    <member name="T:System.Xml.XmlConvert">
      <summary>对 XML 名称进行编码和解码，并提供方法在公共语言运行时类型和 XML 架构定义语言 (XSD) 类型之间进行转换。转换数据类型时，返回的值是独立于区域设置的。</summary>
    </member>
    <member name="M:System.Xml.XmlConvert.DecodeName(System.String)">
      <summary>对名称进行解码。该方法完成 <see cref="M:System.Xml.XmlConvert.EncodeName(System.String)" /> 和 <see cref="M:System.Xml.XmlConvert.EncodeLocalName(System.String)" /> 方法的反向操作。</summary>
      <returns>解码的名称。</returns>
      <param name="name">要转换的名称。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeLocalName(System.String)">
      <summary>将名称转换为有效的 XML 本地名称。</summary>
      <returns>已编码的名称。</returns>
      <param name="name">要编码的名称。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeName(System.String)">
      <summary>将名称转换为有效的 XML 名称。</summary>
      <returns>返回名称，任何无效的字符都由转义字符串替换。</returns>
      <param name="name">要转换的名称。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeNmToken(System.String)">
      <summary>根据 XML 规范验证该名称是否有效。</summary>
      <returns>已编码的名称。</returns>
      <param name="name">要编码的名称。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToBoolean(System.String)">
      <summary>将 <see cref="T:System.String" /> 转换为等效的 <see cref="T:System.Boolean" />。</summary>
      <returns>一个 Boolean 值，即 true 或 false。</returns>
      <param name="s">要转换的字符串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> does not represent a Boolean value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToByte(System.String)">
      <summary>将 <see cref="T:System.String" /> 转换为等效的 <see cref="T:System.Byte" />。</summary>
      <returns>与该字符串等效的 Byte。</returns>
      <param name="s">要转换的字符串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToChar(System.String)">
      <summary>将 <see cref="T:System.String" /> 转换为等效的 <see cref="T:System.Char" />。</summary>
      <returns>代表单个字符的 Char。</returns>
      <param name="s">包含所要转换的单个字符的字符串。</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="s" /> parameter is null. </exception>
      <exception cref="T:System.FormatException">The <paramref name="s" /> parameter contains more than one character. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTime(System.String,System.Xml.XmlDateTimeSerializationMode)">
      <summary>使用指定的 <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> 将 <see cref="T:System.String" /> 转换为 <see cref="T:System.DateTime" /></summary>
      <returns>
        <see cref="T:System.DateTime" /> 的等效 <see cref="T:System.String" />。</returns>
      <param name="s">要转换的 <see cref="T:System.String" /> 值。</param>
      <param name="dateTimeOption">
        <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> 值之一，用于指定日期是应该转换为本地时间，还是应该保留为协调通用时间 (UTC)（如果它为 UTC 日期）。</param>
      <exception cref="T:System.NullReferenceException">
        <paramref name="s" /> is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="dateTimeOption" /> value is null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is an empty string or is not in a valid format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String)">
      <summary>将提供的 <see cref="T:System.String" /> 转换为等效的 <see cref="T:System.DateTimeOffset" />。</summary>
      <returns>与提供的字符串等效的 <see cref="T:System.DateTimeOffset" />。</returns>
      <param name="s">要转换的字符串。“注意”   该字符串必须符合 XML DateTime 类型的 W3C 建议的子集。更多信息，请参见 http://www.w3.org/TR/xmlschema-2/#dateTime。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The argument passed to this method is outside the range of allowable values.For information about allowable values, see <see cref="T:System.DateTimeOffset" />.</exception>
      <exception cref="T:System.FormatException">The argument passed to this method does not conform to a subset of the W3C Recommendations for the XML dateTime type.For more information see http://www.w3.org/TR/xmlschema-2/#dateTime.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String)">
      <summary>将提供的 <see cref="T:System.String" /> 转换为等效的 <see cref="T:System.DateTimeOffset" />。</summary>
      <returns>与提供的字符串等效的 <see cref="T:System.DateTimeOffset" />。</returns>
      <param name="s">要转换的字符串。</param>
      <param name="format">从中转换 <paramref name="s" /> 的格式。该格式参数可以是 XML DateTime 类型的 W3C 建议的任何子集。（有关更多信息，请参见 http://www.w3.org/TR/xmlschema-2/#dateTime。） 根据此格式验证字符串 <paramref name="s" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> or <paramref name="format" /> is an empty string or is not in the specified format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String[])">
      <summary>将提供的 <see cref="T:System.String" /> 转换为等效的 <see cref="T:System.DateTimeOffset" />。</summary>
      <returns>与提供的字符串等效的 <see cref="T:System.DateTimeOffset" />。</returns>
      <param name="s">要转换的字符串。</param>
      <param name="formats">可以转换 <paramref name="s" /> 的格式数组。<paramref name="formats" /> 中的每个格式均可以是 XML DateTime 类型的 W3C 建议的任何子集。（有关更多信息，请参见 http://www.w3.org/TR/xmlschema-2/#dateTime。） 将根据这些格式中的一个格式验证字符串 <paramref name="s" />。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDecimal(System.String)">
      <summary>将 <see cref="T:System.String" /> 转换为等效的 <see cref="T:System.Decimal" />。</summary>
      <returns>与该字符串等效的 Decimal。</returns>
      <param name="s">要转换的字符串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Decimal.MinValue" /> or greater than <see cref="F:System.Decimal.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDouble(System.String)">
      <summary>将 <see cref="T:System.String" /> 转换为等效的 <see cref="T:System.Double" />。</summary>
      <returns>与该字符串等效的 Double。</returns>
      <param name="s">要转换的字符串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Double.MinValue" /> or greater than <see cref="F:System.Double.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToGuid(System.String)">
      <summary>将 <see cref="T:System.String" /> 转换为等效的 <see cref="T:System.Guid" />。</summary>
      <returns>与该字符串等效的 Guid。</returns>
      <param name="s">要转换的字符串。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt16(System.String)">
      <summary>将 <see cref="T:System.String" /> 转换为等效的 <see cref="T:System.Int16" />。</summary>
      <returns>与该字符串等效的 Int16。</returns>
      <param name="s">要转换的字符串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int16.MinValue" /> or greater than <see cref="F:System.Int16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt32(System.String)">
      <summary>将 <see cref="T:System.String" /> 转换为等效的 <see cref="T:System.Int32" />。</summary>
      <returns>与该字符串等效的 Int32。</returns>
      <param name="s">要转换的字符串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int32.MinValue" /> or greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt64(System.String)">
      <summary>将 <see cref="T:System.String" /> 转换为等效的 <see cref="T:System.Int64" />。</summary>
      <returns>与该字符串等效的 Int64。</returns>
      <param name="s">要转换的字符串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int64.MinValue" /> or greater than <see cref="F:System.Int64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSByte(System.String)">
      <summary>将 <see cref="T:System.String" /> 转换为等效的 <see cref="T:System.SByte" />。</summary>
      <returns>与该字符串等效的 SByte。</returns>
      <param name="s">要转换的字符串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.SByte.MinValue" /> or greater than <see cref="F:System.SByte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSingle(System.String)">
      <summary>将 <see cref="T:System.String" /> 转换为等效的 <see cref="T:System.Single" />。</summary>
      <returns>与该字符串等效的 Single。</returns>
      <param name="s">要转换的字符串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Single.MinValue" /> or greater than <see cref="F:System.Single.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Boolean)">
      <summary>将 <see cref="T:System.Boolean" /> 转换为 <see cref="T:System.String" />。</summary>
      <returns>Boolean 的字符串表示形式，即“true”或“false”。</returns>
      <param name="value">要转换的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Byte)">
      <summary>将 <see cref="T:System.Byte" /> 转换为 <see cref="T:System.String" />。</summary>
      <returns>Byte 的字符串表示形式。</returns>
      <param name="value">要转换的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Char)">
      <summary>将 <see cref="T:System.Char" /> 转换为 <see cref="T:System.String" />。</summary>
      <returns>Char 的字符串表示形式。</returns>
      <param name="value">要转换的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTime,System.Xml.XmlDateTimeSerializationMode)">
      <summary>使用指定的 <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> 将 <see cref="T:System.DateTime" /> 转换为 <see cref="T:System.String" />。</summary>
      <returns>
        <see cref="T:System.String" /> 的等效 <see cref="T:System.DateTime" />。</returns>
      <param name="value">要转换的 <see cref="T:System.DateTime" /> 值。</param>
      <param name="dateTimeOption">
        <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> 值之一，用于指定如何处理 <see cref="T:System.DateTime" /> 值。</param>
      <exception cref="T:System.ArgumentException">The <paramref name="dateTimeOption" /> value is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> or <paramref name="dateTimeOption" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset)">
      <summary>将提供的 <see cref="T:System.DateTimeOffset" /> 转换为 <see cref="T:System.String" />。</summary>
      <returns>提供的 <see cref="T:System.DateTimeOffset" /> 的 <see cref="T:System.String" /> 表示形式。</returns>
      <param name="value">要转换的 <see cref="T:System.DateTimeOffset" />。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset,System.String)">
      <summary>将提供的 <see cref="T:System.DateTimeOffset" /> 转换为指定格式的 <see cref="T:System.String" />。</summary>
      <returns>提供的 <see cref="T:System.DateTimeOffset" /> 的指定格式的 <see cref="T:System.String" /> 表示形式。</returns>
      <param name="value">要转换的 <see cref="T:System.DateTimeOffset" />。</param>
      <param name="format">
        <paramref name="s" /> 转换为的格式。该格式参数可以是 XML DateTime 类型的 W3C 建议的任何子集。（有关更多信息，请参见 http://www.w3.org/TR/xmlschema-2/#dateTime。）</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Decimal)">
      <summary>将 <see cref="T:System.Decimal" /> 转换为 <see cref="T:System.String" />。</summary>
      <returns>Decimal 的字符串表示形式。</returns>
      <param name="value">要转换的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Double)">
      <summary>将 <see cref="T:System.Double" /> 转换为 <see cref="T:System.String" />。</summary>
      <returns>Double 的字符串表示形式。</returns>
      <param name="value">要转换的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Guid)">
      <summary>将 <see cref="T:System.Guid" /> 转换为 <see cref="T:System.String" />。</summary>
      <returns>Guid 的字符串表示形式。</returns>
      <param name="value">要转换的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int16)">
      <summary>将 <see cref="T:System.Int16" /> 转换为 <see cref="T:System.String" />。</summary>
      <returns>Int16 的字符串表示形式。</returns>
      <param name="value">要转换的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int32)">
      <summary>将 <see cref="T:System.Int32" /> 转换为 <see cref="T:System.String" />。</summary>
      <returns>Int32 的字符串表示形式。</returns>
      <param name="value">要转换的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int64)">
      <summary>将 <see cref="T:System.Int64" /> 转换为 <see cref="T:System.String" />。</summary>
      <returns>Int64 的字符串表示形式。</returns>
      <param name="value">要转换的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.SByte)">
      <summary>将 <see cref="T:System.SByte" /> 转换为 <see cref="T:System.String" />。</summary>
      <returns>SByte 的字符串表示形式。</returns>
      <param name="value">要转换的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Single)">
      <summary>将 <see cref="T:System.Single" /> 转换为 <see cref="T:System.String" />。</summary>
      <returns>Single 的字符串表示形式。</returns>
      <param name="value">要转换的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.TimeSpan)">
      <summary>将 <see cref="T:System.TimeSpan" /> 转换为 <see cref="T:System.String" />。</summary>
      <returns>TimeSpan 的字符串表示形式。</returns>
      <param name="value">要转换的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt16)">
      <summary>将 <see cref="T:System.UInt16" /> 转换为 <see cref="T:System.String" />。</summary>
      <returns>UInt16 的字符串表示形式。</returns>
      <param name="value">要转换的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt32)">
      <summary>将 <see cref="T:System.UInt32" /> 转换为 <see cref="T:System.String" />。</summary>
      <returns>UInt32 的字符串表示形式。</returns>
      <param name="value">要转换的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt64)">
      <summary>将 <see cref="T:System.UInt64" /> 转换为 <see cref="T:System.String" />。</summary>
      <returns>UInt64 的字符串表示形式。</returns>
      <param name="value">要转换的值。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToTimeSpan(System.String)">
      <summary>将 <see cref="T:System.String" /> 转换为等效的 <see cref="T:System.TimeSpan" />。</summary>
      <returns>与该字符串等效的 TimeSpan。</returns>
      <param name="s">要转换的字符串。字符串格式必须符合 W3C XML 架构第 2 部分：持续时间数据类型建议。</param>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in correct format to represent a TimeSpan value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt16(System.String)">
      <summary>将 <see cref="T:System.String" /> 转换为等效的 <see cref="T:System.UInt16" />。</summary>
      <returns>与该字符串等效的 UInt16。</returns>
      <param name="s">要转换的字符串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt16.MinValue" /> or greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt32(System.String)">
      <summary>将 <see cref="T:System.String" /> 转换为等效的 <see cref="T:System.UInt32" />。</summary>
      <returns>与该字符串等效的 UInt32。</returns>
      <param name="s">要转换的字符串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt32.MinValue" /> or greater than <see cref="F:System.UInt32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt64(System.String)">
      <summary>将 <see cref="T:System.String" /> 转换为等效的 <see cref="T:System.UInt64" />。</summary>
      <returns>与该字符串等效的 UInt64。</returns>
      <param name="s">要转换的字符串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt64.MinValue" /> or greater than <see cref="F:System.UInt64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyName(System.String)">
      <summary>根据 W3C 可扩展标记语言建议验证该名称是否是有效的名称。</summary>
      <returns>该名称（如果它是有效的 XML 名称）。</returns>
      <param name="name">要验证的名称。</param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid XML name. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNCName(System.String)">
      <summary>根据 W3C 可扩展标记语言建议，验证名称是否是有效的 NCName。NCName 是不能包含冒号的名称。</summary>
      <returns>该名称（如果它是有效的 NCName）。</returns>
      <param name="name">要验证的名称。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid non-colon name. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNMTOKEN(System.String)">
      <summary>根据 W3C 的 XML 架构第 2 部分“数据类型建议”，验证字符串是否为有效 NMTOKEN</summary>
      <returns>名称标记（如果它是有效的 NMTOKEN）。</returns>
      <param name="name">要验证的字符串。</param>
      <exception cref="T:System.Xml.XmlException">The string is not a valid name token.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyPublicId(System.String)">
      <summary>如果字符串参数中的所有字符都是有效的公共 ID 字符，则返回传入的字符串实例。</summary>
      <returns>如果参数中的所有字符都是有效的公共 ID 字符，则返回传入的字符串。</returns>
      <param name="publicId">包含要验证的 ID 的 <see cref="T:System.String" />。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyWhitespace(System.String)">
      <summary>如果字符串参数中的所有字符都是有效的空白字符，则返回传入的字符串实例。</summary>
      <returns>如果字符串参数中的所有字符都是有效的空白字符，则返回传入的字符串实例；否则返回 null。</returns>
      <param name="content">要验证的 <see cref="T:System.String" />。</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyXmlChars(System.String)">
      <summary>如果字符串参数中的所有字符和代理项对字符都是有效的 XML 字符，则返回传入的字符串；否则将引发 XmlException 并提供有关遇到的第一个无效字符的信息。</summary>
      <returns>如果字符串参数中的所有字符和代理项对字符都是有效的 XML 字符，则返回传入的字符串；否则将引发 XmlException 并提供有关遇到的第一个无效字符的信息。</returns>
      <param name="content">包含要验证的字符的 <see cref="T:System.String" />。</param>
    </member>
    <member name="T:System.Xml.XmlDateTimeSerializationMode">
      <summary>指定在字符串与 <see cref="T:System.DateTime" /> 之间转换时，如何处理时间值。</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Local">
      <summary>作为本地时间处理。如果 <see cref="T:System.DateTime" /> 对象表示协调通用时间 (UTC)，它将转换为本地时间。</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.RoundtripKind">
      <summary>转换时应保留时区信息。</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Unspecified">
      <summary>如果 <see cref="T:System.DateTime" /> 要转换为字符串，将作为本地时间处理。</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Utc">
      <summary>作为 UTC 处理。如果 <see cref="T:System.DateTime" /> 对象表示本地时间，它将转换为 UTC。</summary>
    </member>
    <member name="T:System.Xml.XmlException">
      <summary>返回有关上一个异常的详细信息。</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor">
      <summary>初始化 XmlException 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String)">
      <summary>使用指定的错误信息初始化 XmlException 类的新实例。</summary>
      <param name="message">错误说明。</param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception)">
      <summary>初始化 XmlException 类的新实例。</summary>
      <param name="message">错误条件的说明。</param>
      <param name="innerException">引发 XmlException 的 <see cref="T:System.Exception" />（如果有的话）。此值可为 null。</param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception,System.Int32,System.Int32)">
      <summary>用指定的消息、内部异常、行号和行位置初始化 XmlException 类的新实例。</summary>
      <param name="message">错误说明。</param>
      <param name="innerException">导致当前异常的异常。此值可为 null。</param>
      <param name="lineNumber">指示错误发生位置的行号。</param>
      <param name="linePosition">指示错误发生位置的行位置。</param>
    </member>
    <member name="P:System.Xml.XmlException.LineNumber">
      <summary>获取指示错误发生位置的行号。</summary>
      <returns>指示错误发生位置的行号。</returns>
    </member>
    <member name="P:System.Xml.XmlException.LinePosition">
      <summary>获取指示错误发生位置的行位置。</summary>
      <returns>指示错误发生位置的行位置。</returns>
    </member>
    <member name="P:System.Xml.XmlException.Message">
      <summary>获取描述当前异常的消息。</summary>
      <returns>解释异常原因的错误信息。</returns>
    </member>
    <member name="T:System.Xml.XmlNamespaceManager">
      <summary>解析集合的命名空间、向集合添加命名空间和从集合中移除命名空间，以及提供对这些命名空间的范围管理。</summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.#ctor(System.Xml.XmlNameTable)">
      <summary>用指定的 <see cref="T:System.Xml.XmlNameTable" /> 初始化 <see cref="T:System.Xml.XmlNamespaceManager" /> 类的新实例。</summary>
      <param name="nameTable">要使用的 <see cref="T:System.Xml.XmlNameTable" />。</param>
      <exception cref="T:System.NullReferenceException">null is passed to the constructor </exception>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.AddNamespace(System.String,System.String)">
      <summary>将给定的命名空间添加到集合。</summary>
      <param name="prefix">与要添加的命名空间关联的前缀。使用 String.Empty 来添加默认命名空间。注意如果 <see cref="T:System.Xml.XmlNamespaceManager" /> 将用于解析 XML 路径语言 (XPath) 表达式中的命名空间，则必须指定前缀。如果 XPath 表达式不包含前缀，则假定命名空间统一资源标识符 (URI) 为空命名空间。有关 XPath 表达式和 <see cref="T:System.Xml.XmlNamespaceManager" /> 的更多信息，请参考 <see cref="M:System.Xml.XmlNode.SelectNodes(System.String)" /> 和 <see cref="M:System.Xml.XPath.XPathExpression.SetContext(System.Xml.XmlNamespaceManager)" /> 方法。</param>
      <param name="uri">要添加的命名空间。</param>
      <exception cref="T:System.ArgumentException">The value for <paramref name="prefix" /> is "xml" or "xmlns". </exception>
      <exception cref="T:System.ArgumentNullException">The value for <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.DefaultNamespace">
      <summary>获取默认命名空间的命名空间 URI。</summary>
      <returns>返回默认命名空间的命名空间 URI；如果没有默认命名空间，则返回 String.Empty。</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetEnumerator">
      <summary>返回一个枚举数以用于循环访问 <see cref="T:System.Xml.XmlNamespaceManager" /> 中的命名空间。</summary>
      <returns>一个包含 <see cref="T:System.Xml.XmlNamespaceManager" /> 存储的前缀的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>获取被可用于枚举当前范围内的命名空间的前缀键控的命名空间名称的集合。</summary>
      <returns>当前范围中的命名空间和前缀对的集合。</returns>
      <param name="scope">一个指定要返回的命名空间节点的类型的枚举值。</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.HasNamespace(System.String)">
      <summary>获取一个值，该值指示所提供的前缀是否具有为当前推送的范围定义的命名空间。</summary>
      <returns>如果定义有命名空间，则为 true；否则为 false。</returns>
      <param name="prefix">要查找的命名空间的前缀。 </param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupNamespace(System.String)">
      <summary>获取指定前缀的命名空间 URI。</summary>
      <returns>返回 <paramref name="prefix" /> 的命名空间 URI；如果没有映射的命名空间，则返回 null。返回的字符串是原子化的。有关原子化字符串的更多信息，请参见 <see cref="T:System.Xml.XmlNameTable" /> 类。</returns>
      <param name="prefix">要解析其命名空间 URI 的前缀。若要匹配默认命名空间，请传递 String.Empty。</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupPrefix(System.String)">
      <summary>查找为给定的命名空间 URI 声明的前缀。</summary>
      <returns>匹配的前缀。如果没有映射的前缀，则方法返回 String.Empty。如果提供 null 值，则返回 null。</returns>
      <param name="uri">要为前缀解析的命名空间。</param>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.NameTable">
      <summary>获取与此对象关联的 <see cref="T:System.Xml.XmlNameTable" />。</summary>
      <returns>此对象使用的 <see cref="T:System.Xml.XmlNameTable" />。</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PopScope">
      <summary>将命名空间范围弹出堆栈。</summary>
      <returns>如果堆栈上留有命名空间范围，则为 true；如果不再有要弹出的命名空间，则为 false。</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PushScope">
      <summary>将命名空间范围推送到堆栈上。</summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.RemoveNamespace(System.String,System.String)">
      <summary>为给定的前缀移除给定的命名空间。</summary>
      <param name="prefix">命名空间的前缀</param>
      <param name="uri">要为给定的前缀移除的命名空间。所移除的命名空间来自当前的命名空间范围。忽略当前范围以外的命名空间。</param>
      <exception cref="T:System.ArgumentNullException">The value of <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="T:System.Xml.XmlNamespaceScope">
      <summary>定义命名空间范围。</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.All">
      <summary>在当前节点范围内定义的所有命名空间。这包括总是隐式声明的 xmlns:xml 命名空间。未定义返回的命名空间的顺序。</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.ExcludeXml">
      <summary>在当前节点范围内定义的所有命名空间，但不包括总是隐式声明的 xmlns:xml 命名空间。未定义返回的命名空间的顺序。</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.Local">
      <summary>在当前节点本地定义的所有命名空间。</summary>
    </member>
    <member name="T:System.Xml.XmlNameTable">
      <summary>原子化字符串对象表。</summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.#ctor">
      <summary>初始化 <see cref="T:System.Xml.XmlNameTable" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>当在派生类中被重写时，将指定的字符串原子化并将其添加到 XmlNameTable。</summary>
      <returns>新的原子化字符串；如果已存在原子化字符串，则为此现有的原子化字符串。如果 length 为零，则返回 String.Empty。</returns>
      <param name="array">包含要添加的名称的字符数组。</param>
      <param name="offset">数组中指定名称第一个字符的从零开始的索引。</param>
      <param name="length">名称中的字符数。</param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />- 或 -<paramref name="offset" /> &gt;= <paramref name="array" />.Length- 或 -<paramref name="length" /> &gt; <paramref name="array" />.Length如果 <paramref name="length" /> =0，则上述条件不会导致引发异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0。</exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.String)">
      <summary>当在派生类中被重写时，将指定的字符串原子化并将其添加到 XmlNameTable。</summary>
      <returns>新的原子化字符串；如果已存在原子化字符串，则为此现有的原子化字符串。</returns>
      <param name="array">要添加的名称。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>当在派生类中被重写时，获取与给定数组中指定范围的字符包含相同字符的原子化字符串。</summary>
      <returns>原子化字符串；如果字符串尚未原子化，则为 null。如果 <paramref name="length" /> 为零，则返回 String.Empty。</returns>
      <param name="array">包含要查找的名称的字符数组。</param>
      <param name="offset">数组中指定名称第一个字符的从零开始的索引。</param>
      <param name="length">名称中的字符数。</param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />- 或 -<paramref name="offset" /> &gt;= <paramref name="array" />.Length- 或 -<paramref name="length" /> &gt; <paramref name="array" />.Length如果 <paramref name="length" /> =0，则上述条件不会导致引发异常。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0。</exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.String)">
      <summary>当在派生类中被重写时，获取与指定的字符串包含相同值的原子化字符串。</summary>
      <returns>原子化字符串；如果字符串尚未原子化，则为 null。</returns>
      <param name="array">要查找的名称。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
    </member>
    <member name="T:System.Xml.XmlNodeType">
      <summary>指定节点的类型。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Attribute">
      <summary>特性（例如，id='123'）。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.CDATA">
      <summary>CDATA 节（例如，&lt;![CDATA[my escaped text]]&gt;）。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Comment">
      <summary>注释（例如，&lt;!-- my comment --&gt;）。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Document">
      <summary>作为文档树的根的文档对象提供对整个 XML 文档的访问。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentFragment">
      <summary>文档片段。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentType">
      <summary>由以下标记指示的文档类型声明（例如，&lt;!DOCTYPE...&gt;）。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Element">
      <summary>元素（例如，&lt;item&gt;）。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndElement">
      <summary>末尾元素标记（例如，&lt;/item&gt;）。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndEntity">
      <summary>由于调用 <see cref="M:System.Xml.XmlReader.ResolveEntity" /> 而使 XmlReader 到达实体替换的末尾时返回。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Entity">
      <summary>实体声明（例如，&lt;!ENTITY...&gt;）。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EntityReference">
      <summary>实体引用（例如，&amp;num;）。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.None">
      <summary>如果未调用 Read 方法，则由 <see cref="T:System.Xml.XmlReader" /> 返回。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Notation">
      <summary>文档类型声明中的表示法（例如，&lt;!NOTATION...&gt;）。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.ProcessingInstruction">
      <summary>处理指令（例如，&lt;?pi test?&gt;）。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.SignificantWhitespace">
      <summary>混合内容模型中标记间的空白或 xml:space="preserve" 范围内的空白。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Text">
      <summary>节点的文本内容。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Whitespace">
      <summary>标记间的空白。</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.XmlDeclaration">
      <summary>XML 声明（例如，&lt;?xml version='1.0'?&gt;）。</summary>
    </member>
    <member name="T:System.Xml.XmlParserContext">
      <summary>提供 <see cref="T:System.Xml.XmlReader" /> 分析 XML 片段所需的所有上下文信息。</summary>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace)">
      <summary>用指定的 <see cref="T:System.Xml.XmlNameTable" />、<see cref="T:System.Xml.XmlNamespaceManager" />、基 URI、xml:lang、xml:space 和文档类型值初始化 XmlParserContext 类的新实例。</summary>
      <param name="nt">用于将原子化字符串的 <see cref="T:System.Xml.XmlNameTable" />。如果这为 null，则改用用于构造 <paramref name="nsMgr" /> 的名称表。有关原子化字符串的更多信息，请参见 <see cref="T:System.Xml.XmlNameTable" />。</param>
      <param name="nsMgr">用于查找命名空间信息的 <see cref="T:System.Xml.XmlNamespaceManager" />，或者为 null。</param>
      <param name="docTypeName">文档类型声明的名称。</param>
      <param name="pubId">public 标识符。</param>
      <param name="sysId">系统标识符。</param>
      <param name="internalSubset">内部 DTD 子集。DTD 子集用于实体解析，而不能用于文档验证。</param>
      <param name="baseURI">XML 片段的基 URI（从其加载片段的位置）。</param>
      <param name="xmlLang">xml:lang 范围。</param>
      <param name="xmlSpace">一个 <see cref="T:System.Xml.XmlSpace" /> 值，指示 xml:space 范围。</param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> 与用来构造 <paramref name="nsMgr" /> 的 XmlNameTable 不同。</exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>用指定的 <see cref="T:System.Xml.XmlNameTable" />、<see cref="T:System.Xml.XmlNamespaceManager" />、基 URI、xml:lang、xml:space、编码和文档类型值初始化 XmlParserContext 类的新实例。</summary>
      <param name="nt">用于将原子化字符串的 <see cref="T:System.Xml.XmlNameTable" />。如果这为 null，则改用用于构造 <paramref name="nsMgr" /> 的名称表。有关原子化字符串的更多信息，请参见 <see cref="T:System.Xml.XmlNameTable" />。</param>
      <param name="nsMgr">用于查找命名空间信息的 <see cref="T:System.Xml.XmlNamespaceManager" />，或者为 null。</param>
      <param name="docTypeName">文档类型声明的名称。</param>
      <param name="pubId">public 标识符。</param>
      <param name="sysId">系统标识符。</param>
      <param name="internalSubset">内部 DTD 子集。DTD 用于实体解析，而不能用于文档验证。</param>
      <param name="baseURI">XML 片段的基 URI（从其加载片段的位置）。</param>
      <param name="xmlLang">xml:lang 范围。</param>
      <param name="xmlSpace">一个 <see cref="T:System.Xml.XmlSpace" /> 值，指示 xml:space 范围。</param>
      <param name="enc">一个 <see cref="T:System.Text.Encoding" /> 对象，指示编码方式设置。</param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> 与用来构造 <paramref name="nsMgr" /> 的 XmlNameTable 不同。</exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace)">
      <summary>用指定的 <see cref="T:System.Xml.XmlNameTable" />、<see cref="T:System.Xml.XmlNamespaceManager" />、xml:lang 和 xml:space 值初始化 XmlParserContext 类的新实例。</summary>
      <param name="nt">用于将原子化字符串的 <see cref="T:System.Xml.XmlNameTable" />。如果这为 null，则改用用于构造 <paramref name="nsMgr" /> 的名称表。有关原子化字符串的更多信息，请参见 <see cref="T:System.Xml.XmlNameTable" />。</param>
      <param name="nsMgr">用于查找命名空间信息的 <see cref="T:System.Xml.XmlNamespaceManager" />，或者为 null。</param>
      <param name="xmlLang">xml:lang 范围。</param>
      <param name="xmlSpace">一个 <see cref="T:System.Xml.XmlSpace" /> 值，指示 xml:space 范围。</param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> 与用来构造 <paramref name="nsMgr" /> 的 XmlNameTable 不同。</exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>用指定的 <see cref="T:System.Xml.XmlNameTable" />、<see cref="T:System.Xml.XmlNamespaceManager" />、xml:lang、xml:space 和编码初始化 XmlParserContext 类的新实例。</summary>
      <param name="nt">用于将原子化字符串的 <see cref="T:System.Xml.XmlNameTable" />。如果这为 null，则改用用于构造 <paramref name="nsMgr" /> 的名称表。有关原子化字符串的更多信息，请参见 <see cref="T:System.Xml.XmlNameTable" />。</param>
      <param name="nsMgr">用于查找命名空间信息的 <see cref="T:System.Xml.XmlNamespaceManager" />，或者为 null。</param>
      <param name="xmlLang">xml:lang 范围。</param>
      <param name="xmlSpace">一个 <see cref="T:System.Xml.XmlSpace" /> 值，指示 xml:space 范围。</param>
      <param name="enc">一个 <see cref="T:System.Text.Encoding" /> 对象，指示编码方式设置。</param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> 与用来构造 <paramref name="nsMgr" /> 的 XmlNameTable 不同。</exception>
    </member>
    <member name="P:System.Xml.XmlParserContext.BaseURI">
      <summary>获取或设置基 URI。</summary>
      <returns>用于解析 DTD 文件的基 URI。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.DocTypeName">
      <summary>获取或设置文档类型声明的名称。</summary>
      <returns>文档类型声明的名称。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.Encoding">
      <summary>获取或设置编码类型。</summary>
      <returns>一个 <see cref="T:System.Text.Encoding" /> 对象，指示编码类型。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.InternalSubset">
      <summary>获取或设置内部 DTD 子集。</summary>
      <returns>内部 DTD 子集。例如，此属性返回方括号 &lt;!DOCTYPE doc [...]&gt; 之间的所有内容。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NamespaceManager">
      <summary>获取或设置 <see cref="T:System.Xml.XmlNamespaceManager" />。</summary>
      <returns>XmlNamespaceManager。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NameTable">
      <summary>获取用于原子化字符串的 <see cref="T:System.Xml.XmlNameTable" />。有关原子化字符串的更多信息，请参见 <see cref="T:System.Xml.XmlNameTable" />。</summary>
      <returns>XmlNameTable。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.PublicId">
      <summary>获取或设置公共标识符。</summary>
      <returns>public 标识符。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.SystemId">
      <summary>获取或设置系统标识符。</summary>
      <returns>系统标识符。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlLang">
      <summary>获取或设置当前 xml:lang 范围。</summary>
      <returns>当前的 xml:lang 范围。如果范围中没有 xml:lang，则返回 String.Empty。</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlSpace">
      <summary>获取或设置当前 xml:space 范围。</summary>
      <returns>一个 <see cref="T:System.Xml.XmlSpace" /> 值，指示 xml:space 范围。</returns>
    </member>
    <member name="T:System.Xml.XmlQualifiedName">
      <summary>表示 XML 限定名。</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor">
      <summary>初始化 <see cref="T:System.Xml.XmlQualifiedName" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String)">
      <summary>用指定的名称初始化 <see cref="T:System.Xml.XmlQualifiedName" /> 类的新实例。</summary>
      <param name="name">要用作 <see cref="T:System.Xml.XmlQualifiedName" /> 对象的名称的本地名称。</param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String,System.String)">
      <summary>用指定的名称和命名空间初始化 <see cref="T:System.Xml.XmlQualifiedName" /> 类的新实例。</summary>
      <param name="name">要用作 <see cref="T:System.Xml.XmlQualifiedName" /> 对象的名称的本地名称。</param>
      <param name="ns">
        <see cref="T:System.Xml.XmlQualifiedName" /> 对象的命名空间。</param>
    </member>
    <member name="F:System.Xml.XmlQualifiedName.Empty">
      <summary>提供空 <see cref="T:System.Xml.XmlQualifiedName" />。</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.Equals(System.Object)">
      <summary>确定指定的 <see cref="T:System.Xml.XmlQualifiedName" /> 对象是否等同于当前的 <see cref="T:System.Xml.XmlQualifiedName" />。</summary>
      <returns>如果它们两个是相同的实例对象，则为 true；否则为 false。</returns>
      <param name="other">要比较的 <see cref="T:System.Xml.XmlQualifiedName" />。</param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.GetHashCode">
      <summary>返回 <see cref="T:System.Xml.XmlQualifiedName" /> 的哈希代码。</summary>
      <returns>该对象的哈希代码。</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.IsEmpty">
      <summary>获取一个值，该值指示 <see cref="T:System.Xml.XmlQualifiedName" /> 是否为空。</summary>
      <returns>如果名称和命名空间为空字符串，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Name">
      <summary>获取 <see cref="T:System.Xml.XmlQualifiedName" /> 的限定名的字符串表示形式。</summary>
      <returns>限定名的字符串表示形式，或者如果没有为对象定义名称，则为 String.Empty。</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Namespace">
      <summary>获取 <see cref="T:System.Xml.XmlQualifiedName" /> 的命名空间的字符串表示形式。</summary>
      <returns>命名空间的字符串表示形式，或者如果没有为对象定义命名空间，则为 String.Empty。</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Equality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>比较两个 <see cref="T:System.Xml.XmlQualifiedName" /> 对象。</summary>
      <returns>如果两个对象具有相同的名称和命名空间值，则为 true；否则为 false。</returns>
      <param name="a">要比较的 <see cref="T:System.Xml.XmlQualifiedName" />。</param>
      <param name="b">要比较的 <see cref="T:System.Xml.XmlQualifiedName" />。</param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Inequality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>比较两个 <see cref="T:System.Xml.XmlQualifiedName" /> 对象。</summary>
      <returns>如果两个对象的名称和命名空间值不同，则为 true；否则为 false。</returns>
      <param name="a">要比较的 <see cref="T:System.Xml.XmlQualifiedName" />。</param>
      <param name="b">要比较的 <see cref="T:System.Xml.XmlQualifiedName" />。</param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString">
      <summary>返回 <see cref="T:System.Xml.XmlQualifiedName" /> 的字符串值。</summary>
      <returns>采用 namespace:localname 格式的 <see cref="T:System.Xml.XmlQualifiedName" /> 的字符串值。如果对象没有已定义的命名空间，则此方法只返回本地名称。</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString(System.String,System.String)">
      <summary>返回 <see cref="T:System.Xml.XmlQualifiedName" /> 的字符串值。</summary>
      <returns>采用 namespace:localname 格式的 <see cref="T:System.Xml.XmlQualifiedName" /> 的字符串值。如果对象没有已定义的命名空间，则此方法只返回本地名称。</returns>
      <param name="name">对象的名称。</param>
      <param name="ns">对象的命名空间。</param>
    </member>
    <member name="T:System.Xml.XmlReader">
      <summary>表示提供对 XML 数据进行快速、非缓存、只进访问的读取器。若要浏览此类型的.NET Framework 源代码，请参阅参考源。</summary>
    </member>
    <member name="M:System.Xml.XmlReader.#ctor">
      <summary>初始化 XmlReader 类的新实例。</summary>
    </member>
    <member name="P:System.Xml.XmlReader.AttributeCount">
      <summary>当在派生类中被重写时，获取当前节点上的属性数。</summary>
      <returns>当前节点上的属性数目。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.BaseURI">
      <summary>当在派生类中被重写时，获取当前节点的基 URI。</summary>
      <returns>当前节点的基 URI。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadBinaryContent">
      <summary>获取一个值，该值指示 <see cref="T:System.Xml.XmlReader" /> 是否实现二进制内容读取方法。</summary>
      <returns>如果实现二进制内容读取方法，则为 true；否则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadValueChunk">
      <summary>获取一个值，该值指示 <see cref="T:System.Xml.XmlReader" /> 是否实现 <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" /> 方法。</summary>
      <returns>true if the <see cref="T:System.Xml.XmlReader" /> implements the <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" /> method; otherwise false.</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanResolveEntity">
      <summary>获取一个值，该值指示此读取器是否可以分析和解析实体。</summary>
      <returns>如果此读取器可以分析和解析实体，则为 true；否则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream)">
      <summary>创建一个新<see cref="T:System.Xml.XmlReader" />实例使用默认设置使用指定的流。</summary>
      <returns>一个用于读取数据流中所含数据的对象。</returns>
      <param name="input">包含 XML 数据的流。<see cref="T:System.Xml.XmlReader" /> 对流的前几个字节进行扫描，查找字节顺序标记或其他编码标志。在确定编码方式后，使用该编码方式继续读取流，而处理过程继续将输入内容分析为 (Unicode) 字符流。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 值为 null。</exception>
      <exception cref="T:System.Security.SecurityException">
        <see cref="T:System.Xml.XmlReader" /> 没有访问 XML 数据位置所需的足够权限。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings)">
      <summary>创建一个新<see cref="T:System.Xml.XmlReader" />具有指定的流和设置的实例。</summary>
      <returns>一个用于读取数据流中所含数据的对象。</returns>
      <param name="input">包含 XML 数据的流。<see cref="T:System.Xml.XmlReader" /> 对流的前几个字节进行扫描，查找字节顺序标记或其他编码标志。在确定编码方式后，使用该编码方式继续读取流，而处理过程继续将输入内容分析为 (Unicode) 字符流。</param>
      <param name="settings">新的设置<see cref="T:System.Xml.XmlReader" />实例。此值可为 null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 值为 null。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>创建一个新<see cref="T:System.Xml.XmlReader" />实例使用指定的流、 设置和上下文信息用于分析。</summary>
      <returns>一个用于读取数据流中所含数据的对象。</returns>
      <param name="input">包含 XML 数据的流。<see cref="T:System.Xml.XmlReader" /> 对流的前几个字节进行扫描，查找字节顺序标记或其他编码标志。在确定编码方式后，使用该编码方式继续读取流，而处理过程继续将输入内容分析为 (Unicode) 字符流。</param>
      <param name="settings">新的设置<see cref="T:System.Xml.XmlReader" />实例。此值可为 null。</param>
      <param name="inputContext">分析 XML 片段所需的上下文信息.上下文信息可以包括要使用的 <see cref="T:System.Xml.XmlNameTable" />、编码、命名空间范围、当前的 xml:lang 和 xml:space 范围、基 URI 和文档类型定义。此值可为 null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 值为 null。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader)">
      <summary>创建一个新<see cref="T:System.Xml.XmlReader" />通过使用指定的文本读取器的实例。</summary>
      <returns>一个用于读取数据流中所含数据的对象。</returns>
      <param name="input">从其中读取 XML 数据的文本读取器。由于文本读取器返回的是 Unicode 字符流，因此，XML 读取器未使用 XML 声明中指定的编码对数据流进行解码。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 值为 null。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings)">
      <summary>创建一个新<see cref="T:System.Xml.XmlReader" />通过使用指定的文本读取器和设置的实例。</summary>
      <returns>一个用于读取数据流中所含数据的对象。</returns>
      <param name="input">从其中读取 XML 数据的文本读取器。由于文本读取器返回的是 Unicode 字符流，因此，XML 读取器未使用 XML 声明中指定的编码对数据流进行解码。</param>
      <param name="settings">新的设置<see cref="T:System.Xml.XmlReader" />。此值可为 null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 值为 null。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>创建一个新<see cref="T:System.Xml.XmlReader" />通过使用指定的文本读取器、 设置和上下文信息用于分析的实例。</summary>
      <returns>一个用于读取数据流中所含数据的对象。</returns>
      <param name="input">从其中读取 XML 数据的文本读取器。由于文本读取器返回的是 Unicode 字符流，因此，XML 读取器未使用 XML 声明中指定的编码对数据流进行解码。</param>
      <param name="settings">新的设置<see cref="T:System.Xml.XmlReader" />实例。此值可为 null。</param>
      <param name="inputContext">分析 XML 片段所需的上下文信息.上下文信息可以包括要使用的 <see cref="T:System.Xml.XmlNameTable" />、编码、命名空间范围、当前的 xml:lang 和 xml:space 范围、基 URI 和文档类型定义。此值可为 null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 值为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.Xml.XmlReaderSettings.NameTable" /> 和 <see cref="P:System.Xml.XmlParserContext.NameTable" /> 属性都包含值。（只可以设置并使用这两个 NameTable 属性之中的一个。）</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String)">
      <summary>使用指定的 URI 创建一个新的 <see cref="T:System.Xml.XmlReader" /> 实例。</summary>
      <returns>一个用于读取数据流中所含数据的对象。</returns>
      <param name="inputUri">包含 XML 数据的文件的 URI。<see cref="T:System.Xml.XmlUrlResolver" /> 类用于将路径转换为规范化数据表示形式。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputUri" /> 值为 null。</exception>
      <exception cref="T:System.Security.SecurityException">
        <see cref="T:System.Xml.XmlReader" /> 没有访问 XML 数据位置所需的足够权限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">由 URI 标识的文件不存在。</exception>
      <exception cref="T:System.UriFormatException">在 .NET for Windows Store 应用程序 或 可移植类库 中，请改为捕获基类异常 <see cref="T:System.FormatException" />。URI 格式不正确。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String,System.Xml.XmlReaderSettings)">
      <summary>创建一个新<see cref="T:System.Xml.XmlReader" />通过使用指定的 URI 和设置的实例。</summary>
      <returns>一个用于读取数据流中所含数据的对象。</returns>
      <param name="inputUri">包含 XML 数据的文件的 URI。<see cref="T:System.Xml.XmlReaderSettings" /> 对象上的 <see cref="T:System.Xml.XmlResolver" /> 对象用于将路径转换为规范化数据表示形式。如果 <see cref="P:System.Xml.XmlReaderSettings.XmlResolver" /> 为 null，则使用新的 <see cref="T:System.Xml.XmlUrlResolver" /> 对象。</param>
      <param name="settings">新的设置<see cref="T:System.Xml.XmlReader" />实例。此值可为 null。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="inputUri" /> 值为 null。</exception>
      <exception cref="T:System.IO.FileNotFoundException">无法找到由该 URI 指定的文件。</exception>
      <exception cref="T:System.UriFormatException">在 .NET for Windows Store 应用程序 或 可移植类库 中，请改为捕获基类异常 <see cref="T:System.FormatException" />。URI 格式不正确。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.Xml.XmlReader,System.Xml.XmlReaderSettings)">
      <summary>创建一个新<see cref="T:System.Xml.XmlReader" />通过使用指定的 XML 读取器和设置的实例。</summary>
      <returns>包装的对象周围指定<see cref="T:System.Xml.XmlReader" />对象。</returns>
      <param name="reader">要用作基础 XML 编写器的对象。</param>
      <param name="settings">新的设置<see cref="T:System.Xml.XmlReader" />实例。<see cref="T:System.Xml.XmlReaderSettings" /> 对象的一致性级别要么必须与基础读取器的一致性级别匹配，要么必须设置为 <see cref="F:System.Xml.ConformanceLevel.Auto" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> 值为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReaderSettings" /> 对象指定的一致性级别与基础读取器的一致性级别不一致。- 或 -基础 <see cref="T:System.Xml.XmlReader" /> 处于 <see cref="F:System.Xml.ReadState.Error" /> 或 <see cref="F:System.Xml.ReadState.Closed" /> 状态。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Depth">
      <summary>当在派生类中被重写时，获取 XML 文档中当前节点的深度。</summary>
      <returns>XML 文档中当前节点的深度。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose">
      <summary>释放由 <see cref="T:System.Xml.XmlReader" /> 类的当前实例占用的所有资源。</summary>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose(System.Boolean)">
      <summary>释放由 <see cref="T:System.Xml.XmlReader" /> 占用的非托管资源，还可以另外再释放托管资源。</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.EOF">
      <summary>当在派生类中被重写时，获取一个值，该值指示此读取器是否定位在流的结尾。</summary>
      <returns>如果此读取器定位在流的结尾，则为 true；否则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.Int32)">
      <summary>当在派生类中被重写时，获取具有指定索引的属性的值。</summary>
      <returns>指定的属性的值。此方法不移动读取器。</returns>
      <param name="i">属性的索引。索引是从零开始的。（第一个属性的索引为 0。）</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="i" /> 超出范围。它必须是非负数且小于特性集合的大小。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String)">
      <summary>当在派生类中被重写时，获取具有指定 <see cref="P:System.Xml.XmlReader.Name" /> 的属性的值。</summary>
      <returns>指定的属性的值。如果找不到该属性，或者值为 String.Empty，则返回 null。</returns>
      <param name="name">属性的限定名称。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String,System.String)">
      <summary>当在派生类中被重写时，获取具有指定 <see cref="P:System.Xml.XmlReader.LocalName" /> 和 <see cref="P:System.Xml.XmlReader.NamespaceURI" /> 的属性的值。</summary>
      <returns>指定的属性的值。如果找不到该属性，或者值为 String.Empty，则返回 null。此方法不移动读取器。</returns>
      <param name="name">属性的本地名称。</param>
      <param name="namespaceURI">属性的命名空间 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetValueAsync">
      <summary>异步获取当前节点的值。</summary>
      <returns>当前节点的值。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.InvalidOperationException">调用<see cref="T:System.Xml.XmlReader" /> 异步方法，而无需为 true 设置 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 标志。在这种情况下，将使用消息 “如果要使用异步方法，设置 XmlReaderSettings.Async 为 true”引发<see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasAttributes">
      <summary>获取一个值，该值指示当前节点是否有任何属性。</summary>
      <returns>如果当前节点具有属性，则为 true；否则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasValue">
      <summary>当在派生类中被重写时，获取一个值，该值指示当前节点是否可以具有 <see cref="P:System.Xml.XmlReader.Value" />。</summary>
      <returns>如果读取器当前定位在的节点可以具有 Value，则为 true；否则为 false。如果为 false，则节点值为 String.Empty。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsDefault">
      <summary>当在派生类中被重写时，获取一个值，该值指示当前节点是否是从 DTD 或架构中定义的默认值生成的特性。</summary>
      <returns>如果当前节点是其值从 DTD 或架构中定义的默认值生成的特性，则为 true；如果特性值是显式设置的，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsEmptyElement">
      <summary>当在派生类中被重写时，获取一个值，该值指示当前节点是否为空元素（例如 &lt;MyElement/&gt;）。</summary>
      <returns>如果当前节点是一个以 /&gt; 结尾的元素（<see cref="P:System.Xml.XmlReader.NodeType" /> 等于 XmlNodeType.Element），则为 true；否则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsName(System.String)">
      <summary>返回一个值，该值指示字符串参数是否是有效的 XML 名称。</summary>
      <returns>如果该名称有效，则为 true；否则为 false。</returns>
      <param name="str">要验证的名称。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="str" /> 值为 null。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsNameToken(System.String)">
      <summary>返回一个值，该值指示该字符串参数是否是有效的 XML 名称标记。</summary>
      <returns>如果它是有效的名称标记，则为 true；否则为 false。</returns>
      <param name="str">要验证的名称标记。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="str" /> 值为 null。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement">
      <summary>调用 <see cref="M:System.Xml.XmlReader.MoveToContent" /> 并测试当前内容节点是否是开始标记或空元素标记。</summary>
      <returns>如果 <see cref="M:System.Xml.XmlReader.MoveToContent" /> 找到开始标记或空元素标记，则为 true；如果找到不同于 XmlNodeType.Element 的节点类型，则为 false。</returns>
      <exception cref="T:System.Xml.XmlException">在输入流中遇到不正确的 XML。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String)">
      <summary>调用 <see cref="M:System.Xml.XmlReader.MoveToContent" /> 并测试当前内容节点是否是开始标记或空元素标记，以及所找到元素的 <see cref="P:System.Xml.XmlReader.Name" /> 属性是否与给定的参数匹配。</summary>
      <returns>如果生成的节点是一个元素，且 Name 属性与指定的字符串匹配，则为 true。如果找到 XmlNodeType.Element 之外的节点类型，或者元素的 Name 属性与指定的字符串不匹配，则为 false。</returns>
      <param name="name">与找到的元素的 Name 属性匹配的字符串。</param>
      <exception cref="T:System.Xml.XmlException">在输入流中遇到不正确的 XML。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String,System.String)">
      <summary>调用 <see cref="M:System.Xml.XmlReader.MoveToContent" /> 并测试当前内容节点是否是开始标记或空元素标记，以及所找到元素的 <see cref="P:System.Xml.XmlReader.LocalName" /> 和 <see cref="P:System.Xml.XmlReader.NamespaceURI" /> 属性是否与给定的字符串匹配。</summary>
      <returns>如果生成的节点是一个元素，则为 true。如果找到 XmlNodeType.Element 之外的节点类型，或者元素的 LocalName 和 NamespaceURI 属性与指定的字符串不匹配，则为 false。</returns>
      <param name="localname">与找到的元素的 LocalName 属性匹配的字符串。</param>
      <param name="ns">与找到的元素的 NamespaceURI 属性匹配的字符串。</param>
      <exception cref="T:System.Xml.XmlException">在输入流中遇到不正确的 XML。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.Int32)">
      <summary>当在派生类中被重写时，获取具有指定索引的属性的值。</summary>
      <returns>指定的属性的值。</returns>
      <param name="i">属性的索引。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String)">
      <summary>当在派生类中被重写时，获取具有指定 <see cref="P:System.Xml.XmlReader.Name" /> 的属性的值。</summary>
      <returns>指定的属性的值。如果未找到该属性，则返回 null。</returns>
      <param name="name">属性的限定名称。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String,System.String)">
      <summary>当在派生类中被重写时，获取具有指定 <see cref="P:System.Xml.XmlReader.LocalName" /> 和 <see cref="P:System.Xml.XmlReader.NamespaceURI" /> 的属性的值。</summary>
      <returns>指定的属性的值。如果未找到该属性，则返回 null。</returns>
      <param name="name">属性的本地名称。</param>
      <param name="namespaceURI">属性的命名空间 URI。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.LocalName">
      <summary>当在派生类中被重写时，获取当前节点的本地名称。</summary>
      <returns>移除了前缀的当前节点的名称。例如，对于元素 &lt;bk:book&gt;，LocalName 为 book。对于没有名称的节点类型（如 Text、Comment 等），该属性返回 String.Empty。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.LookupNamespace(System.String)">
      <summary>当在派生类中被重写时，在当前元素的范围内解析命名空间前缀。</summary>
      <returns>前缀映射到的命名空间 URI；如果未找到任何匹配的前缀，则为 null。</returns>
      <param name="prefix">要解析其命名空间 URI 的前缀。若要匹配默认命名空间，请传递一个空字符串。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.Int32)">
      <summary>当在派生类中被重写时，移动到具有指定索引的属性。</summary>
      <param name="i">属性的索引。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">参数为负值。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String)">
      <summary>当在派生类中被重写时，移动到具有指定 <see cref="P:System.Xml.XmlReader.Name" /> 的属性。</summary>
      <returns>如果找到了属性，则为 true；否则为 false。如果为 false，则读取器的位置未改变。</returns>
      <param name="name">属性的限定名称。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.ArgumentException"> 参数是空字符串。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String,System.String)">
      <summary>当在派生类中被重写时，移动到具有指定的 <see cref="P:System.Xml.XmlReader.LocalName" /> 和 <see cref="P:System.Xml.XmlReader.NamespaceURI" /> 的属性。</summary>
      <returns>如果找到了属性，则为 true；否则为 false。如果为 false，则读取器的位置未改变。</returns>
      <param name="name">属性的本地名称。</param>
      <param name="ns">属性的命名空间 URI。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.ArgumentNullException">两个参数值为 null。 </exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContent">
      <summary>检查当前节点是否是内容（非空白文本、CDATA、Element、EndElement、EntityReference 或 EndEntity）节点。如果此节点不是内容节点，则读取器向前跳至下一个内容节点或文件结尾。它跳过以下类型的节点：ProcessingInstruction、DocumentType、Comment、Whitespace 或 SignificantWhitespace。</summary>
      <returns>此方法找到的当前节点的 <see cref="P:System.Xml.XmlReader.NodeType" />；如果读取器已到达输入流的末尾，则为 XmlNodeType.None。</returns>
      <exception cref="T:System.Xml.XmlException">在输入流中遇到不正确的 XML。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContentAsync">
      <summary>异步检查当前节点是否为内容节点。如果此节点不是内容节点，则读取器向前跳至下一个内容节点或文件结尾。</summary>
      <returns>此方法找到的当前节点的 <see cref="P:System.Xml.XmlReader.NodeType" />；如果读取器已到达输入流的末尾，则为 XmlNodeType.None。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.InvalidOperationException">调用<see cref="T:System.Xml.XmlReader" /> 异步方法，而无需为 true 设置 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 标志。在这种情况下，将使用消息 “如果要使用异步方法，设置 XmlReaderSettings.Async 为 true”引发<see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToElement">
      <summary>当在派生类中被重写时，移动到包含当前属性节点的元素。</summary>
      <returns>如果读取器定位在属性上，则为 true（读取器移动到拥有该属性的元素）；如果读取器不是定位在属性上，则为 false（读取器的位置不改变）。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToFirstAttribute">
      <summary>当在派生类中被重写时，移动到第一个属性。</summary>
      <returns>如果属性存在，则为 true（读取器移动到第一个属性）；否则为 false（读取器的位置不改变）。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToNextAttribute">
      <summary>当在派生类中被重写时，移动到下一个属性。</summary>
      <returns>如果存在下一个属性，则为 true；如果没有其他属性，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Name">
      <summary>当在派生类中被重写时，获取当前节点的限定名。</summary>
      <returns>当前节点的限定名称。例如，对于元素 &lt;bk:book&gt;，Name 为 bk:book。返回的名称取决于节点的 <see cref="P:System.Xml.XmlReader.NodeType" />。下列节点类型返回所列的值。所有其他节点类型返回空字符串。节点类型名称 Attribute属性名。 DocumentType文档类型名称。 Element标记名称。 EntityReference引用的实体的名称。 ProcessingInstruction处理指令的目标。 XmlDeclaration字符串 xml。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NamespaceURI">
      <summary>当在派生类中被重写时，获取读取器定位在其上的节点的命名空间 URI（采用 W3C 命名空间规范中定义的形式）。</summary>
      <returns>当前节点的命名空间 URI；否则为空字符串。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NameTable">
      <summary>当在派生类中被重写时，获取与该实现关联的 <see cref="T:System.Xml.XmlNameTable" />。</summary>
      <returns>XmlNameTable，它使您能够获取该节点内字符串的原子化版本。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NodeType">
      <summary>当在派生类中被重写时，获取当前节点的类型。</summary>
      <returns>指定当前节点的类型的枚举值之一。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Prefix">
      <summary>当在派生类中被重写时，获取与当前节点关联的命名空间前缀。</summary>
      <returns>与当前节点关联的命名空间前缀。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Read">
      <summary>当在派生类中被重写时，从流中读取下一个节点。</summary>
      <returns>true如果成功，则读取下一个节点否则为false。</returns>
      <exception cref="T:System.Xml.XmlException">分析 XML 时出错。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAsync">
      <summary>异步读取该流的下一个节点。</summary>
      <returns>如果成功读取了下一个节点，则为 true；如果没有其他节点可读取，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.InvalidOperationException">调用<see cref="T:System.Xml.XmlReader" /> 异步方法，而无需为 true 设置 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 标志。在这种情况下，将使用消息 “如果要使用异步方法，设置 XmlReaderSettings.Async 为 true”引发<see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAttributeValue">
      <summary>当在派生类中被重写时，将属性值解析为一个或多个 Text、EntityReference 或 EndEntity 节点。</summary>
      <returns>如果有可返回的节点，则为 true。如果进行初始调用时读取器不是定位在属性节点上，或者如果已读取了所有属性值，则为 false。如果是空属性（如 misc=""），则返回 true，同时返回值为 String.Empty 的单个节点。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>将内容作为指定类型的对象读取。</summary>
      <returns>已转换为请求类型的串联文本内容或属性值。</returns>
      <param name="returnType">要返回的值的类型。“注意”   随着 .NET Framework 3.5 的发布，<paramref name="returnType" /> 参数的值现在可以是 <see cref="T:System.DateTimeOffset" /> 类型。</param>
      <param name="namespaceResolver">一个 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 对象，用于解析与类型转换有关的任何命名空间前缀。例如，将 <see cref="T:System.Xml.XmlQualifiedName" /> 对象转换为 xs:string 时可以使用此对象。此值可为 null。</param>
      <exception cref="T:System.FormatException">内容格式不是目标类型的正确格式。</exception>
      <exception cref="T:System.InvalidCastException">试图进行的强制转换无效。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="returnType" /> 值为 null。</exception>
      <exception cref="T:System.InvalidOperationException">当前节点不是所支持的节点类型。有关详细信息，请参见下表。</exception>
      <exception cref="T:System.OverflowException">读取 Decimal.MaxValue。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>将内容作为指定类型的对象异步读取。</summary>
      <returns>已转换为请求类型的串联文本内容或属性值。</returns>
      <param name="returnType">要返回的值的类型。</param>
      <param name="namespaceResolver">一个 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 对象，用于解析与类型转换有关的任何命名空间前缀。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.InvalidOperationException">调用<see cref="T:System.Xml.XmlReader" /> 异步方法，而无需为 true 设置 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 标志。在这种情况下，将使用消息 “如果要使用异步方法，设置 XmlReaderSettings.Async 为 true”引发<see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>读取内容并返回 Base64 解码的二进制字节。</summary>
      <returns>写入缓冲区的字节数。</returns>
      <param name="buffer">结果文本复制到的缓冲区。此值不能为 null。</param>
      <param name="index">缓冲区中的偏移，从这个位置开始将结果复制到缓冲区中。</param>
      <param name="count">要复制到缓冲区的最大字节数。此方法返回复制的实际字节数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 值为 null。</exception>
      <exception cref="T:System.InvalidOperationException">当前节点不支持 <see cref="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)" />。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">缓冲区中的索引或者索引与计数之和大于分配的缓冲区大小。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.XmlReader" /> 实现不支持此方法。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>异步读取内容并返回 Base64 解码的二进制字节。</summary>
      <returns>写入缓冲区的字节数。</returns>
      <param name="buffer">结果文本复制到的缓冲区。此值不能为 null。</param>
      <param name="index">缓冲区中的偏移，从这个位置开始将结果复制到缓冲区中。</param>
      <param name="count">要复制到缓冲区的最大字节数。此方法返回复制的实际字节数。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.InvalidOperationException">调用<see cref="T:System.Xml.XmlReader" /> 异步方法，而无需为 true 设置 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 标志。在这种情况下，将使用消息 “如果要使用异步方法，设置 XmlReaderSettings.Async 为 true”引发<see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>读取内容并返回 BinHex 解码的二进制字节。</summary>
      <returns>写入缓冲区的字节数。</returns>
      <param name="buffer">结果文本复制到的缓冲区。此值不能为 null。</param>
      <param name="index">缓冲区中的偏移，从这个位置开始将结果复制到缓冲区中。</param>
      <param name="count">要复制到缓冲区的最大字节数。此方法返回复制的实际字节数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 值为 null。</exception>
      <exception cref="T:System.InvalidOperationException">当前节点不支持 <see cref="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)" />。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">缓冲区中的索引或者索引与计数之和大于分配的缓冲区大小。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.XmlReader" /> 实现不支持此方法。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>异步读取内容并返回 BinHex 解码的二进制字节。</summary>
      <returns>写入缓冲区的字节数。</returns>
      <param name="buffer">结果文本复制到的缓冲区。此值不能为 null。</param>
      <param name="index">缓冲区中的偏移，从这个位置开始将结果复制到缓冲区中。</param>
      <param name="count">要复制到缓冲区的最大字节数。此方法返回复制的实际字节数。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.InvalidOperationException">调用<see cref="T:System.Xml.XmlReader" /> 异步方法，而无需为 true 设置 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 标志。在这种情况下，将使用消息 “如果要使用异步方法，设置 XmlReaderSettings.Async 为 true”引发<see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBoolean">
      <summary>将当前位置的文本内容作为 Boolean 读取。</summary>
      <returns>作为 <see cref="T:System.Boolean" /> 对象的文本内容。</returns>
      <exception cref="T:System.InvalidCastException">试图进行的强制转换无效。</exception>
      <exception cref="T:System.FormatException">该字符串格式无效。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDateTimeOffset">
      <summary>将当前位置的文本内容作为 <see cref="T:System.DateTimeOffset" /> 对象读取。</summary>
      <returns>作为 <see cref="T:System.DateTimeOffset" /> 对象的文本内容。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDecimal">
      <summary>将当前位置的文本内容作为 <see cref="T:System.Decimal" /> 对象读取。</summary>
      <returns>作为 <see cref="T:System.Decimal" /> 对象的当前位置的文本内容。</returns>
      <exception cref="T:System.InvalidCastException">试图进行的强制转换无效。</exception>
      <exception cref="T:System.FormatException">该字符串格式无效。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDouble">
      <summary>将当前位置的文本内容作为双精度浮点数读取。</summary>
      <returns>作为双精度浮点数的文本内容。</returns>
      <exception cref="T:System.InvalidCastException">试图进行的强制转换无效。</exception>
      <exception cref="T:System.FormatException">该字符串格式无效。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsFloat">
      <summary>将当前位置的文本内容作为单精度浮点数读取。</summary>
      <returns>作为单精度浮点数的当前位置的文本内容。</returns>
      <exception cref="T:System.InvalidCastException">试图进行的强制转换无效。</exception>
      <exception cref="T:System.FormatException">该字符串格式无效。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsInt">
      <summary>将当前位置的文本内容作为 32 位有符号整数读取。</summary>
      <returns>作为 32 位有符号整数的文本内容。</returns>
      <exception cref="T:System.InvalidCastException">试图进行的强制转换无效。</exception>
      <exception cref="T:System.FormatException">该字符串格式无效。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsLong">
      <summary>将当前位置的文本内容作为 64 位有符号整数读取。</summary>
      <returns>作为 64 位有符号整数的文本内容。</returns>
      <exception cref="T:System.InvalidCastException">试图进行的强制转换无效。</exception>
      <exception cref="T:System.FormatException">该字符串格式无效。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObject">
      <summary>将当前位置的文本内容作为 <see cref="T:System.Object" /> 读取。</summary>
      <returns>作为最适当的公共语言运行时 (CLR) 对象的文本内容。</returns>
      <exception cref="T:System.InvalidCastException">试图进行的强制转换无效。</exception>
      <exception cref="T:System.FormatException">该字符串格式无效。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObjectAsync">
      <summary>将当前位置的文本内容作为 <see cref="T:System.Object" /> 对象异步读取。</summary>
      <returns>作为最适当的公共语言运行时 (CLR) 对象的文本内容。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.InvalidOperationException">调用<see cref="T:System.Xml.XmlReader" /> 异步方法，而无需为 true 设置 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 标志。在这种情况下，将使用消息 “如果要使用异步方法，设置 XmlReaderSettings.Async 为 true”引发<see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsString">
      <summary>将当前位置的文本内容作为 <see cref="T:System.String" /> 对象读取。</summary>
      <returns>作为 <see cref="T:System.String" /> 对象的文本内容。</returns>
      <exception cref="T:System.InvalidCastException">试图进行的强制转换无效。</exception>
      <exception cref="T:System.FormatException">该字符串格式无效。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsStringAsync">
      <summary>将当前位置的文本内容作为 <see cref="T:System.String" /> 对象异步读取。</summary>
      <returns>作为 <see cref="T:System.String" /> 对象的文本内容。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.InvalidOperationException">调用<see cref="T:System.Xml.XmlReader" /> 异步方法，而无需为 true 设置 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 标志。在这种情况下，将使用消息 “如果要使用异步方法，设置 XmlReaderSettings.Async 为 true”引发<see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>将元素内容作为请求类型读取。</summary>
      <returns>转换为请求类型的对象的元素内容。</returns>
      <param name="returnType">要返回的值的类型。“注意”   随着 .NET Framework 3.5 的发布，<paramref name="returnType" /> 参数的值现在可以是 <see cref="T:System.DateTimeOffset" /> 类型。</param>
      <param name="namespaceResolver">一个 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 对象，用于解析与类型转换有关的任何命名空间前缀。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 未定位在元素上。</exception>
      <exception cref="T:System.Xml.XmlException">当前元素包含子元素。- 或 -无法将元素内容转换成请求的类型。</exception>
      <exception cref="T:System.ArgumentNullException">使用 null 参数调用此方法。</exception>
      <exception cref="T:System.OverflowException">读取 Decimal.MaxValue。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver,System.String,System.String)">
      <summary>检查指定的本地名称和命名空间 URI 与当前元素的本地名称和命名空间 URI 是否匹配，然后将元素内容作为请求类型读取。</summary>
      <returns>转换为请求类型的对象的元素内容。</returns>
      <param name="returnType">要返回的值的类型。“注意”   随着 .NET Framework 3.5 的发布，<paramref name="returnType" /> 参数的值现在可以是 <see cref="T:System.DateTimeOffset" /> 类型。</param>
      <param name="namespaceResolver">一个 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 对象，用于解析与类型转换有关的任何命名空间前缀。</param>
      <param name="localName">元素的本地名称。</param>
      <param name="namespaceURI">元素的命名空间 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 未定位在元素上。</exception>
      <exception cref="T:System.Xml.XmlException">当前元素包含子元素。- 或 -无法将元素内容转换成请求的类型。</exception>
      <exception cref="T:System.ArgumentNullException">使用 null 参数调用此方法。</exception>
      <exception cref="T:System.ArgumentException">指定的本地名称和命名空间 URI 与所读取的当前元素的本地名称和命名空间 URI 不匹配。</exception>
      <exception cref="T:System.OverflowException">读取 Decimal.MaxValue。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>将元素内容作为请求类型异步读取。</summary>
      <returns>转换为请求类型的对象的元素内容。</returns>
      <param name="returnType">要返回的值的类型。</param>
      <param name="namespaceResolver">一个 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 对象，用于解析与类型转换有关的任何命名空间前缀。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.InvalidOperationException">调用<see cref="T:System.Xml.XmlReader" /> 异步方法，而无需为 true 设置 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 标志。在这种情况下，将使用消息 “如果要使用异步方法，设置 XmlReaderSettings.Async 为 true”引发<see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>读取元素并对 Base64 内容进行解码。</summary>
      <returns>写入缓冲区的字节数。</returns>
      <param name="buffer">结果文本复制到的缓冲区。此值不能为 null。</param>
      <param name="index">缓冲区中的偏移，从这个位置开始将结果复制到缓冲区中。</param>
      <param name="count">要复制到缓冲区的最大字节数。此方法返回复制的实际字节数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 值为 null。</exception>
      <exception cref="T:System.InvalidOperationException">当前节点不是元素节点。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">缓冲区中的索引或者索引与计数之和大于分配的缓冲区大小。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.XmlReader" /> 实现不支持此方法。</exception>
      <exception cref="T:System.Xml.XmlException">该元素包含混合内容。</exception>
      <exception cref="T:System.FormatException">无法将内容转换成请求的类型。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>异步读取元素并对 Base64 内容进行解码。</summary>
      <returns>写入缓冲区的字节数。</returns>
      <param name="buffer">结果文本复制到的缓冲区。此值不能为 null。</param>
      <param name="index">缓冲区中的偏移，从这个位置开始将结果复制到缓冲区中。</param>
      <param name="count">要复制到缓冲区的最大字节数。此方法返回复制的实际字节数。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.InvalidOperationException">调用<see cref="T:System.Xml.XmlReader" /> 异步方法，而无需为 true 设置 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 标志。在这种情况下，将使用消息 “如果要使用异步方法，设置 XmlReaderSettings.Async 为 true”引发<see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>读取元素并对 BinHex 内容进行解码。</summary>
      <returns>写入缓冲区的字节数。</returns>
      <param name="buffer">结果文本复制到的缓冲区。此值不能为 null。</param>
      <param name="index">缓冲区中的偏移，从这个位置开始将结果复制到缓冲区中。</param>
      <param name="count">要复制到缓冲区的最大字节数。此方法返回复制的实际字节数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 值为 null。</exception>
      <exception cref="T:System.InvalidOperationException">当前节点不是元素节点。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">缓冲区中的索引或者索引与计数之和大于分配的缓冲区大小。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.XmlReader" /> 实现不支持此方法。</exception>
      <exception cref="T:System.Xml.XmlException">该元素包含混合内容。</exception>
      <exception cref="T:System.FormatException">无法将内容转换成请求的类型。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>异步读取元素并对 BinHex 内容进行解码。</summary>
      <returns>写入缓冲区的字节数。</returns>
      <param name="buffer">结果文本复制到的缓冲区。此值不能为 null。</param>
      <param name="index">缓冲区中的偏移，从这个位置开始将结果复制到缓冲区中。</param>
      <param name="count">要复制到缓冲区的最大字节数。此方法返回复制的实际字节数。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.InvalidOperationException">调用<see cref="T:System.Xml.XmlReader" /> 异步方法，而无需为 true 设置 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 标志。在这种情况下，将使用消息 “如果要使用异步方法，设置 XmlReaderSettings.Async 为 true”引发<see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean">
      <summary>读取当前元素并将内容作为 <see cref="T:System.Boolean" /> 对象返回。</summary>
      <returns>作为 <see cref="T:System.Boolean" /> 对象的元素内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 未定位在元素上。</exception>
      <exception cref="T:System.Xml.XmlException">当前元素包含子元素。- 或 -无法将元素内容转换为 <see cref="T:System.Boolean" /> 对象。</exception>
      <exception cref="T:System.ArgumentNullException">使用 null 参数调用此方法。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean(System.String,System.String)">
      <summary>检查指定的本地名称和命名空间 URI 与当前元素的本地名称和命名空间 URI 是否匹配，然后读取当前元素，并将内容作为 <see cref="T:System.Boolean" /> 对象返回。</summary>
      <returns>作为 <see cref="T:System.Boolean" /> 对象的元素内容。</returns>
      <param name="localName">元素的本地名称。</param>
      <param name="namespaceURI">元素的命名空间 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 未定位在元素上。</exception>
      <exception cref="T:System.Xml.XmlException">当前元素包含子元素。- 或 -无法将元素内容转换成请求的类型。</exception>
      <exception cref="T:System.ArgumentNullException">使用 null 参数调用此方法。</exception>
      <exception cref="T:System.ArgumentException">指定的本地名称和命名空间 URI 与所读取的当前元素的本地名称和命名空间 URI 不匹配。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal">
      <summary>读取当前元素并将内容作为 <see cref="T:System.Decimal" /> 对象返回。</summary>
      <returns>作为 <see cref="T:System.Decimal" /> 对象的元素内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 未定位在元素上。</exception>
      <exception cref="T:System.Xml.XmlException">当前元素包含子元素。- 或 -无法将元素内容转换为 <see cref="T:System.Decimal" />。</exception>
      <exception cref="T:System.ArgumentNullException">使用 null 参数调用此方法。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal(System.String,System.String)">
      <summary>检查指定的本地名称和命名空间 URI 与当前元素的本地名称和命名空间 URI 是否匹配，然后读取当前元素，并将内容作为 <see cref="T:System.Decimal" /> 对象返回。</summary>
      <returns>作为 <see cref="T:System.Decimal" /> 对象的元素内容。</returns>
      <param name="localName">元素的本地名称。</param>
      <param name="namespaceURI">元素的命名空间 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 未定位在元素上。</exception>
      <exception cref="T:System.Xml.XmlException">当前元素包含子元素。- 或 -无法将元素内容转换为 <see cref="T:System.Decimal" />。</exception>
      <exception cref="T:System.ArgumentNullException">使用 null 参数调用此方法。</exception>
      <exception cref="T:System.ArgumentException">指定的本地名称和命名空间 URI 与所读取的当前元素的本地名称和命名空间 URI 不匹配。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble">
      <summary>读取当前元素并将内容作为双精度浮点数返回。</summary>
      <returns>作为双精度浮点数的元素内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 未定位在元素上。</exception>
      <exception cref="T:System.Xml.XmlException">当前元素包含子元素。- 或 -无法将元素内容转换为双精度浮点数。</exception>
      <exception cref="T:System.ArgumentNullException">使用 null 参数调用此方法。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble(System.String,System.String)">
      <summary>检查指定的本地名称和命名空间 URI 与当前元素的本地名称和命名空间 URI 是否匹配，然后读取当前元素，并将内容作为双精度浮点数返回。</summary>
      <returns>作为双精度浮点数的元素内容。</returns>
      <param name="localName">元素的本地名称。</param>
      <param name="namespaceURI">元素的命名空间 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 未定位在元素上。</exception>
      <exception cref="T:System.Xml.XmlException">当前元素包含子元素。- 或 -无法将元素内容转换成请求的类型。</exception>
      <exception cref="T:System.ArgumentNullException">使用 null 参数调用此方法。</exception>
      <exception cref="T:System.ArgumentException">指定的本地名称和命名空间 URI 与所读取的当前元素的本地名称和命名空间 URI 不匹配。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat">
      <summary>读取当前元素并将内容作为单精度浮点数返回。</summary>
      <returns>作为单精度浮点数的元素内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 未定位在元素上。</exception>
      <exception cref="T:System.Xml.XmlException">当前元素包含子元素。- 或 -元素内容不能转换为单精度浮点数。</exception>
      <exception cref="T:System.ArgumentNullException">使用 null 参数调用此方法。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat(System.String,System.String)">
      <summary>检查指定的本地名称和命名空间 URI 与当前元素的本地名称和命名空间 URI 是否匹配，然后读取当前元素，并将内容作为单精度浮点数返回。</summary>
      <returns>作为单精度浮点数的元素内容。</returns>
      <param name="localName">元素的本地名称。</param>
      <param name="namespaceURI">元素的命名空间 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 未定位在元素上。</exception>
      <exception cref="T:System.Xml.XmlException">当前元素包含子元素。- 或 -元素内容不能转换为单精度浮点数。</exception>
      <exception cref="T:System.ArgumentNullException">使用 null 参数调用此方法。</exception>
      <exception cref="T:System.ArgumentException">指定的本地名称和命名空间 URI 与所读取的当前元素的本地名称和命名空间 URI 不匹配。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt">
      <summary>读取当前元素并将内容作为 32 位有符号整数返回。</summary>
      <returns>作为 32 位有符号整数的元素内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 未定位在元素上。</exception>
      <exception cref="T:System.Xml.XmlException">当前元素包含子元素。- 或 -无法将元素内容转换为 32 位有符号整数。</exception>
      <exception cref="T:System.ArgumentNullException">使用 null 参数调用此方法。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt(System.String,System.String)">
      <summary>检查指定的本地名称和命名空间 URI 与当前元素的本地名称和命名空间 URI 是否匹配，然后读取当前元素，并将内容作为 32 位有符号整数返回。</summary>
      <returns>作为 32 位有符号整数的元素内容。</returns>
      <param name="localName">元素的本地名称。</param>
      <param name="namespaceURI">元素的命名空间 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 未定位在元素上。</exception>
      <exception cref="T:System.Xml.XmlException">当前元素包含子元素。- 或 -无法将元素内容转换为 32 位有符号整数。</exception>
      <exception cref="T:System.ArgumentNullException">使用 null 参数调用此方法。</exception>
      <exception cref="T:System.ArgumentException">指定的本地名称和命名空间 URI 与所读取的当前元素的本地名称和命名空间 URI 不匹配。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong">
      <summary>读取当前元素并将内容作为 64 位有符号整数返回。</summary>
      <returns>作为 64 位有符号整数的元素内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 未定位在元素上。</exception>
      <exception cref="T:System.Xml.XmlException">当前元素包含子元素。- 或 -无法将元素内容转换为 64 位有符号整数。</exception>
      <exception cref="T:System.ArgumentNullException">使用 null 参数调用此方法。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong(System.String,System.String)">
      <summary>检查指定的本地名称和命名空间 URI 与当前元素的本地名称和命名空间 URI 是否匹配，然后读取当前元素，并将内容作为 64 位有符号整数返回。</summary>
      <returns>作为 64 位有符号整数的元素内容。</returns>
      <param name="localName">元素的本地名称。</param>
      <param name="namespaceURI">元素的命名空间 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 未定位在元素上。</exception>
      <exception cref="T:System.Xml.XmlException">当前元素包含子元素。- 或 -无法将元素内容转换为 64 位有符号整数。</exception>
      <exception cref="T:System.ArgumentNullException">使用 null 参数调用此方法。</exception>
      <exception cref="T:System.ArgumentException">指定的本地名称和命名空间 URI 与所读取的当前元素的本地名称和命名空间 URI 不匹配。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject">
      <summary>读取当前元素并将内容作为 <see cref="T:System.Object" /> 返回。</summary>
      <returns>一个最适当类型的装箱的公共语言运行时 (CLR) 对象。<see cref="P:System.Xml.XmlReader.ValueType" /> 属性确定了适当的 CLR 类型。如果将内容类型化为列表类型，则此方法返回一个适当类型的装箱对象的数组。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 未定位在元素上。</exception>
      <exception cref="T:System.Xml.XmlException">当前元素包含子元素。- 或 -无法将元素内容转换成请求的类型</exception>
      <exception cref="T:System.ArgumentNullException">使用 null 参数调用此方法。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject(System.String,System.String)">
      <summary>检查指定的本地名称和命名空间 URI 与当前元素的本地名称和命名空间 URI 是否匹配，然后读取当前元素，并将内容作为 <see cref="T:System.Object" /> 返回。</summary>
      <returns>一个最适当类型的装箱的公共语言运行时 (CLR) 对象。<see cref="P:System.Xml.XmlReader.ValueType" /> 属性确定了适当的 CLR 类型。如果将内容类型化为列表类型，则此方法返回一个适当类型的装箱对象的数组。</returns>
      <param name="localName">元素的本地名称。</param>
      <param name="namespaceURI">元素的命名空间 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 未定位在元素上。</exception>
      <exception cref="T:System.Xml.XmlException">当前元素包含子元素。- 或 -无法将元素内容转换成请求的类型。</exception>
      <exception cref="T:System.ArgumentNullException">使用 null 参数调用此方法。</exception>
      <exception cref="T:System.ArgumentException">指定的本地名称和命名空间 URI 与所读取的当前元素的本地名称和命名空间 URI 不匹配。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObjectAsync">
      <summary>异步读取当前元素并将内容作为 <see cref="T:System.Object" /> 返回。</summary>
      <returns>一个最适当类型的装箱的公共语言运行时 (CLR) 对象。<see cref="P:System.Xml.XmlReader.ValueType" /> 属性确定了适当的 CLR 类型。如果将内容类型化为列表类型，则此方法返回一个适当类型的装箱对象的数组。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.InvalidOperationException">调用<see cref="T:System.Xml.XmlReader" /> 异步方法，而无需为 true 设置 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 标志。在这种情况下，将使用消息 “如果要使用异步方法，设置 XmlReaderSettings.Async 为 true”引发<see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString">
      <summary>读取当前元素并将内容作为 <see cref="T:System.String" /> 对象返回。</summary>
      <returns>作为 <see cref="T:System.String" /> 对象的元素内容。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 未定位在元素上。</exception>
      <exception cref="T:System.Xml.XmlException">当前元素包含子元素。- 或 -无法将元素内容转换为 <see cref="T:System.String" /> 对象。</exception>
      <exception cref="T:System.ArgumentNullException">使用 null 参数调用此方法。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString(System.String,System.String)">
      <summary>检查指定的本地名称和命名空间 URI 与当前元素的本地名称和命名空间 URI 是否匹配，然后读取当前元素，并将内容作为 <see cref="T:System.String" /> 对象返回。</summary>
      <returns>作为 <see cref="T:System.String" /> 对象的元素内容。</returns>
      <param name="localName">元素的本地名称。</param>
      <param name="namespaceURI">元素的命名空间 URI。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xml.XmlReader" /> 未定位在元素上。</exception>
      <exception cref="T:System.Xml.XmlException">当前元素包含子元素。- 或 -无法将元素内容转换为 <see cref="T:System.String" /> 对象。</exception>
      <exception cref="T:System.ArgumentNullException">使用 null 参数调用此方法。</exception>
      <exception cref="T:System.ArgumentException">指定的本地名称和命名空间 URI 与所读取的当前元素的本地名称和命名空间 URI 不匹配。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsStringAsync">
      <summary>异步读取当前元素并将内容作为 <see cref="T:System.String" /> 对象返回。</summary>
      <returns>作为 <see cref="T:System.String" /> 对象的元素内容。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.InvalidOperationException">调用<see cref="T:System.Xml.XmlReader" /> 异步方法，而无需为 true 设置 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 标志。在这种情况下，将使用消息 “如果要使用异步方法，设置 XmlReaderSettings.Async 为 true”引发<see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadEndElement">
      <summary>检查当前内容节点是否为结束标记并将读取器推进到下一个节点。</summary>
      <exception cref="T:System.Xml.XmlException">当前节点不是一个结束标记，或者如果在输入流中遇到不正确的 XML。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXml">
      <summary>当在派生类中被重写时，将所有内容（包括标记）当做字符串读取。</summary>
      <returns>当前节点中的所有 XML 内容（包括标记）。如果当前节点没有任何子级，则返回空字符串。如果当前节点既非元素，也非属性，则返回空字符串。</returns>
      <exception cref="T:System.Xml.XmlException">XML 的格式不良，或分析 XML 时出错。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXmlAsync">
      <summary>异步读取所有内容，包括作为字符串的标记。</summary>
      <returns>当前节点中的所有 XML 内容（包括标记）。如果当前节点没有任何子级，则返回空字符串。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.InvalidOperationException">调用<see cref="T:System.Xml.XmlReader" /> 异步方法，而无需为 true 设置 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 标志。在这种情况下，将使用消息 “如果要使用异步方法，设置 XmlReaderSettings.Async 为 true”引发<see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXml">
      <summary>当在派生类中被重写时，读取表示该节点和所有它的子级的内容（包括标记）。</summary>
      <returns>如果读取器定位在元素或属性节点上，此方法将返回当前节点及其所有子级的所有 XML 内容（包括标记）；否则返回空字符串。</returns>
      <exception cref="T:System.Xml.XmlException">XML 的格式不良，或分析 XML 时出错。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXmlAsync">
      <summary>异步读取包含该节点和所有它的子级的内容（包括标记）。</summary>
      <returns>如果读取器定位在元素或属性节点上，此方法将返回当前节点及其所有子级的所有 XML 内容（包括标记）；否则返回空字符串。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.InvalidOperationException">调用<see cref="T:System.Xml.XmlReader" /> 异步方法，而无需为 true 设置 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 标志。在这种情况下，将使用消息 “如果要使用异步方法，设置 XmlReaderSettings.Async 为 true”引发<see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement">
      <summary>检查当前节点是否为元素并将读取器推进到下一个节点。</summary>
      <exception cref="T:System.Xml.XmlException">在输入流中遇到不正确的 XML。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String)">
      <summary>检查当前内容节点是否为具有给定 <see cref="P:System.Xml.XmlReader.Name" /> 的元素并将读取器推进到下一个节点。</summary>
      <param name="name">元素的限定名。</param>
      <exception cref="T:System.Xml.XmlException">在输入流中遇到不正确的 XML。- 或 -元素的 <see cref="P:System.Xml.XmlReader.Name" /> 不匹配给定的 <paramref name="name" />。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String,System.String)">
      <summary>检查当前内容节点是否为具有给定 <see cref="P:System.Xml.XmlReader.LocalName" /> 和 <see cref="P:System.Xml.XmlReader.NamespaceURI" /> 的元素并将读取器推进到下一个节点。</summary>
      <param name="localname">元素的本地名称。</param>
      <param name="ns">元素的命名空间 URI。</param>
      <exception cref="T:System.Xml.XmlException">在输入流中遇到不正确的 XML。- 或 -所找到元素的 <see cref="P:System.Xml.XmlReader.LocalName" /> 和 <see cref="P:System.Xml.XmlReader.NamespaceURI" /> 属性与给定的参数不匹配。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ReadState">
      <summary>当在派生类中被重写时，获取读取器的状态。</summary>
      <returns>指定读取器的状态的枚举值之一。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadSubtree">
      <summary>返回新的 XmlReader 实例，此实例可用于读取当前节点及其所有子节点。</summary>
      <returns>新的 XML 读取器实例设置为<see cref="F:System.Xml.ReadState.Initial" />。调用<see cref="M:System.Xml.XmlReader.Read" />方法将新的读取器定位在调用之前的当前节点上<see cref="M:System.Xml.XmlReader.ReadSubtree" />方法。</returns>
      <exception cref="T:System.InvalidOperationException">XML 读取器不被定位在元素上，当调用此方法。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String)">
      <summary>让 <see cref="T:System.Xml.XmlReader" /> 前进到下一个具有指定限定名的子代元素。</summary>
      <returns>如果找到匹配的子代元素，则为 true；否则为 false。如果未找到匹配的子元素，<see cref="T:System.Xml.XmlReader" /> 将定位在元素的结束标记（<see cref="P:System.Xml.XmlReader.NodeType" /> 为 XmlNodeType.EndElement）上。如果调用 <see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String)" /> 时没有将 <see cref="T:System.Xml.XmlReader" /> 定位在某个元素上，则此方法返回 false 且 <see cref="T:System.Xml.XmlReader" /> 的位置保持不变。</returns>
      <param name="name">要移动到的元素的限定名。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.ArgumentException"> 参数是空字符串。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)">
      <summary>让 <see cref="T:System.Xml.XmlReader" /> 前进到下一个具有指定的本地名称和命名空间 URI 的子代元素。</summary>
      <returns>如果找到匹配的子代元素，则为 true；否则为 false。如果未找到匹配的子元素，<see cref="T:System.Xml.XmlReader" /> 将定位在元素的结束标记（<see cref="P:System.Xml.XmlReader.NodeType" /> 为 XmlNodeType.EndElement）上。If the <see cref="T:System.Xml.XmlReader" /> is not positioned on an element when <see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)" /> was called, this method returns false and the position of the <see cref="T:System.Xml.XmlReader" /> is not changed.</returns>
      <param name="localName">要移动到的元素的本地名称。</param>
      <param name="namespaceURI">要移动到的元素的命名空间 URI。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.ArgumentNullException">两个参数值为 null。 </exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String)">
      <summary>一直读取，直到找到具有指定限定名的元素。</summary>
      <returns>如果找到匹配的元素，则为 true；否则为 false 且 <see cref="T:System.Xml.XmlReader" /> 位于文件的末尾。</returns>
      <param name="name">元素的限定名。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.ArgumentException"> 参数是空字符串。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String,System.String)">
      <summary>一直读取，直到找到具有指定的本地名称和命名空间 URI 的元素。</summary>
      <returns>如果找到匹配的元素，则为 true；否则为 false 且 <see cref="T:System.Xml.XmlReader" /> 位于文件的末尾。</returns>
      <param name="localName">元素的本地名称。</param>
      <param name="namespaceURI">元素的命名空间 URI。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.ArgumentNullException">两个参数值为 null。 </exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String)">
      <summary>让 XmlReader 前进到下一个具有指定限定名的同级元素。</summary>
      <returns>如果找到匹配的同级元素，则为 true；否则为 false。如果没有找到匹配的同级元素，XmlReader 会定位在父元素的结束标记（<see cref="P:System.Xml.XmlReader.NodeType" /> 为 XmlNodeType.EndElement）上。</returns>
      <param name="name">要移动到的同级元素的限定名。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.ArgumentException"> 参数是空字符串。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String,System.String)">
      <summary>让 XmlReader 前进到下一个具有指定的本地名称和命名空间 URI 的同级元素。</summary>
      <returns>如果找到匹配的同级元素，则为 true；否则，为 false。如果没有找到匹配的同级元素，XmlReader 会定位在父元素的结束标记（<see cref="P:System.Xml.XmlReader.NodeType" /> 为 XmlNodeType.EndElement）上。</returns>
      <param name="localName">要移动到的同级元素的本地名称。</param>
      <param name="namespaceURI">你希望移动到的同级元素的命名空间 URI。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.ArgumentNullException">两个参数值为 null。 </exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)">
      <summary>读取嵌入在 XML 文档中的大量文本流。</summary>
      <returns>读取到缓冲区中的字符数。如果不再有文本内容，则返回值零。</returns>
      <param name="buffer">作为文本内容写入到的缓冲区的字符数组。此值不能为 null。</param>
      <param name="index">缓冲区中的偏移量，<see cref="T:System.Xml.XmlReader" /> 可以从这个位置开始复制结果。</param>
      <param name="count">要复制到缓冲区中的最大字符数。此方法返回复制的实际字符数。</param>
      <exception cref="T:System.InvalidOperationException">当前节点没有值（<see cref="P:System.Xml.XmlReader.HasValue" /> 为 false）。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 值为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">缓冲区中的索引或者索引与计数之和大于分配的缓冲区大小。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.XmlReader" /> 实现不支持此方法。</exception>
      <exception cref="T:System.Xml.XmlException">XML 数据不是格式良好的。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunkAsync(System.Char[],System.Int32,System.Int32)">
      <summary>异步读取嵌入在 XML 文档中的大量文本流。</summary>
      <returns>读取到缓冲区中的字符数。如果不再有文本内容，则返回值零。</returns>
      <param name="buffer">作为文本内容写入到的缓冲区的字符数组。此值不能为 null。</param>
      <param name="index">缓冲区中的偏移量，<see cref="T:System.Xml.XmlReader" /> 可以从这个位置开始复制结果。</param>
      <param name="count">要复制到缓冲区中的最大字符数。此方法返回复制的实际字符数。</param>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.InvalidOperationException">调用<see cref="T:System.Xml.XmlReader" /> 异步方法，而无需为 true 设置 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 标志。在这种情况下，将使用消息 “如果要使用异步方法，设置 XmlReaderSettings.Async 为 true”引发<see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ResolveEntity">
      <summary>当在派生类中被重写时，解析 EntityReference 节点的实体引用。</summary>
      <exception cref="T:System.InvalidOperationException">读取器未定位在 EntityReference 节点上；该读取器的实现不能解析实体（<see cref="P:System.Xml.XmlReader.CanResolveEntity" /> 返回 false）。</exception>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Settings">
      <summary>Gets the <see cref="T:System.Xml.XmlReaderSettings" /> object used to create this <see cref="T:System.Xml.XmlReader" /> instance.</summary>
      <returns>用于创建此读取器实例的 <see cref="T:System.Xml.XmlReaderSettings" /> 对象。如果此读取器不是使用 <see cref="Overload:System.Xml.XmlReader.Create" /> 方法创建的，则此属性返回 null。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Skip">
      <summary>跳过当前节点的子级。</summary>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="M:System.Xml.XmlReader.SkipAsync">
      <summary>异步跳过当前节点的子级。</summary>
      <returns>当前节点。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
      <exception cref="T:System.InvalidOperationException">调用<see cref="T:System.Xml.XmlReader" /> 异步方法，而无需为 true 设置 <see cref="P:System.Xml.XmlReaderSettings.Async" /> 标志。在这种情况下，将使用消息 “如果要使用异步方法，设置 XmlReaderSettings.Async 为 true”引发<see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Value">
      <summary>当在派生类中被重写时，获取当前节点的文本值。</summary>
      <returns>返回的值取决于节点的 <see cref="P:System.Xml.XmlReader.NodeType" />。下表列出具有要返回的值的节点类型。所有其他节点类型返回 String.Empty。节点类型值 Attribute属性的值。 CDATACDATA 节的内容。 Comment注释的内容。 DocumentType内部子集。 ProcessingInstruction全部内容（不包括指令目标）。 SignificantWhitespace混合内容模型中标记之间的空白。 Text文本节点的内容。 Whitespace标记之间的空白。 XmlDeclaration声明的内容。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ValueType">
      <summary>获取当前节点的公共语言运行时 (CLR) 类型。</summary>
      <returns>与节点的类型化值对应的 CLR 类型。默认值为 System.String。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlLang">
      <summary>当在派生类中被重写时，获取当前的 xml:lang 范围。</summary>
      <returns>当前的 xml:lang 范围。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlSpace">
      <summary>当在派生类中被重写时，获取当前的 xml:space 范围。</summary>
      <returns>
        <see cref="T:System.Xml.XmlSpace" /> 值之一。如果不存在任何 xml:space 范围，则该属性默认值为 XmlSpace.None。</returns>
      <exception cref="T:System.InvalidOperationException">在前一个异步操作完成前调用 <see cref="T:System.Xml.XmlReader" /> 方法。在这种情况下，将通过消息“异步操作已过程中”引发 <see cref="T:System.InvalidOperationException" />。</exception>
    </member>
    <member name="T:System.Xml.XmlReaderSettings">
      <summary>指定在由 <see cref="Overload:System.Xml.XmlReader.Create" /> 方法创建的 <see cref="T:System.Xml.XmlReader" /> 对象上支持的一组功能。</summary>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.#ctor">
      <summary>初始化 <see cref="T:System.Xml.XmlReaderSettings" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.Async">
      <summary>获取或设置是否可对特定 <see cref="T:System.Xml.XmlReader" /> 实例使用异步 <see cref="T:System.Xml.XmlReader" /> 方法。</summary>
      <returns>则可以使用异步方法，则为 true；否则，为 false。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CheckCharacters">
      <summary>获取或设置一个值，该值指示是否进行字符检查。</summary>
      <returns>如果进行字符检查，则为 true；否则为 false。默认值为 true。说明如果 <see cref="T:System.Xml.XmlReader" /> 处理文本数据，则无论属性如何设置，读取器将总是检查 XML 名称和文本内容是否有效。将 <see cref="P:System.Xml.XmlReaderSettings.CheckCharacters" /> 设置为 false 会禁用对字符实体引用的字符检查。</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Clone">
      <summary>创建 <see cref="T:System.Xml.XmlReaderSettings" /> 实例的副本。</summary>
      <returns>克隆的 <see cref="T:System.Xml.XmlReaderSettings" /> 对象。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CloseInput">
      <summary>获取或设置一个值，该值指示当读取器关闭时，是否应关闭基础流或 <see cref="T:System.IO.TextReader" />。</summary>
      <returns>如果当读取器关闭时基础流或 <see cref="T:System.IO.TextReader" /> 也应关闭，则为 true；否则为 false。默认值为 false。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.ConformanceLevel">
      <summary>获取或设置 <see cref="T:System.Xml.XmlReader" /> 将遵循的一致性级别。</summary>
      <returns>指定一致性级别（XML 读取器将强制该级别）的枚举值之一。默认值为 <see cref="F:System.Xml.ConformanceLevel.Document" />。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.DtdProcessing">
      <summary>获取或设置确定 DTD 的处理的值。</summary>
      <returns>确定 DTD 的处理的枚举值之一。默认值为 <see cref="F:System.Xml.DtdProcessing.Prohibit" />。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreComments">
      <summary>获取或设置一个值，该值指示是否忽略注释。</summary>
      <returns>如果忽略注释，则为 true；否则为 false。默认值为 false。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreProcessingInstructions">
      <summary>获取或设置一个值，该值指示是否忽略处理指令。</summary>
      <returns>如果忽略处理指令，则为 true；否则为 false。默认值为 false。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreWhitespace">
      <summary>获取或设置一个值，该值指示是否忽略无关紧要的空白区域。</summary>
      <returns>如果忽略空白，则为 true；否则为 false。默认值为 false。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LineNumberOffset">
      <summary>获取或设置 <see cref="T:System.Xml.XmlReader" /> 对象的行号偏移量。</summary>
      <returns>行号偏移量。默认值为 0。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LinePositionOffset">
      <summary>获取或设置 <see cref="T:System.Xml.XmlReader" /> 对象的行位置偏移量。</summary>
      <returns>行位置偏移量。默认值为 0。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersFromEntities">
      <summary>获取或设置一个值，该值指示文档中允许扩展实体产生的最大字符数。</summary>
      <returns>扩展实体中允许的最大字符数。默认值为 0。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersInDocument">
      <summary>获取或设置一个值，该值指明 XML 文档中所允许的最大字符数。零 (0) 值表示对 XML 文档的大小没有限制。非零值指定最大大小（以字符数计）。</summary>
      <returns>XML 文档中所允许的最大字符数。默认值为 0。</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.NameTable">
      <summary>获取或设置用于原子化字符串比较的 <see cref="T:System.Xml.XmlNameTable" />。</summary>
      <returns>
        <see cref="T:System.Xml.XmlNameTable" />，它存储使用此 <see cref="T:System.Xml.XmlReaderSettings" /> 对象创建的所有 <see cref="T:System.Xml.XmlReader" /> 实例使用的所有原子化字符串。默认值为 null。如果该值为null，创建的 <see cref="T:System.Xml.XmlReader" /> 实例将使用新的空 <see cref="T:System.Xml.NameTable" />。</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Reset">
      <summary>将设置类的成员重置为各自的默认值。</summary>
    </member>
    <member name="T:System.Xml.XmlSpace">
      <summary>指定当前 xml:space 范围。</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Default">
      <summary>xml:space 范围等于 default。</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.None">
      <summary>没有 xml:space 范围。</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Preserve">
      <summary>xml:space 范围等于 preserve。</summary>
    </member>
    <member name="T:System.Xml.XmlWriter">
      <summary>表示一个写入器，该写入器提供一种快速、非缓存和只进方式以生成包含 XML 数据的流或文件。</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.#ctor">
      <summary>初始化 <see cref="T:System.Xml.XmlWriter" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream)">
      <summary>使用指定的流创建新的 <see cref="T:System.Xml.XmlWriter" /> 实例。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 对象。</returns>
      <param name="output">要对其写入的流。<see cref="T:System.Xml.XmlWriter" /> 写入 XML 1.0 文本语法并将其追加到指定的流中。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream,System.Xml.XmlWriterSettings)">
      <summary>使用流和 <see cref="T:System.Xml.XmlWriterSettings" /> 对象创建新的 <see cref="T:System.Xml.XmlWriter" /> 实例。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 对象。</returns>
      <param name="output">要对其写入的流。<see cref="T:System.Xml.XmlWriter" /> 写入 XML 1.0 文本语法并将其追加到指定的流中。</param>
      <param name="settings">用于配置新 <see cref="T:System.Xml.XmlWriter" /> 实例的 <see cref="T:System.Xml.XmlWriterSettings" /> 对象。如果这是 null，则使用具有默认设置的 <see cref="T:System.Xml.XmlWriterSettings" />。如果将 <see cref="T:System.Xml.XmlWriter" /> 用于 <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> 方法，则应使用 <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> 属性获取具有正确设置的 <see cref="T:System.Xml.XmlWriterSettings" /> 对象。这样可以确保所创建的 <see cref="T:System.Xml.XmlWriter" /> 对象的输出设置是正确的。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter)">
      <summary>使用指定的 <see cref="T:System.IO.TextWriter" /> 创建新的 <see cref="T:System.Xml.XmlWriter" /> 实例。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 对象。</returns>
      <param name="output">计划写入的 <see cref="T:System.IO.TextWriter" />。<see cref="T:System.Xml.XmlWriter" /> 写入 XML 1.0 文本语法，并将该语法追加到指定 <see cref="T:System.IO.TextWriter" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter,System.Xml.XmlWriterSettings)">
      <summary>使用 <see cref="T:System.IO.TextWriter" /> 和 <see cref="T:System.Xml.XmlWriterSettings" /> 对象创建新的 <see cref="T:System.Xml.XmlWriter" /> 实例。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 对象。</returns>
      <param name="output">计划写入的 <see cref="T:System.IO.TextWriter" />。<see cref="T:System.Xml.XmlWriter" /> 写入 XML 1.0 文本语法，并将该语法追加到指定 <see cref="T:System.IO.TextWriter" />。</param>
      <param name="settings">用于配置新 <see cref="T:System.Xml.XmlWriter" /> 实例的 <see cref="T:System.Xml.XmlWriterSettings" /> 对象。如果这是 null，则使用具有默认设置的 <see cref="T:System.Xml.XmlWriterSettings" />。如果将 <see cref="T:System.Xml.XmlWriter" /> 用于 <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> 方法，则应使用 <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> 属性获取具有正确设置的 <see cref="T:System.Xml.XmlWriterSettings" /> 对象。这样可以确保所创建的 <see cref="T:System.Xml.XmlWriter" /> 对象的输出设置是正确的。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder)">
      <summary>使用指定的 <see cref="T:System.Text.StringBuilder" /> 创建一个新的 <see cref="T:System.Xml.XmlWriter" /> 实例。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 对象。</returns>
      <param name="output">要写入的 <see cref="T:System.Text.StringBuilder" />。由 <see cref="T:System.Xml.XmlWriter" /> 写入的内容被追加到 <see cref="T:System.Text.StringBuilder" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder,System.Xml.XmlWriterSettings)">
      <summary>使用 <see cref="T:System.Text.StringBuilder" /> 和 <see cref="T:System.Xml.XmlWriterSettings" /> 对象创建一个新的 <see cref="T:System.Xml.XmlWriter" /> 实例。</summary>
      <returns>
        <see cref="T:System.Xml.XmlWriter" /> 对象。</returns>
      <param name="output">要写入的 <see cref="T:System.Text.StringBuilder" />。由 <see cref="T:System.Xml.XmlWriter" /> 写入的内容被追加到 <see cref="T:System.Text.StringBuilder" />。</param>
      <param name="settings">用于配置新 <see cref="T:System.Xml.XmlWriter" /> 实例的 <see cref="T:System.Xml.XmlWriterSettings" /> 对象。如果这是 null，则使用具有默认设置的 <see cref="T:System.Xml.XmlWriterSettings" />。如果将 <see cref="T:System.Xml.XmlWriter" /> 用于 <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> 方法，则应使用 <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> 属性获取具有正确设置的 <see cref="T:System.Xml.XmlWriterSettings" /> 对象。这样可以确保所创建的 <see cref="T:System.Xml.XmlWriter" /> 对象的输出设置是正确的。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter)">
      <summary>使用指定的 <see cref="T:System.Xml.XmlWriter" /> 对象创建新的 <see cref="T:System.Xml.XmlWriter" /> 实例。</summary>
      <returns>一个 <see cref="T:System.Xml.XmlWriter" /> 对象，是指定的 <see cref="T:System.Xml.XmlWriter" /> 对象周围的包装。</returns>
      <param name="output">要用作基础编写器的 <see cref="T:System.Xml.XmlWriter" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter,System.Xml.XmlWriterSettings)">
      <summary>使用指定的 <see cref="T:System.Xml.XmlWriter" /> 和 <see cref="T:System.Xml.XmlWriterSettings" /> 对象创建新的 <see cref="T:System.Xml.XmlWriter" /> 实例。</summary>
      <returns>一个 <see cref="T:System.Xml.XmlWriter" /> 对象，是指定的 <see cref="T:System.Xml.XmlWriter" /> 对象周围的包装。</returns>
      <param name="output">要用作基础编写器的 <see cref="T:System.Xml.XmlWriter" /> 对象。</param>
      <param name="settings">用于配置新 <see cref="T:System.Xml.XmlWriter" /> 实例的 <see cref="T:System.Xml.XmlWriterSettings" /> 对象。如果这是 null，则使用具有默认设置的 <see cref="T:System.Xml.XmlWriterSettings" />。如果将 <see cref="T:System.Xml.XmlWriter" /> 用于 <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> 方法，则应使用 <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> 属性获取具有正确设置的 <see cref="T:System.Xml.XmlWriterSettings" /> 对象。这样可以确保所创建的 <see cref="T:System.Xml.XmlWriter" /> 对象的输出设置是正确的。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose">
      <summary>释放由 <see cref="T:System.Xml.XmlWriter" /> 类的当前实例占用的所有资源。</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose(System.Boolean)">
      <summary>释放由 <see cref="T:System.Xml.XmlWriter" /> 占用的非托管资源，还可以另外再释放托管资源。</summary>
      <param name="disposing">true 表示释放托管资源和非托管资源；false 表示仅释放非托管资源。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Flush">
      <summary>当在派生类中被重写时，将缓冲区中的所有内容刷新到基础流，并同时刷新基础流。</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.FlushAsync">
      <summary>将缓冲区中的所有内容异步刷新到基础流，并同时刷新基础流。</summary>
      <returns>表示 Flush 异步操作的任务。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.LookupPrefix(System.String)">
      <summary>当在派生类中被重写时，返回在当前命名空间范围中为该命名空间 URI 定义的最近的前缀。</summary>
      <returns>匹配的前缀；如果未在当前范围内找到匹配的命名空间 URI，则为 null。</returns>
      <param name="ns">要查找其前缀的命名空间 URI。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="ns" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.Settings">
      <summary>获取用于创建此 <see cref="T:System.Xml.XmlWriter" /> 实例的 <see cref="T:System.Xml.XmlWriterSettings" /> 对象。</summary>
      <returns>用于创建此写入器实例的 <see cref="T:System.Xml.XmlWriterSettings" /> 对象。如果此写入器不是使用 <see cref="Overload:System.Xml.XmlWriter.Create" /> 方法创建的，则此属性返回 null。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributes(System.Xml.XmlReader,System.Boolean)">
      <summary>当在派生类中被重写时，写出在 <see cref="T:System.Xml.XmlReader" /> 的当前位置找到的所有属性。</summary>
      <param name="reader">从其中复制属性的 XmlReader。</param>
      <param name="defattr">若要从 XmlReader 中复制默认属性，则为 true；否则为 false。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null. </exception>
      <exception cref="T:System.Xml.XmlException">The reader is not positioned on an element, attribute or XmlDeclaration node. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributesAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>异步写出在 <see cref="T:System.Xml.XmlReader" /> 的当前位置找到的所有属性。</summary>
      <returns>表示 WriteAttributes 异步操作的任务。</returns>
      <param name="reader">从其中复制属性的 XmlReader。</param>
      <param name="defattr">若要从 XmlReader 中复制默认属性，则为 true；否则为 false。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String)">
      <summary>当在派生类中被重写时，写出具有指定的本地名称和值的属性。</summary>
      <param name="localName">属性的本地名称。</param>
      <param name="value">属性的值。</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String)">
      <summary>当在派生类中被重写时，写入具有指定的本地名称、命名空间 URI 和值的属性。</summary>
      <param name="localName">属性的本地名称。</param>
      <param name="ns">与属性关联的命名空间 URI。</param>
      <param name="value">属性的值。</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String,System.String)">
      <summary>当在派生类中被重写时，写出具有指定的前缀、本地名称、命名空间 URI 和值的属性。</summary>
      <param name="prefix">属性的命名空间前缀。</param>
      <param name="localName">属性的本地名称。</param>
      <param name="ns">属性的命名空间 URI。</param>
      <param name="value">属性的值。</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.Xml.XmlException">The <paramref name="localName" /> or <paramref name="ns" /> is null. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeStringAsync(System.String,System.String,System.String,System.String)">
      <summary>异步写出具有指定前缀、本地名称、命名空间 URI 和值的属性。</summary>
      <returns>表示 WriteAttributeString 异步操作的任务。</returns>
      <param name="prefix">属性的命名空间前缀。</param>
      <param name="localName">属性的本地名称。</param>
      <param name="ns">属性的命名空间 URI。</param>
      <param name="value">属性的值。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>当在派生类中被重写时，将指定的二进制字节编码为 Base64 并写出结果文本。</summary>
      <param name="buffer">要进行编码的字节数组。</param>
      <param name="index">缓冲区中指示要写入字节的起始位置的位置。</param>
      <param name="count">要写入的字节数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>将指定的二进制字节异步编码为 Base64 并写出结果文本。</summary>
      <returns>表示 WriteBase64 异步操作的任务。</returns>
      <param name="buffer">要进行编码的字节数组。</param>
      <param name="index">缓冲区中指示要写入字节的起始位置的位置。</param>
      <param name="count">要写入的字节数。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>当在派生类中被重写时，将指定的二进制字节编码为 BinHex 并写出结果文本。</summary>
      <param name="buffer">要进行编码的字节数组。</param>
      <param name="index">缓冲区中指示要写入字节的起始位置的位置。</param>
      <param name="count">要写入的字节数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>将指定的二进制字节异步编码为 BinHex 并写出结果文本。</summary>
      <returns>表示 WriteBinHex 异步操作的任务。</returns>
      <param name="buffer">要进行编码的字节数组。</param>
      <param name="index">缓冲区中指示要写入字节的起始位置的位置。</param>
      <param name="count">要写入的字节数。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCData(System.String)">
      <summary>当在派生类中被重写时，写出包含指定文本的 &lt;![CDATA[...]]&gt; 块。</summary>
      <param name="text">要放置在 CDATA 块中的文本。</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCDataAsync(System.String)">
      <summary>异步写出一个包含指定文本的 &lt;![CDATA[...]]&gt; 块。</summary>
      <returns>表示 WriteCData 异步操作的任务。</returns>
      <param name="text">要放置在 CDATA 块中的文本。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntity(System.Char)">
      <summary>当在派生类中被重写时，为指定的 Unicode 字符值强制生成字符实体。</summary>
      <param name="ch">为其生成字符实体的 Unicode 字符。</param>
      <exception cref="T:System.ArgumentException">The character is in the surrogate pair character range, 0xd800 - 0xdfff.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntityAsync(System.Char)">
      <summary>为指定的 Unicode 字符值异步强制生成字符实体。</summary>
      <returns>表示 WriteCharEntity 异步操作的任务。</returns>
      <param name="ch">为其生成字符实体的 Unicode 字符。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteChars(System.Char[],System.Int32,System.Int32)">
      <summary>当在派生类中被重写时，以每次一个缓冲区的方式写入文本。</summary>
      <param name="buffer">包含要写入的文本的字符数组。</param>
      <param name="index">缓冲区中指示要写入文本的起始位置的位置。</param>
      <param name="count">要写入的字符数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />; the call results in surrogate pair characters being split or an invalid surrogate pair being written.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="buffer" /> parameter value is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharsAsync(System.Char[],System.Int32,System.Int32)">
      <summary>以每次一个缓冲区的方式异步写入文本。</summary>
      <returns>表示 WriteChars 异步操作的任务。</returns>
      <param name="buffer">包含要写入的文本的字符数组。</param>
      <param name="index">缓冲区中指示要写入文本的起始位置的位置。</param>
      <param name="count">要写入的字符数。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteComment(System.String)">
      <summary>当在派生类中被重写时，写出包含指定文本的注释 &lt;!--...--&gt;。</summary>
      <param name="text">要放在注释内的文本。</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well-formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCommentAsync(System.String)">
      <summary>异步写出一个包含指定文本的注释 &lt;!--...--&gt;。</summary>
      <returns>表示 WriteComment 异步操作的任务。</returns>
      <param name="text">要放在注释内的文本。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocType(System.String,System.String,System.String,System.String)">
      <summary>当在派生类中被重写时，写出具有指定名称和可选属性的 DOCTYPE 声明。</summary>
      <param name="name">DOCTYPE 的名称。它必须是非空的。</param>
      <param name="pubid">如果非 null，则它还将写入 PUBLIC "pubid" "sysid"，这里的 <paramref name="pubid" /> 和 <paramref name="sysid" /> 用给定参数的值替换。</param>
      <param name="sysid">如果 <paramref name="pubid" /> 为 null 而 <paramref name="sysid" /> 非 null，则它将写入 SYSTEM "sysid"，这里的 <paramref name="sysid" /> 用此参数的值替换。</param>
      <param name="subset">如果非 null，则它写入 [subset]，其中 subset 替换为此参数的值。</param>
      <exception cref="T:System.InvalidOperationException">This method was called outside the prolog (after the root element). </exception>
      <exception cref="T:System.ArgumentException">The value for <paramref name="name" /> would result in invalid XML.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocTypeAsync(System.String,System.String,System.String,System.String)">
      <summary>异步写入具有指定名称和可选属性的 DOCTYPE 声明。</summary>
      <returns>表示 WriteDocType 异步操作的任务。</returns>
      <param name="name">DOCTYPE 的名称。它必须是非空的。</param>
      <param name="pubid">如果非 null，则它还将写入 PUBLIC "pubid" "sysid"，这里的 <paramref name="pubid" /> 和 <paramref name="sysid" /> 用给定参数的值替换。</param>
      <param name="sysid">如果 <paramref name="pubid" /> 为 null 而 <paramref name="sysid" /> 非 null，则它将写入 SYSTEM "sysid"，这里的 <paramref name="sysid" /> 用此参数的值替换。</param>
      <param name="subset">如果非 null，则它写入 [subset]，其中 subset 替换为此参数的值。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String)">
      <summary>写入具有指定的本地名称和值的元素。</summary>
      <param name="localName">元素的本地名称。</param>
      <param name="value">元素的值。</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String)">
      <summary>写入具有指定的本地名称、命名空间 URI 和值的元素。</summary>
      <param name="localName">元素的本地名称。</param>
      <param name="ns">与元素关联的命名空间 URI。</param>
      <param name="value">元素的值。</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String,System.String)">
      <summary>写入具有指定的前缀、本地名称、命名空间 URI 和值的元素。</summary>
      <param name="prefix">元素的前缀。</param>
      <param name="localName">元素的本地名称。</param>
      <param name="ns">元素的命名空间 URI。</param>
      <param name="value">元素的值。</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementStringAsync(System.String,System.String,System.String,System.String)">
      <summary>异步写入具有指定的前缀、本地名称、命名空间 URI 和值的元素。</summary>
      <returns>表示 WriteElementString 异步操作的任务。</returns>
      <param name="prefix">元素的前缀。</param>
      <param name="localName">元素的本地名称。</param>
      <param name="ns">元素的命名空间 URI。</param>
      <param name="value">元素的值。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttribute">
      <summary>当在派生类中被重写时，关闭上一个 <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" /> 调用。</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttributeAsync">
      <summary>异步关闭前一个 <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" /> 调用。</summary>
      <returns>表示 WriteEndAttribute 异步操作的任务。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocument">
      <summary>当在派生类中被重写时，关闭任何打开的元素或属性并将写入器重新设置为起始状态。</summary>
      <exception cref="T:System.ArgumentException">The XML document is invalid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocumentAsync">
      <summary>异步关闭任何打开的元素或属性并将写入器重新设置为起始状态。</summary>
      <returns>表示 WriteEndDocument 异步操作的任务。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElement">
      <summary>当在派生类中被重写时，关闭一个元素并弹出相应的命名空间范围。</summary>
      <exception cref="T:System.InvalidOperationException">This results in an invalid XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElementAsync">
      <summary>异步关闭一个元素并弹出相应的命名空间范围。</summary>
      <returns>表示 WriteEndElement 异步操作的任务。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRef(System.String)">
      <summary>当在派生类中被重写时，按 &amp;name; 写出实体引用。</summary>
      <param name="name">实体引用的名称。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRefAsync(System.String)">
      <summary>按 &amp;name; 异步写出实体引用。</summary>
      <returns>表示 WriteEntityRef 异步操作的任务。</returns>
      <param name="name">实体引用的名称。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElement">
      <summary>当在派生类中被重写时，关闭一个元素并弹出相应的命名空间范围。</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElementAsync">
      <summary>异步关闭一个元素并弹出相应的命名空间范围。</summary>
      <returns>表示 WriteFullEndElement 异步操作的任务。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteName(System.String)">
      <summary>当在派生类中被重写时，写出指定的名称，确保它是符合 W3C XML 1.0 建议 (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name) 的有效名称。</summary>
      <param name="name">要写入的名称。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid XML name; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNameAsync(System.String)">
      <summary>异步写出指定的名称，确保它是符合 W3C XML 1.0 建议 (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name) 的有效名称。</summary>
      <returns>表示 WriteName 异步操作的任务。</returns>
      <param name="name">要写入的名称。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmToken(System.String)">
      <summary>当在派生类中被重写时，写出指定的名称，确保它是符合 W3C XML 1.0 建议 (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name) 的有效 NmToken。</summary>
      <param name="name">要写入的名称。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid NmToken; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmTokenAsync(System.String)">
      <summary>异步写出指定的名称，确保它是符合 W3C XML 1.0 建议 (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name) 的有效 NmToken。</summary>
      <returns>表示 WriteNmToken 异步操作的任务。</returns>
      <param name="name">要写入的名称。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNode(System.Xml.XmlReader,System.Boolean)">
      <summary>当在派生类中被重写时，将全部内容从读取器复制到写入器并将读取器移动到下一个同级的开始位置。</summary>
      <param name="reader">要从其进行读取的 <see cref="T:System.Xml.XmlReader" />。</param>
      <param name="defattr">若要从 XmlReader 中复制默认属性，则为 true；否则为 false。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> contains invalid characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNodeAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>将所有内容从读取器异步复制到写入器并将读取器移动到下一个同级的开头。</summary>
      <returns>表示 WriteNode 异步操作的任务。</returns>
      <param name="reader">要从其进行读取的 <see cref="T:System.Xml.XmlReader" />。</param>
      <param name="defattr">若要从 XmlReader 中复制默认属性，则为 true；否则为 false。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstruction(System.String,System.String)">
      <summary>当在派生类中被重写时，写出在名称和文本之间带有空格的处理指令，如下所示：&lt;?name text?&gt;。</summary>
      <param name="name">处理指令的名称。</param>
      <param name="text">要包括在处理指令中的文本。</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.<paramref name="name" /> is either null or String.Empty.This method is being used to create an XML declaration after <see cref="M:System.Xml.XmlWriter.WriteStartDocument" /> has already been called. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstructionAsync(System.String,System.String)">
      <summary>异步写出在名称和文本之间有空格的处理指令，如下所示：&lt;?name text?&gt;。</summary>
      <returns>表示 WriteProcessingInstruction 异步操作的任务。</returns>
      <param name="name">处理指令的名称。</param>
      <param name="text">要包括在处理指令中的文本。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedName(System.String,System.String)">
      <summary>当在派生类中被重写时，写出命名空间限定的名称。此方法查找位于给定命名空间范围内的前缀。</summary>
      <param name="localName">要写入的本地名称。</param>
      <param name="ns">名称的命名空间 URI。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="localName" /> is either null or String.Empty.<paramref name="localName" /> is not a valid name. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedNameAsync(System.String,System.String)">
      <summary>异步写出命名空间限定的名称。此方法查找位于给定命名空间范围内的前缀。</summary>
      <returns>表示 WriteQualifiedName 异步操作的任务。</returns>
      <param name="localName">要写入的本地名称。</param>
      <param name="ns">名称的命名空间 URI。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.Char[],System.Int32,System.Int32)">
      <summary>当在派生类中被重写时，从字符缓冲区手动写入原始标记。</summary>
      <param name="buffer">包含要写入的文本的字符数组。</param>
      <param name="index">缓冲区中的位置，指示要写入文本的起始位置。</param>
      <param name="count">要写入的字符数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.String)">
      <summary>当在派生类中被重写时，从字符串手动写入原始标记。</summary>
      <param name="data">包含要写入的文本的字符串。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="data" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.Char[],System.Int32,System.Int32)">
      <summary>从字符缓冲区手动异步写入原始标记。</summary>
      <returns>表示 WriteRaw 异步操作的任务。</returns>
      <param name="buffer">包含要写入的文本的字符数组。</param>
      <param name="index">缓冲区中的位置，指示要写入文本的起始位置。</param>
      <param name="count">要写入的字符数。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.String)">
      <summary>从字符串手动异步写入原始标记。</summary>
      <returns>表示 WriteRaw 异步操作的任务。</returns>
      <param name="data">包含要写入的文本的字符串。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String)">
      <summary>写入具有指定本地名称的属性的开头。</summary>
      <param name="localName">属性的本地名称。</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)">
      <summary>写入具有指定本地名称和命名空间 URI 的属性的开头。</summary>
      <param name="localName">属性的本地名称。</param>
      <param name="ns">属性的命名空间 URI。</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String,System.String)">
      <summary>当在派生类中被重写时，写入具有指定的前缀、本地名称和命名空间 URI 的属性的开头。</summary>
      <param name="prefix">属性的命名空间前缀。</param>
      <param name="localName">属性的本地名称。</param>
      <param name="ns">属性的命名空间 URI。</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttributeAsync(System.String,System.String,System.String)">
      <summary>异步写入具有指定前缀、本地名称和命名空间 URI 的属性的开头。</summary>
      <returns>表示 WriteStartAttribute 异步操作的任务。</returns>
      <param name="prefix">属性的命名空间前缀。</param>
      <param name="localName">属性的本地名称。</param>
      <param name="ns">属性的命名空间 URI。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument">
      <summary>当在派生类中被重写时，写入版本为“1.0”的 XML 声明。</summary>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument(System.Boolean)">
      <summary>当在派生类中被重写时，写入版本为“1.0”的 XML 声明和独立的属性。</summary>
      <param name="standalone">如果为 true，则它将写入"standalone=yes"；如果为 false，则它将写入"standalone=no"。</param>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync">
      <summary>异步写入版本为“1.0”的 XML 声明。</summary>
      <returns>表示 WriteStartDocument 异步操作的任务。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync(System.Boolean)">
      <summary>异步写入版本为“1.0”的 XML 声明和独立的属性。</summary>
      <returns>表示 WriteStartDocument 异步操作的任务。</returns>
      <param name="standalone">如果为 true，则它将写入"standalone=yes"；如果为 false，则它将写入"standalone=no"。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String)">
      <summary>当在派生类中被重写时，写出具有指定的本地名称的开始标记。</summary>
      <param name="localName">元素的本地名称。</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String)">
      <summary>当在派生类中被重写时，写入指定的开始标记并将其与给定的命名空间关联起来。</summary>
      <param name="localName">元素的本地名称。</param>
      <param name="ns">与元素关联的命名空间 URI。如果此命名空间已在范围中并具有关联的前缀，则写入器也将自动写入该前缀。</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String,System.String)">
      <summary>当在派生类中被重写时，写入指定的开始标记并将其与给定的命名空间和前缀关联起来。</summary>
      <param name="prefix">元素的命名空间前缀。</param>
      <param name="localName">元素的本地名称。</param>
      <param name="ns">与元素关联的命名空间 URI。</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElementAsync(System.String,System.String,System.String)">
      <summary>异步写入指定的开始标记并将其与给定的命名空间和前缀关联起来。</summary>
      <returns>表示 WriteStartElement 异步操作的任务。</returns>
      <param name="prefix">元素的命名空间前缀。</param>
      <param name="localName">元素的本地名称。</param>
      <param name="ns">与元素关联的命名空间 URI。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.WriteState">
      <summary>当在派生类中被重写时，获取写入器的状态。</summary>
      <returns>
        <see cref="T:System.Xml.WriteState" /> 值之一。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteString(System.String)">
      <summary>当在派生类中被重写时，写入给定的文本内容。</summary>
      <param name="text">要写入的文本。</param>
      <exception cref="T:System.ArgumentException">The text string contains an invalid surrogate pair.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStringAsync(System.String)">
      <summary>异步写入给定的文本内容。</summary>
      <returns>表示 WriteString 异步操作的任务。</returns>
      <param name="text">要写入的文本。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntity(System.Char,System.Char)">
      <summary>当在派生类中被重写时，为代理项字符对生成并写入代理项字符实体。</summary>
      <param name="lowChar">低代理项。它必须是介于 0xDC00 和 0xDFFF 之间的值。</param>
      <param name="highChar">高代理项。它必须是介于 0xD800 和 0xDBFF 之间的值。</param>
      <exception cref="T:System.ArgumentException">An invalid surrogate character pair was passed.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntityAsync(System.Char,System.Char)">
      <summary>为代理项字符对异步生成并写入代理项字符实体。</summary>
      <returns>表示 WriteSurrogateCharEntity 异步操作的任务。</returns>
      <param name="lowChar">低代理项。它必须是介于 0xDC00 和 0xDFFF 之间的值。</param>
      <param name="highChar">高代理项。它必须是介于 0xD800 和 0xDBFF 之间的值。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Boolean)">
      <summary>写入一个 <see cref="T:System.Boolean" /> 值。</summary>
      <param name="value">要编写的 <see cref="T:System.Boolean" /> 值。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.DateTimeOffset)">
      <summary>写入一个 <see cref="T:System.DateTimeOffset" /> 值。</summary>
      <param name="value">要编写的 <see cref="T:System.DateTimeOffset" /> 值。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Decimal)">
      <summary>写入一个 <see cref="T:System.Decimal" /> 值。</summary>
      <param name="value">要编写的 <see cref="T:System.Decimal" /> 值。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Double)">
      <summary>写入一个 <see cref="T:System.Double" /> 值。</summary>
      <param name="value">要编写的 <see cref="T:System.Double" /> 值。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int32)">
      <summary>写入一个 <see cref="T:System.Int32" /> 值。</summary>
      <param name="value">要编写的 <see cref="T:System.Int32" /> 值。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int64)">
      <summary>写入一个 <see cref="T:System.Int64" /> 值。</summary>
      <param name="value">要编写的 <see cref="T:System.Int64" /> 值。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Object)">
      <summary>写入对象值。</summary>
      <param name="value">要写入的对象值。注意   随着 .NET Framework 3.5 的发布，该方法接受将 <see cref="T:System.DateTimeOffset" /> 作为参数。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Single)">
      <summary>写入一个单精度浮点数。</summary>
      <param name="value">要写入的单精度浮点数。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.String)">
      <summary>写入一个 <see cref="T:System.String" /> 值。</summary>
      <param name="value">要编写的 <see cref="T:System.String" /> 值。</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespace(System.String)">
      <summary>当在派生类中被重写时，写出给定的空白区域。</summary>
      <param name="ws">空格字符的字符串。</param>
      <exception cref="T:System.ArgumentException">The string contains non-white space characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespaceAsync(System.String)">
      <summary>异步写出给定的空白区域。</summary>
      <returns>表示 WriteWhitespace 异步操作的任务。</returns>
      <param name="ws">空格字符的字符串。</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlLang">
      <summary>当在派生类中被重写时，获取当前的 xml:lang 范围。</summary>
      <returns>当前的 xml:lang 范围。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlSpace">
      <summary>当在派生类中被重写时，获取表示当前 xml:space 范围的 <see cref="T:System.Xml.XmlSpace" />。</summary>
      <returns>一个表示当前 xml:space 范围的 XmlSpace。值含义 None如果不存在 xml:space 范围，则此为默认值。Default当前范围为 xml:space="default"。Preserve当前范围为 xml:space=“preserve”。</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="T:System.Xml.XmlWriterSettings">
      <summary>指定在 <see cref="Overload:System.Xml.XmlWriter.Create" /> 方法创建的 <see cref="T:System.Xml.XmlWriter" /> 对象上支持的一组功能。</summary>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.#ctor">
      <summary>初始化 <see cref="T:System.Xml.XmlWriterSettings" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Async">
      <summary>获取或设置一个值，该值指示是否可对特定的 <see cref="T:System.Xml.XmlWriter" /> 实例使用异步 <see cref="T:System.Xml.XmlWriter" /> 方法。</summary>
      <returns>如果可以使用异步方法，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CheckCharacters">
      <summary>获取或设置一个值，该值指示是否应检查 XML 写入器以确保文档中的所有字符都符合 W3C XML 1.0 建议 中的“2.2 字符”部分。</summary>
      <returns>如果要检查字符，则为 true，否则为 false。默认值为 true。</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Clone">
      <summary>创建 <see cref="T:System.Xml.XmlWriterSettings" /> 实例的副本。</summary>
      <returns>克隆的 <see cref="T:System.Xml.XmlWriterSettings" /> 对象。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CloseOutput">
      <summary>获取或设置一个值，该值指示调用 <see cref="M:System.Xml.XmlWriter.Close" /> 时 <see cref="T:System.Xml.XmlWriter" /> 是否也应该关闭基础流或 <see cref="T:System.IO.TextWriter" />。</summary>
      <returns>如果要关闭基础流或 <see cref="T:System.IO.TextWriter" />，则为 true；否则为 false。默认值为 false。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.ConformanceLevel">
      <summary>获取或设置的 XML 写入器检查 XML 输出的一致性级别。</summary>
      <returns>指定一致性级别（文档、片段或自动检测）的枚举值之一。默认值为 <see cref="F:System.Xml.ConformanceLevel.Document" />。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Encoding">
      <summary>获取或设置要使用的文本编码的类型。</summary>
      <returns>要使用的文本编码。默认值为 Encoding.UTF8。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Indent">
      <summary>获取或设置指示是否缩进元素的值。</summary>
      <returns>如果在新行上写入单独的元素并将其缩进，则为 true；否则为 false。默认值为 false。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.IndentChars">
      <summary>获取或设置缩进时要使用的字符串。在 <see cref="P:System.Xml.XmlWriterSettings.Indent" /> 属性设置为 true 时使用此设置。</summary>
      <returns>缩进时要使用的字符串。它可以设置为任何字符串值。但是，为了确保 XML 有效，应该只指定有效的空格字符，例如空格、制表符、回车符或换行符。默认值为两个空格。</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.IndentChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NamespaceHandling">
      <summary>获取或设置一个值，该值指示在写入 XML 内容时 <see cref="T:System.Xml.XmlWriter" /> 是否应移除重复的命名空间声明。写入器的默认行为是输出写入器的命名空间解析程序中存在的所有命名空间声明。</summary>
      <returns>用于指定是否移除 <see cref="T:System.Xml.XmlWriter" /> 中重复的命名空间声明的 <see cref="T:System.Xml.NamespaceHandling" /> 枚举。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineChars">
      <summary>获取或设置要用于换行符的字符串。</summary>
      <returns>要用于换行符的字符串。它可以设置为任何字符串值。但是，为了确保 XML 有效，应该只指定有效的空格字符，例如空格、制表符、回车符或换行符。默认值为 \r\n（回车符、新行）。</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineHandling">
      <summary>获取或设置一个值，该值指示是否将输出中的换行符规范化。</summary>
      <returns>
        <see cref="T:System.Xml.NewLineHandling" /> 值之一。默认值为 <see cref="F:System.Xml.NewLineHandling.Replace" />。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineOnAttributes">
      <summary>获取或设置一个值，该值指示是否在新行上写入属性。</summary>
      <returns>如果要在单独的行上写入属性，则为 true；否则为 false。默认值为 false。说明<see cref="P:System.Xml.XmlWriterSettings.Indent" /> 属性值为 false时此设置无效。<see cref="P:System.Xml.XmlWriterSettings.NewLineOnAttributes" /> 设置为 true时，每个属性都会预先挂起一个新行和一个额外的缩进级别。</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.OmitXmlDeclaration">
      <summary>获取或设置一个值，该值指示是否省略 XML 声明。</summary>
      <returns>如果省略 XML 声明，则为 true；否则为 false。默认值为 false，即写入 XML 声明。</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Reset">
      <summary>将设置类的成员重置为各自的默认值。</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.WriteEndDocumentOnClose">
      <summary>获取或设置一个值，该值指示在调用 <see cref="M:System.Xml.XmlWriter.Close" /> 方法时 <see cref="T:System.Xml.XmlWriter" /> 是否会向所有未关闭的元素标记添加结束标记。</summary>
      <returns>如果将结束所有未关闭元素标记，则为 true；否则为 false。默认值为 true。</returns>
    </member>
    <member name="T:System.Xml.Schema.XmlSchema">
      <summary>一个 XML 架构的内存表示形式，它按照万维网联合会 (W3C) XML 架构第 1 部分进行指定：“结构” 和 XML 架构第 2 部分：“数据类型” 规范。</summary>
    </member>
    <member name="T:System.Xml.Schema.XmlSchemaForm">
      <summary>指示是否需要用命名空间前缀限定属性或元素。</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.None">
      <summary>架构中不指定元素和属性窗体。</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Qualified">
      <summary>必须用命名空间前缀限定元素和属性。</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Unqualified">
      <summary>不要求用命名空间前缀限定元素和属性。</summary>
    </member>
    <member name="T:System.Xml.Serialization.IXmlSerializable">
      <summary>提供面向 XML 序列化和反序列化的自定义格式。</summary>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.GetSchema">
      <summary>此方法是保留方法，请不要使用。在实现 IXmlSerializable 接口时，应从此方法返回 null（在 Visual Basic 中为 Nothing），如果需要指定自定义架构，应向该类应用 <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" />。</summary>
      <returns>
        <see cref="T:System.Xml.Schema.XmlSchema" />，描述由 <see cref="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)" /> 方法产生并由 <see cref="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)" /> 方法使用的对象的 XML 表示形式。</returns>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)">
      <summary>从对象的 XML 表示形式生成该对象。</summary>
      <param name="reader">对象从中进行反序列化的 <see cref="T:System.Xml.XmlReader" /> 流。</param>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)">
      <summary>将对象转换为其 XML 表示形式。</summary>
      <param name="writer">对象要序列化为的 <see cref="T:System.Xml.XmlWriter" /> 流。</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlSchemaProviderAttribute">
      <summary>应用于某个类型时，存储返回 XML 架构的该类型静态方法的名称和控制该类型序列化的 <see cref="T:System.Xml.XmlQualifiedName" />（对于匿名类型，为 <see cref="T:System.Xml.Schema.XmlSchemaType" />）。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaProviderAttribute.#ctor(System.String)">
      <summary>采用提供类型的 XML 架构的静态方法的名称，初始化 <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" /> 类的新实例。</summary>
      <param name="methodName">必须实现的静态方法的名称。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.IsAny">
      <summary>获取或设置一个值，该值确定目标类是通配符，还是该类的架构仅包含一个 xs:any 元素。</summary>
      <returns>如果该类是通配符，或者该架构仅包含 xs:any 元素，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.MethodName">
      <summary>获取提供类型的 XML 架构及其 XML 架构数据类型名称的静态方法的名称。</summary>
      <returns>XML 基础结构调用来返回 XML 架构的方法的名称。</returns>
    </member>
  </members>
</doc>