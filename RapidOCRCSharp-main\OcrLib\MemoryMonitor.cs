using System;
using System.Diagnostics;
using System.Runtime;
using System.Text;

namespace OcrLiteLib
{
    /// <summary>
    /// 内存监控工具，用于跟踪内存使用情况和潜在泄漏
    /// </summary>
    public static class MemoryMonitor
    {
        private static long _initialMemory = 0;
        private static long _peakMemory = 0;
        private static int _gcCollections = 0;

        /// <summary>
        /// 开始监控内存
        /// </summary>
        public static void StartMonitoring()
        {
            // 强制垃圾回收以获得准确的基线
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            _initialMemory = GC.GetTotalMemory(false);
            _peakMemory = _initialMemory;
            _gcCollections = GC.CollectionCount(0) + GC.CollectionCount(1) + GC.CollectionCount(2);

            Console.WriteLine($"[MemoryMonitor] 开始监控 - 初始内存: {FormatBytes(_initialMemory)}");
        }

        /// <summary>
        /// 记录当前内存使用情况
        /// </summary>
        /// <param name="checkpoint">检查点名称</param>
        public static void Checkpoint(string checkpoint)
        {
            long currentMemory = GC.GetTotalMemory(false);
            long memoryDiff = currentMemory - _initialMemory;
            
            if (currentMemory > _peakMemory)
            {
                _peakMemory = currentMemory;
            }

            int currentGcCollections = GC.CollectionCount(0) + GC.CollectionCount(1) + GC.CollectionCount(2);
            int gcDiff = currentGcCollections - _gcCollections;

            Console.WriteLine($"[MemoryMonitor] {checkpoint}: " +
                            $"当前内存: {FormatBytes(currentMemory)}, " +
                            $"变化: {FormatBytes(memoryDiff)}, " +
                            $"GC次数: +{gcDiff}");
        }

        /// <summary>
        /// 强制垃圾回收并记录内存使用情况
        /// </summary>
        /// <param name="checkpoint">检查点名称</param>
        public static void CheckpointWithGC(string checkpoint)
        {
            long beforeGC = GC.GetTotalMemory(false);
            
            // 强制垃圾回收
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            
            long afterGC = GC.GetTotalMemory(false);
            long memoryDiff = afterGC - _initialMemory;
            long gcFreed = beforeGC - afterGC;

            if (afterGC > _peakMemory)
            {
                _peakMemory = afterGC;
            }

            Console.WriteLine($"[MemoryMonitor] {checkpoint} (GC后): " +
                            $"当前内存: {FormatBytes(afterGC)}, " +
                            $"变化: {FormatBytes(memoryDiff)}, " +
                            $"GC释放: {FormatBytes(gcFreed)}");
        }

        /// <summary>
        /// 获取详细的内存报告
        /// </summary>
        /// <returns>内存报告字符串</returns>
        public static string GetDetailedReport()
        {
            var sb = new StringBuilder();
            sb.AppendLine("=== 详细内存报告 ===");

            long currentMemory = GC.GetTotalMemory(false);
            long memoryDiff = currentMemory - _initialMemory;

            sb.AppendLine($"托管内存:");
            sb.AppendLine($"  初始: {FormatBytes(_initialMemory)}");
            sb.AppendLine($"  当前: {FormatBytes(currentMemory)}");
            sb.AppendLine($"  峰值: {FormatBytes(_peakMemory)}");
            sb.AppendLine($"  变化: {FormatBytes(memoryDiff)}");

            // GC信息
            sb.AppendLine($"GC统计:");
            sb.AppendLine($"  Gen 0: {GC.CollectionCount(0)}");
            sb.AppendLine($"  Gen 1: {GC.CollectionCount(1)}");
            sb.AppendLine($"  Gen 2: {GC.CollectionCount(2)}");

            // 进程信息（包含非托管内存）
            using (var process = Process.GetCurrentProcess())
            {
                sb.AppendLine($"进程内存（包含非托管）:");
                sb.AppendLine($"  工作集: {FormatBytes(process.WorkingSet64)}");
                sb.AppendLine($"  私有内存: {FormatBytes(process.PrivateMemorySize64)}");
                sb.AppendLine($"  虚拟内存: {FormatBytes(process.VirtualMemorySize64)}");
                sb.AppendLine($"  非托管内存估算: {FormatBytes(process.WorkingSet64 - currentMemory)}");
            }

            return sb.ToString();
        }

        /// <summary>
        /// 检查是否存在内存泄漏
        /// </summary>
        /// <param name="threshold">泄漏阈值（字节）</param>
        /// <returns>是否存在泄漏</returns>
        public static bool CheckForMemoryLeak(long threshold = 50 * 1024 * 1024) // 默认50MB
        {
            // 强制垃圾回收
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            
            long currentMemory = GC.GetTotalMemory(false);
            long memoryDiff = currentMemory - _initialMemory;
            
            bool hasLeak = memoryDiff > threshold;
            
            if (hasLeak)
            {
                Console.WriteLine($"[MemoryMonitor] 警告: 检测到可能的内存泄漏! " +
                                $"内存增长: {FormatBytes(memoryDiff)}, " +
                                $"阈值: {FormatBytes(threshold)}");
            }
            
            return hasLeak;
        }

        /// <summary>
        /// 格式化字节数为可读字符串
        /// </summary>
        /// <param name="bytes">字节数</param>
        /// <returns>格式化后的字符串</returns>
        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }

        /// <summary>
        /// 重置监控状态
        /// </summary>
        public static void Reset()
        {
            _initialMemory = 0;
            _peakMemory = 0;
            _gcCollections = 0;
        }

        /// <summary>
        /// 获取当前内存使用量（字节）
        /// </summary>
        /// <returns>当前内存使用量</returns>
        public static long GetCurrentMemoryUsage()
        {
            return GC.GetTotalMemory(false);
        }

        /// <summary>
        /// 获取内存使用变化量（字节）
        /// </summary>
        /// <returns>内存使用变化量</returns>
        public static long GetMemoryDifference()
        {
            return GC.GetTotalMemory(false) - _initialMemory;
        }
    }

    /// <summary>
    /// 内存监控作用域，用于自动监控代码块的内存使用
    /// </summary>
    public class MemoryScope : IDisposable
    {
        private readonly string _scopeName;
        private readonly long _startMemory;
        private readonly bool _forceGC;

        public MemoryScope(string scopeName, bool forceGC = false)
        {
            _scopeName = scopeName;
            _forceGC = forceGC;
            
            if (_forceGC)
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }
            
            _startMemory = GC.GetTotalMemory(false);
            Console.WriteLine($"[MemoryScope] 进入 {_scopeName} - 内存: {FormatBytes(_startMemory)}");
        }

        public void Dispose()
        {
            if (_forceGC)
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }
            
            long endMemory = GC.GetTotalMemory(false);
            long memoryDiff = endMemory - _startMemory;
            
            Console.WriteLine($"[MemoryScope] 退出 {_scopeName} - " +
                            $"内存: {FormatBytes(endMemory)}, " +
                            $"变化: {FormatBytes(memoryDiff)}");
        }

        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }
    }
}
