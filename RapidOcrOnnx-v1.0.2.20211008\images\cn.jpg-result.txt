=====Input Params=====
numThread(12),padding(0),maxSide<PERSON><PERSON>(1024),boxScoreThresh(0.500000),boxThresh(0.300000),unClipRatio(1.500000),doAngle(1),mostAngle(1)
=====Init Models=====
--- Init DbNet ---
--- Init AngleNet ---
--- Init CrnnNet ---
Init Models Success!
=====Start detect=====
ScaleParam(sw:640,sh:640,dw:640,dh:640,1.000000,1.000000)
---------- step: dbNet getTextBoxes ----------
dbNetTime(167.899500ms)
TextBox[0](+padding)[score(0.636081),[x: 263, y: 17], [x: 470, y: 236], [x: 429, y: 275], [x: 222, y: 56]]
TextBox[1](+padding)[score(0.600436),[x: 384, y: 9], [x: 634, y: 254], [x: 577, y: 312], [x: 327, y: 67]]
TextBox[2](+padding)[score(0.539062),[x: 212, y: 79], [x: 510, y: 416], [x: 446, y: 471], [x: 148, y: 134]]
TextBox[3](+padding)[score(0.585920),[x: 128, y: 134], [x: 426, y: 480], [x: 363, y: 535], [x: 64, y: 189]]
TextBox[4](+padding)[score(0.571278),[x: 58, y: 185], [x: 275, y: 452], [x: 209, y: 506], [x: 0, y: 239]]
TextBox[5](+padding)[score(0.624928),[x: 482, y: 252], [x: 615, y: 385], [x: 576, y: 425], [x: 443, y: 292]]
---------- step: drawTextBoxes ----------
---------- step: angleNet getAngles ----------
angle[0][index(0), score(1.000000), time(3.896500ms)]
angle[1][index(0), score(1.000000), time(3.199500ms)]
angle[2][index(0), score(1.000000), time(3.686100ms)]
angle[3][index(0), score(1.000000), time(2.574400ms)]
angle[4][index(0), score(1.000000), time(5.328500ms)]
angle[5][index(0), score(0.999933), time(3.326800ms)]
---------- step: crnnNet getTextLine ----------
textLine[0](香港深圳抽血，)
textScores[0]{0.999711 ,0.999707 ,0.984979 ,0.971684 ,0.989187 ,0.99744 ,0.986882}
crnnTime[0](141.642000ms)
textLine[1](专业查性别)
textScores[1]{0.99996 ,0.999955 ,0.999385 ,0.999485 ,0.999665}
crnnTime[1](147.566800ms)
textLine[2](专业鉴定B超单)
textScores[2]{0.999927 ,0.999974 ,0.841588 ,0.999893 ,0.999897 ,0.99962 ,0.995834}
crnnTime[2](171.978500ms)
textLine[3](b超仪器查性别)
textScores[3]{0.999664 ,0.999534 ,0.997935 ,0.999813 ,0.998738 ,0.998033 ,0.999486}
crnnTime[3](123.910300ms)
textLine[4](加微信eee)
textScores[4]{0.999843 ,0.99962 ,0.999866 ,0.99854 ,0.99923 ,0.99982}
crnnTime[4](131.678300ms)
textLine[5](可邮寄)
textScores[5]{0.999474 ,0.996648 ,0.99824}
crnnTime[5](90.088800ms)
=====End detect=====
FullDetectTime(352.881100ms)
香港深圳抽血，
专业查性别
专业鉴定B超单
b超仪器查性别
加微信eee
可邮寄

