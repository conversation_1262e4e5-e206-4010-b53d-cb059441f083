{"version": 3, "targets": {".NETFramework,Version=v4.6.1": {"OcrLib/1.0.0": {"type": "project", "compile": {"bin/placeholder/OcrLib.dll": {}}, "runtime": {"bin/placeholder/OcrLib.dll": {}}}}, ".NETFramework,Version=v4.6.1/win-x86": {"OcrLib/1.0.0": {"type": "project", "compile": {"bin/placeholder/OcrLib.dll": {}}, "runtime": {"bin/placeholder/OcrLib.dll": {}}}}}, "libraries": {"OcrLib/1.0.0": {"type": "project", "path": "../OcrLib/OcrLib.csproj", "msbuildProject": "../OcrLib/OcrLib.csproj"}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.6.1": ["OcrLib >= 1.0.0"]}, "packageFolders": {"F:\\HelperToolsSource\\NuGetCache": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrConsoleApp\\OcrConsoleApp.csproj", "projectName": "OcrConsoleApp", "projectPath": "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrConsoleApp\\OcrConsoleApp.csproj", "packagesPath": "F:\\HelperToolsSource\\NuGetCache", "outputPath": "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrConsoleApp\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net461"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net461": {"targetAlias": "net461", "projectReferences": {"D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrLib\\OcrLib.csproj": {"projectPath": "D:\\ProductSoftware\\Occr\\RapidOCRCSharp-main\\OcrLib\\OcrLib.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net461": {"targetAlias": "net461", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}