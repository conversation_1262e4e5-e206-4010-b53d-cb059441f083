﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.ReaderWriter</name>
  </assembly>
  <members>
    <member name="T:System.Xml.ConformanceLevel">
      <summary>Gibt den Umfang der Eingabe- oder Ausgabeüberprüfung an, die von dem <see cref="T:System.Xml.XmlReader" />-Objekt und dem <see cref="T:System.Xml.XmlWriter" />-Objekt ausgeführt wird.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Auto">
      <summary>Das <see cref="T:System.Xml.XmlReader" />-Objekt oder das <see cref="T:System.Xml.XmlWriter" />-Objekt erkennen automatisch, ob eine Dokumentebenen- oder Fragmentebenenprüfung ausgeführt werden soll, und nehmen die entsprechende Prüfung vor.Wenn Sie ein weiteres <see cref="T:System.Xml.XmlReader" />- oder <see cref="T:System.Xml.XmlWriter" />-Objekt umschließen, wird für das äußere Objekt keine zusätzliche Übereinstimmungsprüfung vorgenommen.Die Übereinstimmungsprüfung wird dem zugrunde liegenden Objekt überlassen.Weitere Details dahingehend, wie die Übereinstimmungsprüfung festgelegt wird, finden Sie unter den <see cref="P:System.Xml.XmlReaderSettings.ConformanceLevel" />- und den <see cref="P:System.Xml.XmlWriterSettings.ConformanceLevel" />-Eigenschaften.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Document">
      <summary>Die XML-Daten entsprechen den Regeln für ein wohlgeformtes XML-Dokument, Version 1.0, wie diese vom World Wide Web Consortium (W3C) festgelegt sind.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Fragment">
      <summary>Die XML-Daten stellen ein wohlgeformtes XML-Fragment dar, wie dies vom World Wide Web Consortium (W3C) festgelegt ist.</summary>
    </member>
    <member name="T:System.Xml.DtdProcessing">
      <summary>Gibt die Optionen zum Verarbeiten von DTDs an.Die <see cref="T:System.Xml.DtdProcessing" />-Enumeration wird von der <see cref="T:System.Xml.XmlReaderSettings" />-Klasse verwendet.</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Ignore">
      <summary>Führt dazu, dass das DOCTYPE-Element ignoriert wird.Keine DTD-Verarbeitung wird durchgeführt.</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Prohibit">
      <summary>Gibt an, dass beim Auftreten einer DTD eine <see cref="T:System.Xml.XmlException" /> mit der Meldung ausgelöst wird, dass DTDs nicht zulässig sind.Dies ist das Standardverhalten.</summary>
    </member>
    <member name="T:System.Xml.IXmlLineInfo">
      <summary>Stellt eine Schnittstelle bereit, über die eine Klasse Zeilen- und Positionsinformationen zurückgeben kann.</summary>
    </member>
    <member name="M:System.Xml.IXmlLineInfo.HasLineInfo">
      <summary>Ruft einen Wert ab, der angibt, ob die Klasse Zeileninformationen zurückgeben kann.</summary>
      <returns>true, wenn <see cref="P:System.Xml.IXmlLineInfo.LineNumber" /> und <see cref="P:System.Xml.IXmlLineInfo.LinePosition" /> angegeben werden können, andernfalls false.</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LineNumber">
      <summary>Ruft die aktuelle Zeilennummer ab.</summary>
      <returns>Die aktuelle Zeilennummer oder 0, wenn keine Zeileninformationen vorliegen (z. B. gibt <see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" />false zurück).</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LinePosition">
      <summary>Ruft die aktuelle Zeilenposition ab.</summary>
      <returns>Die aktuelle Zeilenposition oder 0, wenn keine Zeileninformationen vorliegen (z. B. gibt <see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" />false zurück).</returns>
    </member>
    <member name="T:System.Xml.IXmlNamespaceResolver">
      <summary>Stellt den schreibgeschützten Zugriff auf eine Gruppe von Präfix- und Namespacezuordnungen bereit.</summary>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>Ruft eine Auflistung von definierten Präfix-Namespace-Zuordnungen ab, die sich derzeit im Gültigkeitsbereich befinden.</summary>
      <returns>Ein <see cref="T:System.Collections.IDictionary" />, das die derzeit im Gültigkeitsbereich enthaltenen Namespaces enthält.</returns>
      <param name="scope">Ein <see cref="T:System.Xml.XmlNamespaceScope" />-Wert, der den Typ der Namespaceknoten angibt, die zurückgegeben werden sollen.</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupNamespace(System.String)">
      <summary>Ruft den dem angegebenen Präfix zugeordneten Namespace-URI ab.</summary>
      <returns>Der Namespace-URI, der dem Präfix zugeordnet ist. null, wenn das Präfix keinem Namespace-URI zugeordnet ist.</returns>
      <param name="prefix">Das Präfix, dessen Namespace-URI gesucht werden soll.</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupPrefix(System.String)">
      <summary>Ruft das Präfix ab, das dem angegebenen Namespace-URI zugeordnet ist.</summary>
      <returns>Das Präfix, das dem Namespace-URI zugeordnet ist; null, wenn der Namespace-URI keinem Präfix zugeordnet ist.</returns>
      <param name="namespaceName">Der Namespace-URI, dessen Präfix gesucht werden soll.</param>
    </member>
    <member name="T:System.Xml.NamespaceHandling">
      <summary>Gibt an, ob doppelte Namespacedeklarationen im <see cref="T:System.Xml.XmlWriter" /> entfernt werden sollen. </summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.Default">
      <summary>Gibt an, dass doppelte Namespacedeklarationen nicht entfernt werden.</summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.OmitDuplicates">
      <summary>Gibt an, dass doppelte Namespacedeklarationen entfernt werden.Voraussetzung für das Entfernen des doppelten Namespace ist, dass Präfix und Namespace übereinstimmen.</summary>
    </member>
    <member name="T:System.Xml.NameTable">
      <summary>Implementiert eine Singlethread-<see cref="T:System.Xml.XmlNameTable" />.</summary>
    </member>
    <member name="M:System.Xml.NameTable.#ctor">
      <summary>Initialisiert eine neue Instanz der NameTable-Klasse.</summary>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>Atomisiert die angegebene Zeichenfolge und fügt diese der NameTable hinzu.</summary>
      <returns>Die atomisierte Zeichenfolge bzw. die vorhandene, sofern bereits eine Zeichenfolge in der NameTable vorhanden ist.Wenn <paramref name="len" /> 0 ist, wird String.Empty zurückgegeben.</returns>
      <param name="key">Das Zeichenarray mit der hinzuzufügenden Zeichenfolge. </param>
      <param name="start">Der nullbasierte Index im Array, der das erste Zeichen der Zeichenfolge angibt. </param>
      <param name="len">Die Anzahl der Zeichen in der Zeichenfolge. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />– oder – <paramref name="start" /> &gt;= <paramref name="key" />.Length – oder – <paramref name="len" /> &gt;= <paramref name="key" />.Length Die oben genannten Bedingungen führen nicht zum Auslösen einer Ausnahme, wenn <paramref name="len" /> = 0 (null). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.String)">
      <summary>Atomisiert die angegebene Zeichenfolge und fügt diese der NameTable hinzu.</summary>
      <returns>Die atomisierte Zeichenfolge bzw. die vorhandene, sofern bereits eine Zeichenfolge in der NameTable vorhanden ist.</returns>
      <param name="key">Die hinzuzufügende Zeichenfolge. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>Ruft die atomisierte Zeichenfolge ab, die dieselben Zeichen wie der angegebene Zeichenbereich im angegebenen Array enthält.</summary>
      <returns>Die atomisierte Zeichenfolge oder null, wenn die Zeichenfolge noch nicht atomisiert wurde.Wenn <paramref name="len" /> 0 ist, wird String.Empty zurückgegeben.</returns>
      <param name="key">Das Zeichenarray mit dem gesuchten Namen. </param>
      <param name="start">Der nullbasierte Index im Array, der das erste Zeichen des Namens angibt. </param>
      <param name="len">Die Anzahl der Zeichen im Namen. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />– oder – <paramref name="start" /> &gt;= <paramref name="key" />.Length – oder – <paramref name="len" /> &gt;= <paramref name="key" />.Length Die oben genannten Bedingungen führen nicht zum Auslösen einer Ausnahme, wenn <paramref name="len" /> = 0 (null). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.String)">
      <summary>Ruft die atomisierte Zeichenfolge mit dem angegebenen Wert ab.</summary>
      <returns>Das atomisierte Zeichenfolgenobjekt oder null, wenn die Zeichenfolge noch nicht atomisiert wurde.</returns>
      <param name="value">Der gesuchte Name. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist null. </exception>
    </member>
    <member name="T:System.Xml.NewLineHandling">
      <summary>Gibt an, wie Zeilenumbrüche behandelt werden.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Entitize">
      <summary>Zeilenumbruchzeichen werden durch Entitätenzeichen ersetzt.Mit dieser Einstellung werden alle Zeichen beibehalten, wenn die Ausgabe von einem normalisierenden <see cref="T:System.Xml.XmlReader" /> gelesen wird.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.None">
      <summary>Die Zeilenumbruchzeichen sind unverändert.Die Ausgabe ist gleich der Eingabe.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Replace">
      <summary>Zeilenumbruchzeichen werden ersetzt, damit sie mit dem in der <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" />-Eigenschaft angegebenen Zeichen übereinstimmen.</summary>
    </member>
    <member name="T:System.Xml.ReadState">
      <summary>Gibt den Zustand des Readers an.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Closed">
      <summary>Die <see cref="M:System.Xml.XmlReader.Close" />-Methode wurde aufgerufen.</summary>
    </member>
    <member name="F:System.Xml.ReadState.EndOfFile">
      <summary>Das Ende der Datei wurde mit Erfolg erreicht.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Error">
      <summary>Es ist ein Fehler aufgetreten, der verhindert, dass der Lesevorgang fortgeführt werden kann.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Initial">
      <summary>Die Read-Methode wurde nicht aufgerufen.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Interactive">
      <summary>Die Read-Methode wurde aufgerufen.Für den Reader können zusätzliche Methoden aufgerufen werden.</summary>
    </member>
    <member name="T:System.Xml.WriteState">
      <summary>Gibt den Zustand des <see cref="T:System.Xml.XmlWriter" /> an.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Attribute">
      <summary>Gibt an, dass ein Attributwert geschrieben wird.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Closed">
      <summary>Gibt an, dass die <see cref="M:System.Xml.XmlWriter.Close" />-Methode aufgerufen wurde.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Content">
      <summary>Gibt an, dass Elementinhalt geschrieben wird.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Element">
      <summary>Gibt an, dass ein Elementstarttag geschrieben wird.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Error">
      <summary>Eine Ausnahme wurde ausgelöst, die den <see cref="T:System.Xml.XmlWriter" /> in einem ungültigen Zustand versetzt hat.Sie können die <see cref="M:System.Xml.XmlWriter.Close" />-Methode aufrufen, um den <see cref="T:System.Xml.XmlWriter" /> in den <see cref="F:System.Xml.WriteState.Closed" />-Zustand zu versetzen.Alle anderen <see cref="T:System.Xml.XmlWriter" />-Methodenaufrufe führen zu einer <see cref="T:System.InvalidOperationException" />.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Prolog">
      <summary>Gibt an, dass der Prolog geschrieben wird.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Start">
      <summary>Gibt an, dass eine Write-Methode noch nicht aufgerufen wurde.</summary>
    </member>
    <member name="T:System.Xml.XmlConvert">
      <summary>Codiert und decodiert XML-Namen und stellt Methoden für das Konvertieren zwischen Typen der Common Language Runtime und XSD-Typen (XML Schema Definition) bereit.Bei der Konvertierung von Datentypen sind die zurückgegebenen Werte vom Gebietsschema unabhängig.</summary>
    </member>
    <member name="M:System.Xml.XmlConvert.DecodeName(System.String)">
      <summary>Decodiert einen Namen.Diese Methode ist die Umkehrung der <see cref="M:System.Xml.XmlConvert.EncodeName(System.String)" />-Methode und der <see cref="M:System.Xml.XmlConvert.EncodeLocalName(System.String)" />-Methode.</summary>
      <returns>Der decodierte Name.</returns>
      <param name="name">Der umzuwandelnde Name. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeLocalName(System.String)">
      <summary>Konvertiert den Namen in einen gültigen lokalen XML-Namen.</summary>
      <returns>Der codierte Name.</returns>
      <param name="name">Der zu codierende Name. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeName(System.String)">
      <summary>Konvertiert den Namen in einen gültigen XML-Namen.</summary>
      <returns>Gibt den Namen zurück, wobei ungültige Zeichen durch eine Escapezeichenfolge ersetzt wurden.</returns>
      <param name="name">Ein zu übersetzender Name. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeNmToken(System.String)">
      <summary>Überprüft, ob der Name entsprechend der XML-Spezifikation gültig ist.</summary>
      <returns>Der codierte Name.</returns>
      <param name="name">Der zu codierende Name. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToBoolean(System.String)">
      <summary>Konvertiert den <see cref="T:System.String" /> in ein <see cref="T:System.Boolean" />-Äquivalent.</summary>
      <returns>Ein Boolean-Wert, d. h. true oder false.</returns>
      <param name="s">Die zu konvertierende Zeichenfolge. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> does not represent a Boolean value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToByte(System.String)">
      <summary>Konvertiert den <see cref="T:System.String" /> in ein <see cref="T:System.Byte" />-Äquivalent.</summary>
      <returns>Ein Byte-Äquivalent der Zeichenfolge.</returns>
      <param name="s">Die zu konvertierende Zeichenfolge. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToChar(System.String)">
      <summary>Konvertiert den <see cref="T:System.String" /> in ein <see cref="T:System.Char" />-Äquivalent.</summary>
      <returns>Ein Char, das für das einzelne Zeichen steht.</returns>
      <param name="s">Die Zeichenfolge, die ein einzelnes zu konvertierendes Zeichen enthält. </param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="s" /> parameter is null. </exception>
      <exception cref="T:System.FormatException">The <paramref name="s" /> parameter contains more than one character. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTime(System.String,System.Xml.XmlDateTimeSerializationMode)">
      <summary>Konvertiert den <see cref="T:System.String" /> mithilfe von <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> in eine <see cref="T:System.DateTime" />-Struktur.</summary>
      <returns>Ein <see cref="T:System.DateTime" />-Äquivalent von <see cref="T:System.String" />.</returns>
      <param name="s">Der zu konvertierende <see cref="T:System.String" />-Wert.</param>
      <param name="dateTimeOption">Einer der <see cref="T:System.Xml.XmlDateTimeSerializationMode" />-Werte, die angeben, ob das Datum in die Ortszeit konvertiert oder als UTC-Zeit (Coordinated Universal Time) beibehalten werden soll, falls es sich um ein UTC-Datum handelt.</param>
      <exception cref="T:System.NullReferenceException">
        <paramref name="s" /> is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="dateTimeOption" /> value is null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is an empty string or is not in a valid format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String)">
      <summary>Konvertiert den angegebenen <see cref="T:System.String" /> in ein <see cref="T:System.DateTimeOffset" />-Äquivalent.</summary>
      <returns>Das <see cref="T:System.DateTimeOffset" />-Äquivalent der angegebenen Zeichenfolge.</returns>
      <param name="s">Die zu konvertierende Zeichenfolge.Hinweis   Die Zeichenfolge muss einer Teilmenge der W3C-Empfehlung für den XML-dateTime-Typ entsprechen.Weitere Informationen finden Sie unter „http://www.w3.org/TR/xmlschema-2/#dateTime“.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The argument passed to this method is outside the range of allowable values.For information about allowable values, see <see cref="T:System.DateTimeOffset" />.</exception>
      <exception cref="T:System.FormatException">The argument passed to this method does not conform to a subset of the W3C Recommendations for the XML dateTime type.For more information see http://www.w3.org/TR/xmlschema-2/#dateTime.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String)">
      <summary>Konvertiert den angegebenen <see cref="T:System.String" /> in ein <see cref="T:System.DateTimeOffset" />-Äquivalent.</summary>
      <returns>Das <see cref="T:System.DateTimeOffset" />-Äquivalent der angegebenen Zeichenfolge.</returns>
      <param name="s">Die zu konvertierende Zeichenfolge.</param>
      <param name="format">Das Format, aus dem <paramref name="s" /> konvertiert wird.Der Formatparameter kann eine beliebige Teilmenge der W3C-Empfehlung für den XML-DateTime-Typ sein.(Weitere Informationen finden Sie unter „http://www.w3.org/TR/xmlschema-2/#dateTime“.) Die Gültigkeit der Zeichenfolge <paramref name="s" /> wird anhand dieses Formats überprüft.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> or <paramref name="format" /> is an empty string or is not in the specified format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String[])">
      <summary>Konvertiert den angegebenen <see cref="T:System.String" /> in ein <see cref="T:System.DateTimeOffset" />-Äquivalent.</summary>
      <returns>Das <see cref="T:System.DateTimeOffset" />-Äquivalent der angegebenen Zeichenfolge.</returns>
      <param name="s">Die zu konvertierende Zeichenfolge.</param>
      <param name="formats">Ein Array von Formaten, aus denen <paramref name="s" /> konvertiert werden kann.Jedes Format in <paramref name="formats" /> kann eine beliebige Teilmenge der W3C-Empfehlung für den XML-DateTime-Typ sein.(Weitere Informationen finden Sie unter „http://www.w3.org/TR/xmlschema-2/#dateTime“.) Die Gültigkeit der Zeichenfolge <paramref name="s" /> wird im Vergleich mit einem dieser Formate überprüft.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDecimal(System.String)">
      <summary>Konvertiert den <see cref="T:System.String" /> in ein <see cref="T:System.Decimal" />-Äquivalent.</summary>
      <returns>Ein Decimal-Äquivalent der Zeichenfolge.</returns>
      <param name="s">Die zu konvertierende Zeichenfolge. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Decimal.MinValue" /> or greater than <see cref="F:System.Decimal.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDouble(System.String)">
      <summary>Konvertiert den <see cref="T:System.String" /> in ein <see cref="T:System.Double" />-Äquivalent.</summary>
      <returns>Ein Double-Äquivalent der Zeichenfolge.</returns>
      <param name="s">Die zu konvertierende Zeichenfolge. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Double.MinValue" /> or greater than <see cref="F:System.Double.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToGuid(System.String)">
      <summary>Konvertiert den <see cref="T:System.String" /> in ein <see cref="T:System.Guid" />-Äquivalent.</summary>
      <returns>Ein Guid-Äquivalent der Zeichenfolge.</returns>
      <param name="s">Die zu konvertierende Zeichenfolge. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt16(System.String)">
      <summary>Konvertiert den <see cref="T:System.String" /> in ein <see cref="T:System.Int16" />-Äquivalent.</summary>
      <returns>Ein Int16-Äquivalent der Zeichenfolge.</returns>
      <param name="s">Die zu konvertierende Zeichenfolge. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int16.MinValue" /> or greater than <see cref="F:System.Int16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt32(System.String)">
      <summary>Konvertiert den <see cref="T:System.String" /> in ein <see cref="T:System.Int32" />-Äquivalent.</summary>
      <returns>Ein Int32-Äquivalent der Zeichenfolge.</returns>
      <param name="s">Die zu konvertierende Zeichenfolge. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int32.MinValue" /> or greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt64(System.String)">
      <summary>Konvertiert den <see cref="T:System.String" /> in ein <see cref="T:System.Int64" />-Äquivalent.</summary>
      <returns>Ein Int64-Äquivalent der Zeichenfolge.</returns>
      <param name="s">Die zu konvertierende Zeichenfolge. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int64.MinValue" /> or greater than <see cref="F:System.Int64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSByte(System.String)">
      <summary>Konvertiert den <see cref="T:System.String" /> in ein <see cref="T:System.SByte" />-Äquivalent.</summary>
      <returns>Ein SByte-Äquivalent der Zeichenfolge.</returns>
      <param name="s">Die zu konvertierende Zeichenfolge. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.SByte.MinValue" /> or greater than <see cref="F:System.SByte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSingle(System.String)">
      <summary>Konvertiert den <see cref="T:System.String" /> in ein <see cref="T:System.Single" />-Äquivalent.</summary>
      <returns>Ein Single-Äquivalent der Zeichenfolge.</returns>
      <param name="s">Die zu konvertierende Zeichenfolge. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Single.MinValue" /> or greater than <see cref="F:System.Single.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Boolean)">
      <summary>Konvertiert das <see cref="T:System.Boolean" />-Element in eine <see cref="T:System.String" />.</summary>
      <returns>Eine Zeichenfolgendarstellung von Boolean, d. h. „true“ oder „false“.</returns>
      <param name="value">Der zu konvertierende Wert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Byte)">
      <summary>Konvertiert das <see cref="T:System.Byte" />-Element in eine <see cref="T:System.String" />.</summary>
      <returns>Eine Zeichenfolgendarstellung des Byte.</returns>
      <param name="value">Der zu konvertierende Wert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Char)">
      <summary>Konvertiert das <see cref="T:System.Char" />-Element in eine <see cref="T:System.String" />.</summary>
      <returns>Eine Zeichenfolgendarstellung des Char.</returns>
      <param name="value">Der zu konvertierende Wert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTime,System.Xml.XmlDateTimeSerializationMode)">
      <summary>Konvertiert die <see cref="T:System.DateTime" />-Struktur mithilfe von <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> in eine <see cref="T:System.String" />.</summary>
      <returns>Ein <see cref="T:System.String" />-Äquivalent von <see cref="T:System.DateTime" />.</returns>
      <param name="value">Der zu konvertierende <see cref="T:System.DateTime" />-Wert.</param>
      <param name="dateTimeOption">Einer der <see cref="T:System.Xml.XmlDateTimeSerializationMode" />-Werte, die angeben, wie der <see cref="T:System.DateTime" />-Wert behandelt wird.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="dateTimeOption" /> value is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> or <paramref name="dateTimeOption" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset)">
      <summary>Konvertiert den angegebenen <see cref="T:System.DateTimeOffset" /> in einen <see cref="T:System.String" />.</summary>
      <returns>Eine <see cref="T:System.String" />-Darstellung des angegebenen <see cref="T:System.DateTimeOffset" />.</returns>
      <param name="value">Der zu konvertierende <see cref="T:System.DateTimeOffset" />.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset,System.String)">
      <summary>Konvertiert den angegebenen <see cref="T:System.DateTimeOffset" /> in einen <see cref="T:System.String" /> im angegebenen Format.</summary>
      <returns>Eine <see cref="T:System.String" />-Darstellung im angegebenen Format des bereitgestellten <see cref="T:System.DateTimeOffset" />.</returns>
      <param name="value">Der zu konvertierende <see cref="T:System.DateTimeOffset" />.</param>
      <param name="format">Das Format, in das <paramref name="s" /> konvertiert wird.Der Formatparameter kann eine beliebige Teilmenge der W3C-Empfehlung für den XML-DateTime-Typ sein.(Weitere Informationen finden Sie unter „http://www.w3.org/TR/xmlschema-2/#dateTime“.)</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Decimal)">
      <summary>Konvertiert das <see cref="T:System.Decimal" />-Element in eine <see cref="T:System.String" />.</summary>
      <returns>Eine Zeichenfolgendarstellung des Decimal.</returns>
      <param name="value">Der zu konvertierende Wert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Double)">
      <summary>Konvertiert das <see cref="T:System.Double" />-Element in eine <see cref="T:System.String" />.</summary>
      <returns>Eine Zeichenfolgendarstellung des Double.</returns>
      <param name="value">Der zu konvertierende Wert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Guid)">
      <summary>Konvertiert das <see cref="T:System.Guid" />-Element in eine <see cref="T:System.String" />.</summary>
      <returns>Eine Zeichenfolgendarstellung des Guid.</returns>
      <param name="value">Der zu konvertierende Wert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int16)">
      <summary>Konvertiert das <see cref="T:System.Int16" />-Element in eine <see cref="T:System.String" />.</summary>
      <returns>Eine Zeichenfolgendarstellung des Int16.</returns>
      <param name="value">Der zu konvertierende Wert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int32)">
      <summary>Konvertiert das <see cref="T:System.Int32" />-Element in eine <see cref="T:System.String" />.</summary>
      <returns>Eine Zeichenfolgendarstellung des Int32.</returns>
      <param name="value">Der zu konvertierende Wert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int64)">
      <summary>Konvertiert das <see cref="T:System.Int64" />-Element in eine <see cref="T:System.String" />.</summary>
      <returns>Eine Zeichenfolgendarstellung des Int64.</returns>
      <param name="value">Der zu konvertierende Wert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.SByte)">
      <summary>Konvertiert das <see cref="T:System.SByte" />-Element in eine <see cref="T:System.String" />.</summary>
      <returns>Eine Zeichenfolgendarstellung des SByte.</returns>
      <param name="value">Der zu konvertierende Wert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Single)">
      <summary>Konvertiert das <see cref="T:System.Single" />-Element in eine <see cref="T:System.String" />.</summary>
      <returns>Eine Zeichenfolgendarstellung des Single.</returns>
      <param name="value">Der zu konvertierende Wert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.TimeSpan)">
      <summary>Konvertiert das <see cref="T:System.TimeSpan" />-Element in eine <see cref="T:System.String" />.</summary>
      <returns>Eine Zeichenfolgendarstellung des TimeSpan.</returns>
      <param name="value">Der zu konvertierende Wert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt16)">
      <summary>Konvertiert das <see cref="T:System.UInt16" />-Element in eine <see cref="T:System.String" />.</summary>
      <returns>Eine Zeichenfolgendarstellung des UInt16.</returns>
      <param name="value">Der zu konvertierende Wert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt32)">
      <summary>Konvertiert das <see cref="T:System.UInt32" />-Element in eine <see cref="T:System.String" />.</summary>
      <returns>Eine Zeichenfolgendarstellung des UInt32.</returns>
      <param name="value">Der zu konvertierende Wert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt64)">
      <summary>Konvertiert das <see cref="T:System.UInt64" />-Element in eine <see cref="T:System.String" />.</summary>
      <returns>Eine Zeichenfolgendarstellung des UInt64.</returns>
      <param name="value">Der zu konvertierende Wert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToTimeSpan(System.String)">
      <summary>Konvertiert den <see cref="T:System.String" /> in ein <see cref="T:System.TimeSpan" />-Äquivalent.</summary>
      <returns>Ein TimeSpan-Äquivalent der Zeichenfolge.</returns>
      <param name="s">Die zu konvertierende Zeichenfolge.Das Zeichenfolgenformat muss dem W3C-XML-Schema Teil 2 entsprechen: Empfehlung für Datentypen für Dauer.</param>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in correct format to represent a TimeSpan value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt16(System.String)">
      <summary>Konvertiert den <see cref="T:System.String" /> in ein <see cref="T:System.UInt16" />-Äquivalent.</summary>
      <returns>Ein UInt16-Äquivalent der Zeichenfolge.</returns>
      <param name="s">Die zu konvertierende Zeichenfolge. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt16.MinValue" /> or greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt32(System.String)">
      <summary>Konvertiert den <see cref="T:System.String" /> in ein <see cref="T:System.UInt32" />-Äquivalent.</summary>
      <returns>Ein UInt32-Äquivalent der Zeichenfolge.</returns>
      <param name="s">Die zu konvertierende Zeichenfolge. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt32.MinValue" /> or greater than <see cref="F:System.UInt32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt64(System.String)">
      <summary>Konvertiert den <see cref="T:System.String" /> in ein <see cref="T:System.UInt64" />-Äquivalent.</summary>
      <returns>Ein UInt64-Äquivalent der Zeichenfolge.</returns>
      <param name="s">Die zu konvertierende Zeichenfolge. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt64.MinValue" /> or greater than <see cref="F:System.UInt64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyName(System.String)">
      <summary>Überprüft, ob der Name ein gültiger Name gemäß der W3C-Empfehlung für XML (Extended Markup Language) ist.</summary>
      <returns>Der Name, wenn dieser ein gültiger XML-Name ist.</returns>
      <param name="name">Der zu überprüfende Name. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid XML name. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNCName(System.String)">
      <summary>Überprüft, ob der Name ein gültiger NCName gemäß der W3C-Empfehlung für XML (Extended Markup Language) ist.Ein NCName darf keinen Doppelpunkt enthalten.</summary>
      <returns>Der Name, wenn dieser ein gültiger NCName ist.</returns>
      <param name="name">Der zu überprüfende Name. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid non-colon name. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNMTOKEN(System.String)">
      <summary>Überprüft, ob die Zeichenfolge ein gültiges NMTOKEN gemäß der Empfehlung in W3C XML Schema, Teil 2: „Datentypen“, ist.</summary>
      <returns>Das Namenstoken, wenn es sich um ein gültiges NMTOKEN handelt.</returns>
      <param name="name">Die Zeichenfolge, die überprüft werden soll.</param>
      <exception cref="T:System.Xml.XmlException">The string is not a valid name token.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyPublicId(System.String)">
      <summary>Gibt die übergebene Zeichenfolgeninstanz zurück, wenn alle Zeichen im Zeichenfolgenargument gültige Zeichen für eine öffentliche ID sind.</summary>
      <returns>Gibt die übergebene Zeichenfolge zurück, wenn alle Zeichen im Argument gültige Zeichen für eine öffentliche ID sind.</returns>
      <param name="publicId">
        <see cref="T:System.String" />, der die zu überprüfende ID enthält.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyWhitespace(System.String)">
      <summary>Gibt die übergebene Zeichenfolgeninstanz zurück, wenn alle Zeichen im Zeichenfolgenargument gültige Leerraumzeichen sind. </summary>
      <returns>Gibt die übergebene Zeichenfolgeninstanz zurück, wenn alle Zeichen im Zeichenfolgenargument gültige Leerraumzeichen sind, andernfalls null.</returns>
      <param name="content">Der zu überprüfende <see cref="T:System.String" />.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyXmlChars(System.String)">
      <summary>Gibt die übergebene Zeichenfolge zurück, wenn alle Zeichen und Ersatzzeichenpaare im Zeichenfolgenargument gültige XML-Zeichen sind, andernfalls wird eine XmlException mit Informationen zum ersten ungültigen Zeichen ausgelöst. </summary>
      <returns>Gibt die übergebene Zeichenfolge zurück, wenn alle Zeichen und Ersatzzeichenpaare im Zeichenfolgenargument gültige XML-Zeichen sind, andernfalls wird eine XmlException mit Informationen zum ersten ungültigen Zeichen ausgelöst.</returns>
      <param name="content">Der <see cref="T:System.String" /> mit den zu überprüfenden Zeichen.</param>
    </member>
    <member name="T:System.Xml.XmlDateTimeSerializationMode">
      <summary>Gibt an, wie der Wert für die Uhrzeit beim Konvertieren zwischen einer Zeichenfolge und <see cref="T:System.DateTime" /> behandelt werden soll.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Local">
      <summary>Als lokale Zeit behandeln.Wenn das <see cref="T:System.DateTime" />-Objekt eine UTC (Coordinated Universal Time, koordinierte Weltzeit) darstellt, wird es in die lokale Zeit konvertiert.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.RoundtripKind">
      <summary>Zeitzoneninformationen sollten bei der Konvertierung beibehalten werden.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Unspecified">
      <summary>Als lokale Zeit behandeln, wenn eine <see cref="T:System.DateTime" />-Struktur in eine Zeichenfolge konvertiert wird.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Utc">
      <summary>Als UTC behandeln.Wenn das <see cref="T:System.DateTime" />-Objekt eine lokale Zeit darstellt, wird es in die koordinierte Weltzeit konvertiert.</summary>
    </member>
    <member name="T:System.Xml.XmlException">
      <summary>Gibt ausführliche Informationen über die letzte Ausnahme zurück.</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor">
      <summary>Initialisiert eine neue Instanz der XmlException-Klasse.</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der XmlException-Klasse mit einer angegebenen Fehlermeldung.</summary>
      <param name="message">Die Fehlerbeschreibung. </param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der XmlException-Klasse.</summary>
      <param name="message">Die Beschreibung des Fehlerzustands. </param>
      <param name="innerException">Die <see cref="T:System.Exception" />, die die XmlException ausgelöst hat (falls eine Ausnahme ausgelöst wurde).Dieser Wert kann null sein.</param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception,System.Int32,System.Int32)">
      <summary>Initialisiert eine neue Instanz der XmlException-Klasse mit der angegebenen Meldung, inneren Ausnahme, Zeilennummer und Zeilenposition.</summary>
      <param name="message">Die Fehlerbeschreibung. </param>
      <param name="innerException">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Dieser Wert kann null sein.</param>
      <param name="lineNumber">Die Nummer der Zeile, in der der Fehler aufgetreten ist. </param>
      <param name="linePosition">Die Position der Zeile, an der der Fehler aufgetreten ist. </param>
    </member>
    <member name="P:System.Xml.XmlException.LineNumber">
      <summary>Ruft die Nummer der Zeile ab, in der der Fehler aufgetreten ist.</summary>
      <returns>Die Nummer der Zeile, in der der Fehler aufgetreten ist.</returns>
    </member>
    <member name="P:System.Xml.XmlException.LinePosition">
      <summary>Ruft die Position der Zeile ab, an der der Fehler aufgetreten ist.</summary>
      <returns>Die Position der Zeile, an der der Fehler aufgetreten ist.</returns>
    </member>
    <member name="P:System.Xml.XmlException.Message">
      <summary>Ruft eine Meldung ab, die die aktuelle Ausnahme beschreibt.</summary>
      <returns>Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird.</returns>
    </member>
    <member name="T:System.Xml.XmlNamespaceManager">
      <summary>Löst Namespaces auf, fügt sie einer Auflistung hinzu bzw. entfernt sie daraus und ermöglicht die Verwaltung der Gültigkeitsbereiche dieser Namespaces. </summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.#ctor(System.Xml.XmlNameTable)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Xml.XmlNamespaceManager" />-Klasse mit der angegebenen <see cref="T:System.Xml.XmlNameTable" />.</summary>
      <param name="nameTable">Die zu verwendende <see cref="T:System.Xml.XmlNameTable" />. </param>
      <exception cref="T:System.NullReferenceException">null is passed to the constructor </exception>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.AddNamespace(System.String,System.String)">
      <summary>Fügt der Auflistung den angegebenen Namespace hinzu.</summary>
      <param name="prefix">Das Präfix, das dem hinzugefügten Namespace zugeordnet werden soll.Verwenden Sie String.Empty, um einen Standardnamespace hinzuzufügen.HinweisWenn der <see cref="T:System.Xml.XmlNamespaceManager" /> jedoch für das Auflösen von Namespaces in einem XPath (XML Path Language)-Ausdruck verwendet wird, muss ein Präfix angegeben werden.Wenn ein XPath-Ausdruck kein Präfix enthält, wird davon ausgegangen, dass der Namespace-URI (Uniform Resource Identifier) der leere Namespace ist.Weitere Informationen über XPath-Ausdrücke und den <see cref="T:System.Xml.XmlNamespaceManager" /> finden Sie in der <see cref="M:System.Xml.XmlNode.SelectNodes(System.String)" />-Methode und der <see cref="M:System.Xml.XPath.XPathExpression.SetContext(System.Xml.XmlNamespaceManager)" />-Methode.</param>
      <param name="uri">Der hinzuzufügende Namespace. </param>
      <exception cref="T:System.ArgumentException">The value for <paramref name="prefix" /> is "xml" or "xmlns". </exception>
      <exception cref="T:System.ArgumentNullException">The value for <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.DefaultNamespace">
      <summary>Ruft den Namespace-URI für den Standardnamespace ab.</summary>
      <returns>Gibt den Namespace-URI für den Standardnamespace zurück oder String.Empty, wenn kein Standardnamespace vorhanden ist.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetEnumerator">
      <summary>Gibt einen Enumerator für das Durchlaufen der Namespaces im <see cref="T:System.Xml.XmlNamespaceManager" /> zurück.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" />, der die vom <see cref="T:System.Xml.XmlNamespaceManager" /> gespeicherten Präfixe enthält.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>Ruft eine Auflistung von Namen sortiert nach Präfix ab, mit der die aktuell im Gültigkeitsbereich vorhanden Namespaces durchlaufen werden können.</summary>
      <returns>Eine Auflistung der derzeit im Gültigkeitsbereich vorhandenen Paare aus Namespace und Präfix.</returns>
      <param name="scope">Ein Enumerationswert, der den Typ der Namespaceknoten angibt, die zurückgegeben werden sollen.</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.HasNamespace(System.String)">
      <summary>Ruft einen Wert ab, der angibt, ob für das angegebene Präfix ein Namespace für den aktuellen abgelegten Gültigkeitsbereich definiert ist.</summary>
      <returns>true, wenn ein definierter Namespace vorhanden ist, andernfalls false.</returns>
      <param name="prefix">Das Präfix des zu suchenden Namespaces. </param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupNamespace(System.String)">
      <summary>Ruft den Namespace-URI für das angegebene Präfix ab.</summary>
      <returns>Gibt den Namespace-URI für <paramref name="prefix" /> zurück oder null, wenn kein zugeordneter Namespace vorhanden ist.Die zurückgegebene Zeichenfolge ist atomisiert.Weitere Informationen zu atomisierten Zeichenfolgen finden Sie unter der <see cref="T:System.Xml.XmlNameTable" />-Klasse.</returns>
      <param name="prefix">Das Präfix, dessen Namespace-URI aufgelöst werden soll.Um eine Übereinstimmung mit dem Standardnamespace zu suchen, übergeben Sie String.Empty.</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupPrefix(System.String)">
      <summary>Sucht das für den angegebenen Namespace-URI deklarierte Präfix.</summary>
      <returns>Das passende Präfix.Wenn es kein zugeordnetes Präfix gibt, gibt die Methode den Wert "String.Empty" zurück.Wenn ein Nullwert angegeben wird, dann wird null zurückgegeben.</returns>
      <param name="uri">Der für das Präfix aufzulösende Namespace. </param>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.NameTable">
      <summary>Ruft den <see cref="T:System.Xml.XmlNameTable" /> ab, der diesem Objekt zugeordnet ist.</summary>
      <returns>Die von diesem Objekt verwendete <see cref="T:System.Xml.XmlNameTable" />.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PopScope">
      <summary>Holt einen Namespacebereich vom Stapel.</summary>
      <returns>true, wenn noch Namespacebereiche im Stapel vorhanden sind, false, wenn keine Namespaces mehr geholt werden können.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PushScope">
      <summary>Legt einen Namespacebereich auf den Stapel.</summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.RemoveNamespace(System.String,System.String)">
      <summary>Entfernt den angegebenen Namespace für das angegebene Präfix.</summary>
      <param name="prefix">Das Präfix für den Namespace. </param>
      <param name="uri">Der für das angegebene Präfix zu entfernende Namespace.Der entfernte Namespace stammt aus dem aktuellen Namespacebereich.Namespaces außerhalb des aktuellen Gültigkeitsbereichs werden ignoriert.</param>
      <exception cref="T:System.ArgumentNullException">The value of <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="T:System.Xml.XmlNamespaceScope">
      <summary>Definiert den Namespacebereich.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.All">
      <summary>Alle Namespaces, die im Gültigkeitsbereich des aktuellen Knotens definiert sind.Dies beinhaltet den xmlns:xml-Namespace, der immer implizit deklariert wird.Die Reihenfolge der zurückgegebenen Namespaces ist nicht definiert.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.ExcludeXml">
      <summary>Alle Namespaces, die im Gültigkeitsbereich des aktuellen Knotens definiert sind. Davon ausgeschlossen ist der xmlns:xml-Namespace, der immer implizit deklariert ist.Die Reihenfolge der zurückgegebenen Namespaces ist nicht definiert.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.Local">
      <summary>Alle Namespaces, die am aktuellen Knoten lokal definiert sind.</summary>
    </member>
    <member name="T:System.Xml.XmlNameTable">
      <summary>Tabelle atomisierter Zeichenfolgenobjekte.</summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Xml.XmlNameTable" />-Klasse. </summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>Atomisiert beim Überschreiben in einer abgeleiteten Klasse die angegebene Zeichenfolge und fügt sie der XmlNameTable hinzu.</summary>
      <returns>Die neue atomisierte Zeichenfolge bzw. die vorhandene, sofern bereits vorhanden.Wenn die Länge 0 ist, wird String.Empty zurückgegeben.</returns>
      <param name="array">Das Zeichenarray mit dem hinzuzufügenden Namen. </param>
      <param name="offset">Nullbasierter Index im Array, der das erste Zeichen des Namens angibt. </param>
      <param name="length">Die Anzahl der Zeichen im Namen. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />– oder – <paramref name="offset" /> &gt;= <paramref name="array" />.Length – oder – <paramref name="length" /> &gt; <paramref name="array" />.Length Die oben genannten Bedingungen führen nicht zum Auslösen einer Ausnahme, wenn <paramref name="length" /> = 0 (null). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.String)">
      <summary>Atomisiert beim Überschreiben in einer abgeleiteten Klasse die angegebene Zeichenfolge und fügt sie der XmlNameTable hinzu.</summary>
      <returns>Die neue atomisierte Zeichenfolge bzw. die vorhandene, sofern bereits vorhanden.</returns>
      <param name="array">Der hinzuzufügende Name. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse die atomisierte Zeichenfolge ab, die dieselben Zeichen wie der angegebene Zeichenbereich im angegebenen Array enthält.</summary>
      <returns>Die atomisierte Zeichenfolge oder null, wenn die Zeichenfolge noch nicht atomisiert wurde.Wenn <paramref name="length" /> 0 ist, wird String.Empty zurückgegeben.</returns>
      <param name="array">Das Zeichenarray mit dem zu suchenden Namen. </param>
      <param name="offset">Der nullbasierte Index im Array, der das erste Zeichen des Namens angibt. </param>
      <param name="length">Die Anzahl der Zeichen im Namen. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />– oder – <paramref name="offset" /> &gt;= <paramref name="array" />.Length – oder – <paramref name="length" /> &gt; <paramref name="array" />.Length Die oben genannten Bedingungen führen nicht zum Auslösen einer Ausnahme, wenn <paramref name="length" /> = 0 (null). </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.String)">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse die atomisierte Zeichenfolge ab, die denselben Wert wie die angegebenen Zeichenfolge hat.</summary>
      <returns>Die atomisierte Zeichenfolge oder null, wenn die Zeichenfolge noch nicht atomisiert wurde.</returns>
      <param name="array">Der zu suchende Name. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null. </exception>
    </member>
    <member name="T:System.Xml.XmlNodeType">
      <summary>Gibt den Typ des Knotens an.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Attribute">
      <summary>Ein Attribut (z. B. id='123').</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.CDATA">
      <summary>Ein CDATA-Abschnitt (z. B. &lt;![CDATA[my escaped text]]&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Comment">
      <summary>Ein Kommentar (z. B. &lt;!-- my comment --&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Document">
      <summary>Ein Dokumentobjekt, das als Stamm der Dokumentstruktur Zugriff auf das gesamte XML-Dokument gewährt.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentFragment">
      <summary>Ein Dokumentfragment.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentType">
      <summary>Die vom folgenden Tag angegebene Dokumenttypdeklaration (z. B. &lt;!DOCTYPE...&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Element">
      <summary>Ein Element (z. B. &lt;item&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndElement">
      <summary>Ein Endelementtag (z. B. &lt;/item&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndEntity">
      <summary>Wird zurückgegeben, wenn XmlReader aufgrund eines Aufrufs von <see cref="M:System.Xml.XmlReader.ResolveEntity" /> das Ende der Entitätsersetzung erreicht.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Entity">
      <summary>Eine Entitätsdeklaration (z. B. &lt;!ENTITY...&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EntityReference">
      <summary>Ein Verweis auf eine Entität (z. B. &amp;num;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.None">
      <summary>Dies wird vom <see cref="T:System.Xml.XmlReader" /> zurückgegeben, wenn keine Read-Methode aufgerufen wurde.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Notation">
      <summary>Eine Notation in der Dokumenttypdeklaration (z. B. &lt;!NOTATION...&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.ProcessingInstruction">
      <summary>Eine Verarbeitungsanweisung (z. B. &lt;?pi test?&gt;).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.SignificantWhitespace">
      <summary>Leerraum zwischen Markup in einem Modell für gemischten Inhalt oder Leerraum im xml:space="preserve"-Bereich.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Text">
      <summary>Der Textinhalt eines Knotens.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Whitespace">
      <summary>Leerraum zwischen Markup.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.XmlDeclaration">
      <summary>Die XML-Deklaration (z. B. &lt;?xml version='1.0'?&gt;).</summary>
    </member>
    <member name="T:System.Xml.XmlParserContext">
      <summary>Stellt sämtliche Kontextinformationen bereit, die von <see cref="T:System.Xml.XmlReader" /> für das Analysieren eines XML-Fragments benötigt werden.</summary>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace)">
      <summary>Initialisiert eine neue Instanz der XmlParserContext-Klasse mit den angegebenen Werten für <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, Basis-URI, xml:lang, xml:space und Dokumenttyp.</summary>
      <param name="nt">Die zum Atomisieren von Zeichenfolgen zu verwendende <see cref="T:System.Xml.XmlNameTable" />.Wenn diese null ist, wird stattdessen die Namenstabelle zum Erstellen von <paramref name="nsMgr" /> verwendet.Weitere Informationen zu atomisierten Zeichenfolgen finden Sie unter <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">Der <see cref="T:System.Xml.XmlNamespaceManager" />, der für die Suche nach Namespaceinformationen verwendet werden soll, oder null. </param>
      <param name="docTypeName">Der Name der Dokumenttypdeklaration. </param>
      <param name="pubId">Der öffentliche Bezeichner. </param>
      <param name="sysId">Der Systembezeichner. </param>
      <param name="internalSubset">Die Teilmenge der internen DTD.Die DTD-Teilmenge wird für die Entitätsauflösung verwendet, nicht für die Dokumentvalidierung.</param>
      <param name="baseURI">Der Basis-URI für das XML-Fragment (der Speicherort, aus dem das Fragment geladen wurde). </param>
      <param name="xmlLang">Der xml:lang-Bereich. </param>
      <param name="xmlSpace">Ein <see cref="T:System.Xml.XmlSpace" />-Wert, der den xml:space-Bereich angibt. </param>
      <exception cref="T:System.Xml.XmlException">Bei <paramref name="nt" /> handelt es sich nicht um die gleiche XmlNameTable, die zum Erstellen von <paramref name="nsMgr" /> verwendet wird. </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>Initialisiert eine neue Instanz der XmlParserContext-Klasse mit den angegebenen Werten für <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, Basis-URI, xml:lang, xml:space, Codierung und Dokumenttyp.</summary>
      <param name="nt">Die zum Atomisieren von Zeichenfolgen zu verwendende <see cref="T:System.Xml.XmlNameTable" />.Wenn diese null ist, wird stattdessen die Namenstabelle zum Erstellen von <paramref name="nsMgr" /> verwendet.Weitere Informationen zu atomisierten Zeichenfolgen finden Sie unter <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">Der <see cref="T:System.Xml.XmlNamespaceManager" />, der für die Suche nach Namespaceinformationen verwendet werden soll, oder null. </param>
      <param name="docTypeName">Der Name der Dokumenttypdeklaration. </param>
      <param name="pubId">Der öffentliche Bezeichner. </param>
      <param name="sysId">Der Systembezeichner. </param>
      <param name="internalSubset">Die Teilmenge der internen DTD.Die DTD wird für die Entitätsauflösung verwendet, nicht für die Dokumentvalidierung.</param>
      <param name="baseURI">Der Basis-URI für das XML-Fragment (der Speicherort, aus dem das Fragment geladen wurde). </param>
      <param name="xmlLang">Der xml:lang-Bereich. </param>
      <param name="xmlSpace">Ein <see cref="T:System.Xml.XmlSpace" />-Wert, der den xml:space-Bereich angibt. </param>
      <param name="enc">Ein <see cref="T:System.Text.Encoding" />-Objekt, das die Codierungseinstellung angibt. </param>
      <exception cref="T:System.Xml.XmlException">Bei <paramref name="nt" /> handelt es sich nicht um die gleiche XmlNameTable, die zum Erstellen von <paramref name="nsMgr" /> verwendet wird. </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace)">
      <summary>Initialisiert eine neue Instanz der XmlParserContext-Klasse mit den angegebenen Werten für <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, xml:lang und xml:space.</summary>
      <param name="nt">Die zum Atomisieren von Zeichenfolgen zu verwendende <see cref="T:System.Xml.XmlNameTable" />.Wenn diese null ist, wird stattdessen die Namenstabelle zum Erstellen von <paramref name="nsMgr" /> verwendet.Weitere Informationen zu atomisierten Zeichenfolgen finden Sie unter <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">Der <see cref="T:System.Xml.XmlNamespaceManager" />, der für die Suche nach Namespaceinformationen verwendet werden soll, oder null. </param>
      <param name="xmlLang">Der xml:lang-Bereich. </param>
      <param name="xmlSpace">Ein <see cref="T:System.Xml.XmlSpace" />-Wert, der den xml:space-Bereich angibt. </param>
      <exception cref="T:System.Xml.XmlException">Bei <paramref name="nt" /> handelt es sich nicht um die gleiche XmlNameTable, die zum Erstellen von <paramref name="nsMgr" /> verwendet wird. </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>Initialisiert eine neue Instanz der XmlParserContext-Klasse mit den angegebenen Werten für <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, xml:lang, xml:space sowie Codierung.</summary>
      <param name="nt">Die zum Atomisieren von Zeichenfolgen zu verwendende <see cref="T:System.Xml.XmlNameTable" />.Wenn diese null ist, wird stattdessen die Namenstabelle zum Erstellen von <paramref name="nsMgr" /> verwendet.Weitere Informationen zu atomisierten Zeichenfolgen finden Sie unter <see cref="T:System.Xml.XmlNameTable" />.</param>
      <param name="nsMgr">Der <see cref="T:System.Xml.XmlNamespaceManager" />, der für die Suche nach Namespaceinformationen verwendet werden soll, oder null. </param>
      <param name="xmlLang">Der xml:lang-Bereich. </param>
      <param name="xmlSpace">Ein <see cref="T:System.Xml.XmlSpace" />-Wert, der den xml:space-Bereich angibt. </param>
      <param name="enc">Ein <see cref="T:System.Text.Encoding" />-Objekt, das die Codierungseinstellung angibt. </param>
      <exception cref="T:System.Xml.XmlException">Bei <paramref name="nt" /> handelt es sich nicht um die gleiche XmlNameTable, die zum Erstellen von <paramref name="nsMgr" /> verwendet wird. </exception>
    </member>
    <member name="P:System.Xml.XmlParserContext.BaseURI">
      <summary>Ruft den Basis-URI ab oder legt diesen fest.</summary>
      <returns>Der Basis-URI, der zum Auflösen der DTD-Datei verwendet werden soll.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.DocTypeName">
      <summary>Ruft den Namen der Dokumenttypdeklaration ab oder legt diesen fest.</summary>
      <returns>Der Name der Dokumenttypdeklaration.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.Encoding">
      <summary>Ruft den Codierungstyp ab oder legt diesen fest.</summary>
      <returns>Ein <see cref="T:System.Text.Encoding" />-Objekt, das den Codierungstyp angibt.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.InternalSubset">
      <summary>Ruft die Teilmenge der internen DTD ab oder legt diese fest.</summary>
      <returns>Die Teilmenge der internen DTD.Diese Eigenschaft gibt z. B. den Inhalt zwischen den eckigen Klammern &lt;!DOCTYPE doc [...]&gt; zurück.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NamespaceManager">
      <summary>Ruft den <see cref="T:System.Xml.XmlNamespaceManager" /> ab oder legt diesen fest.</summary>
      <returns>XmlNamespaceManager.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NameTable">
      <summary>Ruft die zum Atomisieren von Zeichenfolgen verwendete <see cref="T:System.Xml.XmlNameTable" /> ab.Weitere Informationen zu atomisierten Zeichenfolgen finden Sie unter <see cref="T:System.Xml.XmlNameTable" />.</summary>
      <returns>XmlNameTable.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.PublicId">
      <summary>Ruft den öffentlichen Bezeichner ab oder legt diesen fest.</summary>
      <returns>Der öffentliche Bezeichner.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.SystemId">
      <summary>Ruft den Systembezeichner ab oder legt diesen fest.</summary>
      <returns>Der Systembezeichner.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlLang">
      <summary>Ruft den aktuellen xml:lang-Bereich ab oder legt diesen fest.</summary>
      <returns>Der aktuelle xml:lang-Bereich.Wenn xml:lang im Gültigkeitsbereich nicht vorhanden ist, wird String.Empty zurückgegeben.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlSpace">
      <summary>Ruft den aktuellen xml:space-Bereich ab oder legt diesen fest.</summary>
      <returns>Ein <see cref="T:System.Xml.XmlSpace" />-Wert, der den xml:space-Bereich angibt.</returns>
    </member>
    <member name="T:System.Xml.XmlQualifiedName">
      <summary>Stellt einen XML-gekennzeichneten Namen dar.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Xml.XmlQualifiedName" />-Klasse.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Xml.XmlQualifiedName" />-Klasse mit dem angegebenen Namen.</summary>
      <param name="name">Der als Name für das <see cref="T:System.Xml.XmlQualifiedName" />-Objekt zu verwendende lokale Name. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Xml.XmlQualifiedName" />-Klasse mit dem angegebenen Namen und Namespace.</summary>
      <param name="name">Der als Name für das <see cref="T:System.Xml.XmlQualifiedName" />-Objekt zu verwendende lokale Name. </param>
      <param name="ns">Der Namespace für das <see cref="T:System.Xml.XmlQualifiedName" />-Objekt. </param>
    </member>
    <member name="F:System.Xml.XmlQualifiedName.Empty">
      <summary>Stellt einen leeren <see cref="T:System.Xml.XmlQualifiedName" /> bereit.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene <see cref="T:System.Xml.XmlQualifiedName" />-Objekt mit dem aktuellen <see cref="T:System.Xml.XmlQualifiedName" />-Objekt identisch ist. </summary>
      <returns>true, wenn beide dieselbe Objektinstanz sind, andernfalls false.</returns>
      <param name="other">Das <see cref="T:System.Xml.XmlQualifiedName" />, das verglichen werden soll. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.GetHashCode">
      <summary>Gibt den Hashcode für den <see cref="T:System.Xml.XmlQualifiedName" /> zurück.</summary>
      <returns>Ein Hashcode für dieses Objekt.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.IsEmpty">
      <summary>Ruft einen Wert ab, der angibt, ob <see cref="T:System.Xml.XmlQualifiedName" /> leer ist.</summary>
      <returns>true, wenn Name und Namespace leere Zeichenfolgen sind, andernfalls false.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Name">
      <summary>Ruft eine Zeichenfolgendarstellung für den qualifizierten Namen des <see cref="T:System.Xml.XmlQualifiedName" /> ab.</summary>
      <returns>Eine Zeichenfolgendarstellung des gekennzeichneten Namens oder String.Empty, wenn kein Name für das Objekt definiert ist.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Namespace">
      <summary>Ruft eine Zeichenfolgendarstellung für den Namespaces des <see cref="T:System.Xml.XmlQualifiedName" /> ab.</summary>
      <returns>Eine Zeichenfolgendarstellung für den Namespace oder String.Empty, wenn kein Namespace für das Objekt definiert ist.</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Equality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>Vergleicht zwei <see cref="T:System.Xml.XmlQualifiedName" />-Objekte.</summary>
      <returns>true, wenn beide Objekte dieselben Werte für Name und Namespace aufweisen, andernfalls false.</returns>
      <param name="a">Ein zu vergleichender <see cref="T:System.Xml.XmlQualifiedName" />. </param>
      <param name="b">Ein zu vergleichender <see cref="T:System.Xml.XmlQualifiedName" />. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Inequality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>Vergleicht zwei <see cref="T:System.Xml.XmlQualifiedName" />-Objekte.</summary>
      <returns>true, wenn die beiden Objekte unterschiedliche Werte für Name und Namespace aufweisen, andernfalls false.</returns>
      <param name="a">Ein zu vergleichender <see cref="T:System.Xml.XmlQualifiedName" />. </param>
      <param name="b">Ein zu vergleichender <see cref="T:System.Xml.XmlQualifiedName" />. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString">
      <summary>Gibt den Zeichenfolgenwert von <see cref="T:System.Xml.XmlQualifiedName" /> zurück.</summary>
      <returns>Der Zeichenfolgenwert von <see cref="T:System.Xml.XmlQualifiedName" /> im Format namespace:localname.Wenn für das Objekt kein Namespace definiert ist, gibt diese Methode nur den lokalen Namen zurück.</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString(System.String,System.String)">
      <summary>Gibt den Zeichenfolgenwert von <see cref="T:System.Xml.XmlQualifiedName" /> zurück.</summary>
      <returns>Der Zeichenfolgenwert von <see cref="T:System.Xml.XmlQualifiedName" /> im Format namespace:localname.Wenn für das Objekt kein Namespace definiert ist, gibt diese Methode nur den lokalen Namen zurück.</returns>
      <param name="name">Der Name des Objekts. </param>
      <param name="ns">Der Namespace des Objekts. </param>
    </member>
    <member name="T:System.Xml.XmlReader">
      <summary>Stellt einen Reader dar, der einen schnellen Zugriff auf XML-Daten bietet, ohne Zwischenspeicher und welcher nur vorwärts möglich ist.Um den .NET Framework-Quellcode für diesen Typ zu durchsuchen, rufen Sie die Verweisquelle auf.</summary>
    </member>
    <member name="M:System.Xml.XmlReader.#ctor">
      <summary>Initialisiert eine neue Instanz derXmlReader-Klasse.</summary>
    </member>
    <member name="P:System.Xml.XmlReader.AttributeCount">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse die Anzahl der Attribute für den aktuellen Knoten ab.</summary>
      <returns>Die Anzahl der Attribute im aktuellen Knoten.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.BaseURI">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den Basis-URI des aktuellen Knotens ab.</summary>
      <returns>Der Basis-URI des aktuellen Knotens.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadBinaryContent">
      <summary>Ruft einen Wert ab, der angibt, ob der <see cref="T:System.Xml.XmlReader" /> die Methoden für das Lesen von Inhalt im Binärformat implementiert.</summary>
      <returns>true, wenn die Methoden für das Lesen von Inhalt im Binärformat implementiert werden, andernfalls false.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadValueChunk">
      <summary>Ruft einen Wert ab, der angibt, ob der <see cref="T:System.Xml.XmlReader" /> die angegebene <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" />-Methode implementiert.</summary>
      <returns>true, wenn der <see cref="T:System.Xml.XmlReader" /> die <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" />-Methode implementiert, andernfalls false.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanResolveEntity">
      <summary>Ruft einen Wert ab, der angibt, ob dieser Reader Entitäten analysieren und auflösen kann.</summary>
      <returns>true, wenn der Reader Entitäten analysieren und auflösen kann, andernfalls false.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream)">
      <summary>Erstellt mit dem angegebenen Stream mit den Standardeinstellungen eine neue <see cref="T:System.Xml.XmlReader" />-Instanz.</summary>
      <returns>Ein Objekt, mit dem die im Stream enthaltenen XML-Daten gelesen werden.</returns>
      <param name="input">Der Stream, der die XML-Daten enthält.Der <see cref="T:System.Xml.XmlReader" /> überprüft die ersten Bytes des Streams und durchsucht sie nach einer Bytereihenfolgemarkierung oder einem anderen Codierungszeichen.Nachdem die Codierung bestimmt wurde, wird sie zum weiteren Lesen des Streams verwendet, und die Eingabe wird weiterhin als Stream von (Unicode-)Zeichen analysiert.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="input" />-Wert ist null.</exception>
      <exception cref="T:System.Security.SecurityException">Der <see cref="T:System.Xml.XmlReader" /> verfügt nicht über ausreichende Berechtigungen für den Zugriff auf den Speicherort der XML-Daten.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings)">
      <summary>Erstellt eine neue <see cref="T:System.Xml.XmlReader" />-Instanz mit dem angegebenen Stream und den angegebenen Einstellungen.</summary>
      <returns>Ein Objekt, mit dem die im Stream enthaltenen XML-Daten gelesen werden.</returns>
      <param name="input">Der Stream, der die XML-Daten enthält.Der <see cref="T:System.Xml.XmlReader" /> überprüft die ersten Bytes des Streams und durchsucht sie nach einer Bytereihenfolgemarkierung oder einem anderen Codierungszeichen.Nachdem die Codierung bestimmt wurde, wird sie zum weiteren Lesen des Streams verwendet, und die Eingabe wird weiterhin als Stream von (Unicode-)Zeichen analysiert.</param>
      <param name="settings">Die Einstellungen für die neue <see cref="T:System.Xml.XmlReader" />-Instanz.Dieser Wert kann null sein.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="input" />-Wert ist null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>Erstellt mit dem angegebenen Stream, den Einstellungen und den Kontextinformationen für Analysezwecke eine neue <see cref="T:System.Xml.XmlReader" />-Instanz.</summary>
      <returns>Ein Objekt, mit dem die im Stream enthaltenen XML-Daten gelesen werden.</returns>
      <param name="input">Der Stream, der die XML-Daten enthält. Der <see cref="T:System.Xml.XmlReader" /> überprüft die ersten Bytes des Streams und durchsucht sie nach einer Bytereihenfolgemarkierung oder einem anderen Codierungszeichen.Nachdem die Codierung bestimmt wurde, wird sie zum weiteren Lesen des Streams verwendet, und die Eingabe wird weiterhin als Stream von (Unicode-)Zeichen analysiert.</param>
      <param name="settings">Die Einstellungen für die neue <see cref="T:System.Xml.XmlReader" />-Instanz.Dieser Wert kann null sein.</param>
      <param name="inputContext">Die Kontextinformationen, die zum Analysieren des XML-Fragments erforderlich sind.Die Kontextinformationen können die zu verwendende <see cref="T:System.Xml.XmlNameTable" />, die Codierung, den Namespacebereich, den aktuellen xml:lang-Bereich, den aktuellen xml:space-Bereich, den Basis-URI und die Dokumenttypdefinition enthalten.Dieser Wert kann null sein.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="input" />-Wert ist null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader)">
      <summary>Erstellt mit dem angegebenen Text-Reader eine neue <see cref="T:System.Xml.XmlReader" />-Instanz.</summary>
      <returns>Ein Objekt, mit dem die im Stream enthaltenen XML-Daten gelesen werden.</returns>
      <param name="input">Der Text-Reader, aus dem die XML-Daten gelesen werden sollen.Ein Text-Reader gibt einen Stream von Unicode-Zeichen zurück, sodass die in der XML-Deklaration angegebene Codierung nicht vom XML-Reader zum Decodieren des Datenstreams verwendet wird.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="input" />-Wert ist null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings)">
      <summary>Erstellt mit dem angegebenen Text-Reader und den angegebenen Einstellungen eine neue <see cref="T:System.Xml.XmlReader" />-Instanz.</summary>
      <returns>Ein Objekt, mit dem die im Stream enthaltenen XML-Daten gelesen werden.</returns>
      <param name="input">Der Text-Reader, aus dem die XML-Daten gelesen werden sollen.Ein Text-Reader gibt einen Stream von Unicode-Zeichen zurück, sodass die in der XML-Deklaration angegebene Codierung nicht vom XML-Reader zum Decodieren des Datenstreams verwendet wird.</param>
      <param name="settings">Die Einstellungen für den neuen <see cref="T:System.Xml.XmlReader" />.Dieser Wert kann null sein.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="input" />-Wert ist null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>Erstellt mit dem angegebenen Text-Reader, den Einstellungen und den Kontextinformationen für Analysezwecke eine neue <see cref="T:System.Xml.XmlReader" />-Instanz.</summary>
      <returns>Ein Objekt, mit dem die im Stream enthaltenen XML-Daten gelesen werden.</returns>
      <param name="input">Der Text-Reader, aus dem die XML-Daten gelesen werden sollen.Ein Text-Reader gibt einen Stream von Unicode-Zeichen zurück, sodass die in der XML-Deklaration angegebene Codierung nicht vom XML-Reader zum Decodieren des Datenstreams verwendet wird.</param>
      <param name="settings">Die Einstellungen für die neue <see cref="T:System.Xml.XmlReader" />-Instanz.Dieser Wert kann null sein.</param>
      <param name="inputContext">Die Kontextinformationen, die zum Analysieren des XML-Fragments erforderlich sind.Die Kontextinformationen können die zu verwendende <see cref="T:System.Xml.XmlNameTable" />, die Codierung, den Namespacebereich, den aktuellen xml:lang-Bereich, den aktuellen xml:space-Bereich, den Basis-URI und die Dokumenttypdefinition enthalten.Dieser Wert kann null sein.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="input" />-Wert ist null.</exception>
      <exception cref="T:System.ArgumentException">Die <see cref="P:System.Xml.XmlReaderSettings.NameTable" /> und die <see cref="P:System.Xml.XmlParserContext.NameTable" />-Eigenschaften enthalten Werte.(Nur eine dieser NameTable-Eigenschaften kann festgelegt und verwendet werden).</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String)">
      <summary>Erstellt eine neue <see cref="T:System.Xml.XmlReader" />-Instanz mit angegebenem URI.</summary>
      <returns>Ein Objekt, mit dem die im Stream enthaltenen XML-Daten gelesen werden.</returns>
      <param name="inputUri">Der URI der Datei, die die XML-Daten enthält.Mit der <see cref="T:System.Xml.XmlUrlResolver" />-Klasse wird der Pfad in eine kanonische Datendarstellung konvertiert.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="inputUri" />-Wert ist null.</exception>
      <exception cref="T:System.Security.SecurityException">Der <see cref="T:System.Xml.XmlReader" /> verfügt nicht über ausreichende Berechtigungen für den Zugriff auf den Speicherort der XML-Daten.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Die durch den URI angegebene Datei ist nicht vorhanden.</exception>
      <exception cref="T:System.UriFormatException">Unter .NET for Windows Store apps oder in der Portable Klassenbibliothek verwenden Sie stattdessen die Basisklassenausnahme <see cref="T:System.FormatException" />.Das URI-Format ist nicht korrekt.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String,System.Xml.XmlReaderSettings)">
      <summary>Erstellt mit dem angegebenen URI und den angegebenen Einstellungen eine neue <see cref="T:System.Xml.XmlReader" />-Instanz.</summary>
      <returns>Ein Objekt, mit dem die im Stream enthaltenen XML-Daten gelesen werden.</returns>
      <param name="inputUri">Der URI der Datei, die die XML-Daten enthält.Das <see cref="T:System.Xml.XmlResolver" />-Objekt für das <see cref="T:System.Xml.XmlReaderSettings" />-Objekt wird zum Konvertieren des Pfads in eine kanonische Datendarstellung verwendet.Wenn <see cref="P:System.Xml.XmlReaderSettings.XmlResolver" />null ist, wird ein neues <see cref="T:System.Xml.XmlUrlResolver" />-Objekt verwendet.</param>
      <param name="settings">Die Einstellungen für die neue <see cref="T:System.Xml.XmlReader" />-Instanz.Dieser Wert kann null sein.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="inputUri" />-Wert ist null.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Die durch den URI angegebene Datei kann nicht gefunden werden.</exception>
      <exception cref="T:System.UriFormatException">Unter .NET for Windows Store apps oder in der Portable Klassenbibliothek verwenden Sie stattdessen die Basisklassenausnahme <see cref="T:System.FormatException" />.Das URI-Format ist nicht korrekt.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.Xml.XmlReader,System.Xml.XmlReaderSettings)">
      <summary>Erstellt mit dem angegebenen XML-Reader und den angegebenen Einstellungen eine neue <see cref="T:System.Xml.XmlReader" />-Instanz.</summary>
      <returns>Ein Objekt, das das angegebene <see cref="T:System.Xml.XmlReader" />-Objekt umschließt.</returns>
      <param name="reader">Das Objekt, dass Sie als zugrunde liegenden XML-Reader verwenden möchten.</param>
      <param name="settings">Die Einstellungen für die neue <see cref="T:System.Xml.XmlReader" />-Instanz.Der Konformitätsgrad des <see cref="T:System.Xml.XmlReaderSettings" />-Objekts muss mit dem Konformitätsgrad des zugrunde liegenden Readers übereinstimmen oder auf <see cref="F:System.Xml.ConformanceLevel.Auto" /> festgelegt werden.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="reader" />-Wert ist null.</exception>
      <exception cref="T:System.InvalidOperationException">Wenn das <see cref="T:System.Xml.XmlReaderSettings" />-Objekt einen Konformitätsgrad angibt, der nicht mit dem Konformitätsgrad des zugrunde liegenden Readers übereinstimmt.- oder - Der zugrunde liegende <see cref="T:System.Xml.XmlReader" /> befindet sich in einem <see cref="F:System.Xml.ReadState.Error" />-Zustand oder einem <see cref="F:System.Xml.ReadState.Closed" />-Zustand.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Depth">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse die Tiefe des aktuellen Knotens im XML-Dokument ab.</summary>
      <returns>Die Tiefe des aktuellen Knotens im XML-Dokument.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose">
      <summary>Gibt alle von der aktuellen Instanz der <see cref="T:System.Xml.XmlReader" />-Klasse verwendeten Ressourcen frei.</summary>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose(System.Boolean)">
      <summary>Gibt die von <see cref="T:System.Xml.XmlReader" /> verwendeten nicht verwalteten Ressourcen und optional die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.EOF">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse einen Wert ab, der angibt, ob sich der Reader am Ende des Streams befindet.</summary>
      <returns>true, wenn der Reader am Ende des Streams positioniert ist, andernfalls false.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.Int32)">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den Wert des Attributs mit dem angegebenen Index ab.</summary>
      <returns>Der Wert des angegebenen Attributs.Diese Methode verschiebt den Reader nicht.</returns>
      <param name="i">Der Index des Attributs.Der Index ist nullbasiert.(Das erste Attribut hat den Index 0.)</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="i" /> liegt außerhalb des Bereichs.Es darf nicht negativ sein und muss kleiner als die Größe der Attributauflistung sein.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String)">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den Wert des Attributs mit dem angegebenen <see cref="P:System.Xml.XmlReader.Name" /> ab.</summary>
      <returns>Der Wert des angegebenen Attributs.Wenn das Attribut nicht gefunden wird oder Wert String.Empty ist, wird null zurückgegeben.</returns>
      <param name="name">Der qualifizierte Name des Attributs.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String,System.String)">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den Wert des Attributs mit dem angegebenen <see cref="P:System.Xml.XmlReader.LocalName" /> und <see cref="P:System.Xml.XmlReader.NamespaceURI" /> ab.</summary>
      <returns>Der Wert des angegebenen Attributs.Wenn das Attribut nicht gefunden wird oder Wert String.Empty ist, wird null zurückgegeben.Diese Methode verschiebt den Reader nicht.</returns>
      <param name="name">Der lokale Name des Attributs.</param>
      <param name="namespaceURI">Der Namespace-URI dieses Attributs.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetValueAsync">
      <summary>Ruft den Wert des aktuellen Knotens asynchron ab.</summary>
      <returns>Der Wert des aktuellen Knotens.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" /> asynchrone Methode wurde aufgerufen, ohne das <see cref="P:System.Xml.XmlReaderSettings.Async" />-Flag auf true festzulegen.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Nachricht ausgelöst "Legen Sie XmlReaderSettings.Async auf True fest, wenn Sie die Async-Methoden verwenden möchten."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasAttributes">
      <summary>Ruft einen Wert ab, der angibt, ob der aktuelle Knoten über Attribute verfügt.</summary>
      <returns>true, wenn der aktuelle Knoten über Attribute verfügt, andernfalls false.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasValue">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse einen Wert ab, der angibt, ob der aktuelle Knoten einen <see cref="P:System.Xml.XmlReader.Value" /> aufweisen kann.</summary>
      <returns>true, wenn der Knoten, auf dem der Reader derzeit positioniert ist, einen Value aufweisen darf, andernfalls false.Wenn false, weist der Knoten den Wert String.Empty auf.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsDefault">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse einen Wert ab, der angibt, ob der aktuelle Knoten ein Attribut ist, das aus dem in der DTD oder dem Schema definierten Standardwert generiert wurde.</summary>
      <returns>true, wenn der aktuelle Knoten ein Attribut ist, dessen Wert aus dem in der DTD oder dem Schema definierten Standardwert generiert wurde. false, wenn der Attributwert explizit festgelegt wurde.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsEmptyElement">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse einen Wert ab, der angibt, ob der aktuelle Knoten ein leeres Element ist (z. B. &lt;MyElement/&gt;).</summary>
      <returns>true, wenn der aktuelle Knoten ein Element ist (<see cref="P:System.Xml.XmlReader.NodeType" /> ist gleich XmlNodeType.Element), das mit /&gt; endet, andernfalls false.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsName(System.String)">
      <summary>Gibt einen Wert zurück, der angibt, ob das Zeichenfolgenargument ein gültiger XML-Name ist.</summary>
      <returns>true, wenn der Name gültig ist, andernfalls false.</returns>
      <param name="str">Der Name, dessen Gültigkeit validiert werden soll.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="str" />-Wert ist null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsNameToken(System.String)">
      <summary>Gibt einen Wert zurück, der angibt, ob das Zeichenfolgenargument ein gültiges XML-Namenstoken ist.</summary>
      <returns>true, wenn es sich um ein gültiges Namenstoken handelt, andernfalls false.</returns>
      <param name="str">Das zu validierende Namenstoken.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="str" />-Wert ist null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement">
      <summary>Ruft <see cref="M:System.Xml.XmlReader.MoveToContent" /> auf und überprüft, ob der aktuelle Inhaltsknoten ein Starttag oder ein leeres Elementtag ist.</summary>
      <returns>true, wenn <see cref="M:System.Xml.XmlReader.MoveToContent" /> ein Starttag oder ein leeres Elementtag findet. false, wenn ein anderer Knotentyp als XmlNodeType.Element gefunden wurde.</returns>
      <exception cref="T:System.Xml.XmlException">Im Eingabestream wurde unzulässiger XML-Code gefunden.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String)">
      <summary>Ruft <see cref="M:System.Xml.XmlReader.MoveToContent" /> auf und überprüft, ob der aktuelle Inhaltsknoten ein Starttag oder ein leeres Elementtag ist und die <see cref="P:System.Xml.XmlReader.Name" />-Eigenschaft des gefundenen Elements mit dem angegebenen Argument übereinstimmt.</summary>
      <returns>true, wenn der resultierende Knoten ein Element ist und die Name-Eigenschaft mit der angegebenen Zeichenfolge übereinstimmt.false, wenn ein anderer Knotentyp als XmlNodeType.Element gefunden wurde oder die Name-Elementeigenschaft nicht mit der angegebenen Zeichenfolge übereinstimmt.</returns>
      <param name="name">Die mit der Name-Eigenschaft des gefundenen Elements verglichene Zeichenfolge.</param>
      <exception cref="T:System.Xml.XmlException">Im Eingabestream wurde unzulässiger XML-Code gefunden.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String,System.String)">
      <summary>Ruft <see cref="M:System.Xml.XmlReader.MoveToContent" /> auf und überprüft, ob der aktuelle Inhaltsknoten ein Starttag oder ein leeres Elementtag ist und ob die <see cref="P:System.Xml.XmlReader.LocalName" />-Eigenschaft und die <see cref="P:System.Xml.XmlReader.NamespaceURI" />-Eigenschaft des gefundenen Elements mit den angegebenen Zeichenfolgen übereinstimmen.</summary>
      <returns>true, wenn der resultlierende Knoten ein Element ist.false, wenn ein anderer Knotentyp als XmlNodeType.Element gefunden wurde oder die LocalName-Eigenschaft und die NamespaceURI-Eigenschaft des Elements nicht mit den angegebenen Zeichenfolgen übereinstimmen.</returns>
      <param name="localname">Die mit der LocalName-Eigenschaft des gefundenen Elements zu vergleichende Zeichenfolge.</param>
      <param name="ns">Die mit der NamespaceURI-Eigenschaft des gefundenen Elements zu vergleichende Zeichenfolge.</param>
      <exception cref="T:System.Xml.XmlException">Im Eingabestream wurde unzulässiger XML-Code gefunden.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.Int32)">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den Wert des Attributs mit dem angegebenen Index ab.</summary>
      <returns>Der Wert des angegebenen Attributs.</returns>
      <param name="i">Der Index des Attributs.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String)">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den Wert des Attributs mit dem angegebenen <see cref="P:System.Xml.XmlReader.Name" /> ab.</summary>
      <returns>Der Wert des angegebenen Attributs.Wenn das Attribut nicht gefunden wurde, wird null zurückgegeben.</returns>
      <param name="name">Der qualifizierte Name des Attributs.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String,System.String)">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den Wert des Attributs mit dem angegebenen <see cref="P:System.Xml.XmlReader.LocalName" /> und <see cref="P:System.Xml.XmlReader.NamespaceURI" /> ab.</summary>
      <returns>Der Wert des angegebenen Attributs.Wenn das Attribut nicht gefunden wurde, wird null zurückgegeben.</returns>
      <param name="name">Der lokale Name des Attributs.</param>
      <param name="namespaceURI">Der Namespace-URI dieses Attributs.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.LocalName">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den lokalen Namen des aktuellen Knotens ab.</summary>
      <returns>Der Name des aktuellen Knotens ohne das Präfix.Der LocalName für das &lt;bk:book&gt;-Element lautet z. B. book.Bei unbenannten Knotentypen wie Text, Comment usw. gibt diese Eigenschaft String.Empty zurück.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.LookupNamespace(System.String)">
      <summary>Löst beim Überschreiben in einer abgeleiteten Klasse ein Namespacepräfix im Gültigkeitsbereich des aktuellen Elements auf.</summary>
      <returns>Der Namespace-URI, dem das Präfix zugeordnet ist, oder null, wenn kein entsprechendes Präfix gefunden wird.</returns>
      <param name="prefix">Das Präfix, dessen Namespace-URI aufgelöst werden soll.Um eine Übereinstimmung mit dem Standardnamespace zu erhalten, übergeben Sie eine leere Zeichenfolge.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.Int32)">
      <summary>Wechselt beim Überschreiben in einer abgeleiteten Klasse zum Attribut mit dem angegebenen Index.</summary>
      <param name="i">Der Index des Attributs.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Parameter hat einen negativen Wert.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String)">
      <summary>Wechselt beim Überschreiben in einer abgeleiteten Klasse zum Attribut mit dem angegebenen <see cref="P:System.Xml.XmlReader.Name" />.</summary>
      <returns>true, wenn das Attribut gefunden wurde, andernfalls false.Bei einem Wert von false ändert sich die Position des Readers nicht.</returns>
      <param name="name">Der qualifizierte Name des Attributs.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.ArgumentException">Der Parameter ist eine leere Zeichenfolge.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String,System.String)">
      <summary>Wechselt beim Überschreiben in einer abgeleiteten Klasse zum Attribut mit dem angegebenen <see cref="P:System.Xml.XmlReader.LocalName" /> und dem angegebenen <see cref="P:System.Xml.XmlReader.NamespaceURI" />.</summary>
      <returns>true, wenn das Attribut gefunden wurde, andernfalls false.Bei einem Wert von false ändert sich die Position des Readers nicht.</returns>
      <param name="name">Der lokale Name des Attributs.</param>
      <param name="ns">Der Namespace-URI dieses Attributs.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.ArgumentNullException">Beide Parameter-Werte sind null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContent">
      <summary>Überprüft, ob der aktuelle Knoten ein Inhaltsknoten (Textknoten ohne Leerraum, CDATA-, Element-, EndElement-, EntityReference- oder EndEntity-Knoten) ist.Wenn der Knoten kein Inhaltsknoten ist, springt der Reader zum nächsten Inhaltsknoten oder an das Ende der Datei.Knoten folgender Typen werden übersprungen: ProcessingInstruction, DocumentType, Comment, Whitespace und SignificantWhitespace.</summary>
      <returns>Der <see cref="P:System.Xml.XmlReader.NodeType" /> des von der Methode gefundenen aktuellen Knotens oder XmlNodeType.None, wenn der Reader das Ende des Eingabestreams erreicht hat.</returns>
      <exception cref="T:System.Xml.XmlException">Im Eingabestream wurde unzulässiger XML-Code gefunden.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContentAsync">
      <summary>Asynchrone Überprüfungen, ob der aktuelle Knoten ein Inhaltsknoten ist.Wenn der Knoten kein Inhaltsknoten ist, springt der Reader zum nächsten Inhaltsknoten oder an das Ende der Datei.</summary>
      <returns>Der <see cref="P:System.Xml.XmlReader.NodeType" /> des von der Methode gefundenen aktuellen Knotens oder XmlNodeType.None, wenn der Reader das Ende des Eingabestreams erreicht hat.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" /> asynchrone Methode wurde aufgerufen, ohne das <see cref="P:System.Xml.XmlReaderSettings.Async" />-Flag auf true festzulegen.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Nachricht ausgelöst "Legen Sie XmlReaderSettings.Async auf True fest, wenn Sie die Async-Methoden verwenden möchten."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToElement">
      <summary>Wechselt beim Überschreiben in einer abgeleiteten Klasse zu dem Element, das den aktuellen Attributknoten enthält.</summary>
      <returns>true, wenn der Reader auf einem Attribut positioniert ist (der Reader wechselt zu dem Element, das das Attribut besitzt); false, wenn der Reader nicht auf einem Attribut positioniert ist (die Position des Readers bleibt unverändert).</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToFirstAttribute">
      <summary>Wechselt beim Überschreiben in einer abgeleiteten Klasse zum ersten Attribut.</summary>
      <returns>true, wenn ein Attribut vorhanden ist (der Reader wechselt zum ersten Attribut), andernfalls false (die Position des Readers bleibt unverändert).</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToNextAttribute">
      <summary>Wechselt beim Überschreiben in einer abgeleiteten Klasse zum nächsten Attribut.</summary>
      <returns>true, wenn ein nächstes Attribut vorhanden ist; false, wenn keine weiteren Attribute vorhanden sind.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Name">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den gekennzeichneten Namen des aktuellen Knotens ab.</summary>
      <returns>Der gekennzeichnete Name des aktuellen Knotens.Der Name für das &lt;bk:book&gt;-Element lautet z. B. bk:book.Der zurückgegebene Name hängt vom <see cref="P:System.Xml.XmlReader.NodeType" /> des Knotens ab.Die folgenden Knotentypen geben die jeweils aufgeführten Werte zurück.Alle anderen Knotentypen geben eine leere Zeichenfolge zurück.Knotentyp Name AttributeDer Name des Attributs. DocumentTypeDer Name des Dokumenttyps. ElementDer Tagname. EntityReferenceDer Name der Entität, auf die verwiesen wird. ProcessingInstructionDas Ziel der Verarbeitungsanweisung. XmlDeclarationDas xml-Zeichenfolgenliteral. </returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NamespaceURI">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den Namespace-URI (entsprechend der Definition in der Namespacespezifikation des W3C) des Knotens ab, auf dem der Reader positioniert ist.</summary>
      <returns>Der Namespace-URI des aktuellen Knotens, andernfalls eine leere Zeichenfolge.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NameTable">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse die <see cref="T:System.Xml.XmlNameTable" /> ab, die dieser Implementierung zugeordnet ist.</summary>
      <returns>Die XmlNameTable, die das Abrufen der atomisierten Version einer Zeichenfolge innerhalb des Knotens erlaubt.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NodeType">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den Typ des aktuellen Knotens ab.</summary>
      <returns>Einer der Enumerationswerte, die den Typ des aktuellen Knotens angeben.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Prefix">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse das dem aktuellen Knoten zugeordnete Namespacepräfix ab.</summary>
      <returns>Das dem aktuellen Knoten zugeordnete Namespacepräfix.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Read">
      <summary>Liest beim Überschreiben in einer abgeleiteten Klasse den nächsten Knoten aus dem Stream.</summary>
      <returns>true, wenn der nächste Knoten erfolgreich gelesen wurde, andernfalls, false.</returns>
      <exception cref="T:System.Xml.XmlException">Beim Analysieren der XML-Daten ist ein Fehler aufgetreten.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAsync">
      <summary>Liest den nächsten Knoten aus dem Stream asynchron.</summary>
      <returns>true, wenn der nächste Knoten erfolgreich gelesen wurde, false, wenn keine weiteren zu lesenden Knoten vorhanden sind.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" /> asynchrone Methode wurde aufgerufen, ohne das <see cref="P:System.Xml.XmlReaderSettings.Async" />-Flag auf true festzulegen.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Nachricht ausgelöst "Legen Sie XmlReaderSettings.Async auf True fest, wenn Sie die Async-Methoden verwenden möchten."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAttributeValue">
      <summary>Löst beim Überschreiben in einer abgeleiteten Klasse den Attributwert in einen oder mehrere Knoten vom Typ Text, EntityReference oder EndEntity auf.</summary>
      <returns>true, wenn zurückzugebende Knoten vorhanden sind.false, wenn der Reader beim ersten Aufruf nicht auf einem Attributknoten positioniert ist oder alle Attributwerte gelesen wurden.Ein leeres Attribut, z. B. misc="", gibt true mit einem einzelnen Knoten mit dem Wert String.Empty zurück.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Liest den Inhalt als Objekt vom angegebenen Typ.</summary>
      <returns>Der verkettete Textinhalt oder Attributwert, der in den angeforderten Typ konvertiert wurde.</returns>
      <param name="returnType">Der Typ des zurückzugebenden Werts.Hinweis   Seit der Veröffentlichung von .NET Framework 3.5 kann der Wert des <paramref name="returnType" />-Parameters nun auch auf den <see cref="T:System.DateTimeOffset" />-Typ festgelegt werden.</param>
      <param name="namespaceResolver">Ein <see cref="T:System.Xml.IXmlNamespaceResolver" />-Objekt, das für die Auflösung von Präfixen von Namespaces verwendet wird, die im Zusammenhang mit der Typkonvertierung stehen.Dieses kann zum Beispiel beim Konvertieren eines <see cref="T:System.Xml.XmlQualifiedName" />-Objekts in eine xs:string verwendet werden.Dieser Wert kann null sein.</param>
      <exception cref="T:System.FormatException">Der Inhalt weist nicht das richtige Format für den Zieltyp auf.</exception>
      <exception cref="T:System.InvalidCastException">Die versuchte Typumwandlung ist ungültig.</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="returnType" />-Wert ist null.</exception>
      <exception cref="T:System.InvalidOperationException">Der aktuelle Knoten ist kein unterstützter Knotentyp.Weitere Informationen finden Sie in der nachfolgenden Tabelle.</exception>
      <exception cref="T:System.OverflowException">Lesen von Decimal.MaxValue.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Liest den Inhalt asynchron als Objekt vom angegebenen Typ.</summary>
      <returns>Der verkettete Textinhalt oder Attributwert, der in den angeforderten Typ konvertiert wurde.</returns>
      <param name="returnType">Der Typ des zurückzugebenden Werts.</param>
      <param name="namespaceResolver">Ein <see cref="T:System.Xml.IXmlNamespaceResolver" />-Objekt, das für die Auflösung von Präfixen von Namespaces verwendet wird, die im Zusammenhang mit der Typkonvertierung stehen.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" /> asynchrone Methode wurde aufgerufen, ohne das <see cref="P:System.Xml.XmlReaderSettings.Async" />-Flag auf true festzulegen.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Nachricht ausgelöst "Legen Sie XmlReaderSettings.Async auf True fest, wenn Sie die Async-Methoden verwenden möchten."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>Liest den Inhalt und gibt die Base64-decodierten binären Bytes zurück.</summary>
      <returns>Die Anzahl der in den Puffer geschriebenen Bytes.</returns>
      <param name="buffer">Der Puffer, in den der resultierende Text kopiert werden soll.Dieser Wert darf nicht null sein.</param>
      <param name="index">Der Offset im Puffer, an dem mit dem Kopieren des Ergebnisses begonnen werden soll.</param>
      <param name="count">Die maximale Anzahl von Bytes, die in den Puffer kopiert werden sollen.Diese Methode gibt die tatsächliche Anzahl von kopierten Bytes zurück.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="buffer" />-Wert ist null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)" /> wird auf dem aktuellen Knoten nicht unterstützt.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Index im Puffer oder Index + Anzahl übersteigen die Größe des zugeordneten Puffers.</exception>
      <exception cref="T:System.NotSupportedException">Die <see cref="T:System.Xml.XmlReader" />-Implementierung unterstützt diese Methode nicht.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Liest den Inhalt asynchron und gibt die Base64-decodierten binären Bytes zurück.</summary>
      <returns>Die Anzahl der in den Puffer geschriebenen Bytes.</returns>
      <param name="buffer">Der Puffer, in den der resultierende Text kopiert werden soll.Dieser Wert darf nicht null sein.</param>
      <param name="index">Der Offset im Puffer, an dem mit dem Kopieren des Ergebnisses begonnen werden soll.</param>
      <param name="count">Die maximale Anzahl von Bytes, die in den Puffer kopiert werden sollen.Diese Methode gibt die tatsächliche Anzahl von kopierten Bytes zurück.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" /> asynchrone Methode wurde aufgerufen, ohne das <see cref="P:System.Xml.XmlReaderSettings.Async" />-Flag auf true festzulegen.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Nachricht ausgelöst "Legen Sie XmlReaderSettings.Async auf True fest, wenn Sie die Async-Methoden verwenden möchten."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>Liest den Inhalt und gibt die BinHex-decodierten binären Bytes zurück.</summary>
      <returns>Die Anzahl der in den Puffer geschriebenen Bytes.</returns>
      <param name="buffer">Der Puffer, in den der resultierende Text kopiert werden soll.Dieser Wert darf nicht null sein.</param>
      <param name="index">Der Offset im Puffer, an dem mit dem Kopieren des Ergebnisses begonnen werden soll.</param>
      <param name="count">Die maximale Anzahl von Bytes, die in den Puffer kopiert werden sollen.Diese Methode gibt die tatsächliche Anzahl von kopierten Bytes zurück.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="buffer" />-Wert ist null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)" /> wird auf dem aktuellen Knoten nicht unterstützt.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Index im Puffer oder Index + Anzahl übersteigen die Größe des zugeordneten Puffers.</exception>
      <exception cref="T:System.NotSupportedException">Die <see cref="T:System.Xml.XmlReader" />-Implementierung unterstützt diese Methode nicht.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Liest den Inhalt asynchron und gibt die BinHex-decodierten binären Bytes zurück.</summary>
      <returns>Die Anzahl der in den Puffer geschriebenen Bytes.</returns>
      <param name="buffer">Der Puffer, in den der resultierende Text kopiert werden soll.Dieser Wert darf nicht null sein.</param>
      <param name="index">Der Offset im Puffer, an dem mit dem Kopieren des Ergebnisses begonnen werden soll.</param>
      <param name="count">Die maximale Anzahl von Bytes, die in den Puffer kopiert werden sollen.Diese Methode gibt die tatsächliche Anzahl von kopierten Bytes zurück.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" /> asynchrone Methode wurde aufgerufen, ohne das <see cref="P:System.Xml.XmlReaderSettings.Async" />-Flag auf true festzulegen.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Nachricht ausgelöst "Legen Sie XmlReaderSettings.Async auf True fest, wenn Sie die Async-Methoden verwenden möchten."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBoolean">
      <summary>Liest den Textinhalt an der aktuellen Position als Boolean.</summary>
      <returns>Der Textinhalt als <see cref="T:System.Boolean" />-Objekt.</returns>
      <exception cref="T:System.InvalidCastException">Die versuchte Typumwandlung ist ungültig.</exception>
      <exception cref="T:System.FormatException">Das Zeichenfolgenformat ist nicht gültig.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDateTimeOffset">
      <summary>Liest den Textinhalt an der aktuellen Position als <see cref="T:System.DateTimeOffset" />-Objekt.</summary>
      <returns>Der Textinhalt als <see cref="T:System.DateTimeOffset" />-Objekt.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDecimal">
      <summary>Liest den Textinhalt an der aktuellen Position als <see cref="T:System.Decimal" />-Objekt.</summary>
      <returns>Der Textinhalt an der aktuellen Position als <see cref="T:System.Decimal" />-Objekt.</returns>
      <exception cref="T:System.InvalidCastException">Die versuchte Typumwandlung ist ungültig.</exception>
      <exception cref="T:System.FormatException">Das Zeichenfolgenformat ist nicht gültig.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDouble">
      <summary>Liest den Textinhalt an der aktuellen Position als Gleitkommazahl mit doppelter Genauigkeit.</summary>
      <returns>Der Textinhalt als Gleitkommazahl mit doppelter Genauigkeit.</returns>
      <exception cref="T:System.InvalidCastException">Die versuchte Typumwandlung ist ungültig.</exception>
      <exception cref="T:System.FormatException">Das Zeichenfolgenformat ist nicht gültig.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsFloat">
      <summary>Liest den Textinhalt an der aktuellen Position als Gleitkommazahl mit einfacher Genauigkeit.</summary>
      <returns>Der Textinhalt an der aktuellen Position als Gleitkommazahl mit einfacher Genauigkeit.</returns>
      <exception cref="T:System.InvalidCastException">Die versuchte Typumwandlung ist ungültig.</exception>
      <exception cref="T:System.FormatException">Das Zeichenfolgenformat ist nicht gültig.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsInt">
      <summary>Liest den Textinhalt an der aktuellen Position als 32-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Der Textinhalt als 32-Bit-Ganzzahl mit Vorzeichen.</returns>
      <exception cref="T:System.InvalidCastException">Die versuchte Typumwandlung ist ungültig.</exception>
      <exception cref="T:System.FormatException">Das Zeichenfolgenformat ist nicht gültig.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsLong">
      <summary>Liest den Textinhalt an der aktuellen Position als 64-Bit-Ganzzahl mit Vorzeichen.</summary>
      <returns>Der Textinhalt als 64-Bit-Ganzzahl mit Vorzeichen.</returns>
      <exception cref="T:System.InvalidCastException">Die versuchte Typumwandlung ist ungültig.</exception>
      <exception cref="T:System.FormatException">Das Zeichenfolgenformat ist nicht gültig.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObject">
      <summary>Liest den Textinhalt an der aktuellen Position als <see cref="T:System.Object" />.</summary>
      <returns>Der Textinhalt als geeignetstes CLR-Objekt (Common Language Runtime).</returns>
      <exception cref="T:System.InvalidCastException">Die versuchte Typumwandlung ist ungültig.</exception>
      <exception cref="T:System.FormatException">Das Zeichenfolgenformat ist nicht gültig.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObjectAsync">
      <summary>Liest den Textinhalt an der aktuellen Position asynchron als <see cref="T:System.Object" />.</summary>
      <returns>Der Textinhalt als geeignetstes CLR-Objekt (Common Language Runtime).</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" /> asynchrone Methode wurde aufgerufen, ohne das <see cref="P:System.Xml.XmlReaderSettings.Async" />-Flag auf true festzulegen.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Nachricht ausgelöst "Legen Sie XmlReaderSettings.Async auf True fest, wenn Sie die Async-Methoden verwenden möchten."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsString">
      <summary>Liest den Textinhalt an der aktuellen Position als <see cref="T:System.String" />-Objekt.</summary>
      <returns>Der Textinhalt als <see cref="T:System.String" />-Objekt.</returns>
      <exception cref="T:System.InvalidCastException">Die versuchte Typumwandlung ist ungültig.</exception>
      <exception cref="T:System.FormatException">Das Zeichenfolgenformat ist nicht gültig.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsStringAsync">
      <summary>Liest den Textinhalt an der aktuellen Position asynchron als <see cref="T:System.String" />-Objekt.</summary>
      <returns>Der Textinhalt als <see cref="T:System.String" />-Objekt.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" /> asynchrone Methode wurde aufgerufen, ohne das <see cref="P:System.Xml.XmlReaderSettings.Async" />-Flag auf true festzulegen.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Nachricht ausgelöst "Legen Sie XmlReaderSettings.Async auf True fest, wenn Sie die Async-Methoden verwenden möchten."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Liest den Elementinhalt als angeforderten Typ.</summary>
      <returns>Der in das angeforderte typisierte Objekt konvertierte Elementinhalt.</returns>
      <param name="returnType">Der Typ des zurückzugebenden Werts.Hinweis   Seit der Veröffentlichung von .NET Framework 3.5 kann der Wert des <paramref name="returnType" />-Parameters nun auch auf den <see cref="T:System.DateTimeOffset" />-Typ festgelegt werden.</param>
      <param name="namespaceResolver">Ein <see cref="T:System.Xml.IXmlNamespaceResolver" />-Objekt, das für die Auflösung von Präfixen von Namespaces verwendet wird, die im Zusammenhang mit der Typkonvertierung stehen.</param>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Xml.XmlReader" /> wird nicht auf einem Element positioniert.</exception>
      <exception cref="T:System.Xml.XmlException">Das aktuelle Element enthält untergeordnete Elemente.- oder - Der Elementinhalt kann nicht in den angeforderten Typ konvertiert werden.</exception>
      <exception cref="T:System.ArgumentNullException">Die Methode wird mit null-Argumenten aufgerufen.</exception>
      <exception cref="T:System.OverflowException">Lesen von Decimal.MaxValue.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver,System.String,System.String)">
      <summary>Überprüft, ob der angegebene lokale Name und der angegebene Namespace-URI mit denen des aktuellen Elements übereinstimmen, und liest dann den Elementinhalt als angeforderten Typ.</summary>
      <returns>Der in das angeforderte typisierte Objekt konvertierte Elementinhalt.</returns>
      <param name="returnType">Der Typ des zurückzugebenden Werts.Hinweis   Seit der Veröffentlichung von .NET Framework 3.5 kann der Wert des <paramref name="returnType" />-Parameters nun auch auf den <see cref="T:System.DateTimeOffset" />-Typ festgelegt werden.</param>
      <param name="namespaceResolver">Ein <see cref="T:System.Xml.IXmlNamespaceResolver" />-Objekt, das für die Auflösung von Präfixen von Namespaces verwendet wird, die im Zusammenhang mit der Typkonvertierung stehen.</param>
      <param name="localName">Der lokale Name des Elements.</param>
      <param name="namespaceURI">Der Namespace-URI des Elements.</param>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Xml.XmlReader" /> wird nicht auf einem Element positioniert.</exception>
      <exception cref="T:System.Xml.XmlException">Das aktuelle Element enthält untergeordnete Elemente.- oder - Der Elementinhalt kann nicht in den angeforderten Typ konvertiert werden.</exception>
      <exception cref="T:System.ArgumentNullException">Die Methode wird mit null-Argumenten aufgerufen.</exception>
      <exception cref="T:System.ArgumentException">Der angegebene lokale Name und der Namespace-URI stimmen nicht mit dem Element überein, das gerade gelesen wird.</exception>
      <exception cref="T:System.OverflowException">Lesen von Decimal.MaxValue.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Liest den Elementinhalt asynchron als angeforderten Typ.</summary>
      <returns>Der in das angeforderte typisierte Objekt konvertierte Elementinhalt.</returns>
      <param name="returnType">Der Typ des zurückzugebenden Werts.</param>
      <param name="namespaceResolver">Ein <see cref="T:System.Xml.IXmlNamespaceResolver" />-Objekt, das für die Auflösung von Präfixen von Namespaces verwendet wird, die im Zusammenhang mit der Typkonvertierung stehen.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" /> asynchrone Methode wurde aufgerufen, ohne das <see cref="P:System.Xml.XmlReaderSettings.Async" />-Flag auf true festzulegen.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Nachricht ausgelöst "Legen Sie XmlReaderSettings.Async auf True fest, wenn Sie die Async-Methoden verwenden möchten."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>Liest das Element und decodiert den Base64-Inhalt.</summary>
      <returns>Die Anzahl der in den Puffer geschriebenen Bytes.</returns>
      <param name="buffer">Der Puffer, in den der resultierende Text kopiert werden soll.Dieser Wert darf nicht null sein.</param>
      <param name="index">Der Offset im Puffer, an dem mit dem Kopieren des Ergebnisses begonnen werden soll.</param>
      <param name="count">Die maximale Anzahl von Bytes, die in den Puffer kopiert werden sollen.Diese Methode gibt die tatsächliche Anzahl von kopierten Bytes zurück.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="buffer" />-Wert ist null.</exception>
      <exception cref="T:System.InvalidOperationException">Der aktuelle Knoten ist kein Elementknoten.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Index im Puffer oder Index + Anzahl übersteigen die Größe des zugeordneten Puffers.</exception>
      <exception cref="T:System.NotSupportedException">Die <see cref="T:System.Xml.XmlReader" />-Implementierung unterstützt diese Methode nicht.</exception>
      <exception cref="T:System.Xml.XmlException">Das Element enthält gemischten Inhalt.</exception>
      <exception cref="T:System.FormatException">Der Inhalt kann nicht in den angeforderten Typ konvertiert werden.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Liest das Element asynchron und decodiert den Base64-Inhalt.</summary>
      <returns>Die Anzahl der in den Puffer geschriebenen Bytes.</returns>
      <param name="buffer">Der Puffer, in den der resultierende Text kopiert werden soll.Dieser Wert darf nicht null sein.</param>
      <param name="index">Der Offset im Puffer, an dem mit dem Kopieren des Ergebnisses begonnen werden soll.</param>
      <param name="count">Die maximale Anzahl von Bytes, die in den Puffer kopiert werden sollen.Diese Methode gibt die tatsächliche Anzahl von kopierten Bytes zurück.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" /> asynchrone Methode wurde aufgerufen, ohne das <see cref="P:System.Xml.XmlReaderSettings.Async" />-Flag auf true festzulegen.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Nachricht ausgelöst "Legen Sie XmlReaderSettings.Async auf True fest, wenn Sie die Async-Methoden verwenden möchten."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>Liest das Element und decodiert den BinHex-Inhalt.</summary>
      <returns>Die Anzahl der in den Puffer geschriebenen Bytes.</returns>
      <param name="buffer">Der Puffer, in den der resultierende Text kopiert werden soll.Dieser Wert darf nicht null sein.</param>
      <param name="index">Der Offset im Puffer, an dem mit dem Kopieren des Ergebnisses begonnen werden soll.</param>
      <param name="count">Die maximale Anzahl von Bytes, die in den Puffer kopiert werden sollen.Diese Methode gibt die tatsächliche Anzahl von kopierten Bytes zurück.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="buffer" />-Wert ist null.</exception>
      <exception cref="T:System.InvalidOperationException">Der aktuelle Knoten ist kein Elementknoten.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Index im Puffer oder Index + Anzahl übersteigen die Größe des zugeordneten Puffers.</exception>
      <exception cref="T:System.NotSupportedException">Die <see cref="T:System.Xml.XmlReader" />-Implementierung unterstützt diese Methode nicht.</exception>
      <exception cref="T:System.Xml.XmlException">Das Element enthält gemischten Inhalt.</exception>
      <exception cref="T:System.FormatException">Der Inhalt kann nicht in den angeforderten Typ konvertiert werden.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Liest das Element asynchron und decodiert den BinHex-Inhalt.</summary>
      <returns>Die Anzahl der in den Puffer geschriebenen Bytes.</returns>
      <param name="buffer">Der Puffer, in den der resultierende Text kopiert werden soll.Dieser Wert darf nicht null sein.</param>
      <param name="index">Der Offset im Puffer, an dem mit dem Kopieren des Ergebnisses begonnen werden soll.</param>
      <param name="count">Die maximale Anzahl von Bytes, die in den Puffer kopiert werden sollen.Diese Methode gibt die tatsächliche Anzahl von kopierten Bytes zurück.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" /> asynchrone Methode wurde aufgerufen, ohne das <see cref="P:System.Xml.XmlReaderSettings.Async" />-Flag auf true festzulegen.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Nachricht ausgelöst "Legen Sie XmlReaderSettings.Async auf True fest, wenn Sie die Async-Methoden verwenden möchten."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean">
      <summary>Liest das aktuelle Element und gibt den Inhalt als <see cref="T:System.Boolean" />-Objekt zurück.</summary>
      <returns>Der Elementinhalt als <see cref="T:System.Boolean" />-Objekt.</returns>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Xml.XmlReader" /> wird nicht auf einem Element positioniert.</exception>
      <exception cref="T:System.Xml.XmlException">Das aktuelle Element enthält untergeordnete Elemente.- oder - Der Elementinhalt kann nicht in ein <see cref="T:System.Boolean" />-Objekt konvertiert werden.</exception>
      <exception cref="T:System.ArgumentNullException">Die Methode wird mit null-Argumenten aufgerufen.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean(System.String,System.String)">
      <summary>Überprüft, ob der angegebene lokale Name und der angegebene Namespace-URI mit denen des aktuellen Elements übereinstimmen, liest dann das aktuelle Element und gibt den Inhalt als <see cref="T:System.Boolean" />-Objekt zurück.</summary>
      <returns>Der Elementinhalt als <see cref="T:System.Boolean" />-Objekt.</returns>
      <param name="localName">Der lokale Name des Elements.</param>
      <param name="namespaceURI">Der Namespace-URI des Elements.</param>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Xml.XmlReader" /> wird nicht auf einem Element positioniert.</exception>
      <exception cref="T:System.Xml.XmlException">Das aktuelle Element enthält untergeordnete Elemente.- oder - Der Elementinhalt kann nicht in den angeforderten Typ konvertiert werden.</exception>
      <exception cref="T:System.ArgumentNullException">Die Methode wird mit null-Argumenten aufgerufen.</exception>
      <exception cref="T:System.ArgumentException">Der angegebene lokale Name und der Namespace-URI stimmen nicht mit dem Element überein, das gerade gelesen wird.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal">
      <summary>Liest das aktuelle Element und gibt den Inhalt als <see cref="T:System.Decimal" />-Objekt zurück.</summary>
      <returns>Der Elementinhalt als <see cref="T:System.Decimal" />-Objekt.</returns>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Xml.XmlReader" /> wird nicht auf einem Element positioniert.</exception>
      <exception cref="T:System.Xml.XmlException">Das aktuelle Element enthält untergeordnete Elemente.- oder - Der Elementinhalt kann nicht in <see cref="T:System.Decimal" /> konvertiert werden.</exception>
      <exception cref="T:System.ArgumentNullException">Die Methode wird mit null-Argumenten aufgerufen.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal(System.String,System.String)">
      <summary>Überprüft, ob der angegebene lokale Name und der angegebene Namespace-URI mit denen des aktuellen Elements übereinstimmen, liest dann das aktuelle Element und gibt den Inhalt als <see cref="T:System.Decimal" />-Objekt zurück.</summary>
      <returns>Der Elementinhalt als <see cref="T:System.Decimal" />-Objekt.</returns>
      <param name="localName">Der lokale Name des Elements.</param>
      <param name="namespaceURI">Der Namespace-URI des Elements.</param>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Xml.XmlReader" /> wird nicht auf einem Element positioniert.</exception>
      <exception cref="T:System.Xml.XmlException">Das aktuelle Element enthält untergeordnete Elemente.- oder - Der Elementinhalt kann nicht in <see cref="T:System.Decimal" /> konvertiert werden.</exception>
      <exception cref="T:System.ArgumentNullException">Die Methode wird mit null-Argumenten aufgerufen.</exception>
      <exception cref="T:System.ArgumentException">Der angegebene lokale Name und der Namespace-URI stimmen nicht mit dem Element überein, das gerade gelesen wird.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble">
      <summary>Liest das aktuelle Element und gibt den Inhalt als Gleitkommazahl mit doppelter Genauigkeit zurück.</summary>
      <returns>Der Elementinhalt als Gleitkommazahl mit doppelter Genauigkeit.</returns>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Xml.XmlReader" /> wird nicht auf einem Element positioniert.</exception>
      <exception cref="T:System.Xml.XmlException">Das aktuelle Element enthält untergeordnete Elemente.- oder - Der Elementinhalt kann nicht in eine Gleitkommazahl mit doppelter Genauigkeit konvertiert werden.</exception>
      <exception cref="T:System.ArgumentNullException">Die Methode wird mit null-Argumenten aufgerufen.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble(System.String,System.String)">
      <summary>Überprüft, ob der angegebene lokale Name und der angegebene Namespace-URI mit denen des aktuellen Elements übereinstimmen, liest dann das aktuelle Element und gibt den Inhalt als Gleitkommazahl mit doppelter Genauigkeit zurück.</summary>
      <returns>Der Elementinhalt als Gleitkommazahl mit doppelter Genauigkeit.</returns>
      <param name="localName">Der lokale Name des Elements.</param>
      <param name="namespaceURI">Der Namespace-URI des Elements.</param>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Xml.XmlReader" /> wird nicht auf einem Element positioniert.</exception>
      <exception cref="T:System.Xml.XmlException">Das aktuelle Element enthält untergeordnete Elemente.- oder - Der Elementinhalt kann nicht in den angeforderten Typ konvertiert werden.</exception>
      <exception cref="T:System.ArgumentNullException">Die Methode wird mit null-Argumenten aufgerufen.</exception>
      <exception cref="T:System.ArgumentException">Der angegebene lokale Name und der Namespace-URI stimmen nicht mit dem Element überein, das gerade gelesen wird.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat">
      <summary>Liest das aktuelle Element und gibt den Inhalt als Gleitkommazahl mit einfacher Genauigkeit zurück.</summary>
      <returns>Der Elementinhalt als Gleitkommazahl mit einfacher Genauigkeit.</returns>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Xml.XmlReader" /> wird nicht auf einem Element positioniert.</exception>
      <exception cref="T:System.Xml.XmlException">Das aktuelle Element enthält untergeordnete Elemente.- oder - Der Elementinhalt kann nicht in eine Gleitkommazahl mit einfacher Genauigkeit konvertiert werden.</exception>
      <exception cref="T:System.ArgumentNullException">Die Methode wird mit null-Argumenten aufgerufen.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat(System.String,System.String)">
      <summary>Überprüft, ob der angegebene lokale Name und der angegebene Namespace-URI mit denen des aktuellen Elements übereinstimmen, liest dann das aktuelle Element und gibt den Inhalt als Gleitkommazahl mit einfacher Genauigkeit zurück.</summary>
      <returns>Der Elementinhalt als Gleitkommazahl mit einfacher Genauigkeit.</returns>
      <param name="localName">Der lokale Name des Elements.</param>
      <param name="namespaceURI">Der Namespace-URI des Elements.</param>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Xml.XmlReader" /> wird nicht auf einem Element positioniert.</exception>
      <exception cref="T:System.Xml.XmlException">Das aktuelle Element enthält untergeordnete Elemente.- oder - Der Elementinhalt kann nicht in eine Gleitkommazahl mit einfacher Genauigkeit konvertiert werden.</exception>
      <exception cref="T:System.ArgumentNullException">Die Methode wird mit null-Argumenten aufgerufen.</exception>
      <exception cref="T:System.ArgumentException">Der angegebene lokale Name und der Namespace-URI stimmen nicht mit dem Element überein, das gerade gelesen wird.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt">
      <summary>Liest das aktuelle Element und gibt den Inhalt als 32-Bit-Ganzzahl mit Vorzeichen zurück.</summary>
      <returns>Der Elementinhalt als 32-Bit-Ganzzahl mit Vorzeichen.</returns>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Xml.XmlReader" /> wird nicht auf einem Element positioniert.</exception>
      <exception cref="T:System.Xml.XmlException">Das aktuelle Element enthält untergeordnete Elemente.- oder - Der Elementinhalt kann nicht in eine 32-Bit-Ganzzahl mit Vorzeichen konvertiert werden.</exception>
      <exception cref="T:System.ArgumentNullException">Die Methode wird mit null-Argumenten aufgerufen.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt(System.String,System.String)">
      <summary>Überprüft, ob der angegebene lokale Name und der angegebene Namespace-URI mit denen des aktuellen Elements übereinstimmen, liest dann das aktuelle Element und gibt den Inhalt als 32-Bit-Ganzzahl mit Vorzeichen zurück.</summary>
      <returns>Der Elementinhalt als 32-Bit-Ganzzahl mit Vorzeichen.</returns>
      <param name="localName">Der lokale Name des Elements.</param>
      <param name="namespaceURI">Der Namespace-URI des Elements.</param>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Xml.XmlReader" /> wird nicht auf einem Element positioniert.</exception>
      <exception cref="T:System.Xml.XmlException">Das aktuelle Element enthält untergeordnete Elemente.- oder - Der Elementinhalt kann nicht in eine 32-Bit-Ganzzahl mit Vorzeichen konvertiert werden.</exception>
      <exception cref="T:System.ArgumentNullException">Die Methode wird mit null-Argumenten aufgerufen.</exception>
      <exception cref="T:System.ArgumentException">Der angegebene lokale Name und der Namespace-URI stimmen nicht mit dem Element überein, das gerade gelesen wird.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong">
      <summary>Liest das aktuelle Element und gibt den Inhalt als 64-Bit-Ganzzahl mit Vorzeichen zurück.</summary>
      <returns>Der Elementinhalt als 64-Bit-Ganzzahl mit Vorzeichen.</returns>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Xml.XmlReader" /> wird nicht auf einem Element positioniert.</exception>
      <exception cref="T:System.Xml.XmlException">Das aktuelle Element enthält untergeordnete Elemente.- oder - Der Elementinhalt kann nicht in eine 64-Bit-Ganzzahl mit Vorzeichen konvertiert werden.</exception>
      <exception cref="T:System.ArgumentNullException">Die Methode wird mit null-Argumenten aufgerufen.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong(System.String,System.String)">
      <summary>Überprüft, ob der angegebene lokale Name und der angegebene Namespace-URI mit denen des aktuellen Elements übereinstimmen, liest dann das aktuelle Element und gibt den Inhalt als 64-Bit-Ganzzahl mit Vorzeichen zurück.</summary>
      <returns>Der Elementinhalt als 64-Bit-Ganzzahl mit Vorzeichen.</returns>
      <param name="localName">Der lokale Name des Elements.</param>
      <param name="namespaceURI">Der Namespace-URI des Elements.</param>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Xml.XmlReader" /> wird nicht auf einem Element positioniert.</exception>
      <exception cref="T:System.Xml.XmlException">Das aktuelle Element enthält untergeordnete Elemente.- oder - Der Elementinhalt kann nicht in eine 64-Bit-Ganzzahl mit Vorzeichen konvertiert werden.</exception>
      <exception cref="T:System.ArgumentNullException">Die Methode wird mit null-Argumenten aufgerufen.</exception>
      <exception cref="T:System.ArgumentException">Der angegebene lokale Name und der Namespace-URI stimmen nicht mit dem Element überein, das gerade gelesen wird.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject">
      <summary>Liest das aktuelle Element und gibt den Inhalt als <see cref="T:System.Object" /> zurück.</summary>
      <returns>Ein geschachteltes CLR-Objekt (Common Language Runtime) des geeignetsten Typs.Die <see cref="P:System.Xml.XmlReader.ValueType" />-Eigenschaft bestimmt den geeigneten CLR-Typ.Wenn der Inhalt als Listentyp typisiert ist, gibt diese Methode ein Array der geschachtelten Objekte des geeigneten Typs zurück.</returns>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Xml.XmlReader" /> wird nicht auf einem Element positioniert.</exception>
      <exception cref="T:System.Xml.XmlException">Das aktuelle Element enthält untergeordnete Elemente.- oder - Der Elementinhalt kann nicht in den angeforderten Typ konvertiert werden.</exception>
      <exception cref="T:System.ArgumentNullException">Die Methode wird mit null-Argumenten aufgerufen.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject(System.String,System.String)">
      <summary>Überprüft, ob der angegebene lokale Name und der angegebene Namespace-URI mit denen des aktuellen Elements übereinstimmen, liest dann das aktuelle Element und gibt den Inhalt als <see cref="T:System.Object" /> zurück.</summary>
      <returns>Ein geschachteltes CLR-Objekt (Common Language Runtime) des geeignetsten Typs.Die <see cref="P:System.Xml.XmlReader.ValueType" />-Eigenschaft bestimmt den geeigneten CLR-Typ.Wenn der Inhalt als Listentyp typisiert ist, gibt diese Methode ein Array der geschachtelten Objekte des geeigneten Typs zurück.</returns>
      <param name="localName">Der lokale Name des Elements.</param>
      <param name="namespaceURI">Der Namespace-URI des Elements.</param>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Xml.XmlReader" /> wird nicht auf einem Element positioniert.</exception>
      <exception cref="T:System.Xml.XmlException">Das aktuelle Element enthält untergeordnete Elemente.- oder - Der Elementinhalt kann nicht in den angeforderten Typ konvertiert werden.</exception>
      <exception cref="T:System.ArgumentNullException">Die Methode wird mit null-Argumenten aufgerufen.</exception>
      <exception cref="T:System.ArgumentException">Der angegebene lokale Name und der Namespace-URI stimmen nicht mit dem Element überein, das gerade gelesen wird.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObjectAsync">
      <summary>Liest das aktuelle Element asynchron und gibt den Inhalt als <see cref="T:System.Object" /> zurück.</summary>
      <returns>Ein geschachteltes CLR-Objekt (Common Language Runtime) des geeignetsten Typs.Die <see cref="P:System.Xml.XmlReader.ValueType" />-Eigenschaft bestimmt den geeigneten CLR-Typ.Wenn der Inhalt als Listentyp typisiert ist, gibt diese Methode ein Array der geschachtelten Objekte des geeigneten Typs zurück.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" /> asynchrone Methode wurde aufgerufen, ohne das <see cref="P:System.Xml.XmlReaderSettings.Async" />-Flag auf true festzulegen.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Nachricht ausgelöst "Legen Sie XmlReaderSettings.Async auf True fest, wenn Sie die Async-Methoden verwenden möchten."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString">
      <summary>Liest das aktuelle Element und gibt den Inhalt als <see cref="T:System.String" />-Objekt zurück.</summary>
      <returns>Der Elementinhalt als <see cref="T:System.String" />-Objekt.</returns>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Xml.XmlReader" /> wird nicht auf einem Element positioniert.</exception>
      <exception cref="T:System.Xml.XmlException">Das aktuelle Element enthält untergeordnete Elemente.- oder - Der Elementinhalt kann nicht in ein <see cref="T:System.String" />-Objekt konvertiert werden.</exception>
      <exception cref="T:System.ArgumentNullException">Die Methode wird mit null-Argumenten aufgerufen.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString(System.String,System.String)">
      <summary>Überprüft, ob der angegebene lokale Name und der angegebene Namespace-URI mit denen des aktuellen Elements übereinstimmen, liest dann das aktuelle Element und gibt den Inhalt als <see cref="T:System.String" />-Objekt zurück.</summary>
      <returns>Der Elementinhalt als <see cref="T:System.String" />-Objekt.</returns>
      <param name="localName">Der lokale Name des Elements.</param>
      <param name="namespaceURI">Der Namespace-URI des Elements.</param>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Xml.XmlReader" /> wird nicht auf einem Element positioniert.</exception>
      <exception cref="T:System.Xml.XmlException">Das aktuelle Element enthält untergeordnete Elemente.- oder - Der Elementinhalt kann nicht in ein <see cref="T:System.String" />-Objekt konvertiert werden.</exception>
      <exception cref="T:System.ArgumentNullException">Die Methode wird mit null-Argumenten aufgerufen.</exception>
      <exception cref="T:System.ArgumentException">Der angegebene lokale Name und der Namespace-URI stimmen nicht mit dem Element überein, das gerade gelesen wird.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsStringAsync">
      <summary>Liest das aktuelle Element asynchron und gibt den Inhalt als <see cref="T:System.String" />-Objekt zurück.</summary>
      <returns>Der Elementinhalt als <see cref="T:System.String" />-Objekt.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" /> asynchrone Methode wurde aufgerufen, ohne das <see cref="P:System.Xml.XmlReaderSettings.Async" />-Flag auf true festzulegen.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Nachricht ausgelöst "Legen Sie XmlReaderSettings.Async auf True fest, wenn Sie die Async-Methoden verwenden möchten."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadEndElement">
      <summary>Überprüft, ob der aktuelle Inhaltsknoten ein Endtag ist, und verschiebt den Reader auf den nächsten Knoten.</summary>
      <exception cref="T:System.Xml.XmlException">Der aktuelle Knoten ist kein Endtag, oder im Eingabestream wurde unzulässiger XML-Code gefunden.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXml">
      <summary>Liest beim Überschreiben in einer abgeleiteten Klasse den gesamten Inhalt, einschließlich Markup, als Zeichenfolge.</summary>
      <returns>Der gesamte XML-Inhalt (einschließlich Markup) im aktuellen Knoten.Wenn der aktuelle Knoten keine untergeordneten Elemente besitzt, wird eine leere Zeichenfolge zurückgegeben.Wenn der aktuelle Knoten weder ein Element noch ein Attribut ist, wird eine leere Zeichenfolge zurückgegeben.</returns>
      <exception cref="T:System.Xml.XmlException">Das XML war nicht wohlgeformt, oder bei der XML-Analyse ist ein Fehler aufgetreten.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXmlAsync">
      <summary>Liest asynchron den gesamten Inhalt, einschließlich Markup als Zeichenfolge.</summary>
      <returns>Der gesamte XML-Inhalt (einschließlich Markup) im aktuellen Knoten.Wenn der aktuelle Knoten keine untergeordneten Elemente besitzt, wird eine leere Zeichenfolge zurückgegeben.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" /> asynchrone Methode wurde aufgerufen, ohne das <see cref="P:System.Xml.XmlReaderSettings.Async" />-Flag auf true festzulegen.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Nachricht ausgelöst "Legen Sie XmlReaderSettings.Async auf True fest, wenn Sie die Async-Methoden verwenden möchten."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXml">
      <summary>Liest beim Überschreiben in einer abgeleiteten Klasse den Inhalt (einschließlich Markup) ab, der diesen Knoten und alle untergeordneten Elemente darstellt.</summary>
      <returns>Wenn der Reader auf einem Elementknoten oder einem Attributknoten positioniert ist, gibt diese Methode den gesamten XML-Inhalt (einschließlich Markup) des aktuellen Knotens sowie aller untergeordneten Elemente zurück. Andernfalls wird eine leere Zeichenfolge zurückgegeben.</returns>
      <exception cref="T:System.Xml.XmlException">Das XML war nicht wohlgeformt, oder bei der XML-Analyse ist ein Fehler aufgetreten.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXmlAsync">
      <summary>Liest den Inhalt, einschließlich Markup, das diesen Knoten und alle untergeordneten Elemente darstellt, asynchron.</summary>
      <returns>Wenn der Reader auf einem Elementknoten oder einem Attributknoten positioniert ist, gibt diese Methode den gesamten XML-Inhalt (einschließlich Markup) des aktuellen Knotens sowie aller untergeordneten Elemente zurück. Andernfalls wird eine leere Zeichenfolge zurückgegeben.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" /> asynchrone Methode wurde aufgerufen, ohne das <see cref="P:System.Xml.XmlReaderSettings.Async" />-Flag auf true festzulegen.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Nachricht ausgelöst "Legen Sie XmlReaderSettings.Async auf True fest, wenn Sie die Async-Methoden verwenden möchten."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement">
      <summary>Überprüft, ob der aktuelle Knoten ein Element ist, und rückt den Reader zum nächsten Knoten vor.</summary>
      <exception cref="T:System.Xml.XmlException">Im Eingabestream wurde unzulässiger XML-Code gefunden.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String)">
      <summary>Überprüft, ob der aktuelle Inhaltsknoten ein Element mit dem angegebenen <see cref="P:System.Xml.XmlReader.Name" /> ist, und verschiebt den Reader auf den nächsten Knoten.</summary>
      <param name="name">Der qualifizierte Name des Elements.</param>
      <exception cref="T:System.Xml.XmlException">Im Eingabestream wurde unzulässiger XML-Code gefunden. - oder -  Der <see cref="P:System.Xml.XmlReader.Name" /> des Elements entspricht nicht dem angegebenen <paramref name="name" />.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String,System.String)">
      <summary>Überprüft, ob der aktuelle Inhaltsknoten ein Element mit dem angegebenen <see cref="P:System.Xml.XmlReader.LocalName" /> und dem angegebenen <see cref="P:System.Xml.XmlReader.NamespaceURI" /> ist, und verschiebt den Reader auf den nächsten Knoten.</summary>
      <param name="localname">Der lokale Name des Elements.</param>
      <param name="ns">Der Namespace-URI des Elements.</param>
      <exception cref="T:System.Xml.XmlException">Im Eingabestream wurde unzulässiger XML-Code gefunden.- oder - Die Eigenschaften <see cref="P:System.Xml.XmlReader.LocalName" /> und <see cref="P:System.Xml.XmlReader.NamespaceURI" /> des gefundenen Elements stimmen nicht mit den angegebenen Argumenten überein.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ReadState">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den Zustand des Readers ab.</summary>
      <returns>Einer der Enumerationswerte, der den Status des Readers angibt.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadSubtree">
      <summary>Gibt eine neue XmlReader-Instanz zurück, die zum Lesen des aktuellen Knotens und aller Nachfolgerknoten verwendet werden kann.</summary>
      <returns>Eine neue auf <see cref="F:System.Xml.ReadState.Initial" /> festgelegte XML-Reader-Instanz.Durch den Aufruf der <see cref="M:System.Xml.XmlReader.Read" />-Methode wird der neue Reader auf dem Knoten positioniert, der vor dem Aufruf der <see cref="M:System.Xml.XmlReader.ReadSubtree" />-Methode aktuell war.</returns>
      <exception cref="T:System.InvalidOperationException">Der XML-Reader ist nicht auf einem Element positioniert, wenn diese Methode aufgerufen wird.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String)">
      <summary>Verschiebt den <see cref="T:System.Xml.XmlReader" /> auf das nächste Nachfolgerelement mit dem angegebenen qualifizierten Namen.</summary>
      <returns>true, wenn ein übereinstimmendes Nachfolgerelement gefunden wurde, andernfalls false.Wenn kein übereinstimmendes untergeordnetes Element gefunden wurde, wird der <see cref="T:System.Xml.XmlReader" /> auf dem Endtag (<see cref="P:System.Xml.XmlReader.NodeType" /> ist XmlNodeType.EndElement) des Elements positioniert.Wenn der <see cref="T:System.Xml.XmlReader" /> beim Aufruf von <see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String)" /> nicht in einem Element positioniert wird, gibt diese Methode false zurück, und die Position des <see cref="T:System.Xml.XmlReader" /> wird nicht geändert.</returns>
      <param name="name">Der qualifizierte Name des Elements, zu dem Sie wechseln möchten.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.ArgumentException">Der Parameter ist eine leere Zeichenfolge.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)">
      <summary>Verschiebt den <see cref="T:System.Xml.XmlReader" /> auf das nächste Nachfolgerelement mit dem angegebenen lokalen Namen und dem angegebenen Namespace-URI.</summary>
      <returns>true, wenn ein übereinstimmendes Nachfolgerelement gefunden wurde, andernfalls false.Wenn kein übereinstimmendes untergeordnetes Element gefunden wurde, wird der <see cref="T:System.Xml.XmlReader" /> auf dem Endtag (<see cref="P:System.Xml.XmlReader.NodeType" /> ist XmlNodeType.EndElement) des Elements positioniert.Wenn der <see cref="T:System.Xml.XmlReader" /> beim Aufruf von <see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)" /> nicht in einem Element positioniert wird, gibt diese Methode false zurück, und die Position des <see cref="T:System.Xml.XmlReader" /> wird nicht geändert.</returns>
      <param name="localName">Der lokale Name des Elements, zu dem Sie wechseln möchten.</param>
      <param name="namespaceURI">Der Namespace-URI des Elements, zu dem Sie wechseln möchten.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.ArgumentNullException">Beide Parameter-Werte sind null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String)">
      <summary>Liest, bis ein Element mit dem angegebenen qualifizierten Namen gefunden wird.</summary>
      <returns>true, wenn ein übereinstimmendes Element gefunden wird, andernfalls false, und der <see cref="T:System.Xml.XmlReader" /> in einem Dateiendezustand.</returns>
      <param name="name">Der qualifizierte Name des Elements.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.ArgumentException">Der Parameter ist eine leere Zeichenfolge.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String,System.String)">
      <summary>Liest, bis ein Element mit dem angegebenen lokalen Namen und dem angegebenen Namespace-URI gefunden wird.</summary>
      <returns>true, wenn ein übereinstimmendes Element gefunden wird, andernfalls false, und der <see cref="T:System.Xml.XmlReader" /> in einem Dateiendezustand.</returns>
      <param name="localName">Der lokale Name des Elements.</param>
      <param name="namespaceURI">Der Namespace-URI des Elements.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.ArgumentNullException">Beide Parameter-Werte sind null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String)">
      <summary>Verschiebt den XmlReader auf das nächste nebengeordnete Element mit dem angegebenen qualifizierten Namen.</summary>
      <returns>true, wenn ein übereinstimmendes nebengeordnetes Element gefunden wurde, andernfalls false.Wenn kein übereinstimmendes nebengeordnetes Element gefunden wurde, wird der XmlReader auf dem Endtag (<see cref="P:System.Xml.XmlReader.NodeType" /> ist XmlNodeType.EndElement) des übergeordneten Elements positioniert.</returns>
      <param name="name">Der qualifizierte Name des nebengeordneten Elements, zu dem Sie wechseln möchten.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.ArgumentException">Der Parameter ist eine leere Zeichenfolge.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String,System.String)">
      <summary>Verschiebt den XmlReader auf das nächste nebengeordnete Element mit dem angegebenen lokalen Namen und dem angegebenen Namespace-URI.</summary>
      <returns>true, wenn ein übereinstimmendes nebengeordnetes Element gefunden wurde, andernfalls false.Wenn kein übereinstimmendes nebengeordnetes Element gefunden wurde, wird der XmlReader auf dem Endtag (<see cref="P:System.Xml.XmlReader.NodeType" /> ist XmlNodeType.EndElement) des übergeordneten Elements positioniert.</returns>
      <param name="localName">Der lokale Name des nebengeordneten Elements, zu dem Sie wechseln möchten.</param>
      <param name="namespaceURI">Der Namespace-URI des nebengeordneten Elements, zu dem Sie wechseln möchten.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.ArgumentNullException">Beide Parameter-Werte sind null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)">
      <summary>Liest umfangreiche Streams von Text, der in ein XML-Dokument eingebettet ist.</summary>
      <returns>Die Anzahl der in den Puffer gelesenen Zeichen.Der Wert 0 (null) wird zurückgegeben, wenn kein weiterer Textinhalt vorhanden ist.</returns>
      <param name="buffer">Das Array von Zeichen, das als Puffer dient, in den der Textinhalt geschrieben wird.Dieser Wert darf nicht null sein.</param>
      <param name="index">Der Offset im Puffer, ab dem der <see cref="T:System.Xml.XmlReader" /> die Ergebnisse kopieren kann.</param>
      <param name="count">Die maximale Anzahl von Zeichen, die in den Puffer kopiert werden sollen.Diese Methode gibt die tatsächliche Anzahl der kopierten Zeichen zurück.</param>
      <exception cref="T:System.InvalidOperationException">Der aktuelle Knoten verfügt über keinen Wert (<see cref="P:System.Xml.XmlReader.HasValue" /> ist false).</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="buffer" />-Wert ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der Index im Puffer oder Index + Anzahl übersteigen die Größe des zugeordneten Puffers.</exception>
      <exception cref="T:System.NotSupportedException">Die <see cref="T:System.Xml.XmlReader" />-Implementierung unterstützt diese Methode nicht.</exception>
      <exception cref="T:System.Xml.XmlException">Die XML-Daten sind nicht wohlgeformt.</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunkAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Liest asynchron umfangreiche Streams von Text, der in ein XML-Dokument eingebettet ist.</summary>
      <returns>Die Anzahl der in den Puffer gelesenen Zeichen.Der Wert 0 (null) wird zurückgegeben, wenn kein weiterer Textinhalt vorhanden ist.</returns>
      <param name="buffer">Das Array von Zeichen, das als Puffer dient, in den der Textinhalt geschrieben wird.Dieser Wert darf nicht null sein.</param>
      <param name="index">Der Offset im Puffer, ab dem der <see cref="T:System.Xml.XmlReader" /> die Ergebnisse kopieren kann.</param>
      <param name="count">Die maximale Anzahl von Zeichen, die in den Puffer kopiert werden sollen.Diese Methode gibt die tatsächliche Anzahl der kopierten Zeichen zurück.</param>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" /> asynchrone Methode wurde aufgerufen, ohne das <see cref="P:System.Xml.XmlReaderSettings.Async" />-Flag auf true festzulegen.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Nachricht ausgelöst "Legen Sie XmlReaderSettings.Async auf True fest, wenn Sie die Async-Methoden verwenden möchten."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ResolveEntity">
      <summary>Löst beim Überschreiben in einer abgeleiteten Klasse den Entitätsverweis für EntityReference-Knoten auf.</summary>
      <exception cref="T:System.InvalidOperationException">Der Reader ist nicht auf einem EntityReference-Knoten positioniert. Diese Implementierung des Readers kann Entitäten nicht auflösen (<see cref="P:System.Xml.XmlReader.CanResolveEntity" /> gibt false zurück).</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Settings">
      <summary>Ruft das zum Erstellen dieser <see cref="T:System.Xml.XmlReader" />-Instanz verwendete <see cref="T:System.Xml.XmlReaderSettings" />-Objekt ab.</summary>
      <returns>Das zum Erstellen dieser Reader-Instanz verwendete <see cref="T:System.Xml.XmlReaderSettings" />-Objekt.Wenn dieser Reader nicht mit der <see cref="Overload:System.Xml.XmlReader.Create" />-Methode erstellt wurde, gibt diese Eigenschaft null zurück.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Skip">
      <summary>Überspringt die untergeordneten Elemente des aktuellen Knotens.</summary>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="M:System.Xml.XmlReader.SkipAsync">
      <summary>Überspringt die untergeordneten Elemente des aktuellen Knotens asynchron.</summary>
      <returns>Der aktuelle Knoten.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" /> asynchrone Methode wurde aufgerufen, ohne das <see cref="P:System.Xml.XmlReaderSettings.Async" />-Flag auf true festzulegen.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Nachricht ausgelöst "Legen Sie XmlReaderSettings.Async auf True fest, wenn Sie die Async-Methoden verwenden möchten."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Value">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den Textwert des aktuellen Knotens ab.</summary>
      <returns>Der zurückgegebene Wert hängt vom <see cref="P:System.Xml.XmlReader.NodeType" /> des Knotens ab.In der folgenden Tabelle sind Knotentypen aufgeführt, die einen zurückzugebenden Wert haben.Alle anderen Knotentypen geben String.Empty zurück.Knotentyp Wert AttributeDer Wert des Attributs. CDATADer Inhalt des CDATA-Abschnitts. CommentDer Inhalt des Kommentars. DocumentTypeDie interne Teilmenge. ProcessingInstructionDer gesamte Inhalt mit Ausnahme des Ziels. SignificantWhitespaceDer Leerraum zwischen Markups bei einem Modell für gemischten Inhalt. TextDer Inhalt des Textknotens. WhitespaceDer Leerraum zwischen Markups. XmlDeclarationDer Inhalt der Deklaration. </returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ValueType">
      <summary>Ruft den CLR-Typ (Common Language Runtime) für den aktuellen Knoten ab.</summary>
      <returns>Der CLR-Typ, der dem typisierten Wert des Knotens entspricht.Die Standardeinstellung ist System.String.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlLang">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den aktuellen xml:lang-Bereich ab.</summary>
      <returns>Der aktuelle xml:lang-Bereich.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlSpace">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den aktuellen xml:space-Bereich ab.</summary>
      <returns>Einer der <see cref="T:System.Xml.XmlSpace" />-Werte.Wenn kein xml:space-Bereich vorhanden ist, wird für diese Eigenschaft standardmäßig XmlSpace.None festgelegt.</returns>
      <exception cref="T:System.InvalidOperationException">Eine <see cref="T:System.Xml.XmlReader" />-Methode wurde aufgerufen, bevor ein vorheriger asynchroner Vorgang abgeschlossen wurde.In diesem Fall wird <see cref="T:System.InvalidOperationException" /> mit der Meldung ausgelöst "ein asynchroner Vorgang wird bereits ausgeführt."</exception>
    </member>
    <member name="T:System.Xml.XmlReaderSettings">
      <summary>Gibt eine Gruppe von Features an, die für das <see cref="T:System.Xml.XmlReader" />-Objekt unterstützt werden sollen, das von der <see cref="Overload:System.Xml.XmlReader.Create" />-Methode erstellt wurde. </summary>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.#ctor">
      <summary>Initialisiert eine neue Instanz der<see cref="T:System.Xml.XmlReaderSettings" />-Klasse.</summary>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.Async">
      <summary>Ruft ab oder legt fest, ob asynchrone <see cref="T:System.Xml.XmlReader" />-Methoden für eine bestimmte <see cref="T:System.Xml.XmlReader" />-Instanz verwendet werden können.</summary>
      <returns>true, wenn asynchrone Methoden verwendet werden können; andernfalls false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CheckCharacters">
      <summary>Ruft einen Wert ab, der angibt, ob Zeichen überprüft werden sollen, oder legt diesen fest.</summary>
      <returns>true, wenn Zeichen überprüft werden sollen, andernfalls false.Die Standardeinstellung ist true.HinweisWenn der <see cref="T:System.Xml.XmlReader" /> Textdaten verarbeitet, überprüft er unabhängig von der Eigenschafteneinstellung stets, ob die XML-Namen und der Textinhalt gültig sind.Durch Festlegen von <see cref="P:System.Xml.XmlReaderSettings.CheckCharacters" /> auf false wird die Zeichenüberprüfung für Zeichenentitätsverweise deaktiviert.</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Clone">
      <summary>Erstellt eine Kopie der <see cref="T:System.Xml.XmlReaderSettings" />-Instanz.</summary>
      <returns>Das geklonte <see cref="T:System.Xml.XmlReaderSettings" />-Objekt.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CloseInput">
      <summary>Ruft einen Wert ab, der angibt, ob der zugrunde liegende Stream oder <see cref="T:System.IO.TextReader" /> geschlossen werden soll, nachdem der Reader geschlossen wurde, oder legt diesen Wert fest.</summary>
      <returns>true, um den zugrunde liegenden Stream oder <see cref="T:System.IO.TextReader" /> zu schließen, nachdem der Reader geschlossen wurde, andernfalls false.Die Standardeinstellung ist false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.ConformanceLevel">
      <summary>Ruft den Konformitätsgrad ab, dem der <see cref="T:System.Xml.XmlReader" /> entspricht, oder legt diesen fest.</summary>
      <returns>Einer der Enumerationswerte, der das Übereinstimmungsniveau angibt, den der XML-Reader umsetzt.Die Standardeinstellung ist <see cref="F:System.Xml.ConformanceLevel.Document" />.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.DtdProcessing">
      <summary>Ruft einen Wert ab oder legt einen Wert fest, der die Verarbeitung von DTDs bestimmt.</summary>
      <returns>Einer der Enumerationswerte, der die Verarbeitung von DTDs bestimmt.Die Standardeinstellung ist <see cref="F:System.Xml.DtdProcessing.Prohibit" />.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreComments">
      <summary>Ruft einen Wert ab, der angibt, ob Kommentare ignoriert werden sollen, oder legt diesen fest.</summary>
      <returns>true, wenn Kommentare ignoriert werden sollen, andernfalls false.Die Standardeinstellung ist false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreProcessingInstructions">
      <summary>Ruft einen Wert ab, der angibt, ob Verarbeitungsanweisungen ignoriert werden sollen, oder legt diesen fest.</summary>
      <returns>true, wenn Verarbeitungsanweisungen ignoriert werden sollen, andernfalls false.Die Standardeinstellung ist false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreWhitespace">
      <summary>Ruft einen Wert ab, der angibt, ob signifikanter Leerraum ignoriert werden soll, oder legt diesen Wert fest.</summary>
      <returns>true, um Leerraum zu ignorieren, andernfalls false.Die Standardeinstellung ist false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LineNumberOffset">
      <summary>Ruft das Zeilennummernoffset des <see cref="T:System.Xml.XmlReader" />-Objekts ab oder legt dieses fest.</summary>
      <returns>Das Zeilennummernoffset.Der Standard ist 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LinePositionOffset">
      <summary>Ruft das Zeilenpositionsoffset des <see cref="T:System.Xml.XmlReader" />-Objekts ab oder legt dieses fest.</summary>
      <returns>Die Offset der Linienposition.Der Standard ist 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersFromEntities">
      <summary>Ruft einen Wert ab, der die maximal zulässige Anzahl von Zeichen in einem Dokument angibt, die aus dem Erweitern von Entitäten resultieren, oder legt diesen fest.</summary>
      <returns>Die maximale zulässige Anzahl von Zeichen aus erweiterten Entitäten.Der Standard ist 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersInDocument">
      <summary>Ruft einen Wert ab, der die maximale zulässige Anzahl von Zeichen in einem XML-Dokument angibt, oder legt diesen fest.Der Wert 0 (null) gibt an, dass die Größe des XML-Dokuments nicht beschränkt ist.Ein Wert ungleich 0 (null) gibt die maximale Größe in Zeichen an.</summary>
      <returns>Die maximale zulässige Anzahl von Zeichen in einem XML-Dokument.Der Standard ist 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.NameTable">
      <summary>Ruft die für Vergleiche von atomisierten Zeichenfolgen verwendete<see cref="T:System.Xml.XmlNameTable" /> ab oder legt diese fest.</summary>
      <returns>Die <see cref="T:System.Xml.XmlNameTable" />, in der alle atomisierten Zeichenfolgen gespeichert werden, die von allen <see cref="T:System.Xml.XmlReader" />-Instanzen verwendet werden, die mit diesem <see cref="T:System.Xml.XmlReaderSettings" />-Objekt erstellt wurden.Die Standardeinstellung ist null.Die erstellte <see cref="T:System.Xml.XmlReader" /> -Instanz verwendet eine neue leere <see cref="T:System.Xml.NameTable" />, wenn dieser Wert null ist.</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Reset">
      <summary>Setzt die Member der settings-Klasse auf ihre Standardwerte zurück.</summary>
    </member>
    <member name="T:System.Xml.XmlSpace">
      <summary>Gibt den aktuellen xml:space-Bereich an.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Default">
      <summary>Der xml:space-Bereich ist gleich default.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.None">
      <summary>Kein xml:space-Bereich.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Preserve">
      <summary>Der xml:space-Bereich ist gleich preserve.</summary>
    </member>
    <member name="T:System.Xml.XmlWriter">
      <summary>Stellt einen Writer für die schnelle, vorwärts gerichtete Generierung von Streams oder Dateien mit XML-Daten ohne Zwischenspeicherung dar.</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Xml.XmlWriter" />-Klasse.</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream)">
      <summary>Erstellt mit dem angegebenen Stream eine neue <see cref="T:System.Xml.XmlWriter" />-Instanz.</summary>
      <returns>Ein <see cref="T:System.Xml.XmlWriter" />-Objekt.</returns>
      <param name="output">Der Stream, in den geschrieben werden soll.Der <see cref="T:System.Xml.XmlWriter" /> schreibt XML 1.0-Textsyntax und fügt diese an den angegebenen Stream an.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream,System.Xml.XmlWriterSettings)">
      <summary>Erstellt eine neue <see cref="T:System.Xml.XmlWriter" />-Instanz mithilfe des Streams und des <see cref="T:System.Xml.XmlWriterSettings" />-Objekts.</summary>
      <returns>Ein <see cref="T:System.Xml.XmlWriter" />-Objekt.</returns>
      <param name="output">Der Stream, in den geschrieben werden soll.Der <see cref="T:System.Xml.XmlWriter" /> schreibt XML 1.0-Textsyntax und fügt diese an den angegebenen Stream an.</param>
      <param name="settings">Das <see cref="T:System.Xml.XmlWriterSettings" />-Objekt zum Konfigurieren der neuen <see cref="T:System.Xml.XmlWriter" />-Instanz.Wenn dies null ist, wird <see cref="T:System.Xml.XmlWriterSettings" /> mit Standardeinstellungen verwendet.Wenn der <see cref="T:System.Xml.XmlWriter" /> mit der <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />-Methode verwendet wird, sollten Sie die <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" />-Eigenschaft verwenden, um ein <see cref="T:System.Xml.XmlWriterSettings" />-Objekt mit den korrekten Einstellungen abzurufen.Dieses Verfahren gewährleistet, dass das erstellte <see cref="T:System.Xml.XmlWriter" />-Objekt über die korrekten Ausgabeeinstellungen verfügt.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter)">
      <summary>Erstellt eine neue <see cref="T:System.Xml.XmlWriter" />-Instanz mithilfe des angegebenen <see cref="T:System.IO.TextWriter" />.</summary>
      <returns>Ein <see cref="T:System.Xml.XmlWriter" />-Objekt.</returns>
      <param name="output">Der <see cref="T:System.IO.TextWriter" />, in den geschrieben werden soll.Der <see cref="T:System.Xml.XmlWriter" /> schreibt XML 1.0-Textsyntax und fügt diese an den angegebenen <see cref="T:System.IO.TextWriter" /> an.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter,System.Xml.XmlWriterSettings)">
      <summary>Erstellt eine neue <see cref="T:System.Xml.XmlWriter" />-Instanz mithilfe des <see cref="T:System.IO.TextWriter" /> und der <see cref="T:System.Xml.XmlWriterSettings" />-Objekte.</summary>
      <returns>Ein <see cref="T:System.Xml.XmlWriter" />-Objekt.</returns>
      <param name="output">Der <see cref="T:System.IO.TextWriter" />, in den geschrieben werden soll.Der <see cref="T:System.Xml.XmlWriter" /> schreibt XML 1.0-Textsyntax und fügt diese an den angegebenen <see cref="T:System.IO.TextWriter" /> an.</param>
      <param name="settings">Das <see cref="T:System.Xml.XmlWriterSettings" />-Objekt zum Konfigurieren der neuen <see cref="T:System.Xml.XmlWriter" />-Instanz.Wenn dies null ist, wird <see cref="T:System.Xml.XmlWriterSettings" /> mit Standardeinstellungen verwendet.Wenn der <see cref="T:System.Xml.XmlWriter" /> mit der <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />-Methode verwendet wird, sollten Sie die <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" />-Eigenschaft verwenden, um ein <see cref="T:System.Xml.XmlWriterSettings" />-Objekt mit den korrekten Einstellungen abzurufen.Dieses Verfahren gewährleistet, dass das erstellte <see cref="T:System.Xml.XmlWriter" />-Objekt über die korrekten Ausgabeeinstellungen verfügt.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder)">
      <summary>Erstellt mit dem angegebenen <see cref="T:System.Text.StringBuilder" /> eine neue <see cref="T:System.Xml.XmlWriter" />-Instanz.</summary>
      <returns>Ein <see cref="T:System.Xml.XmlWriter" />-Objekt.</returns>
      <param name="output">Der <see cref="T:System.Text.StringBuilder" />, in den geschrieben werden soll.Vom <see cref="T:System.Xml.XmlWriter" /> geschriebener Inhalt wird an den <see cref="T:System.Text.StringBuilder" /> angefügt.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder,System.Xml.XmlWriterSettings)">
      <summary>Erstellt mit dem <see cref="T:System.Text.StringBuilder" />-Objekt und dem <see cref="T:System.Xml.XmlWriterSettings" />-Objekt eine neue <see cref="T:System.Xml.XmlWriter" />-Instanz.</summary>
      <returns>Ein <see cref="T:System.Xml.XmlWriter" />-Objekt.</returns>
      <param name="output">Der <see cref="T:System.Text.StringBuilder" />, in den geschrieben werden soll.Vom <see cref="T:System.Xml.XmlWriter" /> geschriebener Inhalt wird an den <see cref="T:System.Text.StringBuilder" /> angefügt.</param>
      <param name="settings">Das <see cref="T:System.Xml.XmlWriterSettings" />-Objekt zum Konfigurieren der neuen <see cref="T:System.Xml.XmlWriter" />-Instanz.Wenn dies null ist, wird <see cref="T:System.Xml.XmlWriterSettings" /> mit Standardeinstellungen verwendet.Wenn der <see cref="T:System.Xml.XmlWriter" /> mit der <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />-Methode verwendet wird, sollten Sie die <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" />-Eigenschaft verwenden, um ein <see cref="T:System.Xml.XmlWriterSettings" />-Objekt mit den korrekten Einstellungen abzurufen.Dieses Verfahren gewährleistet, dass das erstellte <see cref="T:System.Xml.XmlWriter" />-Objekt über die korrekten Ausgabeeinstellungen verfügt.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter)">
      <summary>Erstellt eine neue <see cref="T:System.Xml.XmlWriter" />-Instanz mithilfe des angegebenen <see cref="T:System.Xml.XmlWriter" />-Objekts.</summary>
      <returns>Ein <see cref="T:System.Xml.XmlWriter" />-Objekt, das das angegebene <see cref="T:System.Xml.XmlWriter" />-Objekt umschließt.</returns>
      <param name="output">Das <see cref="T:System.Xml.XmlWriter" />-Objekt, dass Sie als zugrunde liegenden Writer verwenden möchten.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter,System.Xml.XmlWriterSettings)">
      <summary>Erstellt eine neue <see cref="T:System.Xml.XmlWriter" />-Instanz mithilfe des angegebenen <see cref="T:System.Xml.XmlWriter" /> und der <see cref="T:System.Xml.XmlWriterSettings" />-Objekte.</summary>
      <returns>Ein <see cref="T:System.Xml.XmlWriter" />-Objekt, das das angegebene <see cref="T:System.Xml.XmlWriter" />-Objekt umschließt.</returns>
      <param name="output">Das <see cref="T:System.Xml.XmlWriter" />-Objekt, dass Sie als zugrunde liegenden Writer verwenden möchten.</param>
      <param name="settings">Das <see cref="T:System.Xml.XmlWriterSettings" />-Objekt zum Konfigurieren der neuen <see cref="T:System.Xml.XmlWriter" />-Instanz.Wenn dies null ist, wird <see cref="T:System.Xml.XmlWriterSettings" /> mit Standardeinstellungen verwendet.Wenn der <see cref="T:System.Xml.XmlWriter" /> mit der <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" />-Methode verwendet wird, sollten Sie die <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" />-Eigenschaft verwenden, um ein <see cref="T:System.Xml.XmlWriterSettings" />-Objekt mit den korrekten Einstellungen abzurufen.Dieses Verfahren gewährleistet, dass das erstellte <see cref="T:System.Xml.XmlWriter" />-Objekt über die korrekten Ausgabeeinstellungen verfügt.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose">
      <summary>Gibt alle von der aktuellen Instanz der <see cref="T:System.Xml.XmlWriter" />-Klasse verwendeten Ressourcen frei.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose(System.Boolean)">
      <summary>Gibt die von <see cref="T:System.Xml.XmlWriter" /> verwendeten nicht verwalteten Ressourcen und optional die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Flush">
      <summary>Entleert beim Überschreiben in einer abgeleiteten Klasse den Inhalt des Puffers in die zugrunde liegenden Streams und leert den zugrunde liegenden Stream ebenfalls.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.FlushAsync">
      <summary>Entleert den Pufferinhalt asynchron in die zugrunde liegenden Streams und entleert den zugrunde liegenden Stream ebenfalls.</summary>
      <returns>Die Aufgabe, die den asynchronen Flush-Vorgang darstellt.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.LookupPrefix(System.String)">
      <summary>Gibt beim Überschreiben in einer abgeleiteten Klasse das nächstliegende Präfix zurück, das im aktuellen Namespacebereich für den Namespace-URI definiert ist.</summary>
      <returns>Das passende Präfix oder null, wenn im aktuellen Gültigkeitsbereich kein übereinstimmender Namespace-URI gefunden wird.</returns>
      <param name="ns">Der Namespace-URI, dessen Präfix gesucht werden soll.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="ns" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.Settings">
      <summary>Ruft das zum Erstellen dieser <see cref="T:System.Xml.XmlWriter" />-Instanz verwendete <see cref="T:System.Xml.XmlWriterSettings" />-Objekt ab.</summary>
      <returns>Das zum Erstellen dieser Writer-Instanz verwendete <see cref="T:System.Xml.XmlWriterSettings" />-Objekt.Wenn dieser Writer nicht mit der <see cref="Overload:System.Xml.XmlWriter.Create" />-Methode erstellt wurde, gibt diese Eigenschaft null zurück.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributes(System.Xml.XmlReader,System.Boolean)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse sämtliche an der aktuellen Position gefundenen Attribute in den <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="reader">Der XmlReader, aus dem die Attribute kopiert werden sollen.</param>
      <param name="defattr">true, um die Standardattribute aus dem XmlReader zu kopieren, andernfalls false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null. </exception>
      <exception cref="T:System.Xml.XmlException">The reader is not positioned on an element, attribute or XmlDeclaration node. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributesAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>Schreibt asynchron alle Attribute aus, die in der aktuellen Position in <see cref="T:System.Xml.XmlReader" /> gefunden werden.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteAttributes-Vorgang darstellt.</returns>
      <param name="reader">Der XmlReader, aus dem die Attribute kopiert werden sollen.</param>
      <param name="defattr">true, um die Standardattribute aus dem XmlReader zu kopieren, andernfalls false.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse das Attribut mit dem angegebenen lokalen Namen und Wert.</summary>
      <param name="localName">Der lokale Name des Attributs.</param>
      <param name="value">Der Wert des Attributs.</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse ein Attribut mit dem angegebenen lokalen Namen, Namespace-URI und Wert.</summary>
      <param name="localName">Der lokale Name des Attributs.</param>
      <param name="ns">Der Namespace-URI, der dem Attribut zugeordnet werden soll.</param>
      <param name="value">Der Wert des Attributs.</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String,System.String)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse das Attribut mit dem angegebenen Präfix, lokalen Namen, Namespace-URI und Wert.</summary>
      <param name="prefix">Das Namespacepräfix des Attributs.</param>
      <param name="localName">Der lokale Name des Attributs.</param>
      <param name="ns">Der Namespace-URI dieses Attributs.</param>
      <param name="value">Der Wert des Attributs.</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.Xml.XmlException">The <paramref name="localName" /> or <paramref name="ns" /> is null. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeStringAsync(System.String,System.String,System.String,System.String)">
      <summary>Schreibt ein Attribut asynchron mit dem angegebenen Präfix, lokalen Namen, Namespace-URI und Wert.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteAttributeString-Vorgang darstellt.</returns>
      <param name="prefix">Das Namespacepräfix des Attributs.</param>
      <param name="localName">Der lokale Name des Attributs.</param>
      <param name="ns">Der Namespace-URI dieses Attributs.</param>
      <param name="value">Der Wert des Attributs.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>Codiert beim Überschreiben in einer abgeleiteten Klasse die angegebenen binären Bytes als Base64 und schreibt den resultierenden Text.</summary>
      <param name="buffer">Zu codierendes Bytearray.</param>
      <param name="index">Die Position innerhalb des Puffers, die den Anfang der zu schreibenden Bytes kennzeichnet.</param>
      <param name="count">Die Anzahl der zu schreibenden Bytes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Codiert die angegebenen binären Bytes asynchron als Base64 und schreibt den resultierenden Text.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteBase64-Vorgang darstellt.</returns>
      <param name="buffer">Zu codierendes Bytearray.</param>
      <param name="index">Die Position innerhalb des Puffers, die den Anfang der zu schreibenden Bytes kennzeichnet.</param>
      <param name="count">Die Anzahl der zu schreibenden Bytes.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>Codiert beim Überschreiben in einer abgeleiteten Klasse die angegebenen binären Bytes als BinHex und schreibt den resultierenden Text.</summary>
      <param name="buffer">Zu codierendes Bytearray.</param>
      <param name="index">Die Position innerhalb des Puffers, die den Anfang der zu schreibenden Bytes kennzeichnet.</param>
      <param name="count">Die Anzahl der zu schreibenden Bytes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Codiert die angegebenen binären Bytes asynchron als BinHex und schreibt den resultierenden Text.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteBinHex-Vorgang darstellt.</returns>
      <param name="buffer">Zu codierendes Bytearray.</param>
      <param name="index">Die Position innerhalb des Puffers, die den Anfang der zu schreibenden Bytes kennzeichnet.</param>
      <param name="count">Die Anzahl der zu schreibenden Bytes.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCData(System.String)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse einen &lt;![CDATA[...]]&gt;-Block mit dem angegebenen Text.</summary>
      <param name="text">Der Text, der in den CDATA-Block eingefügt werden soll.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCDataAsync(System.String)">
      <summary>Schreibt asynchron einen &lt;![CDATA[...]]&gt;-Block mit dem angegebenen Text.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteCData-Vorgang darstellt.</returns>
      <param name="text">Der Text, der in den CDATA-Block eingefügt werden soll.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntity(System.Char)">
      <summary>Erzwingt beim Überschreiben in einer abgeleiteten Klasse die Generierung einer Zeichenentität für den angegebenen Wert eines Unicode-Zeichens.</summary>
      <param name="ch">Das Unicode-Zeichen, für das eine Zeichenentität generiert werden soll.</param>
      <exception cref="T:System.ArgumentException">The character is in the surrogate pair character range, 0xd800 - 0xdfff.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntityAsync(System.Char)">
      <summary>Erzwingt das Generieren einer Zeichenentität asynchron für den angegebenen Unicode-Zeichenwert.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteCharEntity-Vorgang darstellt.</returns>
      <param name="ch">Das Unicode-Zeichen, für das eine Zeichenentität generiert werden soll.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteChars(System.Char[],System.Int32,System.Int32)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse Text in jeweils einen Puffer.</summary>
      <param name="buffer">Zeichenarray, das den zu schreibenden Text enthält.</param>
      <param name="index">Die Position innerhalb des Puffers, die den Anfang des zu schreibenden Texts kennzeichnet.</param>
      <param name="count">Die Anzahl der zu schreibenden Zeichen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />; the call results in surrogate pair characters being split or an invalid surrogate pair being written.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="buffer" /> parameter value is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharsAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Schreibt Text asynchron in jeweils einen Puffer.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteChars-Vorgang darstellt.</returns>
      <param name="buffer">Zeichenarray, das den zu schreibenden Text enthält.</param>
      <param name="index">Die Position innerhalb des Puffers, die den Anfang des zu schreibenden Texts kennzeichnet.</param>
      <param name="count">Die Anzahl der zu schreibenden Zeichen.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteComment(System.String)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse den Kommentar &lt;!--...--&gt; mit dem angegebenen Text.</summary>
      <param name="text">Text, der in den Kommentar eingefügt werden soll.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well-formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCommentAsync(System.String)">
      <summary>Schreibt asynchron einen Kommentar &lt;!--...--&gt;, der den angegebenen Text enthält.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteComment-Vorgang darstellt.</returns>
      <param name="text">Text, der in den Kommentar eingefügt werden soll.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocType(System.String,System.String,System.String,System.String)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse die DOCTYPE-Deklaration mit dem angegebenen Namen und optionalen Attributen.</summary>
      <param name="name">Der Name des DOCTYPE.Dieser darf nicht leer sein.</param>
      <param name="pubid">Bei einem Wert ungleich NULL wird auch PUBLIC "pubid" "sysid" geschrieben, wobei <paramref name="pubid" /> und <paramref name="sysid" /> durch die Werte der angegebenen Argumente ersetzt werden.</param>
      <param name="sysid">Wenn <paramref name="pubid" /> gleich null und <paramref name="sysid" /> ungleich NULL ist, wird SYSTEM "sysid" geschrieben, wobei <paramref name="sysid" /> durch den Wert dieses Arguments ersetzt wird.</param>
      <param name="subset">Wenn nicht NULL, wird [subset] geschrieben, wobei subset durch den Wert dieses Arguments ersetzt wird.</param>
      <exception cref="T:System.InvalidOperationException">This method was called outside the prolog (after the root element). </exception>
      <exception cref="T:System.ArgumentException">The value for <paramref name="name" /> would result in invalid XML.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocTypeAsync(System.String,System.String,System.String,System.String)">
      <summary>Schreibt die DOCTYPE-Deklaration asynchron mit dem angegebenen Namen und optionalen Attributen.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteDocType-Vorgang darstellt.</returns>
      <param name="name">Der Name des DOCTYPE.Dieser darf nicht leer sein.</param>
      <param name="pubid">Bei einem Wert ungleich NULL wird auch PUBLIC "pubid" "sysid" geschrieben, wobei <paramref name="pubid" /> und <paramref name="sysid" /> durch die Werte der angegebenen Argumente ersetzt werden.</param>
      <param name="sysid">Wenn <paramref name="pubid" /> gleich null und <paramref name="sysid" /> ungleich NULL ist, wird SYSTEM "sysid" geschrieben, wobei <paramref name="sysid" /> durch den Wert dieses Arguments ersetzt wird.</param>
      <param name="subset">Wenn nicht NULL, wird [subset] geschrieben, wobei subset durch den Wert dieses Arguments ersetzt wird.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String)">
      <summary>Schreibt ein Element mit dem angegebenen lokalen Namen und Wert.</summary>
      <param name="localName">Der lokale Name des Elements.</param>
      <param name="value">Der Wert des Elements.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String)">
      <summary>Schreibt ein Element mit dem angegebenen lokalen Namen, Namespace-URI und Wert.</summary>
      <param name="localName">Der lokale Name des Elements.</param>
      <param name="ns">Der Namespace-URI, der dem Element zugeordnet werden soll.</param>
      <param name="value">Der Wert des Elements.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String,System.String)">
      <summary>Schreibt ein Element mit dem angegebenen Präfix, lokalen Namen, Namespace-URI und Wert.</summary>
      <param name="prefix">Das Präfix des Elements.</param>
      <param name="localName">Der lokale Name des Elements.</param>
      <param name="ns">Die Namespace-URI des Elements.</param>
      <param name="value">Der Wert des Elements.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementStringAsync(System.String,System.String,System.String,System.String)">
      <summary>Schreibt ein Element asynchron mit dem angegebenen Präfix, lokalen Namen, Namespace-URI und Wert.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteElementString-Vorgang darstellt.</returns>
      <param name="prefix">Das Präfix des Elements.</param>
      <param name="localName">Der lokale Name des Elements.</param>
      <param name="ns">Die Namespace-URI des Elements.</param>
      <param name="value">Der Wert des Elements.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttribute">
      <summary>Schließt beim Überschreiben in einer abgeleiteten Klasse den vorherigen <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" />-Aufruf.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttributeAsync">
      <summary>Schließt asynchron den vorherigen <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" />-Aufruf.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteEndAttribute-Vorgang darstellt.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocument">
      <summary>Schließt beim Überschreiben in einer abgeleiteten Klasse alle geöffneten Elemente oder Attribute und setzt den Writer in den Anfangszustand zurück.</summary>
      <exception cref="T:System.ArgumentException">The XML document is invalid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocumentAsync">
      <summary>Schließt alle geöffneten Elemente oder Attribute asynchron und setzt den Writer in den Startzustand zurück.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteEndDocument-Vorgang darstellt.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElement">
      <summary>Schließt beim Überschreiben in einer abgeleiteten Klasse ein Element und löst den entsprechenden Namespacebereich auf.</summary>
      <exception cref="T:System.InvalidOperationException">This results in an invalid XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElementAsync">
      <summary>Schließt ein Element asynchron und löst den entsprechenden Namespacebereich auf.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteEndElement-Vorgang darstellt.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRef(System.String)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse einen Entitätsverweis als &amp;name;.</summary>
      <param name="name">Der Name des Entitätsverweises.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRefAsync(System.String)">
      <summary>Schreibt einen Entitätsverweis asynchron als &amp;name; aus.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteEntityRef-Vorgang darstellt.</returns>
      <param name="name">Der Name des Entitätsverweises.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElement">
      <summary>Schließt beim Überschreiben in einer abgeleiteten Klasse ein Element und löst den entsprechenden Namespacebereich auf.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElementAsync">
      <summary>Schließt ein Element asynchron und löst den entsprechenden Namespacebereich auf.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteFullEndElement-Vorgang darstellt.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteName(System.String)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse den angegebenen Namen und stellt sicher, dass dieser gemäß der W3C-Empfehlung zu XML, Version 1.0 (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name), ein gültiger Name ist.</summary>
      <param name="name">Der zu schreibende Name.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid XML name; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNameAsync(System.String)">
      <summary>Schreibt den angegebenen Namen asynchron und prüft dessen Gültigkeit entsprechend der W3C-Empfehlung für XML, Version 1.0 (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name).</summary>
      <returns>Die Aufgabe, die den asynchronen WriteName-Vorgang darstellt.</returns>
      <param name="name">Der zu schreibende Name.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmToken(System.String)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse den angegebenen Namen und stellt sicher, dass dieser gemäß der W3C-Empfehlung zu XML, Version 1.0 (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name), ein gültiges NmToken ist.</summary>
      <param name="name">Der zu schreibende Name.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid NmToken; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmTokenAsync(System.String)">
      <summary>Schreibt den angegebenen Namen asynchron und prüft, dass es sich entsprechend der W3C-Empfehlung für XML, Version 1.0 (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name) um ein gültiges NmToken handelt.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteNmToken-Vorgang darstellt.</returns>
      <param name="name">Der zu schreibende Name.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNode(System.Xml.XmlReader,System.Boolean)">
      <summary>Kopiert beim Überschreiben in einer abgeleiteten Klasse den gesamten Inhalt des Readers in den Writer und verschiebt den Reader zum Anfang des nächsten nebengeordneten Elements.</summary>
      <param name="reader">Der <see cref="T:System.Xml.XmlReader" />, aus dem gelesen werden soll.</param>
      <param name="defattr">true, um die Standardattribute aus dem XmlReader zu kopieren, andernfalls false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> contains invalid characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNodeAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>Kopiert beim Überschreiben in einer abgeleiteten Klasse den gesamten Inhalt des Readers asynchron in den Writer und verschiebt den Reader zum Anfang des nächsten nebengeordneten Elements.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteNode-Vorgang darstellt.</returns>
      <param name="reader">Der <see cref="T:System.Xml.XmlReader" />, aus dem gelesen werden soll.</param>
      <param name="defattr">true, um die Standardattribute aus dem XmlReader zu kopieren, andernfalls false.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstruction(System.String,System.String)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse eine Verarbeitungsanweisung mit einem Leerzeichen zwischen dem Namen und dem Text wie folgt: &lt;?name text?&gt;.</summary>
      <param name="name">Der Name der Verarbeitungsanweisung.</param>
      <param name="text">Der in die Verarbeitungsanweisung einzufügende Text.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.<paramref name="name" /> is either null or String.Empty.This method is being used to create an XML declaration after <see cref="M:System.Xml.XmlWriter.WriteStartDocument" /> has already been called. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstructionAsync(System.String,System.String)">
      <summary>Schreibt eine Verarbeitungsanweisung asynchron mit einem Leerzeichen zwischen dem Namen und dem Text wie folgt: &lt;?name text?&gt;.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteProcessingInstruction-Vorgang darstellt.</returns>
      <param name="name">Der Name der Verarbeitungsanweisung.</param>
      <param name="text">Der in die Verarbeitungsanweisung einzufügende Text.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedName(System.String,System.String)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse den durch den Namespace angegebenen Namen.Diese Methode sucht das Präfix im Gültigkeitsbereich des Namespaces.</summary>
      <param name="localName">Der zu schreibende lokale Name.</param>
      <param name="ns">Der Namespace-URI für den Namen.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="localName" /> is either null or String.Empty.<paramref name="localName" /> is not a valid name. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedNameAsync(System.String,System.String)">
      <summary>Schreibt den durch Namespace gekennzeichneten Namen asynchron.Diese Methode sucht das Präfix im Gültigkeitsbereich des Namespaces.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteQualifiedName-Vorgang darstellt.</returns>
      <param name="localName">Der zu schreibende lokale Name.</param>
      <param name="ns">Der Namespace-URI für den Namen.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.Char[],System.Int32,System.Int32)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse Rohdatenmarkup manuell aus einem Zeichenpuffer.</summary>
      <param name="buffer">Zeichenarray, das den zu schreibenden Text enthält.</param>
      <param name="index">Die Position innerhalb des Puffers, die den Anfang des zu schreibenden Texts kennzeichnet.</param>
      <param name="count">Die Anzahl der zu schreibenden Zeichen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.String)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse Rohdatenmarkup manuell aus einer Zeichenfolge.</summary>
      <param name="data">Zeichenfolge, die den zu schreibenden Text enthält.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="data" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Schreibt asynchron unformatiertes Markup manuell aus einem Zeichenpuffer.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteRaw-Vorgang darstellt.</returns>
      <param name="buffer">Zeichenarray, das den zu schreibenden Text enthält.</param>
      <param name="index">Die Position innerhalb des Puffers, die den Anfang des zu schreibenden Texts kennzeichnet.</param>
      <param name="count">Die Anzahl der zu schreibenden Zeichen.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.String)">
      <summary>Schreibt asynchron unformatiertes Markup manuell aus einer Zeichenfolge.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteRaw-Vorgang darstellt.</returns>
      <param name="data">Zeichenfolge, die den zu schreibenden Text enthält.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String)">
      <summary>Schreibt den Anfang eines Attributs mit dem angegebenen lokalen Namen.</summary>
      <param name="localName">Der lokale Name des Attributs.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)">
      <summary>Schreibt den Anfang eines Attributs mit dem angegebenen lokalen Namen und dem angegebenen Namespace-URI.</summary>
      <param name="localName">Der lokale Name des Attributs.</param>
      <param name="ns">Der Namespace-URI dieses Attributs.</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String,System.String)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse den Anfang eines Attributs mit dem angegebenen Präfix, dem angegebenen lokalen Namen und dem angegebenen Namespace-URI.</summary>
      <param name="prefix">Das Namespacepräfix des Attributs.</param>
      <param name="localName">Der lokale Name des Attributs.</param>
      <param name="ns">Der Namespace-URI für das Attribut.</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttributeAsync(System.String,System.String,System.String)">
      <summary>Schreibt den Anfang eines Attributs asynchron mit dem angegebenen Präfix, lokalen Namen und Namespace-URI.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteStartAttribute-Vorgang darstellt.</returns>
      <param name="prefix">Das Namespacepräfix des Attributs.</param>
      <param name="localName">Der lokale Name des Attributs.</param>
      <param name="ns">Der Namespace-URI für das Attribut.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse die XML-Deklaration mit der Version "1.0".</summary>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument(System.Boolean)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse die XML-Deklaration mit der Version "1.0" und dem eigenständigen Attribut.</summary>
      <param name="standalone">Wenn true, wird "standalone=yes" geschrieben, wenn false, wird "standalone=no" geschrieben.</param>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync">
      <summary>Schreibt die XML-Deklaration asynchron mit der Version "1.0".</summary>
      <returns>Die Aufgabe, die den asynchronen WriteStartDocument-Vorgang darstellt.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync(System.Boolean)">
      <summary>Schreibt die XML-Deklaration asynchron mit der Version "1.0" und dem eigenständigen Attribut.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteStartDocument-Vorgang darstellt.</returns>
      <param name="standalone">Wenn true, wird "standalone=yes" geschrieben, wenn false, wird "standalone=no" geschrieben.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse ein Starttag mit dem angegebenen lokalen Namen.</summary>
      <param name="localName">Der lokale Name des Elements.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse das angegebene Starttag und ordnet dieses dem angegebenen Namespace zu.</summary>
      <param name="localName">Der lokale Name des Elements.</param>
      <param name="ns">Der Namespace-URI, der dem Element zugeordnet werden soll.Wenn sich dieser Namespace bereits im Gültigkeitsbereich befindet und dem Namespace ein Präfix zugeordnet ist, schreibt der Writer automatisch auch das Präfix.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String,System.String)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse das angegebene Starttag und ordnet dieses dem angegebenen Namespace und Präfix zu.</summary>
      <param name="prefix">Das Namespacepräfix des Elements.</param>
      <param name="localName">Der lokale Name des Elements.</param>
      <param name="ns">Der Namespace-URI, der dem Element zugeordnet werden soll.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding.For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names.The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer.Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values).However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElementAsync(System.String,System.String,System.String)">
      <summary>Schreibt das angegebene Starttag asynchron und ordnet dieses dem angegebenen Namespace und Präfix zu.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteStartElement-Vorgang darstellt.</returns>
      <param name="prefix">Das Namespacepräfix des Elements.</param>
      <param name="localName">Der lokale Name des Elements.</param>
      <param name="ns">Der Namespace-URI, der dem Element zugeordnet werden soll.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.WriteState">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den Zustand des Writers ab.</summary>
      <returns>Einer der <see cref="T:System.Xml.WriteState" />-Werte.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteString(System.String)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse den angegebenen Textinhalt.</summary>
      <param name="text">Der zu schreibende Text.</param>
      <exception cref="T:System.ArgumentException">The text string contains an invalid surrogate pair.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStringAsync(System.String)">
      <summary>Schreibt den angegebenen Textinhalt asynchron.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteString-Vorgang darstellt.</returns>
      <param name="text">Der zu schreibende Text.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntity(System.Char,System.Char)">
      <summary>Generiert und schreibt beim Überschreiben in einer abgeleiteten Klasse die Ersatzzeichenentität für das Ersatzzeichenpaar.</summary>
      <param name="lowChar">Das niedrige Ersatzzeichen.Dieses muss ein Wert zwischen 0xDC00 und 0xDFFF sein.</param>
      <param name="highChar">Das hohe Ersatzzeichen.Dieses muss ein Wert zwischen 0xD800 und 0xDBFF sein.</param>
      <exception cref="T:System.ArgumentException">An invalid surrogate character pair was passed.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntityAsync(System.Char,System.Char)">
      <summary>Generiert und schreibt die Ersatzzeichenentität asynchron für das Ersatzzeichenpaar.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteSurrogateCharEntity-Vorgang darstellt.</returns>
      <param name="lowChar">Das niedrige Ersatzzeichen.Dieses muss ein Wert zwischen 0xDC00 und 0xDFFF sein.</param>
      <param name="highChar">Das hohe Ersatzzeichen.Dieses muss ein Wert zwischen 0xD800 und 0xDBFF sein.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Boolean)">
      <summary>Schreibt einen <see cref="T:System.Boolean" />-Wert.</summary>
      <param name="value">Der zu schreibende <see cref="T:System.Boolean" />-Wert.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.DateTimeOffset)">
      <summary>Schreibt einen <see cref="T:System.DateTimeOffset" />-Wert.</summary>
      <param name="value">Der zu schreibende <see cref="T:System.DateTimeOffset" />-Wert.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Decimal)">
      <summary>Schreibt einen <see cref="T:System.Decimal" />-Wert.</summary>
      <param name="value">Der zu schreibende <see cref="T:System.Decimal" />-Wert.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Double)">
      <summary>Schreibt einen <see cref="T:System.Double" />-Wert.</summary>
      <param name="value">Der zu schreibende <see cref="T:System.Double" />-Wert.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int32)">
      <summary>Schreibt einen <see cref="T:System.Int32" />-Wert.</summary>
      <param name="value">Der zu schreibende <see cref="T:System.Int32" />-Wert.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int64)">
      <summary>Schreibt einen <see cref="T:System.Int64" />-Wert.</summary>
      <param name="value">Der zu schreibende <see cref="T:System.Int64" />-Wert.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Object)">
      <summary>Schreibt den Objektwert.</summary>
      <param name="value">Der zu schreibende Objektwert.Hinweis   Seit der Veröffentlichung von .NET Framework 3.5 akzeptiert diese Methode nun auch <see cref="T:System.DateTimeOffset" /> als Parameter.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Single)">
      <summary>Schreibt eine Gleitkommazahl mit einfacher Genauigkeit.</summary>
      <param name="value">Die zu schreibende Gleitkommazahl mit einfacher Genauigkeit.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.String)">
      <summary>Schreibt einen <see cref="T:System.String" />-Wert.</summary>
      <param name="value">Der zu schreibende <see cref="T:System.String" />-Wert.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespace(System.String)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse den angegebenen Leerraum.</summary>
      <param name="ws">Die Zeichenfolge von Leerraumzeichen.</param>
      <exception cref="T:System.ArgumentException">The string contains non-white space characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespaceAsync(System.String)">
      <summary>Schreibt den angegebenen Leerraum asynchron.</summary>
      <returns>Die Aufgabe, die den asynchronen WriteWhitespace-Vorgang darstellt.</returns>
      <param name="ws">Die Zeichenfolge von Leerraumzeichen.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlLang">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den aktuellen xml:lang-Bereich ab.</summary>
      <returns>Der aktuelle xml:lang-Bereich.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlSpace">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse einen <see cref="T:System.Xml.XmlSpace" /> ab, der den aktuellen xml:space-Bereich darstellt.</summary>
      <returns>Ein XmlSpace, der den aktuellen xml:space-Bereich darstellt.Wert Bedeutung NoneDies ist der Standardwert, wenn kein xml:space-Bereich vorhanden ist.DefaultDer aktuelle Bereich ist xml:space="default".PreserveDer aktuelle Bereich ist xml:space="preserve".</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished.In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="T:System.Xml.XmlWriterSettings">
      <summary>Gibt eine Gruppe von Features an, die für das <see cref="T:System.Xml.XmlWriter" />-Objekt unterstützt werden sollen, welches von der <see cref="Overload:System.Xml.XmlWriter.Create" />-Methode erstellt wurde.</summary>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Xml.XmlWriterSettings" />-Klasse.</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Async">
      <summary>Ruft einen Wert ab oder legt einen Wert fest, der angibt, ob asynchrone <see cref="T:System.Xml.XmlWriter" />-Methoden für eine bestimmte <see cref="T:System.Xml.XmlWriter" />-Instanz verwendet werden können.</summary>
      <returns>true, wenn asynchrone Methoden verwendet werden können; andernfalls false.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CheckCharacters">
      <summary>Ruft einen Wert ab, der angibt, ob der XML-Writer eine Prüfung durchführen soll, um sicherzustellen, dass alle Zeichen im Dokument den im Abschnitt der W3C-Empfehlung für XML 1.0 beschriebenen "2.2 Characters" entsprechen, oder legt diesen Wert fest.</summary>
      <returns>true, wenn Zeichen überprüft werden sollen, andernfalls false.Die Standardeinstellung ist true.</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Clone">
      <summary>Erstellt eine Kopie der <see cref="T:System.Xml.XmlWriterSettings" />-Instanz.</summary>
      <returns>Das geklonte <see cref="T:System.Xml.XmlWriterSettings" />-Objekt.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CloseOutput">
      <summary>Ruft einen Wert ab, der angibt, ob der <see cref="T:System.Xml.XmlWriter" /> auch den zugrunde liegenden Stream oder <see cref="T:System.IO.TextWriter" /> schließen soll, wenn die <see cref="M:System.Xml.XmlWriter.Close" />-Methode aufgerufen wird, oder legt diesen Wert fest.</summary>
      <returns>true, wenn auch der zugrunde liegende Stream oder <see cref="T:System.IO.TextWriter" /> geschlossen werden soll, andernfalls false.Die Standardeinstellung ist false.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.ConformanceLevel">
      <summary>Ruft das Übereinstimmungsniveau ab, auf den der XML-Writer die XML-Ausgabe überprüft, oder legt dieses fest.</summary>
      <returns>Einer der Enumerationswerte, der das Übereinstimmungsniveau angibt (Dokument, Fragment oder automatische Erkennung).Die Standardeinstellung ist <see cref="F:System.Xml.ConformanceLevel.Document" />.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Encoding">
      <summary>Ruft den Typ der Textcodierung ab oder legt diesen fest.</summary>
      <returns>Die zu verwendende Textcodierung.Die Standardeinstellung ist Encoding.UTF8.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Indent">
      <summary>Ruft einen Wert ab, der angibt, ob Elemente eingezogen werden sollen, oder legt diesen fest.</summary>
      <returns>true, wenn einzelne Elemente mit Einzug in neue Zeilen geschrieben werden sollen, andernfalls false.Die Standardeinstellung ist false.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.IndentChars">
      <summary>Ruft die Zeichenfolge ab, die für den Einzug verwendet werden soll, oder legt diese fest.Diese Einstellung wird verwendet, wenn die <see cref="P:System.Xml.XmlWriterSettings.Indent" />-Eigenschaft auf true festgelegt ist.</summary>
      <returns>Die für den Einzug zu verwendende Zeichenfolge.Diese kann auf jeden Zeichenfolgenwert festgelegt werden.Wenn Sie die Gültigkeit des XML-Codes sicherstellen möchten, sollten Sie jedoch nur gültige Leerraumzeichen, z. B. Leerzeichen, Tabstoppzeichen, Wagenrückläufe oder Zeilenvorschübe angeben.Der Standard beträgt zwei Leerzeichen.</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.IndentChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NamespaceHandling">
      <summary>Ruft einen Wert ab, der angibt, ob der <see cref="T:System.Xml.XmlWriter" /> beim Schreiben von XML-Inhalt doppelte Namespacedeklarationen entfernen soll, oder legt diesen fest.Im Standardverhalten gibt der Writer alle Namespacedeklarationen aus, die in der Namespaceauflösung des Writers vorhanden sind.</summary>
      <returns>Die <see cref="T:System.Xml.NamespaceHandling" />-Enumeration, die verwendet wird, um anzugeben, ob doppelte Namespacedeklarationen im <see cref="T:System.Xml.XmlWriter" /> entfernt werden.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineChars">
      <summary>Ruft die Zeichenfolge ab, die für Zeilenumbrüche verwendet werden soll, oder legt diese fest.</summary>
      <returns>Die Zeichenfolge, die für Zeilenumbrüche verwendet werden soll.Diese kann auf jeden Zeichenfolgenwert festgelegt werden.Wenn Sie die Gültigkeit des XML-Codes sicherstellen möchten, sollten Sie jedoch nur gültige Leerraumzeichen, z. B. Leerzeichen, Tabstoppzeichen, Wagenrückläufe oder Zeilenvorschübe angeben.Der Standardwert ist \r\n (Wagenrücklauf, neue Zeile).</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineHandling">
      <summary>Ruft einen Wert ab, der angibt, ob Zeilenumbrüche in der Ausgabe normalisiert werden sollen, oder legt diesen fest.</summary>
      <returns>Einer der <see cref="T:System.Xml.NewLineHandling" />-Werte.Die Standardeinstellung ist <see cref="F:System.Xml.NewLineHandling.Replace" />.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineOnAttributes">
      <summary>Ruft einen Wert ab, der angibt, ob Attribute in eine neue Zeile geschrieben werden sollen, oder legt diesen fest.</summary>
      <returns>true, um Attribute in einzelne Zeilen zu schreiben, andernfalls false.Die Standardeinstellung ist false.HinweisDiese Einstellung hat keinerlei Auswirkungen, wenn der <see cref="P:System.Xml.XmlWriterSettings.Indent" />-Eigenschaftswert false ist.Wenn <see cref="P:System.Xml.XmlWriterSettings.NewLineOnAttributes" /> auf true festgelegt ist, wird jedem Attribut eine neue Zeile und eine zusätzliche Einzugsebene vorangestellt.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.OmitXmlDeclaration">
      <summary>Ruft einen Wert ab, der angibt, ob eine XML-Deklaration ausgelassen werden soll, oder legt diesen fest.</summary>
      <returns>true, um die XML-Deklaration auszulassen, andernfalls false.Der Standardwert ist false. Es wird eine XML-Deklaration geschrieben.</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Reset">
      <summary>Setzt die Member der settings-Klasse auf ihre Standardwerte zurück.</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.WriteEndDocumentOnClose">
      <summary>Ruft einen Wert ab oder legt einen Wert fest, der angibt, ob <see cref="T:System.Xml.XmlWriter" /> Endtags zu allen nicht geschlossenen Elementtags hinzufügt, wenn die <see cref="M:System.Xml.XmlWriter.Close" />-Methode aufgerufen wird.</summary>
      <returns>true, wenn alle nicht geschlossenen Elementtags geschlossen werden; andernfalls false.Der Standardwert ist true.</returns>
    </member>
    <member name="T:System.Xml.Schema.XmlSchema">
      <summary>Eine speicherinterne Darstellung eines XML Schema, wie vom World Wide Web Consortium (W3C) in den Spezifikationen unter XML Schema Part 1: Strukturen festgelegt und XML Schema Part 2: Datentypen Spezifikationen.</summary>
    </member>
    <member name="T:System.Xml.Schema.XmlSchemaForm">
      <summary>Gibt an, ob Attribute oder Elemente mit einem Namespacepräfix qualifiziert werden müssen.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.None">
      <summary>Element- und Attributform werden im nicht Schema angegeben.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Qualified">
      <summary>Elemente und Attribute müssen mit einem Namespacepräfix qualifiziert werden.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Unqualified">
      <summary>Elemente und Attribute müssen nicht mit einem Namespacepräfix qualifiziert werden.</summary>
    </member>
    <member name="T:System.Xml.Serialization.IXmlSerializable">
      <summary>Stellt benutzerdefinierte Formatierungen für die XML-Serialisierung und -Deserialisierung bereit.</summary>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.GetSchema">
      <summary>Diese Methode ist reserviert und sollte nicht verwendet werden.Wenn Sie die IXmlSerializable-Schnittstelle implementieren, sollten Sie null (Nothing in Visual Basic) von der Methode zurückgeben und stattdessen das <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" /> auf die Klasse anwenden, wenn ein benutzerdefiniertes Schema erforderlich ist.</summary>
      <returns>Ein <see cref="T:System.Xml.Schema.XmlSchema" /> zur Beschreibung der XML-Darstellung des Objekts, das von der <see cref="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)" />-Methode erstellt und von der <see cref="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)" />-Methode verwendet wird.</returns>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)">
      <summary>Generiert ein Objekt aus seiner XML-Darstellung.</summary>
      <param name="reader">Der <see cref="T:System.Xml.XmlReader" />-Stream, aus dem das Objekt deserialisiert wird. </param>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)">
      <summary>Konvertiert ein Objekt in seine XML-Darstellung.</summary>
      <param name="writer">Der <see cref="T:System.Xml.XmlWriter" />-Stream, in den das Objekt serialisiert wird. </param>
    </member>
    <member name="T:System.Xml.Serialization.XmlSchemaProviderAttribute">
      <summary>Bei Anwendung auf einen Typ wird der Name einer statischen Methode des Typs gespeichert, die ein XML-Schema und einen <see cref="T:System.Xml.XmlQualifiedName" /> (bzw. einen <see cref="T:System.Xml.Schema.XmlSchemaType" /> bei anonymen Typen) zurückgibt, der die Serialisierung des Typs steuert.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaProviderAttribute.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" />-Klasse und übernimmt den Namen der statischen Methode, die vom XML-Schema des Typs zur Verfügung gestellt wird.</summary>
      <param name="methodName">Der Name der statischen Methode, die implementiert werden muss.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.IsAny">
      <summary>Ruft einen Wert ab, der bestimmt, ob die Zielklasse ein Platzhalter ist oder ob das Schema für die Klasse nur ein xs:any-Element enthält, oder legt diesen fest.</summary>
      <returns>true, wenn die Klasse ein Platzhalter ist oder das Schema nur das xs:any-Element enthält, andernfalls false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.MethodName">
      <summary>Ruft den Namen der statischen Methode ab, die das XML-Schema des Typs und den Namen seines XML-Schemadatentyps bereitstellt.</summary>
      <returns>Der Name der Methode, die von der XML-Infrastruktur aufgerufen wird, sodass ein XML-Schema zurückgegeben wird.</returns>
    </member>
  </members>
</doc>