using System;
using System.Collections.Generic;
using System.IO;

namespace OcrLiteLib
{
    /// <summary>
    /// 优化后的OCR使用示例
    /// </summary>
    public class OptimizedOcrExample
    {
        /// <summary>
        /// 简单的OCR文本识别示例
        /// </summary>
        public static void RunSimpleExample()
        {
            try
            {
                using (var ocrLite = new OcrLite())
                {
                    // 初始化模型
                    string detPath = "models/det.onnx";
                    string clsPath = "models/cls.onnx";
                    string recPath = "models/rec.onnx";
                    string keysPath = "models/keys.txt";
                    int numThread = 4;

                    ocrLite.InitModels(detPath, clsPath, recPath, keysPath, numThread);

                    string imagePath = "test.jpg";
                    if (File.Exists(imagePath))
                    {
                        // 方式1: 只获取文本
                        string text = ocrLite.DetectText(imagePath);
                        Console.WriteLine("识别的文本:");
                        Console.WriteLine(text);

                        Console.WriteLine("\n" + new string('=', 50) + "\n");

                        // 方式2: 获取详细信息（位置、尺寸、置信度）
                        var textBlocks = ocrLite.DetectTextBlocks(imagePath);
                        Console.WriteLine("详细识别结果:");
                        foreach (var block in textBlocks)
                        {
                            Console.WriteLine($"文本: {block.Text}");
                            Console.WriteLine($"位置: [{block.BoxPoints[0].X},{block.BoxPoints[0].Y}] -> [{block.BoxPoints[2].X},{block.BoxPoints[2].Y}]");
                            Console.WriteLine($"文本框置信度: {block.BoxScore:F3}");
                            Console.WriteLine($"角度置信度: {block.AngleScore:F3}");
                            Console.WriteLine($"识别时间: {block.CrnnTime:F1}ms");
                            Console.WriteLine();
                        }
                    }
                    else
                    {
                        Console.WriteLine($"图像文件不存在: {imagePath}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 演示如何正确使用优化后的OCR库
        /// </summary>
        public static void RunExample()
        {
            // 开始内存监控
            MemoryMonitor.StartMonitoring();

            try
            {
                // 使用using确保资源正确释放
                using (var ocrLite = new OcrLite())
                {
                    MemoryMonitor.Checkpoint("OcrLite创建完成");

                    // 初始化模型
                    string detPath = "models/det.onnx";
                    string clsPath = "models/cls.onnx";
                    string recPath = "models/rec.onnx";
                    string keysPath = "models/keys.txt";
                    int numThread = 4;

                    ocrLite.InitModels(detPath, clsPath, recPath, keysPath, numThread);
                    MemoryMonitor.Checkpoint("模型初始化完成");

                    // 批量处理图像 - 简化版本
                    string[] imageFiles = { "test1.jpg", "test2.jpg", "test3.jpg" };

                    foreach (string imageFile in imageFiles)
                    {
                        if (File.Exists(imageFile))
                        {
                            Console.WriteLine($"\n处理图像: {imageFile}");

                            using (var scope = new MemoryScope($"处理 {imageFile}"))
                            {
                                // 直接获取文本，无需复杂的结果对象
                                string text = ocrLite.DetectText(imageFile);
                                Console.WriteLine($"识别结果: {text}");
                            }

                            // 检查内存泄漏
                            if (MemoryMonitor.CheckForMemoryLeak(30 * 1024 * 1024)) // 30MB阈值
                            {
                                Console.WriteLine("警告: 检测到内存泄漏!");
                                Console.WriteLine(MemoryMonitor.GetDetailedReport());
                            }
                        }
                    }

                    MemoryMonitor.CheckpointWithGC("所有图像处理完成");
                }

                MemoryMonitor.CheckpointWithGC("OcrLite已释放");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
            finally
            {
                // 显示最终内存报告
                Console.WriteLine("\n" + MemoryMonitor.GetDetailedReport());
            }
        }
        
        /// <summary>
        /// 演示频繁调用的内存优化
        /// </summary>
        public static void RunFrequentCallsExample()
        {
            Console.WriteLine("=== 频繁调用测试 ===");
            MemoryMonitor.StartMonitoring();
            
            using (var ocrLite = new OcrLite())
            {
                // 初始化模型（省略具体路径）
                // ocrLite.InitModels(...);
                
                string testImage = "test.jpg";
                if (!File.Exists(testImage))
                {
                    Console.WriteLine($"测试图像 {testImage} 不存在");
                    return;
                }
                
                // 模拟频繁调用
                for (int i = 0; i < 100; i++)
                {
                    using (var scope = new MemoryScope($"调用 {i + 1}"))
                    {
                        var result = ocrLite.Detect(testImage, 50, 1024, 0.5f, 0.3f, 2.0f, true, true);
                        
                        if (i % 10 == 0)
                        {
                            MemoryMonitor.Checkpoint($"完成 {i + 1} 次调用");
                        }
                    }
                    
                    // 每20次调用检查一次内存泄漏
                    if (i % 20 == 19)
                    {
                        if (MemoryMonitor.CheckForMemoryLeak(50 * 1024 * 1024)) // 50MB阈值
                        {
                            Console.WriteLine($"在第 {i + 1} 次调用后检测到内存泄漏!");
                            break;
                        }
                    }
                }
                
                MemoryMonitor.CheckpointWithGC("频繁调用测试完成");
            }
            
            Console.WriteLine(MemoryMonitor.GetDetailedReport());
        }
        
        /// <summary>
        /// 演示内存池的使用
        /// </summary>
        public static void RunMatPoolExample()
        {
            Console.WriteLine("=== Mat池测试 ===");
            MemoryMonitor.StartMonitoring();
            
            using (var matPool = new MatPool())
            {
                MemoryMonitor.Checkpoint("Mat池创建完成");
                
                // 模拟大量Mat对象的创建和释放
                for (int i = 0; i < 1000; i++)
                {
                    using (var pooledMat = new PooledMat(matPool.GetMat(640, 480), matPool))
                    {
                        // 使用Mat对象进行一些操作
                        var mat = pooledMat.Mat;
                        // ... 进行图像处理 ...
                    } // PooledMat会自动归还Mat到池中
                    
                    if (i % 100 == 99)
                    {
                        MemoryMonitor.Checkpoint($"完成 {i + 1} 次Mat操作");
                        Console.WriteLine($"Mat池状态: {matPool.GetPoolStats()}");
                    }
                }
                
                MemoryMonitor.CheckpointWithGC("Mat池测试完成");
                Console.WriteLine($"最终Mat池状态: {matPool.GetPoolStats()}");
            }
            
            Console.WriteLine(MemoryMonitor.GetDetailedReport());
        }
    }
    
    /// <summary>
    /// 内存优化最佳实践指南
    /// </summary>
    public static class MemoryOptimizationGuide
    {
        public static void PrintBestPractices()
        {
            Console.WriteLine("=== OCR使用最佳实践 ===");
            Console.WriteLine();
            Console.WriteLine("1. 简单文本识别:");
            Console.WriteLine("   using (var ocrLite = new OcrLite()) {");
            Console.WriteLine("       ocrLite.InitModels(detPath, clsPath, recPath, keysPath, numThread);");
            Console.WriteLine("       string text = ocrLite.DetectText(imagePath);");
            Console.WriteLine("   }");
            Console.WriteLine();
            Console.WriteLine("2. 获取详细信息（位置、尺寸、置信度）- 推荐方式:");
            Console.WriteLine("   using (var ocrLite = new OcrLite()) {");
            Console.WriteLine("       ocrLite.InitModels(detPath, clsPath, recPath, keysPath, numThread);");
            Console.WriteLine("       var textBlocks = ocrLite.DetectTextBlocks(imagePath);");
            Console.WriteLine("       foreach (var block in textBlocks) {");
            Console.WriteLine("           Console.WriteLine($\"文本: {block.Text}\");");
            Console.WriteLine("           Console.WriteLine($\"位置: {block.BoxPoints}\");");
            Console.WriteLine("           Console.WriteLine($\"置信度: {block.BoxScore}\");");
            Console.WriteLine("       }");
            Console.WriteLine("   }");
            Console.WriteLine();
            Console.WriteLine("3. 始终使用using语句确保资源释放:");
            Console.WriteLine("   using (var ocrLite = new OcrLite()) { ... }");
            Console.WriteLine();
            Console.WriteLine("4. 启用内存监控（可选）:");
            Console.WriteLine("   MemoryMonitor.StartMonitoring();");
            Console.WriteLine("   MemoryMonitor.Checkpoint(\"检查点名称\");");
            Console.WriteLine();
            Console.WriteLine("5. 频繁调用时检查内存泄漏:");
            Console.WriteLine("   if (MemoryMonitor.CheckForMemoryLeak()) { ... }");
            Console.WriteLine();
            Console.WriteLine("6. 使用Mat池减少内存分配（高级用法）:");
            Console.WriteLine("   using (var pooledMat = new PooledMat(...)) { ... }");
            Console.WriteLine();
            Console.WriteLine("7. TextBlock包含的信息:");
            Console.WriteLine("   - Text: 识别的文本内容");
            Console.WriteLine("   - BoxPoints: 文本框的四个角点坐标");
            Console.WriteLine("   - BoxScore: 文本框检测置信度");
            Console.WriteLine("   - AngleScore: 角度检测置信度");
            Console.WriteLine("   - CharScores: 每个字符的识别置信度");
            Console.WriteLine("   - CrnnTime: 文本识别耗时");
            Console.WriteLine();
            Console.WriteLine("8. 设置合理的内存泄漏阈值:");
            Console.WriteLine("   CheckForMemoryLeak(50 * 1024 * 1024); // 50MB");
            Console.WriteLine();
        }
    }
}
