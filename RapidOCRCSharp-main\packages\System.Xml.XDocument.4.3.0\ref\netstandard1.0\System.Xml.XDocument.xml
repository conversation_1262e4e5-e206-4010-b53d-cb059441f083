﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.XDocument</name>
  </assembly>
  <members>
    <member name="T:System.Xml.Linq.Extensions">
      <summary>Contains the LINQ to XML extension methods.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.Extensions.Ancestors``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns a collection of elements that contains the ancestors of every node in the source collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contains the ancestors of every node in the source collection.</returns>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XNode" /> that contains the source collection.</param>
      <typeparam name="T">The type of the objects in <paramref name="source" />, constrained to <see cref="T:System.Xml.Linq.XNode" />.</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.Extensions.Ancestors``1(System.Collections.Generic.IEnumerable{``0},System.Xml.Linq.XName)">
      <summary>Returns a filtered collection of elements that contains the ancestors of every node in the source collection. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contains the ancestors of every node in the source collection. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</returns>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XNode" /> that contains the source collection.</param>
      <param name="name">The <see cref="T:System.Xml.Linq.XName" /> to match.</param>
      <typeparam name="T">The type of the objects in <paramref name="source" />, constrained to <see cref="T:System.Xml.Linq.XNode" />.</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.Extensions.AncestorsAndSelf(System.Collections.Generic.IEnumerable{System.Xml.Linq.XElement})">
      <summary>Returns a collection of elements that contains every element in the source collection, and the ancestors of every element in the source collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contains every element in the source collection, and the ancestors of every element in the source collection.</returns>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contains the source collection.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.Extensions.AncestorsAndSelf(System.Collections.Generic.IEnumerable{System.Xml.Linq.XElement},System.Xml.Linq.XName)">
      <summary>Returns a filtered collection of elements that contains every element in the source collection, and the ancestors of every element in the source collection. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contains every element in the source collection, and the ancestors of every element in the source collection. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</returns>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contains the source collection.</param>
      <param name="name">The <see cref="T:System.Xml.Linq.XName" /> to match.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.Extensions.Attributes(System.Collections.Generic.IEnumerable{System.Xml.Linq.XElement})">
      <summary>Returns a collection of the attributes of every element in the source collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XAttribute" /> that contains the attributes of every element in the source collection.</returns>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contains the source collection.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.Extensions.Attributes(System.Collections.Generic.IEnumerable{System.Xml.Linq.XElement},System.Xml.Linq.XName)">
      <summary>Returns a filtered collection of the attributes of every element in the source collection. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XAttribute" /> that contains a filtered collection of the attributes of every element in the source collection. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</returns>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contains the source collection.</param>
      <param name="name">The <see cref="T:System.Xml.Linq.XName" /> to match.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.Extensions.DescendantNodes``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns a collection of the descendant nodes of every document and element in the source collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XNode" /> of the descendant nodes of every document and element in the source collection.</returns>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XContainer" /> that contains the source collection.</param>
      <typeparam name="T">The type of the objects in <paramref name="source" />, constrained to <see cref="T:System.Xml.Linq.XContainer" />.</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.Extensions.DescendantNodesAndSelf(System.Collections.Generic.IEnumerable{System.Xml.Linq.XElement})">
      <summary>Returns a collection of nodes that contains every element in the source collection, and the descendant nodes of every element in the source collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XNode" /> that contains every element in the source collection, and the descendant nodes of every element in the source collection.</returns>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contains the source collection.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.Extensions.Descendants``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns a collection of elements that contains the descendant elements of every element and document in the source collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contains the descendant elements of every element and document in the source collection.</returns>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XContainer" /> that contains the source collection.</param>
      <typeparam name="T">The type of the objects in <paramref name="source" />, constrained to <see cref="T:System.Xml.Linq.XContainer" />.</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.Extensions.Descendants``1(System.Collections.Generic.IEnumerable{``0},System.Xml.Linq.XName)">
      <summary>Returns a filtered collection of elements that contains the descendant elements of every element and document in the source collection. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contains the descendant elements of every element and document in the source collection. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</returns>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XContainer" /> that contains the source collection.</param>
      <param name="name">The <see cref="T:System.Xml.Linq.XName" /> to match.</param>
      <typeparam name="T">The type of the objects in <paramref name="source" />, constrained to <see cref="T:System.Xml.Linq.XContainer" />.</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.Extensions.DescendantsAndSelf(System.Collections.Generic.IEnumerable{System.Xml.Linq.XElement})">
      <summary>Returns a collection of elements that contains every element in the source collection, and the descendent elements of every element in the source collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contains every element in the source collection, and the descendent elements of every element in the source collection.</returns>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contains the source collection.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.Extensions.DescendantsAndSelf(System.Collections.Generic.IEnumerable{System.Xml.Linq.XElement},System.Xml.Linq.XName)">
      <summary>Returns a filtered collection of elements that contains every element in the source collection, and the descendents of every element in the source collection. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contains every element in the source collection, and the descendents of every element in the source collection. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</returns>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contains the source collection.</param>
      <param name="name">The <see cref="T:System.Xml.Linq.XName" /> to match.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.Extensions.Elements``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns a collection of the child elements of every element and document in the source collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> of the child elements of every element or document in the source collection.</returns>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contains the source collection.</param>
      <typeparam name="T">The type of the objects in <paramref name="source" />, constrained to <see cref="T:System.Xml.Linq.XContainer" />.</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.Extensions.Elements``1(System.Collections.Generic.IEnumerable{``0},System.Xml.Linq.XName)">
      <summary>Returns a filtered collection of the child elements of every element and document in the source collection. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> of the child elements of every element and document in the source collection. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</returns>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contains the source collection.</param>
      <param name="name">The <see cref="T:System.Xml.Linq.XName" /> to match.</param>
      <typeparam name="T">The type of the objects in <paramref name="source" />, constrained to <see cref="T:System.Xml.Linq.XContainer" />.</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.Extensions.InDocumentOrder``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns a collection of nodes that contains all nodes in the source collection, sorted in document order.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XNode" /> that contains all nodes in the source collection, sorted in document order.</returns>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XNode" /> that contains the source collection.</param>
      <typeparam name="T">The type of the objects in <paramref name="source" />, constrained to <see cref="T:System.Xml.Linq.XNode" />.</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.Extensions.Nodes``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Returns a collection of the child nodes of every document and element in the source collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XNode" /> of the child nodes of every document and element in the source collection.</returns>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XNode" /> that contains the source collection.</param>
      <typeparam name="T">The type of the objects in <paramref name="source" />, constrained to <see cref="T:System.Xml.Linq.XContainer" />.</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.Extensions.Remove(System.Collections.Generic.IEnumerable{System.Xml.Linq.XAttribute})">
      <summary>Removes every attribute in the source collection from its parent element.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XAttribute" /> that contains the source collection.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.Extensions.Remove``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Removes every node in the source collection from its parent node.</summary>
      <param name="source">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XNode" /> that contains the source collection.</param>
      <typeparam name="T">The type of the objects in <paramref name="source" />, constrained to <see cref="T:System.Xml.Linq.XNode" />.</typeparam>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Xml.Linq.LoadOptions">
      <summary>Specifies load options when parsing XML. </summary>
    </member>
    <member name="F:System.Xml.Linq.LoadOptions.None">
      <summary>Does not preserve insignificant white space or load base URI and line information.</summary>
    </member>
    <member name="F:System.Xml.Linq.LoadOptions.PreserveWhitespace">
      <summary>Preserves insignificant white space while parsing.</summary>
    </member>
    <member name="F:System.Xml.Linq.LoadOptions.SetBaseUri">
      <summary>Requests the base URI information from the <see cref="T:System.Xml.XmlReader" />, and makes it available via the <see cref="P:System.Xml.Linq.XObject.BaseUri" /> property.</summary>
    </member>
    <member name="F:System.Xml.Linq.LoadOptions.SetLineInfo">
      <summary>Requests the line information from the <see cref="T:System.Xml.XmlReader" /> and makes it available via properties on <see cref="T:System.Xml.Linq.XObject" />.</summary>
    </member>
    <member name="T:System.Xml.Linq.ReaderOptions">
      <summary>Specifies whether to omit duplicate namespaces when loading an <see cref="T:System.Xml.Linq.XDocument" /> with an <see cref="T:System.Xml.XmlReader" />.</summary>
    </member>
    <member name="F:System.Xml.Linq.ReaderOptions.None">
      <summary>No reader options specified.</summary>
    </member>
    <member name="F:System.Xml.Linq.ReaderOptions.OmitDuplicateNamespaces">
      <summary>Omit duplicate namespaces when loading the <see cref="T:System.Xml.Linq.XDocument" />.</summary>
    </member>
    <member name="T:System.Xml.Linq.SaveOptions">
      <summary>Specifies serialization options.</summary>
    </member>
    <member name="F:System.Xml.Linq.SaveOptions.DisableFormatting">
      <summary>Preserve all insignificant white space while serializing.</summary>
    </member>
    <member name="F:System.Xml.Linq.SaveOptions.None">
      <summary>Format (indent) the XML while serializing.</summary>
    </member>
    <member name="F:System.Xml.Linq.SaveOptions.OmitDuplicateNamespaces">
      <summary>Remove the duplicate namespace declarations while serializing.</summary>
    </member>
    <member name="T:System.Xml.Linq.XAttribute">
      <summary>Represents an XML attribute.</summary>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.#ctor(System.Xml.Linq.XAttribute)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XAttribute" /> class from another <see cref="T:System.Xml.Linq.XAttribute" /> object. </summary>
      <param name="other">An <see cref="T:System.Xml.Linq.XAttribute" /> object to copy from.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="other" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.#ctor(System.Xml.Linq.XName,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XAttribute" /> class from the specified name and value. </summary>
      <param name="name">The <see cref="T:System.Xml.Linq.XName" /> of the attribute.</param>
      <param name="value">An <see cref="T:System.Object" /> containing the value of the attribute.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="name" /> or <paramref name="value" /> parameter is null.</exception>
    </member>
    <member name="P:System.Xml.Linq.XAttribute.EmptySequence">
      <summary>Gets an empty collection of attributes.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XAttribute" /> containing an empty collection.</returns>
    </member>
    <member name="P:System.Xml.Linq.XAttribute.IsNamespaceDeclaration">
      <summary>Determines if this attribute is a namespace declaration.</summary>
      <returns>true if this attribute is a namespace declaration; otherwise false.</returns>
    </member>
    <member name="P:System.Xml.Linq.XAttribute.Name">
      <summary>Gets the expanded name of this attribute.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XName" /> containing the name of this attribute.</returns>
    </member>
    <member name="P:System.Xml.Linq.XAttribute.NextAttribute">
      <summary>Gets the next attribute of the parent element.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XAttribute" /> containing the next attribute of the parent element.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Xml.Linq.XAttribute.NodeType">
      <summary>Gets the node type for this node.</summary>
      <returns>The node type. For <see cref="T:System.Xml.Linq.XAttribute" /> objects, this value is <see cref="F:System.Xml.XmlNodeType.Attribute" />.</returns>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Nullable{System.UInt32}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.UInt32" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.UInt32" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.UInt32" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.UInt32" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Nullable{System.UInt64}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.UInt64" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.UInt64" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.UInt64" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.UInt64" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Nullable{System.TimeSpan}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.TimeSpan" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.TimeSpan" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.TimeSpan" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.TimeSpan" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Nullable{System.Int64}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Int64" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.Int64" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Int64" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.Int64" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Nullable{System.Single}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Single" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.Single" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to <see cref="T:System.Nullable`1" /> of <see cref="T:System.Single" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.Single" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.UInt32">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.UInt32" />.</summary>
      <returns>A <see cref="T:System.UInt32" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to <see cref="T:System.UInt32" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.UInt32" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="attribute" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.UInt64">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.UInt64" />.</summary>
      <returns>A <see cref="T:System.UInt64" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to <see cref="T:System.UInt64" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.UInt64" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="attribute" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.TimeSpan">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.TimeSpan" />.</summary>
      <returns>A <see cref="T:System.TimeSpan" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to <see cref="T:System.TimeSpan" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.TimeSpan" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="attribute" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Single">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.Single" />.</summary>
      <returns>A <see cref="T:System.Single" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to <see cref="T:System.Single" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.Single" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="attribute" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.String">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.String" />.</summary>
      <returns>A <see cref="T:System.String" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to <see cref="T:System.String" />.</param>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Nullable{System.Int32}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Int32" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.Int32" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Int32" />.</param>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Double">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.Double" />.</summary>
      <returns>A <see cref="T:System.Double" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to <see cref="T:System.Double" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.Double" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="attribute" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Guid">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.Guid" />.</summary>
      <returns>A <see cref="T:System.Guid" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to <see cref="T:System.Guid" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.Guid" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="attribute" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Int32">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to an <see cref="T:System.Int32" />.</summary>
      <returns>A <see cref="T:System.Int32" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to <see cref="T:System.Int32" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.Int32" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="attribute" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Decimal">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.Decimal" />.</summary>
      <returns>A <see cref="T:System.Decimal" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to <see cref="T:System.Decimal" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.Decimal" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="attribute" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Boolean">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.Boolean" />.</summary>
      <returns>A <see cref="T:System.Boolean" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to <see cref="T:System.Boolean" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.Boolean" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="attribute" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.DateTime">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.DateTime" />.</summary>
      <returns>A <see cref="T:System.DateTime" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to <see cref="T:System.DateTime" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.DateTime" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="attribute" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.DateTimeOffset">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>A <see cref="T:System.DateTimeOffset" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to <see cref="T:System.DateTimeOffset" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.DateTimeOffset" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="attribute" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Nullable{System.Decimal}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Decimal" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.Decimal" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to <see cref="T:System.Nullable`1" /> of <see cref="T:System.Decimal" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.Decimal" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Nullable{System.DateTimeOffset}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.DateTimeOffset" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.DateTimeOffset" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.DateTimeOffset" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Nullable{System.Guid}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Guid" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.Guid" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Guid" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.Guid" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Nullable{System.Double}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Double" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.Double" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to <see cref="T:System.Nullable`1" /> of <see cref="T:System.Double" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.Double" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Int64">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to an <see cref="T:System.Int64" />.</summary>
      <returns>A <see cref="T:System.Int64" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to <see cref="T:System.Int64" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.Int64" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="attribute" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Nullable{System.DateTime}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.DateTime" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.DateTime" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.DateTime" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.DateTime" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.op_Explicit(System.Xml.Linq.XAttribute)~System.Nullable{System.Boolean}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Boolean" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.Boolean" /> that contains the content of this <see cref="T:System.Xml.Linq.XAttribute" />.</returns>
      <param name="attribute">The <see cref="T:System.Xml.Linq.XAttribute" /> to cast to <see cref="T:System.Nullable`1" /> of <see cref="T:System.Boolean" />.</param>
      <exception cref="T:System.FormatException">The attribute does not contain a valid <see cref="T:System.Boolean" /> value.</exception>
    </member>
    <member name="P:System.Xml.Linq.XAttribute.PreviousAttribute">
      <summary>Gets the previous attribute of the parent element.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XAttribute" /> containing the previous attribute of the parent element.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.Remove">
      <summary>Removes this attribute from its parent element.</summary>
      <exception cref="T:System.InvalidOperationException">The parent element is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.SetValue(System.Object)">
      <summary>Sets the value of this attribute.</summary>
      <param name="value">The value to assign to this attribute.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="value" /> is an <see cref="T:System.Xml.Linq.XObject" />.</exception>
    </member>
    <member name="M:System.Xml.Linq.XAttribute.ToString">
      <summary>Converts the current <see cref="T:System.Xml.Linq.XAttribute" /> object to a string representation.</summary>
      <returns>A <see cref="T:System.String" /> containing the XML text representation of an attribute and its value.</returns>
    </member>
    <member name="P:System.Xml.Linq.XAttribute.Value">
      <summary>Gets or sets the value of this attribute.</summary>
      <returns>A <see cref="T:System.String" /> containing the value of this attribute.</returns>
      <exception cref="T:System.ArgumentNullException">When setting, the <paramref name="value" /> is null.</exception>
    </member>
    <member name="T:System.Xml.Linq.XCData">
      <summary>Represents a text node that contains CDATA. </summary>
    </member>
    <member name="M:System.Xml.Linq.XCData.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XCData" /> class. </summary>
      <param name="value">A string that contains the value of the <see cref="T:System.Xml.Linq.XCData" /> node.</param>
    </member>
    <member name="M:System.Xml.Linq.XCData.#ctor(System.Xml.Linq.XCData)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XCData" /> class. </summary>
      <param name="other">The <see cref="T:System.Xml.Linq.XCData" /> node to copy from.</param>
    </member>
    <member name="P:System.Xml.Linq.XCData.NodeType">
      <summary>Gets the node type for this node.</summary>
      <returns>The node type. For <see cref="T:System.Xml.Linq.XCData" /> objects, this value is <see cref="F:System.Xml.XmlNodeType.CDATA" />.</returns>
    </member>
    <member name="M:System.Xml.Linq.XCData.WriteTo(System.Xml.XmlWriter)">
      <summary>Writes this CDATA object to an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlWriter" /> into which this method will write.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Xml.Linq.XComment">
      <summary>Represents an XML comment. </summary>
    </member>
    <member name="M:System.Xml.Linq.XComment.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XComment" /> class with the specified string content. </summary>
      <param name="value">A string that contains the contents of the new <see cref="T:System.Xml.Linq.XComment" /> object.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XComment.#ctor(System.Xml.Linq.XComment)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XComment" /> class from an existing comment node. </summary>
      <param name="other">The <see cref="T:System.Xml.Linq.XComment" /> node to copy from.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="other" /> parameter is null.</exception>
    </member>
    <member name="P:System.Xml.Linq.XComment.NodeType">
      <summary>Gets the node type for this node.</summary>
      <returns>The node type. For <see cref="T:System.Xml.Linq.XComment" /> objects, this value is <see cref="F:System.Xml.XmlNodeType.Comment" />.</returns>
    </member>
    <member name="P:System.Xml.Linq.XComment.Value">
      <summary>Gets or sets the string value of this comment.</summary>
      <returns>A <see cref="T:System.String" /> that contains the string value of this comment.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XComment.WriteTo(System.Xml.XmlWriter)">
      <summary>Write this comment to an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlWriter" /> into which this method will write.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Xml.Linq.XContainer">
      <summary>Represents a node that can contain other nodes.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.XContainer.Add(System.Object)">
      <summary>Adds the specified content as children of this <see cref="T:System.Xml.Linq.XContainer" />.</summary>
      <param name="content">A content object containing simple content or a collection of content objects to be added.</param>
    </member>
    <member name="M:System.Xml.Linq.XContainer.Add(System.Object[])">
      <summary>Adds the specified content as children of this <see cref="T:System.Xml.Linq.XContainer" />.</summary>
      <param name="content">A parameter list of content objects.</param>
    </member>
    <member name="M:System.Xml.Linq.XContainer.AddFirst(System.Object)">
      <summary>Adds the specified content as the first children of this document or element.</summary>
      <param name="content">A content object containing simple content or a collection of content objects to be added.</param>
    </member>
    <member name="M:System.Xml.Linq.XContainer.AddFirst(System.Object[])">
      <summary>Adds the specified content as the first children of this document or element.</summary>
      <param name="content">A parameter list of content objects.</param>
      <exception cref="T:System.InvalidOperationException">The parent is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XContainer.CreateWriter">
      <summary>Creates an <see cref="T:System.Xml.XmlWriter" /> that can be used to add nodes to the <see cref="T:System.Xml.Linq.XContainer" />.</summary>
      <returns>An <see cref="T:System.Xml.XmlWriter" /> that is ready to have content written to it.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.XContainer.DescendantNodes">
      <summary>Returns a collection of the descendant nodes for this document or element, in document order.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XNode" /> containing the descendant nodes of the <see cref="T:System.Xml.Linq.XContainer" />, in document order.</returns>
    </member>
    <member name="M:System.Xml.Linq.XContainer.Descendants">
      <summary>Returns a collection of the descendant elements for this document or element, in document order.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> containing the descendant elements of the <see cref="T:System.Xml.Linq.XContainer" />.</returns>
    </member>
    <member name="M:System.Xml.Linq.XContainer.Descendants(System.Xml.Linq.XName)">
      <summary>Returns a filtered collection of the descendant elements for this document or element, in document order. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> containing the descendant elements of the <see cref="T:System.Xml.Linq.XContainer" /> that match the specified <see cref="T:System.Xml.Linq.XName" />.</returns>
      <param name="name">The <see cref="T:System.Xml.Linq.XName" /> to match.</param>
    </member>
    <member name="M:System.Xml.Linq.XContainer.Element(System.Xml.Linq.XName)">
      <summary>Gets the first (in document order) child element with the specified <see cref="T:System.Xml.Linq.XName" />.</summary>
      <returns>A <see cref="T:System.Xml.Linq.XElement" /> that matches the specified <see cref="T:System.Xml.Linq.XName" />, or null.</returns>
      <param name="name">The <see cref="T:System.Xml.Linq.XName" /> to match.</param>
    </member>
    <member name="M:System.Xml.Linq.XContainer.Elements">
      <summary>Returns a collection of the child elements of this element or document, in document order.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> containing the child elements of this <see cref="T:System.Xml.Linq.XContainer" />, in document order.</returns>
    </member>
    <member name="M:System.Xml.Linq.XContainer.Elements(System.Xml.Linq.XName)">
      <summary>Returns a filtered collection of the child elements of this element or document, in document order. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> containing the children of the <see cref="T:System.Xml.Linq.XContainer" /> that have a matching <see cref="T:System.Xml.Linq.XName" />, in document order.</returns>
      <param name="name">The <see cref="T:System.Xml.Linq.XName" /> to match.</param>
    </member>
    <member name="P:System.Xml.Linq.XContainer.FirstNode">
      <summary>Get the first child node of this node.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XNode" /> containing the first child node of the <see cref="T:System.Xml.Linq.XContainer" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Xml.Linq.XContainer.LastNode">
      <summary>Get the last child node of this node.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XNode" /> containing the last child node of the <see cref="T:System.Xml.Linq.XContainer" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.XContainer.Nodes">
      <summary>Returns a collection of the child nodes of this element or document, in document order.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XNode" /> containing the contents of this <see cref="T:System.Xml.Linq.XContainer" />, in document order.</returns>
    </member>
    <member name="M:System.Xml.Linq.XContainer.RemoveNodes">
      <summary>Removes the child nodes from this document or element.</summary>
    </member>
    <member name="M:System.Xml.Linq.XContainer.ReplaceNodes(System.Object)">
      <summary>Replaces the children nodes of this document or element with the specified content.</summary>
      <param name="content">A content object containing simple content or a collection of content objects that replace the children nodes.</param>
    </member>
    <member name="M:System.Xml.Linq.XContainer.ReplaceNodes(System.Object[])">
      <summary>Replaces the children nodes of this document or element with the specified content.</summary>
      <param name="content">A parameter list of content objects.</param>
    </member>
    <member name="T:System.Xml.Linq.XDeclaration">
      <summary>Represents an XML declaration.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.XDeclaration.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XDeclaration" /> class with the specified version, encoding, and standalone status.</summary>
      <param name="version">The version of the XML, usually "1.0".</param>
      <param name="encoding">The encoding for the XML document.</param>
      <param name="standalone">A string containing "yes" or "no" that specifies whether the XML is standalone or requires external entities to be resolved.</param>
    </member>
    <member name="M:System.Xml.Linq.XDeclaration.#ctor(System.Xml.Linq.XDeclaration)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XDeclaration" /> class from another <see cref="T:System.Xml.Linq.XDeclaration" /> object. </summary>
      <param name="other">The <see cref="T:System.Xml.Linq.XDeclaration" /> used to initialize this <see cref="T:System.Xml.Linq.XDeclaration" /> object.</param>
    </member>
    <member name="P:System.Xml.Linq.XDeclaration.Encoding">
      <summary>Gets or sets the encoding for this document.</summary>
      <returns>A <see cref="T:System.String" /> containing the code page name for this document.</returns>
    </member>
    <member name="P:System.Xml.Linq.XDeclaration.Standalone">
      <summary>Gets or sets the standalone property for this document.</summary>
      <returns>A <see cref="T:System.String" /> containing the standalone property for this document.</returns>
    </member>
    <member name="M:System.Xml.Linq.XDeclaration.ToString">
      <summary>Provides the declaration as a formatted string.</summary>
      <returns>A <see cref="T:System.String" /> that contains the formatted XML string.</returns>
    </member>
    <member name="P:System.Xml.Linq.XDeclaration.Version">
      <summary>Gets or sets the version property for this document.</summary>
      <returns>A <see cref="T:System.String" /> containing the version property for this document.</returns>
    </member>
    <member name="T:System.Xml.Linq.XDocument">
      <summary>Represents an XML document. For the components and usage of an <see cref="T:System.Xml.Linq.XDocument" /> object, see XDocument Class Overview.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
    </member>
    <member name="M:System.Xml.Linq.XDocument.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XDocument" /> class. </summary>
    </member>
    <member name="M:System.Xml.Linq.XDocument.#ctor(System.Object[])">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XDocument" /> class with the specified content.</summary>
      <param name="content">A parameter list of content objects to add to this document.</param>
    </member>
    <member name="M:System.Xml.Linq.XDocument.#ctor(System.Xml.Linq.XDeclaration,System.Object[])">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XDocument" /> class with the specified <see cref="T:System.Xml.Linq.XDeclaration" /> and content.</summary>
      <param name="declaration">An <see cref="T:System.Xml.Linq.XDeclaration" /> for the document.</param>
      <param name="content">The content of the document.</param>
    </member>
    <member name="M:System.Xml.Linq.XDocument.#ctor(System.Xml.Linq.XDocument)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XDocument" /> class from an existing <see cref="T:System.Xml.Linq.XDocument" /> object.</summary>
      <param name="other">The <see cref="T:System.Xml.Linq.XDocument" /> object that will be copied.</param>
    </member>
    <member name="P:System.Xml.Linq.XDocument.Declaration">
      <summary>Gets or sets the XML declaration for this document.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XDeclaration" /> that contains the XML declaration for this document.</returns>
    </member>
    <member name="P:System.Xml.Linq.XDocument.DocumentType">
      <summary>Gets the Document Type Definition (DTD) for this document.</summary>
      <returns>A <see cref="T:System.Xml.Linq.XDocumentType" /> that contains the DTD for this document.</returns>
    </member>
    <member name="M:System.Xml.Linq.XDocument.Load(System.IO.Stream)">
      <summary>Creates a new <see cref="T:System.Xml.Linq.XDocument" /> instance by using the specified stream.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XDocument" /> object that reads the data that is contained in the stream. </returns>
      <param name="stream">The stream that contains the XML data.</param>
    </member>
    <member name="M:System.Xml.Linq.XDocument.Load(System.IO.Stream,System.Xml.Linq.LoadOptions)">
      <summary>Creates a new <see cref="T:System.Xml.Linq.XDocument" /> instance by using the specified stream, optionally preserving white space, setting the base URI, and retaining line information.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XDocument" /> object that reads the data that is contained in the stream.</returns>
      <param name="stream">The stream containing the XML data.</param>
      <param name="options">A <see cref="T:System.Xml.Linq.LoadOptions" /> that specifies whether to load base URI and line information.</param>
    </member>
    <member name="M:System.Xml.Linq.XDocument.Load(System.IO.TextReader)">
      <summary>Creates a new <see cref="T:System.Xml.Linq.XDocument" /> from a <see cref="T:System.IO.TextReader" />. </summary>
      <returns>An <see cref="T:System.Xml.Linq.XDocument" /> that contains the contents of the specified <see cref="T:System.IO.TextReader" />.</returns>
      <param name="textReader">A <see cref="T:System.IO.TextReader" /> that contains the content for the <see cref="T:System.Xml.Linq.XDocument" />.</param>
    </member>
    <member name="M:System.Xml.Linq.XDocument.Load(System.IO.TextReader,System.Xml.Linq.LoadOptions)">
      <summary>Creates a new <see cref="T:System.Xml.Linq.XDocument" /> from a <see cref="T:System.IO.TextReader" />, optionally preserving white space, setting the base URI, and retaining line information.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XDocument" /> that contains the XML that was read from the specified <see cref="T:System.IO.TextReader" />.</returns>
      <param name="textReader">A <see cref="T:System.IO.TextReader" /> that contains the content for the <see cref="T:System.Xml.Linq.XDocument" />.</param>
      <param name="options">A <see cref="T:System.Xml.Linq.LoadOptions" /> that specifies white space behavior, and whether to load base URI and line information.</param>
    </member>
    <member name="M:System.Xml.Linq.XDocument.Load(System.String)">
      <summary>Creates a new <see cref="T:System.Xml.Linq.XDocument" /> from a file. </summary>
      <returns>An <see cref="T:System.Xml.Linq.XDocument" /> that contains the contents of the specified file.</returns>
      <param name="uri">A URI string that references the file to load into a new <see cref="T:System.Xml.Linq.XDocument" />.</param>
    </member>
    <member name="M:System.Xml.Linq.XDocument.Load(System.String,System.Xml.Linq.LoadOptions)">
      <summary>Creates a new <see cref="T:System.Xml.Linq.XDocument" /> from a file, optionally preserving white space, setting the base URI, and retaining line information.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XDocument" /> that contains the contents of the specified file.</returns>
      <param name="uri">A URI string that references the file to load into a new <see cref="T:System.Xml.Linq.XDocument" />.</param>
      <param name="options">A <see cref="T:System.Xml.Linq.LoadOptions" /> that specifies white space behavior, and whether to load base URI and line information.</param>
    </member>
    <member name="M:System.Xml.Linq.XDocument.Load(System.Xml.XmlReader)">
      <summary>Creates a new <see cref="T:System.Xml.Linq.XDocument" /> from an <see cref="T:System.Xml.XmlReader" />. </summary>
      <returns>An <see cref="T:System.Xml.Linq.XDocument" /> that contains the contents of the specified <see cref="T:System.Xml.XmlReader" />.</returns>
      <param name="reader">A <see cref="T:System.Xml.XmlReader" /> that contains the content for the <see cref="T:System.Xml.Linq.XDocument" />.</param>
    </member>
    <member name="M:System.Xml.Linq.XDocument.Load(System.Xml.XmlReader,System.Xml.Linq.LoadOptions)">
      <summary>Loads an <see cref="T:System.Xml.Linq.XDocument" /> from an <see cref="T:System.Xml.XmlReader" />, optionally setting the base URI, and retaining line information.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XDocument" /> that contains the XML that was read from the specified <see cref="T:System.Xml.XmlReader" />.</returns>
      <param name="reader">A <see cref="T:System.Xml.XmlReader" /> that will be read for the content of the <see cref="T:System.Xml.Linq.XDocument" />.</param>
      <param name="options">A <see cref="T:System.Xml.Linq.LoadOptions" /> that specifies whether to load base URI and line information.</param>
    </member>
    <member name="P:System.Xml.Linq.XDocument.NodeType">
      <summary>Gets the node type for this node.</summary>
      <returns>The node type. For <see cref="T:System.Xml.Linq.XDocument" /> objects, this value is <see cref="F:System.Xml.XmlNodeType.Document" />.</returns>
    </member>
    <member name="M:System.Xml.Linq.XDocument.Parse(System.String)">
      <summary>Creates a new <see cref="T:System.Xml.Linq.XDocument" /> from a string.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XDocument" /> populated from the string that contains XML.</returns>
      <param name="text">A string that contains XML.</param>
    </member>
    <member name="M:System.Xml.Linq.XDocument.Parse(System.String,System.Xml.Linq.LoadOptions)">
      <summary>Creates a new <see cref="T:System.Xml.Linq.XDocument" /> from a string, optionally preserving white space, setting the base URI, and retaining line information.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XDocument" /> populated from the string that contains XML.</returns>
      <param name="text">A string that contains XML.</param>
      <param name="options">A <see cref="T:System.Xml.Linq.LoadOptions" /> that specifies white space behavior, and whether to load base URI and line information.</param>
    </member>
    <member name="P:System.Xml.Linq.XDocument.Root">
      <summary>Gets the root element of the XML Tree for this document.</summary>
      <returns>The root <see cref="T:System.Xml.Linq.XElement" /> of the XML tree.</returns>
    </member>
    <member name="M:System.Xml.Linq.XDocument.Save(System.IO.Stream)">
      <summary>Outputs this <see cref="T:System.Xml.Linq.XDocument" /> to the specified <see cref="T:System.IO.Stream" />.</summary>
      <param name="stream">The stream to output this <see cref="T:System.Xml.Linq.XDocument" /> to.</param>
    </member>
    <member name="M:System.Xml.Linq.XDocument.Save(System.IO.Stream,System.Xml.Linq.SaveOptions)">
      <summary>Outputs this <see cref="T:System.Xml.Linq.XDocument" /> to the specified <see cref="T:System.IO.Stream" />, optionally specifying formatting behavior.</summary>
      <param name="stream">The stream to output this <see cref="T:System.Xml.Linq.XDocument" /> to.</param>
      <param name="options">A <see cref="T:System.Xml.Linq.SaveOptions" /> that specifies formatting behavior.</param>
    </member>
    <member name="M:System.Xml.Linq.XDocument.Save(System.IO.TextWriter)">
      <summary>Serialize this <see cref="T:System.Xml.Linq.XDocument" /> to a <see cref="T:System.IO.TextWriter" />.</summary>
      <param name="textWriter">A <see cref="T:System.IO.TextWriter" /> that the <see cref="T:System.Xml.Linq.XDocument" /> will be written to.</param>
    </member>
    <member name="M:System.Xml.Linq.XDocument.Save(System.IO.TextWriter,System.Xml.Linq.SaveOptions)">
      <summary>Serialize this <see cref="T:System.Xml.Linq.XDocument" /> to a <see cref="T:System.IO.TextWriter" />, optionally disabling formatting.</summary>
      <param name="textWriter">The <see cref="T:System.IO.TextWriter" /> to output the XML to.</param>
      <param name="options">A <see cref="T:System.Xml.Linq.SaveOptions" /> that specifies formatting behavior.</param>
    </member>
    <member name="M:System.Xml.Linq.XDocument.Save(System.Xml.XmlWriter)">
      <summary>Serialize this <see cref="T:System.Xml.Linq.XDocument" /> to an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">A <see cref="T:System.Xml.XmlWriter" /> that the <see cref="T:System.Xml.Linq.XDocument" /> will be written to.</param>
    </member>
    <member name="M:System.Xml.Linq.XDocument.WriteTo(System.Xml.XmlWriter)">
      <summary>Write this document to an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlWriter" /> into which this method will write.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Xml.Linq.XDocumentType">
      <summary>Represents an XML Document Type Definition (DTD). </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.XDocumentType.#ctor(System.String,System.String,System.String,System.String)">
      <summary>Initializes an instance of the <see cref="T:System.Xml.Linq.XDocumentType" /> class. </summary>
      <param name="name">A <see cref="T:System.String" /> that contains the qualified name of the DTD, which is the same as the qualified name of the root element of the XML document.</param>
      <param name="publicId">A <see cref="T:System.String" /> that contains the public identifier of an external public DTD.</param>
      <param name="systemId">A <see cref="T:System.String" /> that contains the system identifier of an external private DTD.</param>
      <param name="internalSubset">A <see cref="T:System.String" /> that contains the internal subset for an internal DTD.</param>
    </member>
    <member name="M:System.Xml.Linq.XDocumentType.#ctor(System.Xml.Linq.XDocumentType)">
      <summary>Initializes an instance of the <see cref="T:System.Xml.Linq.XDocumentType" /> class from another <see cref="T:System.Xml.Linq.XDocumentType" /> object.</summary>
      <param name="other">An <see cref="T:System.Xml.Linq.XDocumentType" /> object to copy from.</param>
    </member>
    <member name="P:System.Xml.Linq.XDocumentType.InternalSubset">
      <summary>Gets or sets the internal subset for this Document Type Definition (DTD).</summary>
      <returns>A <see cref="T:System.String" /> that contains the internal subset for this Document Type Definition (DTD).</returns>
    </member>
    <member name="P:System.Xml.Linq.XDocumentType.Name">
      <summary>Gets or sets the name for this Document Type Definition (DTD).</summary>
      <returns>A <see cref="T:System.String" /> that contains the name for this Document Type Definition (DTD).</returns>
    </member>
    <member name="P:System.Xml.Linq.XDocumentType.NodeType">
      <summary>Gets the node type for this node.</summary>
      <returns>The node type. For <see cref="T:System.Xml.Linq.XDocumentType" /> objects, this value is <see cref="F:System.Xml.XmlNodeType.DocumentType" />.</returns>
    </member>
    <member name="P:System.Xml.Linq.XDocumentType.PublicId">
      <summary>Gets or sets the public identifier for this Document Type Definition (DTD).</summary>
      <returns>A <see cref="T:System.String" /> that contains the public identifier for this Document Type Definition (DTD).</returns>
    </member>
    <member name="P:System.Xml.Linq.XDocumentType.SystemId">
      <summary>Gets or sets the system identifier for this Document Type Definition (DTD).</summary>
      <returns>A <see cref="T:System.String" /> that contains the system identifier for this Document Type Definition (DTD).</returns>
    </member>
    <member name="M:System.Xml.Linq.XDocumentType.WriteTo(System.Xml.XmlWriter)">
      <summary>Write this <see cref="T:System.Xml.Linq.XDocumentType" /> to an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlWriter" /> into which this method will write.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Xml.Linq.XElement">
      <summary>Represents an XML element.  See XElement Class Overview and the Remarks section on this page for usage information and examples.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
    </member>
    <member name="M:System.Xml.Linq.XElement.#ctor(System.Xml.Linq.XElement)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XElement" /> class from another <see cref="T:System.Xml.Linq.XElement" /> object.</summary>
      <param name="other">An <see cref="T:System.Xml.Linq.XElement" /> object to copy from.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.#ctor(System.Xml.Linq.XName)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XElement" /> class with the specified name. </summary>
      <param name="name">An <see cref="T:System.Xml.Linq.XName" /> that contains the name of the element.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.#ctor(System.Xml.Linq.XName,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XElement" /> class with the specified name and content.</summary>
      <param name="name">An <see cref="T:System.Xml.Linq.XName" /> that contains the element name.</param>
      <param name="content">The contents of the element.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.#ctor(System.Xml.Linq.XName,System.Object[])">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XElement" /> class with the specified name and content.</summary>
      <param name="name">An <see cref="T:System.Xml.Linq.XName" /> that contains the element name.</param>
      <param name="content">The initial content of the element.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.#ctor(System.Xml.Linq.XStreamingElement)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XElement" /> class from an <see cref="T:System.Xml.Linq.XStreamingElement" /> object.</summary>
      <param name="other">An <see cref="T:System.Xml.Linq.XStreamingElement" /> that contains unevaluated queries that will be iterated for the contents of this <see cref="T:System.Xml.Linq.XElement" />.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.AncestorsAndSelf">
      <summary>Returns a collection of elements that contain this element, and the ancestors of this element. </summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> of elements that contain this element, and the ancestors of this element. </returns>
    </member>
    <member name="M:System.Xml.Linq.XElement.AncestorsAndSelf(System.Xml.Linq.XName)">
      <summary>Returns a filtered collection of elements that contain this element, and the ancestors of this element. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contain this element, and the ancestors of this element. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</returns>
      <param name="name">The <see cref="T:System.Xml.Linq.XName" /> to match.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.Attribute(System.Xml.Linq.XName)">
      <summary>Returns the <see cref="T:System.Xml.Linq.XAttribute" /> of this <see cref="T:System.Xml.Linq.XElement" /> that has the specified <see cref="T:System.Xml.Linq.XName" />.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XAttribute" /> that has the specified <see cref="T:System.Xml.Linq.XName" />; null if there is no attribute with the specified name.</returns>
      <param name="name">The <see cref="T:System.Xml.Linq.XName" /> of the <see cref="T:System.Xml.Linq.XAttribute" /> to get.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.Attributes">
      <summary>Returns a collection of attributes of this element.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XAttribute" /> of attributes of this element.</returns>
    </member>
    <member name="M:System.Xml.Linq.XElement.Attributes(System.Xml.Linq.XName)">
      <summary>Returns a filtered collection of attributes of this element. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XAttribute" /> that contains the attributes of this element. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</returns>
      <param name="name">The <see cref="T:System.Xml.Linq.XName" /> to match.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.DescendantNodesAndSelf">
      <summary>Returns a collection of nodes that contain this element, and all descendant nodes of this element, in document order.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XNode" /> that contain this element, and all descendant nodes of this element, in document order.</returns>
    </member>
    <member name="M:System.Xml.Linq.XElement.DescendantsAndSelf">
      <summary>Returns a collection of elements that contain this element, and all descendant elements of this element, in document order.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> of elements that contain this element, and all descendant elements of this element, in document order.</returns>
    </member>
    <member name="M:System.Xml.Linq.XElement.DescendantsAndSelf(System.Xml.Linq.XName)">
      <summary>Returns a filtered collection of elements that contain this element, and all descendant elements of this element, in document order. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contain this element, and all descendant elements of this element, in document order. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</returns>
      <param name="name">The <see cref="T:System.Xml.Linq.XName" /> to match.</param>
    </member>
    <member name="P:System.Xml.Linq.XElement.EmptySequence">
      <summary>Gets an empty collection of elements.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> that contains an empty collection.</returns>
    </member>
    <member name="P:System.Xml.Linq.XElement.FirstAttribute">
      <summary>Gets the first attribute of this element.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XAttribute" /> that contains the first attribute of this element.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.XElement.GetDefaultNamespace">
      <summary>Gets the default <see cref="T:System.Xml.Linq.XNamespace" /> of this <see cref="T:System.Xml.Linq.XElement" />.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XNamespace" /> that contains the default namespace of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.XElement.GetNamespaceOfPrefix(System.String)">
      <summary>Gets the namespace associated with a particular prefix for this <see cref="T:System.Xml.Linq.XElement" />.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XNamespace" /> for the namespace associated with the prefix for this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="prefix">A string that contains the namespace prefix to look up.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.XElement.GetPrefixOfNamespace(System.Xml.Linq.XNamespace)">
      <summary>Gets the prefix associated with a namespace for this <see cref="T:System.Xml.Linq.XElement" />.</summary>
      <returns>A <see cref="T:System.String" /> that contains the namespace prefix.</returns>
      <param name="ns">An <see cref="T:System.Xml.Linq.XNamespace" /> to look up.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Xml.Linq.XElement.HasAttributes">
      <summary>Gets a value indicating whether this element as at least one attribute.</summary>
      <returns>true if this element has at least one attribute; otherwise false.</returns>
    </member>
    <member name="P:System.Xml.Linq.XElement.HasElements">
      <summary>Gets a value indicating whether this element has at least one child element.</summary>
      <returns>true if this element has at least one child element; otherwise false.</returns>
    </member>
    <member name="P:System.Xml.Linq.XElement.IsEmpty">
      <summary>Gets a value indicating whether this element contains no content.</summary>
      <returns>true if this element contains no content; otherwise false.</returns>
    </member>
    <member name="P:System.Xml.Linq.XElement.LastAttribute">
      <summary>Gets the last attribute of this element.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XAttribute" /> that contains the last attribute of this element.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.XElement.Load(System.IO.Stream)">
      <summary>Creates a new <see cref="T:System.Xml.Linq.XElement" /> instance by using the specified stream.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XElement" /> object used to read the data that is contained in the stream.</returns>
      <param name="stream">The stream that contains the XML data.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.Load(System.IO.Stream,System.Xml.Linq.LoadOptions)">
      <summary>Creates a new <see cref="T:System.Xml.Linq.XElement" /> instance by using the specified stream, optionally preserving white space, setting the base URI, and retaining line information.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XElement" /> object used to read the data that the stream contains.</returns>
      <param name="stream">The stream containing the XML data.</param>
      <param name="options">A <see cref="T:System.Xml.Linq.LoadOptions" /> object that specifies whether to load base URI and line information.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.Load(System.IO.TextReader)">
      <summary>Loads an <see cref="T:System.Xml.Linq.XElement" /> from a <see cref="T:System.IO.TextReader" />. </summary>
      <returns>An <see cref="T:System.Xml.Linq.XElement" /> that contains the XML that was read from the specified <see cref="T:System.IO.TextReader" />.</returns>
      <param name="textReader">A <see cref="T:System.IO.TextReader" /> that will be read for the <see cref="T:System.Xml.Linq.XElement" /> content.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.Load(System.IO.TextReader,System.Xml.Linq.LoadOptions)">
      <summary>Loads an <see cref="T:System.Xml.Linq.XElement" /> from a <see cref="T:System.IO.TextReader" />, optionally preserving white space and retaining line information. </summary>
      <returns>An <see cref="T:System.Xml.Linq.XElement" /> that contains the XML that was read from the specified <see cref="T:System.IO.TextReader" />.</returns>
      <param name="textReader">A <see cref="T:System.IO.TextReader" /> that will be read for the <see cref="T:System.Xml.Linq.XElement" /> content.</param>
      <param name="options">A <see cref="T:System.Xml.Linq.LoadOptions" /> that specifies white space behavior, and whether to load base URI and line information.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.Load(System.String)">
      <summary>Loads an <see cref="T:System.Xml.Linq.XElement" /> from a file.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XElement" /> that contains the contents of the specified file.</returns>
      <param name="uri">A URI string referencing the file to load into a new <see cref="T:System.Xml.Linq.XElement" />.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.Load(System.String,System.Xml.Linq.LoadOptions)">
      <summary>Loads an <see cref="T:System.Xml.Linq.XElement" /> from a file, optionally preserving white space, setting the base URI, and retaining line information.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XElement" /> that contains the contents of the specified file.</returns>
      <param name="uri">A URI string referencing the file to load into an <see cref="T:System.Xml.Linq.XElement" />.</param>
      <param name="options">A <see cref="T:System.Xml.Linq.LoadOptions" /> that specifies white space behavior, and whether to load base URI and line information.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.Load(System.Xml.XmlReader)">
      <summary>Loads an <see cref="T:System.Xml.Linq.XElement" /> from an <see cref="T:System.Xml.XmlReader" />. </summary>
      <returns>An <see cref="T:System.Xml.Linq.XElement" /> that contains the XML that was read from the specified <see cref="T:System.Xml.XmlReader" />.</returns>
      <param name="reader">A <see cref="T:System.Xml.XmlReader" /> that will be read for the content of the <see cref="T:System.Xml.Linq.XElement" />.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.Load(System.Xml.XmlReader,System.Xml.Linq.LoadOptions)">
      <summary>Loads an <see cref="T:System.Xml.Linq.XElement" /> from an <see cref="T:System.Xml.XmlReader" />, optionally preserving white space, setting the base URI, and retaining line information.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XElement" /> that contains the XML that was read from the specified <see cref="T:System.Xml.XmlReader" />.</returns>
      <param name="reader">A <see cref="T:System.Xml.XmlReader" /> that will be read for the content of the <see cref="T:System.Xml.Linq.XElement" />.</param>
      <param name="options">A <see cref="T:System.Xml.Linq.LoadOptions" /> that specifies white space behavior, and whether to load base URI and line information.</param>
    </member>
    <member name="P:System.Xml.Linq.XElement.Name">
      <summary>Gets or sets the name of this element.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XName" /> that contains the name of this element.</returns>
    </member>
    <member name="P:System.Xml.Linq.XElement.NodeType">
      <summary>Gets the node type for this node.</summary>
      <returns>The node type. For <see cref="T:System.Xml.Linq.XElement" /> objects, this value is <see cref="F:System.Xml.XmlNodeType.Element" />.</returns>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Nullable{System.UInt32}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.UInt32" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.UInt32" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.Nullable`1" /> of <see cref="T:System.UInt32" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.UInt32" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Nullable{System.UInt64}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.UInt64" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.UInt64" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.Nullable`1" /> of <see cref="T:System.UInt64" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.UInt64" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Nullable{System.Single}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Single" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.Single" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.Nullable`1" /> of <see cref="T:System.Single" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.Single" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Nullable{System.TimeSpan}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.TimeSpan" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.TimeSpan" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.Nullable`1" /> of <see cref="T:System.TimeSpan" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.TimeSpan" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Single">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.Single" />.</summary>
      <returns>A <see cref="T:System.Single" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.Single" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.Single" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="element" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.UInt32">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.UInt32" />.</summary>
      <returns>A <see cref="T:System.UInt32" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.UInt32" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.UInt32" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="element" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.UInt64">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.UInt64" />.</summary>
      <returns>A <see cref="T:System.UInt64" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.UInt64" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.UInt64" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="element" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.String">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.String" />.</summary>
      <returns>A <see cref="T:System.String" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.String" />.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.TimeSpan">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.TimeSpan" />.</summary>
      <returns>A <see cref="T:System.TimeSpan" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.TimeSpan" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.TimeSpan" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="element" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Boolean">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.Boolean" />.</summary>
      <returns>A <see cref="T:System.Boolean" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.Boolean" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.Boolean" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="element" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.DateTime">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.DateTime" />.</summary>
      <returns>A <see cref="T:System.DateTime" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.DateTime" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.DateTime" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="element" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Int64">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to an <see cref="T:System.Int64" />.</summary>
      <returns>A <see cref="T:System.Int64" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.Int64" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.Int64" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="element" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Int32">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to an <see cref="T:System.Int32" />.</summary>
      <returns>A <see cref="T:System.Int32" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.Int32" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.Int32" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="element" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Double">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.Double" />.</summary>
      <returns>A <see cref="T:System.Double" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.Double" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.Double" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="element" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Guid">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.Guid" />.</summary>
      <returns>A <see cref="T:System.Guid" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.Guid" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.Guid" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="element" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.DateTimeOffset">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XAttribute" /> to a <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>A <see cref="T:System.DateTimeOffset" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.DateTimeOffset" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.DateTimeOffset" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="element" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Decimal">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.Decimal" />.</summary>
      <returns>A <see cref="T:System.Decimal" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.Decimal" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.Decimal" /> value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="element" /> parameter is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Nullable{System.Guid}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Guid" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.Guid" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.Nullable`1" /> of <see cref="T:System.Guid" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.Guid" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Nullable{System.Int32}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Int32" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.Int32" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.Nullable`1" /> of <see cref="T:System.Int32" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.Int32" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Nullable{System.Double}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Double" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.Double" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.Nullable`1" /> of <see cref="T:System.Double" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.Double" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Nullable{System.DateTimeOffset}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.DateTimeOffset" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.DateTimeOffset" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to an <see cref="T:System.Nullable`1" /> of <see cref="T:System.DateTimeOffset" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.DateTimeOffset" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Nullable{System.Decimal}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Decimal" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.Decimal" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.Nullable`1" /> of <see cref="T:System.Decimal" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.Decimal" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Nullable{System.Int64}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Int64" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.Int64" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.Nullable`1" /> of <see cref="T:System.Int64" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.Int64" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Nullable{System.Boolean}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Boolean" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.Boolean" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.Nullable`1" /> of <see cref="T:System.Boolean" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.Boolean" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.op_Explicit(System.Xml.Linq.XElement)~System.Nullable{System.DateTime}">
      <summary>Cast the value of this <see cref="T:System.Xml.Linq.XElement" /> to a <see cref="T:System.Nullable`1" /> of <see cref="T:System.DateTime" />.</summary>
      <returns>A <see cref="T:System.Nullable`1" /> of <see cref="T:System.DateTime" /> that contains the content of this <see cref="T:System.Xml.Linq.XElement" />.</returns>
      <param name="element">The <see cref="T:System.Xml.Linq.XElement" /> to cast to <see cref="T:System.Nullable`1" /> of <see cref="T:System.DateTime" />.</param>
      <exception cref="T:System.FormatException">The element does not contain a valid <see cref="T:System.DateTime" /> value.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.Parse(System.String)">
      <summary>Load an <see cref="T:System.Xml.Linq.XElement" /> from a string that contains XML.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XElement" /> populated from the string that contains XML.</returns>
      <param name="text">A <see cref="T:System.String" /> that contains XML.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.Parse(System.String,System.Xml.Linq.LoadOptions)">
      <summary>Load an <see cref="T:System.Xml.Linq.XElement" /> from a string that contains XML, optionally preserving white space and retaining line information.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XElement" /> populated from the string that contains XML.</returns>
      <param name="text">A <see cref="T:System.String" /> that contains XML.</param>
      <param name="options">A <see cref="T:System.Xml.Linq.LoadOptions" /> that specifies white space behavior, and whether to load base URI and line information.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.RemoveAll">
      <summary>Removes nodes and attributes from this <see cref="T:System.Xml.Linq.XElement" />.</summary>
    </member>
    <member name="M:System.Xml.Linq.XElement.RemoveAttributes">
      <summary>Removes the attributes of this <see cref="T:System.Xml.Linq.XElement" />.</summary>
    </member>
    <member name="M:System.Xml.Linq.XElement.ReplaceAll(System.Object)">
      <summary>Replaces the child nodes and the attributes of this element with the specified content.</summary>
      <param name="content">The content that will replace the child nodes and attributes of this element.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.ReplaceAll(System.Object[])">
      <summary>Replaces the child nodes and the attributes of this element with the specified content.</summary>
      <param name="content">A parameter list of content objects.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.ReplaceAttributes(System.Object)">
      <summary>Replaces the attributes of this element with the specified content.</summary>
      <param name="content">The content that will replace the attributes of this element.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.ReplaceAttributes(System.Object[])">
      <summary>Replaces the attributes of this element with the specified content.</summary>
      <param name="content">A parameter list of content objects.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.Save(System.IO.Stream)">
      <summary>Outputs this <see cref="T:System.Xml.Linq.XElement" /> to the specified <see cref="T:System.IO.Stream" />.</summary>
      <param name="stream">The stream to output this <see cref="T:System.Xml.Linq.XElement" /> to.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.Save(System.IO.Stream,System.Xml.Linq.SaveOptions)">
      <summary>Outputs this <see cref="T:System.Xml.Linq.XElement" /> to the specified <see cref="T:System.IO.Stream" />, optionally specifying formatting behavior.</summary>
      <param name="stream">The stream to output this <see cref="T:System.Xml.Linq.XElement" /> to.</param>
      <param name="options">A <see cref="T:System.Xml.Linq.SaveOptions" /> object that specifies formatting behavior.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.Save(System.IO.TextWriter)">
      <summary>Serialize this element to a <see cref="T:System.IO.TextWriter" />.</summary>
      <param name="textWriter">A <see cref="T:System.IO.TextWriter" /> that the <see cref="T:System.Xml.Linq.XElement" /> will be written to.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.Save(System.IO.TextWriter,System.Xml.Linq.SaveOptions)">
      <summary>Serialize this element to a <see cref="T:System.IO.TextWriter" />, optionally disabling formatting.</summary>
      <param name="textWriter">The <see cref="T:System.IO.TextWriter" /> to output the XML to.</param>
      <param name="options">A <see cref="T:System.Xml.Linq.SaveOptions" /> that specifies formatting behavior.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.Save(System.Xml.XmlWriter)">
      <summary>Serialize this element to an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">A <see cref="T:System.Xml.XmlWriter" /> that the <see cref="T:System.Xml.Linq.XElement" /> will be written to.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.SetAttributeValue(System.Xml.Linq.XName,System.Object)">
      <summary>Sets the value of an attribute, adds an attribute, or removes an attribute. </summary>
      <param name="name">An <see cref="T:System.Xml.Linq.XName" /> that contains the name of the attribute to change.</param>
      <param name="value">The value to assign to the attribute. The attribute is removed if the value is null. Otherwise, the value is converted to its string representation and assigned to the <see cref="P:System.Xml.Linq.XAttribute.Value" /> property of the attribute.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="value" /> is an instance of <see cref="T:System.Xml.Linq.XObject" />.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.SetElementValue(System.Xml.Linq.XName,System.Object)">
      <summary>Sets the value of a child element, adds a child element, or removes a child element.</summary>
      <param name="name">An <see cref="T:System.Xml.Linq.XName" /> that contains the name of the child element to change.</param>
      <param name="value">The value to assign to the child element. The child element is removed if the value is null. Otherwise, the value is converted to its string representation and assigned to the <see cref="P:System.Xml.Linq.XElement.Value" /> property of the child element.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="value" /> is an instance of <see cref="T:System.Xml.Linq.XObject" />.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.SetValue(System.Object)">
      <summary>Sets the value of this element.</summary>
      <param name="value">The value to assign to this element. The value is converted to its string representation and assigned to the <see cref="P:System.Xml.Linq.XElement.Value" /> property.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="value" /> is an <see cref="T:System.Xml.Linq.XObject" />.</exception>
    </member>
    <member name="M:System.Xml.Linq.XElement.System#Xml#Serialization#IXmlSerializable#GetSchema">
      <summary>Gets an XML schema definition that describes the XML representation of this object.</summary>
      <returns>An <see cref="T:System.Xml.Schema.XmlSchema" /> that describes the XML representation of the object that is produced by the <see cref="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)" /> method and consumed by the <see cref="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)" /> method.</returns>
    </member>
    <member name="M:System.Xml.Linq.XElement.System#Xml#Serialization#IXmlSerializable#ReadXml(System.Xml.XmlReader)">
      <summary>Generates an object from its XML representation.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> from which the object is deserialized.</param>
    </member>
    <member name="M:System.Xml.Linq.XElement.System#Xml#Serialization#IXmlSerializable#WriteXml(System.Xml.XmlWriter)">
      <summary>Converts an object into its XML representation.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlWriter" /> to which this object is serialized.</param>
    </member>
    <member name="P:System.Xml.Linq.XElement.Value">
      <summary>Gets or sets the concatenated text contents of this element.</summary>
      <returns>A <see cref="T:System.String" /> that contains all of the text content of this element. If there are multiple text nodes, they will be concatenated.</returns>
    </member>
    <member name="M:System.Xml.Linq.XElement.WriteTo(System.Xml.XmlWriter)">
      <summary>Write this element to an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlWriter" /> into which this method will write.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Xml.Linq.XName">
      <summary>Represents a name of an XML element or attribute. </summary>
    </member>
    <member name="M:System.Xml.Linq.XName.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Xml.Linq.XName" /> is equal to this <see cref="T:System.Xml.Linq.XName" />.</summary>
      <returns>true if the specified <see cref="T:System.Xml.Linq.XName" /> is equal to the current <see cref="T:System.Xml.Linq.XName" />; otherwise false.</returns>
      <param name="obj">The <see cref="T:System.Xml.Linq.XName" /> to compare to the current <see cref="T:System.Xml.Linq.XName" />.</param>
    </member>
    <member name="M:System.Xml.Linq.XName.Get(System.String)">
      <summary>Gets an <see cref="T:System.Xml.Linq.XName" /> object from an expanded name.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XName" /> object constructed from the expanded name.</returns>
      <param name="expandedName">A <see cref="T:System.String" /> that contains an expanded XML name in the format {namespace}localname.</param>
    </member>
    <member name="M:System.Xml.Linq.XName.Get(System.String,System.String)">
      <summary>Gets an <see cref="T:System.Xml.Linq.XName" /> object from a local name and a namespace.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XName" /> object created from the specified local name and namespace.</returns>
      <param name="localName">A local (unqualified) name.</param>
      <param name="namespaceName">An XML namespace.</param>
    </member>
    <member name="M:System.Xml.Linq.XName.GetHashCode">
      <summary>Gets a hash code for this <see cref="T:System.Xml.Linq.XName" />.</summary>
      <returns>An <see cref="T:System.Int32" /> that contains the hash code for the <see cref="T:System.Xml.Linq.XName" />.</returns>
    </member>
    <member name="P:System.Xml.Linq.XName.LocalName">
      <summary>Gets the local (unqualified) part of the name.</summary>
      <returns>A <see cref="T:System.String" /> that contains the local (unqualified) part of the name.</returns>
    </member>
    <member name="P:System.Xml.Linq.XName.Namespace">
      <summary>Gets the namespace part of the fully qualified name.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XNamespace" /> that contains the namespace part of the name.</returns>
    </member>
    <member name="P:System.Xml.Linq.XName.NamespaceName">
      <summary>Returns the URI of the <see cref="T:System.Xml.Linq.XNamespace" /> for this <see cref="T:System.Xml.Linq.XName" />.</summary>
      <returns>The URI of the <see cref="T:System.Xml.Linq.XNamespace" /> for this <see cref="T:System.Xml.Linq.XName" />.</returns>
    </member>
    <member name="M:System.Xml.Linq.XName.op_Equality(System.Xml.Linq.XName,System.Xml.Linq.XName)">
      <summary>Returns a value indicating whether two instances of <see cref="T:System.Xml.Linq.XName" /> are equal.</summary>
      <returns>true if <paramref name="left" /> and <paramref name="right" /> are equal; otherwise false.</returns>
      <param name="left">The first <see cref="T:System.Xml.Linq.XName" /> to compare.</param>
      <param name="right">The second <see cref="T:System.Xml.Linq.XName" /> to compare.</param>
    </member>
    <member name="M:System.Xml.Linq.XName.op_Implicit(System.String)~System.Xml.Linq.XName">
      <summary>Converts a string formatted as an expanded XML name (that is,{namespace}localname) to an <see cref="T:System.Xml.Linq.XName" /> object.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XName" /> object constructed from the expanded name.</returns>
      <param name="expandedName">A string that contains an expanded XML name in the format {namespace}localname.</param>
    </member>
    <member name="M:System.Xml.Linq.XName.op_Inequality(System.Xml.Linq.XName,System.Xml.Linq.XName)">
      <summary>Returns a value indicating whether two instances of <see cref="T:System.Xml.Linq.XName" /> are not equal.</summary>
      <returns>true if <paramref name="left" /> and <paramref name="right" /> are not equal; otherwise false.</returns>
      <param name="left">The first <see cref="T:System.Xml.Linq.XName" /> to compare.</param>
      <param name="right">The second <see cref="T:System.Xml.Linq.XName" /> to compare.</param>
    </member>
    <member name="M:System.Xml.Linq.XName.System#IEquatable{T}#Equals(System.Xml.Linq.XName)">
      <summary>Indicates whether the current <see cref="T:System.Xml.Linq.XName" /> is equal to the specified <see cref="T:System.Xml.Linq.XName" />.</summary>
      <returns>true if this <see cref="T:System.Xml.Linq.XName" /> is equal to the specified <see cref="T:System.Xml.Linq.XName" />, otherwise false.</returns>
      <param name="other">The <see cref="T:System.Xml.Linq.XName" /> to compare with this <see cref="T:System.Xml.Linq.XName" />.</param>
    </member>
    <member name="M:System.Xml.Linq.XName.ToString">
      <summary>Returns the expanded XML name in the format {namespace}localname.</summary>
      <returns>A <see cref="T:System.String" /> that contains the expanded XML name in the format {namespace}localname.</returns>
    </member>
    <member name="T:System.Xml.Linq.XNamespace">
      <summary>Represents an XML namespace. This class cannot be inherited. </summary>
    </member>
    <member name="M:System.Xml.Linq.XNamespace.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Xml.Linq.XNamespace" /> is equal to the current <see cref="T:System.Xml.Linq.XNamespace" />.</summary>
      <returns>A <see cref="T:System.Boolean" /> that indicates whether the specified <see cref="T:System.Xml.Linq.XNamespace" /> is equal to the current <see cref="T:System.Xml.Linq.XNamespace" />.</returns>
      <param name="obj">The <see cref="T:System.Xml.Linq.XNamespace" /> to compare to the current <see cref="T:System.Xml.Linq.XNamespace" />.</param>
    </member>
    <member name="M:System.Xml.Linq.XNamespace.Get(System.String)">
      <summary>Gets an <see cref="T:System.Xml.Linq.XNamespace" /> for the specified Uniform Resource Identifier (URI).</summary>
      <returns>An <see cref="T:System.Xml.Linq.XNamespace" /> created from the specified URI.</returns>
      <param name="namespaceName">A <see cref="T:System.String" /> that contains a namespace URI.</param>
    </member>
    <member name="M:System.Xml.Linq.XNamespace.GetHashCode">
      <summary>Gets a hash code for this <see cref="T:System.Xml.Linq.XNamespace" />.</summary>
      <returns>An <see cref="T:System.Int32" /> that contains the hash code for the <see cref="T:System.Xml.Linq.XNamespace" />.</returns>
    </member>
    <member name="M:System.Xml.Linq.XNamespace.GetName(System.String)">
      <summary>Returns an <see cref="T:System.Xml.Linq.XName" /> object created from this <see cref="T:System.Xml.Linq.XNamespace" /> and the specified local name.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XName" /> created from this <see cref="T:System.Xml.Linq.XNamespace" /> and the specified local name.</returns>
      <param name="localName">A <see cref="T:System.String" /> that contains a local name.</param>
    </member>
    <member name="P:System.Xml.Linq.XNamespace.NamespaceName">
      <summary>Gets the Uniform Resource Identifier (URI) of this namespace.</summary>
      <returns>A <see cref="T:System.String" /> that contains the URI of the namespace.</returns>
    </member>
    <member name="P:System.Xml.Linq.XNamespace.None">
      <summary>Gets the <see cref="T:System.Xml.Linq.XNamespace" /> object that corresponds to no namespace.</summary>
      <returns>The <see cref="T:System.Xml.Linq.XNamespace" /> that corresponds to no namespace.</returns>
    </member>
    <member name="M:System.Xml.Linq.XNamespace.op_Addition(System.Xml.Linq.XNamespace,System.String)">
      <summary>Combines an <see cref="T:System.Xml.Linq.XNamespace" /> object with a local name to create an <see cref="T:System.Xml.Linq.XName" />.</summary>
      <returns>The new <see cref="T:System.Xml.Linq.XName" /> constructed from the namespace and local name.</returns>
      <param name="ns">An <see cref="T:System.Xml.Linq.XNamespace" /> that contains the namespace.</param>
      <param name="localName">A <see cref="T:System.String" /> that contains the local name.</param>
    </member>
    <member name="M:System.Xml.Linq.XNamespace.op_Equality(System.Xml.Linq.XNamespace,System.Xml.Linq.XNamespace)">
      <summary>Returns a value indicating whether two instances of <see cref="T:System.Xml.Linq.XNamespace" /> are equal.</summary>
      <returns>A <see cref="T:System.Boolean" /> that indicates whether <paramref name="left" /> and <paramref name="right" /> are equal.</returns>
      <param name="left">The first <see cref="T:System.Xml.Linq.XNamespace" /> to compare.</param>
      <param name="right">The second <see cref="T:System.Xml.Linq.XNamespace" /> to compare.</param>
    </member>
    <member name="M:System.Xml.Linq.XNamespace.op_Implicit(System.String)~System.Xml.Linq.XNamespace">
      <summary>Converts a string containing a Uniform Resource Identifier (URI) to an <see cref="T:System.Xml.Linq.XNamespace" />.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XNamespace" /> constructed from the URI string.</returns>
      <param name="namespaceName">A <see cref="T:System.String" /> that contains the namespace URI.</param>
    </member>
    <member name="M:System.Xml.Linq.XNamespace.op_Inequality(System.Xml.Linq.XNamespace,System.Xml.Linq.XNamespace)">
      <summary>Returns a value indicating whether two instances of <see cref="T:System.Xml.Linq.XNamespace" /> are not equal.</summary>
      <returns>A <see cref="T:System.Boolean" /> that indicates whether <paramref name="left" /> and <paramref name="right" /> are not equal.</returns>
      <param name="left">The first <see cref="T:System.Xml.Linq.XNamespace" /> to compare.</param>
      <param name="right">The second <see cref="T:System.Xml.Linq.XNamespace" /> to compare.</param>
    </member>
    <member name="M:System.Xml.Linq.XNamespace.ToString">
      <summary>Returns the URI of this <see cref="T:System.Xml.Linq.XNamespace" />.</summary>
      <returns>The URI of this <see cref="T:System.Xml.Linq.XNamespace" />.</returns>
    </member>
    <member name="P:System.Xml.Linq.XNamespace.Xml">
      <summary>Gets the <see cref="T:System.Xml.Linq.XNamespace" /> object that corresponds to the XML URI (http://www.w3.org/XML/1998/namespace).</summary>
      <returns>The <see cref="T:System.Xml.Linq.XNamespace" /> that corresponds to the XML URI (http://www.w3.org/XML/1998/namespace).</returns>
    </member>
    <member name="P:System.Xml.Linq.XNamespace.Xmlns">
      <summary>Gets the <see cref="T:System.Xml.Linq.XNamespace" /> object that corresponds to the xmlns URI (http://www.w3.org/2000/xmlns/).</summary>
      <returns>The <see cref="T:System.Xml.Linq.XNamespace" /> that corresponds to the xmlns URI (http://www.w3.org/2000/xmlns/).</returns>
    </member>
    <member name="T:System.Xml.Linq.XNode">
      <summary>Represents the abstract concept of a node (element, comment, document type, processing instruction, or text node) in the XML tree.  </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.XNode.AddAfterSelf(System.Object)">
      <summary>Adds the specified content immediately after this node.</summary>
      <param name="content">A content object that contains simple content or a collection of content objects to be added after this node.</param>
      <exception cref="T:System.InvalidOperationException">The parent is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XNode.AddAfterSelf(System.Object[])">
      <summary>Adds the specified content immediately after this node.</summary>
      <param name="content">A parameter list of content objects.</param>
      <exception cref="T:System.InvalidOperationException">The parent is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XNode.AddBeforeSelf(System.Object)">
      <summary>Adds the specified content immediately before this node.</summary>
      <param name="content">A content object that contains simple content or a collection of content objects to be added before this node.</param>
      <exception cref="T:System.InvalidOperationException">The parent is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XNode.AddBeforeSelf(System.Object[])">
      <summary>Adds the specified content immediately before this node.</summary>
      <param name="content">A parameter list of content objects.</param>
      <exception cref="T:System.InvalidOperationException">The parent is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XNode.Ancestors">
      <summary>Returns a collection of the ancestor elements of this node.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> of the ancestor elements of this node.</returns>
    </member>
    <member name="M:System.Xml.Linq.XNode.Ancestors(System.Xml.Linq.XName)">
      <summary>Returns a filtered collection of the ancestor elements of this node. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> of the ancestor elements of this node. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.The nodes in the returned collection are in reverse document order.This method uses deferred execution.</returns>
      <param name="name">The <see cref="T:System.Xml.Linq.XName" /> to match.</param>
    </member>
    <member name="M:System.Xml.Linq.XNode.CompareDocumentOrder(System.Xml.Linq.XNode,System.Xml.Linq.XNode)">
      <summary>Compares two nodes to determine their relative XML document order.</summary>
      <returns>An int containing 0 if the nodes are equal; -1 if <paramref name="n1" /> is before <paramref name="n2" />; 1 if <paramref name="n1" /> is after <paramref name="n2" />.</returns>
      <param name="n1">First <see cref="T:System.Xml.Linq.XNode" /> to compare.</param>
      <param name="n2">Second <see cref="T:System.Xml.Linq.XNode" /> to compare.</param>
      <exception cref="T:System.InvalidOperationException">The two nodes do not share a common ancestor.</exception>
    </member>
    <member name="M:System.Xml.Linq.XNode.CreateReader">
      <summary>Creates an <see cref="T:System.Xml.XmlReader" /> for this node.</summary>
      <returns>An <see cref="T:System.Xml.XmlReader" /> that can be used to read this node and its descendants.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.XNode.CreateReader(System.Xml.Linq.ReaderOptions)">
      <summary>Creates an <see cref="T:System.Xml.XmlReader" /> with the options specified by the <paramref name="readerOptions" /> parameter.</summary>
      <returns>An <see cref="T:System.Xml.XmlReader" /> object.</returns>
      <param name="readerOptions">A <see cref="T:System.Xml.Linq.ReaderOptions" /> object that specifies whether to omit duplicate namespaces.</param>
    </member>
    <member name="M:System.Xml.Linq.XNode.DeepEquals(System.Xml.Linq.XNode,System.Xml.Linq.XNode)">
      <summary>Compares the values of two nodes, including the values of all descendant nodes.</summary>
      <returns>true if the nodes are equal; otherwise false.</returns>
      <param name="n1">The first <see cref="T:System.Xml.Linq.XNode" /> to compare.</param>
      <param name="n2">The second <see cref="T:System.Xml.Linq.XNode" /> to compare.</param>
    </member>
    <member name="P:System.Xml.Linq.XNode.DocumentOrderComparer">
      <summary>Gets a comparer that can compare the relative position of two nodes.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XNodeDocumentOrderComparer" /> that can compare the relative position of two nodes.</returns>
    </member>
    <member name="M:System.Xml.Linq.XNode.ElementsAfterSelf">
      <summary>Returns a collection of the sibling elements after this node, in document order.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> of the sibling elements after this node, in document order.</returns>
    </member>
    <member name="M:System.Xml.Linq.XNode.ElementsAfterSelf(System.Xml.Linq.XName)">
      <summary>Returns a filtered collection of the sibling elements after this node, in document order. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> of the sibling elements after this node, in document order. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</returns>
      <param name="name">The <see cref="T:System.Xml.Linq.XName" /> to match.</param>
    </member>
    <member name="M:System.Xml.Linq.XNode.ElementsBeforeSelf">
      <summary>Returns a collection of the sibling elements before this node, in document order.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> of the sibling elements before this node, in document order.</returns>
    </member>
    <member name="M:System.Xml.Linq.XNode.ElementsBeforeSelf(System.Xml.Linq.XName)">
      <summary>Returns a filtered collection of the sibling elements before this node, in document order. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XElement" /> of the sibling elements before this node, in document order. Only elements that have a matching <see cref="T:System.Xml.Linq.XName" /> are included in the collection.</returns>
      <param name="name">The <see cref="T:System.Xml.Linq.XName" /> to match.</param>
    </member>
    <member name="P:System.Xml.Linq.XNode.EqualityComparer">
      <summary>Gets a comparer that can compare two nodes for value equality.</summary>
      <returns>A <see cref="T:System.Xml.Linq.XNodeEqualityComparer" /> that can compare two nodes for value equality.</returns>
    </member>
    <member name="M:System.Xml.Linq.XNode.IsAfter(System.Xml.Linq.XNode)">
      <summary>Determines if the current node appears after a specified node in terms of document order.</summary>
      <returns>true if this node appears after the specified node; otherwise false.</returns>
      <param name="node">The <see cref="T:System.Xml.Linq.XNode" /> to compare for document order.</param>
    </member>
    <member name="M:System.Xml.Linq.XNode.IsBefore(System.Xml.Linq.XNode)">
      <summary>Determines if the current node appears before a specified node in terms of document order.</summary>
      <returns>true if this node appears before the specified node; otherwise false.</returns>
      <param name="node">The <see cref="T:System.Xml.Linq.XNode" /> to compare for document order.</param>
    </member>
    <member name="P:System.Xml.Linq.XNode.NextNode">
      <summary>Gets the next sibling node of this node.</summary>
      <returns>The <see cref="T:System.Xml.Linq.XNode" /> that contains the next sibling node.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.XNode.NodesAfterSelf">
      <summary>Returns a collection of the sibling nodes after this node, in document order.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XNode" /> of the sibling nodes after this node, in document order.</returns>
    </member>
    <member name="M:System.Xml.Linq.XNode.NodesBeforeSelf">
      <summary>Returns a collection of the sibling nodes before this node, in document order.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Xml.Linq.XNode" /> of the sibling nodes before this node, in document order.</returns>
    </member>
    <member name="P:System.Xml.Linq.XNode.PreviousNode">
      <summary>Gets the previous sibling node of this node.</summary>
      <returns>The <see cref="T:System.Xml.Linq.XNode" /> that contains the previous sibling node.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.XNode.ReadFrom(System.Xml.XmlReader)">
      <summary>Creates an <see cref="T:System.Xml.Linq.XNode" /> from an <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XNode" /> that contains the node and its descendant nodes that were read from the reader. The runtime type of the node is determined by the node type (<see cref="P:System.Xml.Linq.XObject.NodeType" />) of the first node encountered in the reader.</returns>
      <param name="reader">An <see cref="T:System.Xml.XmlReader" /> positioned at the node to read into this <see cref="T:System.Xml.Linq.XNode" />.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on a recognized node type.</exception>
      <exception cref="T:System.Xml.XmlException">The underlying <see cref="T:System.Xml.XmlReader" /> throws an exception.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.XNode.Remove">
      <summary>Removes this node from its parent.</summary>
      <exception cref="T:System.InvalidOperationException">The parent is null.</exception>
    </member>
    <member name="M:System.Xml.Linq.XNode.ReplaceWith(System.Object)">
      <summary>Replaces this node with the specified content.</summary>
      <param name="content">Content that replaces this node.</param>
    </member>
    <member name="M:System.Xml.Linq.XNode.ReplaceWith(System.Object[])">
      <summary>Replaces this node with the specified content.</summary>
      <param name="content">A parameter list of the new content.</param>
    </member>
    <member name="M:System.Xml.Linq.XNode.ToString">
      <summary>Returns the indented XML for this node.</summary>
      <returns>A <see cref="T:System.String" /> containing the indented XML.</returns>
    </member>
    <member name="M:System.Xml.Linq.XNode.ToString(System.Xml.Linq.SaveOptions)">
      <summary>Returns the XML for this node, optionally disabling formatting.</summary>
      <returns>A <see cref="T:System.String" /> containing the XML.</returns>
      <param name="options">A <see cref="T:System.Xml.Linq.SaveOptions" /> that specifies formatting behavior.</param>
    </member>
    <member name="M:System.Xml.Linq.XNode.WriteTo(System.Xml.XmlWriter)">
      <summary>Writes this node to an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlWriter" /> into which this method will write.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Xml.Linq.XNodeDocumentOrderComparer">
      <summary>Contains functionality to compare nodes for their document order. This class cannot be inherited. </summary>
    </member>
    <member name="M:System.Xml.Linq.XNodeDocumentOrderComparer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XNodeDocumentOrderComparer" /> class. </summary>
    </member>
    <member name="M:System.Xml.Linq.XNodeDocumentOrderComparer.Compare(System.Xml.Linq.XNode,System.Xml.Linq.XNode)">
      <summary>Compares two nodes to determine their relative document order.</summary>
      <returns>An <see cref="T:System.Int32" /> that contains 0 if the nodes are equal; -1 if <paramref name="x" /> is before <paramref name="y" />; 1 if <paramref name="x" /> is after <paramref name="y" />.</returns>
      <param name="x">The first <see cref="T:System.Xml.Linq.XNode" /> to compare.</param>
      <param name="y">The second <see cref="T:System.Xml.Linq.XNode" /> to compare.</param>
      <exception cref="T:System.InvalidOperationException">The two nodes do not share a common ancestor.</exception>
    </member>
    <member name="M:System.Xml.Linq.XNodeDocumentOrderComparer.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>Compares two nodes to determine their relative document order.</summary>
      <returns>An <see cref="T:System.Int32" /> that contains 0 if the nodes are equal; -1 if <paramref name="x" /> is before <paramref name="y" />; 1 if <paramref name="x" /> is after <paramref name="y" />.</returns>
      <param name="x">The first <see cref="T:System.Xml.Linq.XNode" /> to compare.</param>
      <param name="y">The second <see cref="T:System.Xml.Linq.XNode" /> to compare.</param>
      <exception cref="T:System.InvalidOperationException">The two nodes do not share a common ancestor.</exception>
      <exception cref="T:System.ArgumentException">The two nodes are not derived from <see cref="T:System.Xml.Linq.XNode" />.</exception>
    </member>
    <member name="T:System.Xml.Linq.XNodeEqualityComparer">
      <summary>Compares nodes to determine whether they are equal. This class cannot be inherited. </summary>
    </member>
    <member name="M:System.Xml.Linq.XNodeEqualityComparer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XNodeEqualityComparer" /> class. </summary>
    </member>
    <member name="M:System.Xml.Linq.XNodeEqualityComparer.Equals(System.Xml.Linq.XNode,System.Xml.Linq.XNode)">
      <summary>Compares the values of two nodes.</summary>
      <returns>A <see cref="T:System.Boolean" /> indicating if the nodes are equal.</returns>
      <param name="x">The first <see cref="T:System.Xml.Linq.XNode" /> to compare.</param>
      <param name="y">The second <see cref="T:System.Xml.Linq.XNode" /> to compare.</param>
    </member>
    <member name="M:System.Xml.Linq.XNodeEqualityComparer.GetHashCode(System.Xml.Linq.XNode)">
      <summary>Returns a hash code based on an <see cref="T:System.Xml.Linq.XNode" />.</summary>
      <returns>A <see cref="T:System.Int32" /> that contains a value-based hash code for the node.</returns>
      <param name="obj">The <see cref="T:System.Xml.Linq.XNode" /> to hash.</param>
    </member>
    <member name="M:System.Xml.Linq.XNodeEqualityComparer.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>Compares the values of two nodes.</summary>
      <returns>true if the nodes are equal; otherwise false.</returns>
      <param name="x">The first <see cref="T:System.Xml.Linq.XNode" /> to compare.</param>
      <param name="y">The second <see cref="T:System.Xml.Linq.XNode" /> to compare.</param>
    </member>
    <member name="M:System.Xml.Linq.XNodeEqualityComparer.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>Returns a hash code based on the value of a node.</summary>
      <returns>A <see cref="T:System.Int32" /> that contains a value-based hash code for the node.</returns>
      <param name="obj">The node to hash.</param>
    </member>
    <member name="T:System.Xml.Linq.XObject">
      <summary>Represents a node or an attribute in an XML tree. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.XObject.AddAnnotation(System.Object)">
      <summary>Adds an object to the annotation list of this <see cref="T:System.Xml.Linq.XObject" />.</summary>
      <param name="annotation">An <see cref="T:System.Object" /> that contains the annotation to add.</param>
    </member>
    <member name="M:System.Xml.Linq.XObject.Annotation``1">
      <summary>Get the first annotation object of the specified type from this <see cref="T:System.Xml.Linq.XObject" />. </summary>
      <returns>The first annotation object that matches the specified type, or null if no annotation is of the specified type.</returns>
      <typeparam name="T">The type of the annotation to retrieve.</typeparam>
    </member>
    <member name="M:System.Xml.Linq.XObject.Annotation(System.Type)">
      <summary>Gets the first annotation object of the specified type from this <see cref="T:System.Xml.Linq.XObject" />.</summary>
      <returns>The <see cref="T:System.Object" /> that contains the first annotation object that matches the specified type, or null if no annotation is of the specified type.</returns>
      <param name="type">The <see cref="T:System.Type" /> of the annotation to retrieve.</param>
    </member>
    <member name="M:System.Xml.Linq.XObject.Annotations``1">
      <summary>Gets a collection of annotations of the specified type for this <see cref="T:System.Xml.Linq.XObject" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> that contains the annotations for this <see cref="T:System.Xml.Linq.XObject" />.</returns>
      <typeparam name="T">The type of the annotations to retrieve.</typeparam>
    </member>
    <member name="M:System.Xml.Linq.XObject.Annotations(System.Type)">
      <summary>Gets a collection of annotations of the specified type for this <see cref="T:System.Xml.Linq.XObject" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Object" /> that contains the annotations that match the specified type for this <see cref="T:System.Xml.Linq.XObject" />.</returns>
      <param name="type">The <see cref="T:System.Type" /> of the annotations to retrieve.</param>
    </member>
    <member name="P:System.Xml.Linq.XObject.BaseUri">
      <summary>Gets the base URI for this <see cref="T:System.Xml.Linq.XObject" />.</summary>
      <returns>A <see cref="T:System.String" /> that contains the base URI for this <see cref="T:System.Xml.Linq.XObject" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="E:System.Xml.Linq.XObject.Changed">
      <summary>Raised when this <see cref="T:System.Xml.Linq.XObject" /> or any of its descendants have changed.</summary>
    </member>
    <member name="E:System.Xml.Linq.XObject.Changing">
      <summary>Raised when this <see cref="T:System.Xml.Linq.XObject" /> or any of its descendants are about to change.</summary>
    </member>
    <member name="P:System.Xml.Linq.XObject.Document">
      <summary>Gets the <see cref="T:System.Xml.Linq.XDocument" /> for this <see cref="T:System.Xml.Linq.XObject" />.</summary>
      <returns>The <see cref="T:System.Xml.Linq.XDocument" /> for this <see cref="T:System.Xml.Linq.XObject" />. </returns>
    </member>
    <member name="P:System.Xml.Linq.XObject.NodeType">
      <summary>Gets the node type for this <see cref="T:System.Xml.Linq.XObject" />.</summary>
      <returns>The node type for this <see cref="T:System.Xml.Linq.XObject" />. </returns>
    </member>
    <member name="P:System.Xml.Linq.XObject.Parent">
      <summary>Gets the parent <see cref="T:System.Xml.Linq.XElement" /> of this <see cref="T:System.Xml.Linq.XObject" />.</summary>
      <returns>The parent <see cref="T:System.Xml.Linq.XElement" /> of this <see cref="T:System.Xml.Linq.XObject" />.</returns>
    </member>
    <member name="M:System.Xml.Linq.XObject.RemoveAnnotations``1">
      <summary>Removes the annotations of the specified type from this <see cref="T:System.Xml.Linq.XObject" />.</summary>
      <typeparam name="T">The type of annotations to remove.</typeparam>
    </member>
    <member name="M:System.Xml.Linq.XObject.RemoveAnnotations(System.Type)">
      <summary>Removes the annotations of the specified type from this <see cref="T:System.Xml.Linq.XObject" />.</summary>
      <param name="type">The <see cref="T:System.Type" /> of annotations to remove.</param>
    </member>
    <member name="M:System.Xml.Linq.XObject.System#Xml#IXmlLineInfo#HasLineInfo">
      <summary>Gets a value indicating whether or not this <see cref="T:System.Xml.Linq.XObject" /> has line information.</summary>
      <returns>true if the <see cref="T:System.Xml.Linq.XObject" /> has line information, otherwise false.</returns>
    </member>
    <member name="P:System.Xml.Linq.XObject.System#Xml#IXmlLineInfo#LineNumber">
      <summary>Gets the line number that the underlying <see cref="T:System.Xml.XmlReader" /> reported for this <see cref="T:System.Xml.Linq.XObject" />.</summary>
      <returns>An <see cref="T:System.Int32" /> that contains the line number reported by the <see cref="T:System.Xml.XmlReader" /> for this <see cref="T:System.Xml.Linq.XObject" />.</returns>
    </member>
    <member name="P:System.Xml.Linq.XObject.System#Xml#IXmlLineInfo#LinePosition">
      <summary>Gets the line position that the underlying <see cref="T:System.Xml.XmlReader" /> reported for this <see cref="T:System.Xml.Linq.XObject" />.</summary>
      <returns>An <see cref="T:System.Int32" /> that contains the line position reported by the <see cref="T:System.Xml.XmlReader" /> for this <see cref="T:System.Xml.Linq.XObject" />.</returns>
    </member>
    <member name="T:System.Xml.Linq.XObjectChange">
      <summary>Specifies the event type when an event is raised for an <see cref="T:System.Xml.Linq.XObject" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Xml.Linq.XObjectChange.Add">
      <summary>An <see cref="T:System.Xml.Linq.XObject" /> has been or will be added to an <see cref="T:System.Xml.Linq.XContainer" />.</summary>
    </member>
    <member name="F:System.Xml.Linq.XObjectChange.Name">
      <summary>An <see cref="T:System.Xml.Linq.XObject" /> has been or will be renamed.</summary>
    </member>
    <member name="F:System.Xml.Linq.XObjectChange.Remove">
      <summary>An <see cref="T:System.Xml.Linq.XObject" /> has been or will be removed from an <see cref="T:System.Xml.Linq.XContainer" />.</summary>
    </member>
    <member name="F:System.Xml.Linq.XObjectChange.Value">
      <summary>The value of an <see cref="T:System.Xml.Linq.XObject" /> has been or will be changed. In addition, a change in the serialization of an empty element (either from an empty tag to start/end tag pair or vice versa) raises this event.</summary>
    </member>
    <member name="T:System.Xml.Linq.XObjectChangeEventArgs">
      <summary>Provides data for the <see cref="E:System.Xml.Linq.XObject.Changing" /> and <see cref="E:System.Xml.Linq.XObject.Changed" /> events.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.XObjectChangeEventArgs.#ctor(System.Xml.Linq.XObjectChange)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XObjectChangeEventArgs" /> class. </summary>
      <param name="objectChange">An <see cref="T:System.Xml.Linq.XObjectChange" /> that contains the event arguments for LINQ to XML events.</param>
    </member>
    <member name="F:System.Xml.Linq.XObjectChangeEventArgs.Add">
      <summary>Event argument for an <see cref="F:System.Xml.Linq.XObjectChange.Add" /> change event.</summary>
    </member>
    <member name="F:System.Xml.Linq.XObjectChangeEventArgs.Name">
      <summary>Event argument for a <see cref="F:System.Xml.Linq.XObjectChange.Name" /> change event.</summary>
    </member>
    <member name="P:System.Xml.Linq.XObjectChangeEventArgs.ObjectChange">
      <summary>Gets the type of change.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XObjectChange" /> that contains the type of change.</returns>
    </member>
    <member name="F:System.Xml.Linq.XObjectChangeEventArgs.Remove">
      <summary>Event argument for a <see cref="F:System.Xml.Linq.XObjectChange.Remove" /> change event.</summary>
    </member>
    <member name="F:System.Xml.Linq.XObjectChangeEventArgs.Value">
      <summary>Event argument for a <see cref="F:System.Xml.Linq.XObjectChange.Value" /> change event.</summary>
    </member>
    <member name="T:System.Xml.Linq.XProcessingInstruction">
      <summary>Represents an XML processing instruction. </summary>
    </member>
    <member name="M:System.Xml.Linq.XProcessingInstruction.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XProcessingInstruction" /> class. </summary>
      <param name="target">A <see cref="T:System.String" /> containing the target application for this <see cref="T:System.Xml.Linq.XProcessingInstruction" />.</param>
      <param name="data">The string data for this <see cref="T:System.Xml.Linq.XProcessingInstruction" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="target" /> or <paramref name="data" /> parameter is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="target" /> does not follow the constraints of an XML name.</exception>
    </member>
    <member name="M:System.Xml.Linq.XProcessingInstruction.#ctor(System.Xml.Linq.XProcessingInstruction)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XProcessingInstruction" /> class. </summary>
      <param name="other">The <see cref="T:System.Xml.Linq.XProcessingInstruction" /> node to copy from.</param>
    </member>
    <member name="P:System.Xml.Linq.XProcessingInstruction.Data">
      <summary>Gets or sets the string value of this processing instruction.</summary>
      <returns>A <see cref="T:System.String" /> that contains the string value of this processing instruction.</returns>
      <exception cref="T:System.ArgumentNullException">The string <paramref name="value" /> is null.</exception>
    </member>
    <member name="P:System.Xml.Linq.XProcessingInstruction.NodeType">
      <summary>Gets the node type for this node.</summary>
      <returns>The node type. For <see cref="T:System.Xml.Linq.XProcessingInstruction" /> objects, this value is <see cref="F:System.Xml.XmlNodeType.ProcessingInstruction" />.</returns>
    </member>
    <member name="P:System.Xml.Linq.XProcessingInstruction.Target">
      <summary>Gets or sets a string containing the target application for this processing instruction.</summary>
      <returns>A <see cref="T:System.String" /> containing the target application for this processing instruction.</returns>
      <exception cref="T:System.ArgumentNullException">The string <paramref name="value" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="target" /> does not follow the constraints of an XML name.</exception>
    </member>
    <member name="M:System.Xml.Linq.XProcessingInstruction.WriteTo(System.Xml.XmlWriter)">
      <summary>Writes this processing instruction to an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlWriter" /> to write this processing instruction to.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Xml.Linq.XStreamingElement">
      <summary>Represents elements in an XML tree that supports deferred streaming output.</summary>
    </member>
    <member name="M:System.Xml.Linq.XStreamingElement.#ctor(System.Xml.Linq.XName)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XElement" /> class from the specified <see cref="T:System.Xml.Linq.XName" />.</summary>
      <param name="name">An <see cref="T:System.Xml.Linq.XName" /> that contains the name of the element.</param>
    </member>
    <member name="M:System.Xml.Linq.XStreamingElement.#ctor(System.Xml.Linq.XName,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XStreamingElement" /> class with the specified name and content.</summary>
      <param name="name">An <see cref="T:System.Xml.Linq.XName" /> that contains the element name.</param>
      <param name="content">The contents of the element.</param>
    </member>
    <member name="M:System.Xml.Linq.XStreamingElement.#ctor(System.Xml.Linq.XName,System.Object[])">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XStreamingElement" /> class with the specified name and content.</summary>
      <param name="name">An <see cref="T:System.Xml.Linq.XName" /> that contains the element name.</param>
      <param name="content">The contents of the element.</param>
    </member>
    <member name="M:System.Xml.Linq.XStreamingElement.Add(System.Object)">
      <summary>Adds the specified content as children to this <see cref="T:System.Xml.Linq.XStreamingElement" />.</summary>
      <param name="content">Content to be added to the streaming element.</param>
    </member>
    <member name="M:System.Xml.Linq.XStreamingElement.Add(System.Object[])">
      <summary>Adds the specified content as children to this <see cref="T:System.Xml.Linq.XStreamingElement" />.</summary>
      <param name="content">Content to be added to the streaming element.</param>
    </member>
    <member name="P:System.Xml.Linq.XStreamingElement.Name">
      <summary>Gets or sets the name of this streaming element.</summary>
      <returns>An <see cref="T:System.Xml.Linq.XName" /> that contains the name of this streaming element.</returns>
    </member>
    <member name="M:System.Xml.Linq.XStreamingElement.Save(System.IO.Stream)">
      <summary>Outputs this <see cref="T:System.Xml.Linq.XStreamingElement" /> to the specified <see cref="T:System.IO.Stream" />.</summary>
      <param name="stream">The stream to output this <see cref="T:System.Xml.Linq.XDocument" /> to.</param>
    </member>
    <member name="M:System.Xml.Linq.XStreamingElement.Save(System.IO.Stream,System.Xml.Linq.SaveOptions)">
      <summary>Outputs this <see cref="T:System.Xml.Linq.XStreamingElement" /> to the specified <see cref="T:System.IO.Stream" />, optionally specifying formatting behavior.</summary>
      <param name="stream">The stream to output this <see cref="T:System.Xml.Linq.XDocument" /> to.</param>
      <param name="options">A <see cref="T:System.Xml.Linq.SaveOptions" /> object that specifies formatting behavior.</param>
    </member>
    <member name="M:System.Xml.Linq.XStreamingElement.Save(System.IO.TextWriter)">
      <summary>Serialize this streaming element to a <see cref="T:System.IO.TextWriter" />.</summary>
      <param name="textWriter">A <see cref="T:System.IO.TextWriter" /> that the <see cref="T:System.Xml.Linq.XStreamingElement" /> will be written to.</param>
    </member>
    <member name="M:System.Xml.Linq.XStreamingElement.Save(System.IO.TextWriter,System.Xml.Linq.SaveOptions)">
      <summary>Serialize this streaming element to a <see cref="T:System.IO.TextWriter" />, optionally disabling formatting.</summary>
      <param name="textWriter">The <see cref="T:System.IO.TextWriter" /> to output the XML to.</param>
      <param name="options">A <see cref="T:System.Xml.Linq.SaveOptions" /> that specifies formatting behavior.</param>
    </member>
    <member name="M:System.Xml.Linq.XStreamingElement.Save(System.Xml.XmlWriter)">
      <summary>Serialize this streaming element to an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">A <see cref="T:System.Xml.XmlWriter" /> that the <see cref="T:System.Xml.Linq.XElement" /> will be written to.</param>
    </member>
    <member name="M:System.Xml.Linq.XStreamingElement.ToString">
      <summary>Returns the formatted (indented) XML for this streaming element.</summary>
      <returns>A <see cref="T:System.String" /> containing the indented XML.</returns>
    </member>
    <member name="M:System.Xml.Linq.XStreamingElement.ToString(System.Xml.Linq.SaveOptions)">
      <summary>Returns the XML for this streaming element, optionally disabling formatting.</summary>
      <returns>A <see cref="T:System.String" /> containing the XML.</returns>
      <param name="options">A <see cref="T:System.Xml.Linq.SaveOptions" /> that specifies formatting behavior.</param>
    </member>
    <member name="M:System.Xml.Linq.XStreamingElement.WriteTo(System.Xml.XmlWriter)">
      <summary>Writes this streaming element to an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlWriter" /> into which this method will write.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Xml.Linq.XText">
      <summary>Represents a text node. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Xml.Linq.XText.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XText" /> class. </summary>
      <param name="value">The <see cref="T:System.String" /> that contains the value of the <see cref="T:System.Xml.Linq.XText" /> node.</param>
    </member>
    <member name="M:System.Xml.Linq.XText.#ctor(System.Xml.Linq.XText)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Linq.XText" /> class from another <see cref="T:System.Xml.Linq.XText" /> object.</summary>
      <param name="other">The <see cref="T:System.Xml.Linq.XText" /> node to copy from.</param>
    </member>
    <member name="P:System.Xml.Linq.XText.NodeType">
      <summary>Gets the node type for this node.</summary>
      <returns>The node type. For <see cref="T:System.Xml.Linq.XText" /> objects, this value is <see cref="F:System.Xml.XmlNodeType.Text" />.</returns>
    </member>
    <member name="P:System.Xml.Linq.XText.Value">
      <summary>Gets or sets the value of this node.</summary>
      <returns>A <see cref="T:System.String" /> that contains the value of this node.</returns>
    </member>
    <member name="M:System.Xml.Linq.XText.WriteTo(System.Xml.XmlWriter)">
      <summary>Writes this node to an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlWriter" /> into which this method will write.</param>
      <filterpriority>2</filterpriority>
    </member>
  </members>
</doc>