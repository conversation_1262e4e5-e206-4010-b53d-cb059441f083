using Emgu.CV;
using Emgu.CV.CvEnum;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.Linq;

namespace OcrLiteLib
{
    public class OcrLite : IDisposable
    {
        // 移除调试属性，OCR服务不需要
        private DbNet dbNet;
        private AngleNet angleNet;
        private CrnnNet crnnNet;
        private MatPool matPool;
        private bool disposed = false;
        private int _detectCount = 0; // 检测次数计数器

        public OcrLite()
        {
            dbNet = new DbNet();
            angleNet = new AngleNet();
            crnnNet = new CrnnNet();
            matPool = new MatPool();
        }

        ~OcrLite()
        {
            Dispose(false);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing)
                {
                    // 释放托管资源
                    dbNet?.Dispose();
                    angleNet?.Dispose();
                    crnnNet?.Dispose();
                    matPool?.Dispose();
                }
                disposed = true;
            }
        }

        public void InitModels(string detPath, string clsPath, string recPath, string keysPath, int numThread)
        {
            try
            {
                dbNet.InitModel(detPath, numThread);
                angleNet.InitModel(clsPath, numThread);
                crnnNet.InitModel(recPath, keysPath, numThread);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message + ex.StackTrace);
                throw ex;
            }
        }

        /// <summary>
        /// 简化的OCR检测方法，返回包含位置、尺寸、置信度和文本的详细信息
        /// </summary>
        /// <param name="img">图像路径</param>
        /// <returns>包含详细信息的文本块列表</returns>
        public List<TextBlock> DetectTextBlocks(string img)
        {
            return DetectTextBlocks(img, 50, 1024, 0.5f, 0.3f, 2.0f, true, true);
        }

        /// <summary>
        /// 简化的OCR检测方法，返回包含位置、尺寸、置信度和文本的详细信息
        /// </summary>
        /// <param name="img">图像路径</param>
        /// <param name="padding">填充</param>
        /// <param name="maxSideLen">最大边长</param>
        /// <param name="boxScoreThresh">文本框分数阈值</param>
        /// <param name="boxThresh">文本框阈值</param>
        /// <param name="unClipRatio">扩展比例</param>
        /// <param name="doAngle">是否进行角度检测</param>
        /// <param name="mostAngle">是否使用最常见角度</param>
        /// <returns>包含详细信息的文本块列表</returns>
        public List<TextBlock> DetectTextBlocks(string img, int padding, int maxSideLen, float boxScoreThresh, float boxThresh,
                              float unClipRatio, bool doAngle, bool mostAngle)
        {
            List<TextBlock> result;

            using (Mat originSrc = CvInvoke.Imread(img, ImreadModes.Color))
            {
                if (originSrc.IsEmpty)
                {
                    return new List<TextBlock>();
                }

                int originMaxSide = Math.Max(originSrc.Cols, originSrc.Rows);
                int resize = (maxSideLen <= 0 || maxSideLen > originMaxSide) ? originMaxSide : maxSideLen;
                resize += 2 * padding;

                Rectangle paddingRect = new Rectangle(padding, padding, originSrc.Cols, originSrc.Rows);
                using (Mat paddingSrc = OcrUtils.MakePadding(originSrc, padding))
                {
                    ScaleParam scale = ScaleParam.GetScaleParam(paddingSrc, resize);
                    result = DetectTextBlocksOnce(paddingSrc, paddingRect, scale, boxScoreThresh, boxThresh, unClipRatio, doAngle, mostAngle);
                }
            }

            // 增加检测计数
            _detectCount++;

            // 每10次检测后清理Mat池
            if (_detectCount % 10 == 0)
            {
                matPool?.ForceCleanup();
            }

            // 强制垃圾回收，释放大对象
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            return result;
        }

        /// <summary>
        /// 简单的文本识别方法，只返回文本字符串
        /// </summary>
        /// <param name="img">图像路径</param>
        /// <returns>识别的文本字符串</returns>
        public string DetectText(string img)
        {
            var textBlocks = DetectTextBlocks(img);
            return string.Join("\n", textBlocks.Select(x => x.Text));
        }

        public OcrResult Detect(string img, int padding, int maxSideLen, float boxScoreThresh, float boxThresh,
                              float unClipRatio, bool doAngle, bool mostAngle)
        {
            using (var memoryScope = new MemoryScope("OCR Detect"))
            {
                MemoryMonitor.Checkpoint("开始检测");

                using (Mat originSrc = CvInvoke.Imread(img, ImreadModes.Color))//default : BGR
                {
                    MemoryMonitor.Checkpoint("图像加载完成");

                    int originMaxSide = Math.Max(originSrc.Cols, originSrc.Rows);

                    int resize;
                    if (maxSideLen <= 0 || maxSideLen > originMaxSide)
                    {
                        resize = originMaxSide;
                    }
                    else
                    {
                        resize = maxSideLen;
                    }
                    resize += 2 * padding;
                    Rectangle paddingRect = new Rectangle(padding, padding, originSrc.Cols, originSrc.Rows);
                    using (Mat paddingSrc = OcrUtils.MakePadding(originSrc, padding))
                    {
                        MemoryMonitor.Checkpoint("图像预处理完成");

                        ScaleParam scale = ScaleParam.GetScaleParam(paddingSrc, resize);

                        var result = DetectOnce(paddingSrc, paddingRect, scale, boxScoreThresh, boxThresh, unClipRatio, doAngle, mostAngle);

                        MemoryMonitor.Checkpoint("检测完成");
                        return result;
                    }
                }
            }
        }

        /// <summary>
        /// 简化的文本检测方法，返回包含位置、尺寸、置信度和文本的TextBlock列表
        /// </summary>
        private List<TextBlock> DetectTextBlocksOnce(Mat src, Rectangle originRect, ScaleParam scale, float boxScoreThresh, float boxThresh,
                              float unClipRatio, bool doAngle, bool mostAngle)
        {
            var startTicks = DateTime.Now.Ticks;

            // 获取文本框
            var textBoxes = dbNet.GetTextBoxes(src, scale, boxScoreThresh, boxThresh, unClipRatio);
            if (textBoxes.Count == 0)
            {
                // 立即强制GC，释放dbNet推理过程中的内存
                GC.Collect();
                return new List<TextBlock>();
            }

            // 获取文本区域图像
            List<Mat> partImages = OcrUtils.GetPartImages(src, textBoxes);
            List<Angle> angles = null;
            List<TextLine> textLines = null;

            try
            {
                // 角度检测
                angles = angleNet.GetAngles(partImages, doAngle, mostAngle);

                // 强制GC，释放angleNet推理过程中的内存
                GC.Collect();

                // 旋转图像
                for (int i = 0; i < partImages.Count; ++i)
                {
                    if (angles[i].Index == 1)
                    {
                        using (Mat rotated = OcrUtils.MatRotateClockWise180(partImages[i]))
                        {
                            partImages[i].Dispose(); // 释放原图像
                            partImages[i] = rotated.Clone(); // 使用旋转后的图像
                        }
                    }
                }

                // 文本识别
                textLines = crnnNet.GetTextLines(partImages);

                // 强制GC，释放crnnNet推理过程中的内存
                GC.Collect();

                // 创建TextBlock列表，包含位置、尺寸、置信度和文本信息
                List<TextBlock> textBlocks = new List<TextBlock>(textLines.Count);
                for (int i = 0; i < textLines.Count; ++i)
                {
                    // 调整坐标到原始图像坐标系
                    List<Point> adjustedPoints = new List<Point>(4);
                    foreach (var point in textBoxes[i].Points)
                    {
                        adjustedPoints.Add(new Point(
                            point.X - originRect.X,
                            point.Y - originRect.Y
                        ));
                    }

                    TextBlock textBlock = new TextBlock
                    {
                        BoxPoints = adjustedPoints,
                        BoxScore = textBoxes[i].Score,
                        AngleIndex = angles[i].Index,
                        AngleScore = angles[i].Score,
                        AngleTime = angles[i].Time,
                        Text = textLines[i].Text,
                        CharScores = textLines[i].CharScores,
                        CrnnTime = textLines[i].Time,
                        BlockTime = angles[i].Time + textLines[i].Time
                    };
                    textBlocks.Add(textBlock);
                }

                return textBlocks;
            }
            finally
            {
                // 确保所有Mat对象都被释放
                if (partImages != null)
                {
                    foreach (var mat in partImages)
                    {
                        mat?.Dispose();
                    }
                    partImages.Clear();
                }

                // 清理其他可能的大对象引用
                angles?.Clear();
                textLines?.Clear();

                // 最终强制GC
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }
        }

        private OcrResult DetectOnce(Mat src, Rectangle originRect, ScaleParam scale, float boxScoreThresh, float boxThresh,
                              float unClipRatio, bool doAngle, bool mostAngle)
        {
            var startTicks = DateTime.Now.Ticks;

            // 获取文本框
            var textBoxes = dbNet.GetTextBoxes(src, scale, boxScoreThresh, boxThresh, unClipRatio);
            var dbNetTime = (DateTime.Now.Ticks - startTicks) / 10000F;

            if (textBoxes.Count == 0)
            {
                return new OcrResult
                {
                    TextBlocks = new List<TextBlock>(),
                    DbNetTime = dbNetTime,
                    DetectTime = dbNetTime,
                    StrRes = string.Empty
                };
            }

            // 获取文本区域图像
            List<Mat> partImages = OcrUtils.GetPartImages(src, textBoxes);
            try
            {
                // 角度检测
                List<Angle> angles = angleNet.GetAngles(partImages, doAngle, mostAngle);
                // 旋转图像
                for (int i = 0; i < partImages.Count; ++i)
                {
                    if (angles[i].Index == 1)
                    {
                        using (Mat rotated = OcrUtils.MatRotateClockWise180(partImages[i]))
                        {
                            partImages[i].Dispose();
                            partImages[i] = rotated.Clone();
                        }
                    }
                }

                // 文本识别
                List<TextLine> textLines = crnnNet.GetTextLines(partImages);

                // 创建TextBlock列表
                List<TextBlock> textBlocks = new List<TextBlock>(textLines.Count);
                for (int i = 0; i < textLines.Count; ++i)
                {
                    // 调整坐标到原始图像坐标系
                    List<Point> adjustedPoints = new List<Point>(4);
                    foreach (var point in textBoxes[i].Points)
                    {
                        adjustedPoints.Add(new Point(
                            point.X - originRect.X,
                            point.Y - originRect.Y
                        ));
                    }

                    TextBlock textBlock = new TextBlock
                    {
                        BoxPoints = adjustedPoints,
                        BoxScore = textBoxes[i].Score,
                        AngleIndex = angles[i].Index,
                        AngleScore = angles[i].Score,
                        AngleTime = angles[i].Time,
                        Text = textLines[i].Text,
                        CharScores = textLines[i].CharScores,
                        CrnnTime = textLines[i].Time,
                        BlockTime = angles[i].Time + textLines[i].Time
                    };
                    textBlocks.Add(textBlock);
                }

                var endTicks = DateTime.Now.Ticks;
                var fullDetectTime = (endTicks - startTicks) / 10000F;

                // 创建结果，不需要图像
                StringBuilder strRes = new StringBuilder();
                textBlocks.ForEach(x => strRes.AppendLine(x.Text));

                return new OcrResult
                {
                    TextBlocks = textBlocks,
                    DbNetTime = dbNetTime,
                    DetectTime = fullDetectTime,
                    StrRes = strRes.ToString()
                };
            }
            finally
            {
                // 确保所有Mat对象都被释放
                foreach (var mat in partImages)
                {
                    mat?.Dispose();
                }
                partImages.Clear();

                // 强制垃圾回收
                GC.Collect();
            }
        }

    }
}
