﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading</name>
  </assembly>
  <members>
    <member name="T:System.Threading.AbandonedMutexException">
      <summary>Eccezione generata quando un thread acquisisce un oggetto <see cref="T:System.Threading.Mutex" /> che un altro thread ha abbandonato uscendo senza rilasciarlo.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.AbandonedMutexException.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.AbandonedMutexException" /> con valori predefiniti.</summary>
    </member>
    <member name="M:System.Threading.AbandonedMutexException.#ctor(System.Int32,System.Threading.WaitHandle)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.AbandonedMutexException" /> con un indice specificato per il mutex abbandonato, se applicabile, e un oggetto <see cref="T:System.Threading.Mutex" /> che rappresenta il mutex.</summary>
      <param name="location">Indice del mutex abbandonato nella matrice degli handle di attesa se l'eccezione viene generata per il metodo <see cref="Overload:System.Threading.WaitHandle.WaitAny" /> o –1 se l'eccezione viene generata per i metodi <see cref="Overload:System.Threading.WaitHandle.WaitOne" /> o <see cref="Overload:System.Threading.WaitHandle.WaitAll" />.</param>
      <param name="handle">Oggetto <see cref="T:System.Threading.Mutex" /> che rappresenta il mutex abbandonato.</param>
    </member>
    <member name="M:System.Threading.AbandonedMutexException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.AbandonedMutexException" /> con un messaggio di errore specificato.</summary>
      <param name="message">Messaggio di errore che spiega il motivo dell'eccezione.</param>
    </member>
    <member name="M:System.Threading.AbandonedMutexException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.AbandonedMutexException" /> con il messaggio di errore e l'eccezione interna specificati. </summary>
      <param name="message">Messaggio di errore che spiega il motivo dell'eccezione.</param>
      <param name="inner">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="inner" /> non è null, l'eccezione corrente verrà generata in un blocco catch che gestisce l'eccezione interna.</param>
    </member>
    <member name="M:System.Threading.AbandonedMutexException.#ctor(System.String,System.Exception,System.Int32,System.Threading.WaitHandle)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.AbandonedMutexException" /> con il messaggio di errore, l'eccezione interna, l'indice per il mutex abbandonato, se applicabile, specificati e un oggetto <see cref="T:System.Threading.Mutex" /> che rappresenta il mutex.</summary>
      <param name="message">Messaggio di errore che spiega il motivo dell'eccezione.</param>
      <param name="inner">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="inner" /> non è null, l'eccezione corrente verrà generata in un blocco catch che gestisce l'eccezione interna.</param>
      <param name="location">Indice del mutex abbandonato nella matrice degli handle di attesa se l'eccezione viene generata per il metodo <see cref="Overload:System.Threading.WaitHandle.WaitAny" />,  –1 se l'eccezione viene generata per il metodo <see cref="Overload:System.Threading.WaitHandle.WaitOne" /> o <see cref="Overload:System.Threading.WaitHandle.WaitAll" />.</param>
      <param name="handle">Oggetto <see cref="T:System.Threading.Mutex" /> che rappresenta il mutex abbandonato.</param>
    </member>
    <member name="M:System.Threading.AbandonedMutexException.#ctor(System.String,System.Int32,System.Threading.WaitHandle)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.AbandonedMutexException" /> con il messaggio di errore, l'indice del mutex abbandonato, se applicabile, e il mutex abbandonato specificati. </summary>
      <param name="message">Messaggio di errore che spiega il motivo dell'eccezione.</param>
      <param name="location">Indice del mutex abbandonato nella matrice degli handle di attesa se l'eccezione viene generata per il metodo <see cref="Overload:System.Threading.WaitHandle.WaitAny" />,  –1 se l'eccezione viene generata per il metodo <see cref="Overload:System.Threading.WaitHandle.WaitOne" /> o <see cref="Overload:System.Threading.WaitHandle.WaitAll" />.</param>
      <param name="handle">Oggetto <see cref="T:System.Threading.Mutex" /> che rappresenta il mutex abbandonato.</param>
    </member>
    <member name="P:System.Threading.AbandonedMutexException.Mutex">
      <summary>Ottiene il mutex abbandonato che ha causato l'eccezione, se noto.</summary>
      <returns>Oggetto <see cref="T:System.Threading.Mutex" /> che rappresenta il mutex abbandonato oppure null se il mutex abbandonato non è stato identificato.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Threading.AbandonedMutexException.MutexIndex">
      <summary>Ottiene l'indice del mutex abbandonato che ha causato l'eccezione, se noto.</summary>
      <returns>Nella matrice degli handle in attesa passati al metodo <see cref="Overload:System.Threading.WaitHandle.WaitAny" />, indice dell'oggetto <see cref="T:System.Threading.Mutex" /> che rappresenta il mutex abbandonato oppure –1 se l'indice del mutex abbandonato non è stato determinato.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.AsyncLocal`1">
      <summary>Rappresenta dati di ambiente locali rispetto a un flusso di controllo asincrono specificato, ad esempio un metodo asincrono. </summary>
      <typeparam name="T">Tipo dei dati di ambiente. </typeparam>
    </member>
    <member name="M:System.Threading.AsyncLocal`1.#ctor">
      <summary>Crea un'istanza dell'istanza di <see cref="T:System.Threading.AsyncLocal`1" /> che non riceve notifiche di modifica. </summary>
    </member>
    <member name="M:System.Threading.AsyncLocal`1.#ctor(System.Action{System.Threading.AsyncLocalValueChangedArgs{`0}})">
      <summary>Crea un'istanza dell'istanza di <see cref="T:System.Threading.AsyncLocal`1" /> locale che riceve notifiche di modifica. </summary>
      <param name="valueChangedHandler">Delegato chiamato ogni volta che il valore corrente cambia in qualsiasi thread. </param>
    </member>
    <member name="P:System.Threading.AsyncLocal`1.Value">
      <summary>Ottiene o imposta il valore dei dati di ambiente. </summary>
      <returns>Valore dei dati di ambiente. </returns>
    </member>
    <member name="T:System.Threading.AsyncLocalValueChangedArgs`1">
      <summary>Classe che fornisce le informazioni di modifica dei dati alle istanze di <see cref="T:System.Threading.AsyncLocal`1" /> registrate per le notifiche di modifica. </summary>
      <typeparam name="T">Tipo di dati. </typeparam>
    </member>
    <member name="P:System.Threading.AsyncLocalValueChangedArgs`1.CurrentValue">
      <summary>Ottiene il valore corrente dei dati. </summary>
      <returns>Valore corrente dei dati. </returns>
    </member>
    <member name="P:System.Threading.AsyncLocalValueChangedArgs`1.PreviousValue">
      <summary>Ottiene il valore precedente dei dati.</summary>
      <returns>Valore precedente dei dati. </returns>
    </member>
    <member name="P:System.Threading.AsyncLocalValueChangedArgs`1.ThreadContextChanged">
      <summary>Restituisce un valore che indica se il valore cambia a seguito di una modifica del contesto di esecuzione. </summary>
      <returns>true se il valore è cambiato a seguito di una modifica del contesto di esecuzione; in caso contrario, false. </returns>
    </member>
    <member name="T:System.Threading.AutoResetEvent">
      <summary>Notifica a un thread in attesa che si è verificato un evento.La classe non può essere ereditata.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.AutoResetEvent.#ctor(System.Boolean)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.AutoResetEvent" /> con un valore booleano che indica se impostare lo stato iniziale su segnalato.</summary>
      <param name="initialState">true per impostare lo stato iniziale su segnalato; false per impostare lo stato iniziale su non segnalato. </param>
    </member>
    <member name="T:System.Threading.Barrier">
      <summary>Consente a più attività di funzionare cooperativamente in un algoritmo in parallelo tramite più fasi.</summary>
    </member>
    <member name="M:System.Threading.Barrier.#ctor(System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Barrier" />.</summary>
      <param name="participantCount">Numero di thread che partecipano.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="participantCount" /> è minore di 0 o maggiore di 32,767.</exception>
    </member>
    <member name="M:System.Threading.Barrier.#ctor(System.Int32,System.Action{System.Threading.Barrier})">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Barrier" />.</summary>
      <param name="participantCount">Numero di thread che partecipano.</param>
      <param name="postPhaseAction">Oggetto <see cref="T:System.Action`1" /> da eseguire dopo ogni fase. Può essere passato Null (Nothing in Visual Basic) per indicare che non è stata intrapresa alcuna azione.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="participantCount" /> è minore di 0 o maggiore di 32,767.</exception>
    </member>
    <member name="M:System.Threading.Barrier.AddParticipant">
      <summary>Notifica all'oggetto <see cref="T:System.Threading.Barrier" /> che sarà presente un partecipante aggiuntivo.</summary>
      <returns>Numero di fase della barriera in corrispondenza di cui parteciperanno inizialmente i nuovi partecipanti.</returns>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.InvalidOperationException">L'aggiunta di un partecipante provocherebbe il superamento del conteggio del partecipante della barriera di 32.767.- oppure -Il metodo è stato richiamato dall'interno di un'azione post-fase.</exception>
    </member>
    <member name="M:System.Threading.Barrier.AddParticipants(System.Int32)">
      <summary>Notifica all'oggetto <see cref="T:System.Threading.Barrier" /> che saranno presenti partecipanti aggiuntivi.</summary>
      <returns>Numero di fase della barriera in corrispondenza di cui parteciperanno inizialmente i nuovi partecipanti.</returns>
      <param name="participantCount">Numero di partecipanti aggiuntivi da aggiungere alla barriera.</param>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="participantCount" /> è minore di 0.- oppure -L'aggiunta di partecipanti <paramref name="participantCount" /> provocherebbe il superamento del conteggio del partecipante della barriera di 32.767.</exception>
      <exception cref="T:System.InvalidOperationException">Il metodo è stato richiamato dall'interno di un'azione post-fase.</exception>
    </member>
    <member name="P:System.Threading.Barrier.CurrentPhaseNumber">
      <summary>Ottiene il numero di fase corrente della barriera.</summary>
      <returns>Restituisce il numero di fase corrente della barriera.</returns>
    </member>
    <member name="M:System.Threading.Barrier.Dispose">
      <summary>Rilascia tutte le risorse utilizzate dall'istanza corrente della classe <see cref="T:System.Threading.Barrier" />.</summary>
      <exception cref="T:System.InvalidOperationException">Il metodo è stato richiamato dall'interno di un'azione post-fase.</exception>
    </member>
    <member name="M:System.Threading.Barrier.Dispose(System.Boolean)">
      <summary>Rilascia le risorse non gestite utilizzate dall'oggetto <see cref="T:System.Threading.Barrier" /> ed eventualmente rilascia le risorse gestite.</summary>
      <param name="disposing">true per rilasciare sia le risorse gestite sia quelle non gestite, false per rilasciare solo quelle non gestite.</param>
    </member>
    <member name="P:System.Threading.Barrier.ParticipantCount">
      <summary>Ottiene il numero totale di partecipanti nella barriera.</summary>
      <returns>Restituisce il numero totale di partecipanti nella barriera.</returns>
    </member>
    <member name="P:System.Threading.Barrier.ParticipantsRemaining">
      <summary>Ottiene il numero di partecipanti nella barriera che non hanno ancora eseguito la segnalazione nella fase corrente.</summary>
      <returns>Restituisce il numero di partecipanti nella barriera che non hanno ancora eseguito la segnalazione nella fase corrente.</returns>
    </member>
    <member name="M:System.Threading.Barrier.RemoveParticipant">
      <summary>Notifica all'oggetto <see cref="T:System.Threading.Barrier" /> che sarà presente un partecipante in meno.</summary>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.InvalidOperationException">La barriera dispone già di 0 partecipanti.- oppure -Il metodo è stato richiamato dall'interno di un'azione post-fase.</exception>
    </member>
    <member name="M:System.Threading.Barrier.RemoveParticipants(System.Int32)">
      <summary>Notifica all'oggetto <see cref="T:System.Threading.Barrier" /> che saranno presenti meno partecipanti.</summary>
      <param name="participantCount">Numero di partecipanti aggiuntivi da rimuovere dalla barriera.</param>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="participantCount" /> è minore di 0.</exception>
      <exception cref="T:System.InvalidOperationException">La barriera dispone già di 0 partecipanti.- oppure -Il metodo è stato richiamato dall'interno di un'azione post-fase. - oppure -il conteggio del partecipante corrente è minore del conteggio del partecipante specificato</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il conteggio totale dei partecipanti è minore del <paramref name=" participantCount" /> specificato</exception>
    </member>
    <member name="M:System.Threading.Barrier.SignalAndWait">
      <summary>Segnala che un partecipante ha raggiunto la barriera e attende che venga raggiunta anche da tutti gli altri partecipanti.</summary>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.InvalidOperationException">Il metodo viene richiamato dall'interno di un'azione post-fase, la barriera dispone attualmente di 0 partecipanti o la barriera viene segnalata da più thread registrati come partecipanti.</exception>
      <exception cref="T:System.Threading.BarrierPostPhaseException">Se un'eccezione viene generata da un'azione post-fase di una Barriera dopo che tutti thread che partecipano hanno chiamato SignalAndWait, l'eccezione verrà sottoposta a wrapping in un BarrierPostPhaseException e sarà generata su tutti i thread che partecipano.</exception>
    </member>
    <member name="M:System.Threading.Barrier.SignalAndWait(System.Int32)">
      <summary>Segnala che un partecipante ha raggiunto la barriera e attende che venga raggiunta anche da tutti gli altri partecipanti, utilizzando un Signed Integer a 32 bit per misurare il timeout.</summary>
      <returns>true se tutti i partecipanti raggiungono la barriera entro il tempo specificato; in caso contrario, false.</returns>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure, per un'attesa indefinita, <see cref="F:System.Threading.Timeout.Infinite" /> (-1).</param>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> è un numero negativo diverso da -1 che rappresenta un timeout indeterminato.</exception>
      <exception cref="T:System.InvalidOperationException">Il metodo viene richiamato dall'interno di un'azione post-fase, la barriera dispone attualmente di 0 partecipanti o la barriera viene segnalata da più thread registrati come partecipanti.</exception>
      <exception cref="T:System.Threading.BarrierPostPhaseException">Se un'eccezione viene generata da un'azione post-fase di una Barriera dopo che tutti thread che partecipano hanno chiamato SignalAndWait, l'eccezione verrà sottoposta a wrapping in un BarrierPostPhaseException e sarà generata su tutti i thread che partecipano.</exception>
    </member>
    <member name="M:System.Threading.Barrier.SignalAndWait(System.Int32,System.Threading.CancellationToken)">
      <summary>Segnala che un partecipante ha raggiunto la barriera e attende che venga raggiunta anche da tutti gli altri partecipanti, utilizzando un Signed Integer a 32 bit per misurare il timeout, al contempo osservando un token di annullamento.</summary>
      <returns>true se tutti i partecipanti raggiungono la barriera entro il tempo specificato; in caso contrario, false.</returns>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure, per un'attesa indefinita, <see cref="F:System.Threading.Timeout.Infinite" /> (-1).</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> da osservare.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> è stato annullato.</exception>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> è un numero negativo diverso da -1 che rappresenta un timeout indeterminato.</exception>
      <exception cref="T:System.InvalidOperationException">Il metodo viene richiamato dall'interno di un'azione post-fase, la barriera dispone attualmente di 0 partecipanti o la barriera viene segnalata da più thread registrati come partecipanti.</exception>
    </member>
    <member name="M:System.Threading.Barrier.SignalAndWait(System.Threading.CancellationToken)">
      <summary>Segnala che un partecipante ha raggiunto la barriera e attende che venga raggiunta anche da tutti gli altri partecipanti, al contempo osservando un token di annullamento.</summary>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> da osservare.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> è stato annullato.</exception>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.InvalidOperationException">Il metodo viene richiamato dall'interno di un'azione post-fase, la barriera dispone attualmente di 0 partecipanti o la barriera viene segnalata da più thread registrati come partecipanti.</exception>
    </member>
    <member name="M:System.Threading.Barrier.SignalAndWait(System.TimeSpan)">
      <summary>Segnala che un partecipante ha raggiunto la barriera e attende che venga raggiunta anche da tutti gli altri partecipanti, utilizzando un oggetto <see cref="T:System.TimeSpan" /> per misurare l'intervallo di tempo.</summary>
      <returns>true se tutti gli altri partecipanti hanno raggiunto la barriera. In caso contrario, false.</returns>
      <param name="timeout">
        <see cref="T:System.TimeSpan" /> che rappresenta il numero di millisecondi di attesa oppure <see cref="T:System.TimeSpan" /> che rappresenta -1 millisecondi per un'attesa indefinita.</param>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> è un numero negativo diverso da -1 millisecondi, che rappresenta un timeout infinito, oppure è più grande di 32.767.</exception>
      <exception cref="T:System.InvalidOperationException">Il metodo viene richiamato dall'interno di un'azione post-fase, la barriera dispone attualmente di 0 partecipanti o la barriera viene segnalata da più thread registrati come partecipanti.</exception>
    </member>
    <member name="M:System.Threading.Barrier.SignalAndWait(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Segnala che un partecipante ha raggiunto la barriera e attende che venga raggiunta anche da tutti gli altri partecipanti, utilizzando un oggetto <see cref="T:System.TimeSpan" /> per misurare l'intervallo di tempo, al contempo osservando un token di annullamento.</summary>
      <returns>true se tutti gli altri partecipanti hanno raggiunto la barriera. In caso contrario, false.</returns>
      <param name="timeout">
        <see cref="T:System.TimeSpan" /> che rappresenta il numero di millisecondi di attesa oppure <see cref="T:System.TimeSpan" /> che rappresenta -1 millisecondi per un'attesa indefinita.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> da osservare.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> è stato annullato.</exception>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> è un numero negativo diverso da -1 millisecondi, che rappresenta un timeout infinito.</exception>
      <exception cref="T:System.InvalidOperationException">Il metodo viene richiamato dall'interno di un'azione post-fase, la barriera dispone attualmente di 0 partecipanti o la barriera viene segnalata da più thread registrati come partecipanti.</exception>
    </member>
    <member name="T:System.Threading.BarrierPostPhaseException">
      <summary>Eccezione generata quando l'azione post-fase di un oggetto <see cref="T:System.Threading.Barrier" /> non viene eseguita correttamente.</summary>
    </member>
    <member name="M:System.Threading.BarrierPostPhaseException.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.BarrierPostPhaseException" /> con un messaggio fornito dal sistema in cui viene descritto l'errore.</summary>
    </member>
    <member name="M:System.Threading.BarrierPostPhaseException.#ctor(System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.BarrierPostPhaseException" /> con l'eccezione interna specificata.</summary>
      <param name="innerException">Eccezione causa dell'eccezione corrente.</param>
    </member>
    <member name="M:System.Threading.BarrierPostPhaseException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.BarrierPostPhaseException" /> con un messaggio specifico in cui viene descritto l'errore.</summary>
      <param name="message">Messaggio in cui viene descritta l'eccezione.È necessario che il chiamante del costruttore assicuri che la stringa sia stata localizzata per le impostazioni cultura correnti del sistema.</param>
    </member>
    <member name="M:System.Threading.BarrierPostPhaseException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.BarrierPostPhaseException" /> con un messaggio di errore specificato e un riferimento all'eccezione interna che è la causa dell'eccezione corrente.</summary>
      <param name="message">Messaggio in cui viene descritta l'eccezione.È necessario che il chiamante del costruttore assicuri che la stringa sia stata localizzata per le impostazioni cultura correnti del sistema.</param>
      <param name="innerException">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="innerException" /> non è null, l'eccezione corrente viene generata in un blocco catch in cui viene gestita l'eccezione interna.</param>
    </member>
    <member name="T:System.Threading.ContextCallback">
      <summary>Rappresenta un metodo da chiamare all'interno di un nuovo contesto.  </summary>
      <param name="state">Oggetto contenente informazioni che devono essere utilizzate dal metodo di callback ogni volta che viene eseguito.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.CountdownEvent">
      <summary>Rappresenta un primitiva di sincronizzazione segnalata quando il relativo conteggio raggiunge lo zero.</summary>
    </member>
    <member name="M:System.Threading.CountdownEvent.#ctor(System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.CountdownEvent" /> con il conteggio specificato.</summary>
      <param name="initialCount">Numero di segnali inizialmente richiesti per impostare l'oggetto <see cref="T:System.Threading.CountdownEvent" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialCount" /> è minore di 0.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.AddCount">
      <summary>Incrementa di uno il conteggio corrente di <see cref="T:System.Threading.CountdownEvent" />.</summary>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.InvalidOperationException">L'istanza corrente è già impostata.- oppure -<see cref="P:System.Threading.CountdownEvent.CurrentCount" /> è maggiore di o uguale a <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.AddCount(System.Int32)">
      <summary>Incrementa di un valore specificato il conteggio corrente di <see cref="T:System.Threading.CountdownEvent" />.</summary>
      <param name="signalCount">Valore che indica l'incremento di <see cref="P:System.Threading.CountdownEvent.CurrentCount" />.</param>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signalCount" /> è minore o uguale a 0.</exception>
      <exception cref="T:System.InvalidOperationException">L'istanza corrente è già impostata.- oppure -<see cref="P:System.Threading.CountdownEvent.CurrentCount" /> è uguale o maggiore a <see cref="F:System.Int32.MaxValue" /> dopo che il conteggio è incrementato da <paramref name="signalCount." /></exception>
    </member>
    <member name="P:System.Threading.CountdownEvent.CurrentCount">
      <summary>Ottiene il numero di segnali restanti necessari per impostare l'evento.</summary>
      <returns> Numero di segnali restanti necessari per impostare l'evento.</returns>
    </member>
    <member name="M:System.Threading.CountdownEvent.Dispose">
      <summary>Rilascia tutte le risorse utilizzate dall'istanza corrente della classe <see cref="T:System.Threading.CountdownEvent" />.</summary>
    </member>
    <member name="M:System.Threading.CountdownEvent.Dispose(System.Boolean)">
      <summary>Rilascia le risorse non gestite utilizzate dall'oggetto <see cref="T:System.Threading.CountdownEvent" /> ed eventualmente rilascia le risorse gestite.</summary>
      <param name="disposing">true per rilasciare sia le risorse gestite sia quelle non gestite, false per rilasciare solo quelle non gestite.</param>
    </member>
    <member name="P:System.Threading.CountdownEvent.InitialCount">
      <summary>Ottiene il numero di segnali necessari inizialmente per impostare l'evento.</summary>
      <returns> Numero di segnali necessari inizialmente per impostare l'evento.</returns>
    </member>
    <member name="P:System.Threading.CountdownEvent.IsSet">
      <summary>Determina se l'evento è impostato.</summary>
      <returns>true se l'evento è impostato, altrimenti false.</returns>
    </member>
    <member name="M:System.Threading.CountdownEvent.Reset">
      <summary>Reimposta <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> sul valore di <see cref="P:System.Threading.CountdownEvent.InitialCount" />.</summary>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Reset(System.Int32)">
      <summary>Reimposta la proprietà <see cref="P:System.Threading.CountdownEvent.InitialCount" /> al valore specificato.</summary>
      <param name="count">Numero di segnali necessari per impostare l'oggetto <see cref="T:System.Threading.CountdownEvent" />.</param>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> è minore di 0.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Signal">
      <summary>Registra un segnale con l'oggetto <see cref="T:System.Threading.CountdownEvent" />, decrementando il valore di <see cref="P:System.Threading.CountdownEvent.CurrentCount" />.</summary>
      <returns>true se il conteggio ha raggiunto lo zero a causa del segnale e l'evento è stato impostato. In caso contrario, false.</returns>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.InvalidOperationException">L'istanza corrente è già impostata.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Signal(System.Int32)">
      <summary>Registra più segnali con l'oggetto <see cref="T:System.Threading.CountdownEvent" />, decrementandone il valore di <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> della quantità specificata.</summary>
      <returns>true se il conteggio ha raggiunto lo zero a causa dei segnali e l'evento è stato impostato. In caso contrario, false.</returns>
      <param name="signalCount">Numero di segnali da registrare.</param>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signalCount" /> è minore di 1.</exception>
      <exception cref="T:System.InvalidOperationException">L'istanza corrente è già impostata. oppure <paramref name="signalCount" /> è maggiore di <see cref="P:System.Threading.CountdownEvent.CurrentCount" />.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.TryAddCount">
      <summary>Tenta di incrementare <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> di uno.</summary>
      <returns>true se l'incremento ha avuto esito positivo. In caso contrario, false.Se <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> è già zero, questo metodo restituirà false.</returns>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> è uguale a <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.TryAddCount(System.Int32)">
      <summary>Tenta di incrementare <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> in base a un valore specificato.</summary>
      <returns>true se l'incremento ha avuto esito positivo. In caso contrario, false.Se <see cref="P:System.Threading.CountdownEvent.CurrentCount" /> è già zero, verrà restituito false.</returns>
      <param name="signalCount">Valore che indica l'incremento di <see cref="P:System.Threading.CountdownEvent.CurrentCount" />.</param>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signalCount" /> è minore o uguale a 0.</exception>
      <exception cref="T:System.InvalidOperationException">L'istanza corrente è già impostata.- oppure -<see cref="P:System.Threading.CountdownEvent.CurrentCount" /> + <paramref name="signalCount" /> è uguale o maggiore di <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Wait">
      <summary>Blocca il thread corrente finché l'oggetto <see cref="T:System.Threading.CountdownEvent" /> non viene impostato.</summary>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Wait(System.Int32)">
      <summary>Blocca il thread corrente finché l'oggetto <see cref="T:System.Threading.CountdownEvent" /> non viene impostato, utilizzando un intero con segno a 32 bit per misurare il timeout.</summary>
      <returns>true se <see cref="T:System.Threading.CountdownEvent" /> è stato impostato. In caso contrario, false.</returns>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure, per un'attesa indefinita, <see cref="F:System.Threading.Timeout.Infinite" /> (-1).</param>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> è un numero negativo diverso da -1 che rappresenta un timeout indeterminato.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Wait(System.Int32,System.Threading.CancellationToken)">
      <summary>Blocca il thread corrente finché l'oggetto <see cref="T:System.Threading.CountdownEvent" /> non viene impostato, utilizzando un intero con segno a 32 bit per misurare il timeout e al contempo osservando un oggetto <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>true se <see cref="T:System.Threading.CountdownEvent" /> è stato impostato. In caso contrario, false.</returns>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure, per un'attesa indefinita, <see cref="F:System.Threading.Timeout.Infinite" /> (-1).</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> da osservare.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> è stato annullato.</exception>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata. oppure l'oggetto <see cref="T:System.Threading.CancellationTokenSource" /> aveva creato <paramref name="cancellationToken" /> è già stato eliminato.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> è un numero negativo diverso da -1 che rappresenta un timeout indeterminato.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Wait(System.Threading.CancellationToken)">
      <summary>Blocca il thread corrente finché l'oggetto <see cref="T:System.Threading.CountdownEvent" /> non viene impostato, al contempo osservando un oggetto <see cref="T:System.Threading.CancellationToken" />.</summary>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> da osservare.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> è stato annullato.</exception>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata. oppure l'oggetto <see cref="T:System.Threading.CancellationTokenSource" /> aveva creato <paramref name="cancellationToken" /> è già stato eliminato.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Wait(System.TimeSpan)">
      <summary>Blocca il thread corrente finché l'oggetto <see cref="T:System.Threading.CountdownEvent" /> non viene impostato, utilizzando un oggetto <see cref="T:System.TimeSpan" /> per misurare il timeout.</summary>
      <returns>true se <see cref="T:System.Threading.CountdownEvent" /> è stato impostato. In caso contrario, false.</returns>
      <param name="timeout">
        <see cref="T:System.TimeSpan" /> che rappresenta il numero di millisecondi di attesa oppure <see cref="T:System.TimeSpan" /> che rappresenta -1 millisecondi per un'attesa indefinita.</param>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> è un numero negativo diverso da -1 millisecondi che rappresenta un timeout infinito - o - il timeout è più grande di <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CountdownEvent.Wait(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Blocca il thread corrente finché l'oggetto <see cref="T:System.Threading.CountdownEvent" /> non viene impostato, utilizzando un oggetto <see cref="T:System.TimeSpan" /> per misurare il timeout e al contempo osservando un oggetto <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>true se <see cref="T:System.Threading.CountdownEvent" /> è stato impostato. In caso contrario, false.</returns>
      <param name="timeout">
        <see cref="T:System.TimeSpan" /> che rappresenta il numero di millisecondi di attesa oppure <see cref="T:System.TimeSpan" /> che rappresenta -1 millisecondi per un'attesa indefinita.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> da osservare.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> è stato annullato.</exception>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata. oppure l'oggetto <see cref="T:System.Threading.CancellationTokenSource" /> aveva creato <paramref name="cancellationToken" /> è già stato eliminato.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> è un numero negativo diverso da -1 millisecondi che rappresenta un timeout infinito - o - il timeout è più grande di <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="P:System.Threading.CountdownEvent.WaitHandle">
      <summary>Ottiene un oggetto <see cref="T:System.Threading.WaitHandle" /> utilizzato per attendere l'impostazione dell'evento.</summary>
      <returns>Oggetto <see cref="T:System.Threading.WaitHandle" /> utilizzato per attendere l'impostazione dell'evento.</returns>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
    </member>
    <member name="T:System.Threading.EventResetMode">
      <summary>Indica se <see cref="T:System.Threading.EventWaitHandle" /> verrà reimpostato automaticamente o manualmente dopo la ricezione di un segnale.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Threading.EventResetMode.AutoReset">
      <summary>Con la segnalazione, <see cref="T:System.Threading.EventWaitHandle" /> viene reimpostato automaticamente dopo il rilascio di un singolo thread.Se non sono presenti thread in attesa, <see cref="T:System.Threading.EventWaitHandle" /> resta segnalato fino al blocco di un thread e viene reimpostato dopo il rilascio del thread.</summary>
    </member>
    <member name="F:System.Threading.EventResetMode.ManualReset">
      <summary>Con la segnalazione, <see cref="T:System.Threading.EventWaitHandle" /> rilascia tutti i thread in attesa e resta segnalato finché non viene reimpostato manualmente.</summary>
    </member>
    <member name="T:System.Threading.EventWaitHandle">
      <summary>Rappresenta un evento di sincronizzazione dei thread.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.EventWaitHandle.#ctor(System.Boolean,System.Threading.EventResetMode)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.EventWaitHandle" />, specificando se l'handle di attesa è inizialmente segnalato e se la reimpostazione viene eseguita automaticamente o manualmente.</summary>
      <param name="initialState">true per impostare lo stato iniziale su segnalato; false per impostarlo su non segnalato.</param>
      <param name="mode">Uno dei valori di <see cref="T:System.Threading.EventResetMode" /> che determina se l'evento viene reimpostato automaticamente o manualmente.</param>
    </member>
    <member name="M:System.Threading.EventWaitHandle.#ctor(System.Boolean,System.Threading.EventResetMode,System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.EventWaitHandle" />, specificando se l'handle di attesa è inizialmente segnalato se creato a seguito di questa chiamata e se la reimpostazione viene eseguita automaticamente o manualmente e indicando il nome di un evento di sincronizzazione di sistema.</summary>
      <param name="initialState">true per impostare lo stato iniziale su segnalato se l'evento denominato viene creato in seguito a questa chiamata; false per impostare lo stato su non segnalato.</param>
      <param name="mode">Uno dei valori di <see cref="T:System.Threading.EventResetMode" /> che determina se l'evento viene reimpostato automaticamente o manualmente.</param>
      <param name="name">Nome di un evento di sincronizzazione a livello di sistema.</param>
      <exception cref="T:System.IO.IOException">Si è verificato un errore Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'evento denominato esiste e dispone della sicurezza del controllo di accesso, ma l'utente non possiede i diritti <see cref="F:System.Security.AccessControl.EventWaitHandleRights.FullControl" />.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">Non è possibile creare l'evento denominato, probabilmente perché esiste un handle di attesa di diverso tipo con lo stesso nome.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> è di lunghezza superiore a 260 caratteri.</exception>
    </member>
    <member name="M:System.Threading.EventWaitHandle.#ctor(System.Boolean,System.Threading.EventResetMode,System.String,System.Boolean@)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.EventWaitHandle" />, specificando se l'handle di attesa è inizialmente segnalato se creato a seguito di questa chiamata e se la reimpostazione viene eseguita automaticamente o manualmente e indicando il nome di un evento di sincronizzazione di sistema e una variabile Boolean il cui valore dopo la chiamata specifica se l'evento di sistema denominato è stato creato.</summary>
      <param name="initialState">true per impostare lo stato iniziale su segnalato se l'evento denominato viene creato in seguito a questa chiamata; false per impostare lo stato su non segnalato.</param>
      <param name="mode">Uno dei valori di <see cref="T:System.Threading.EventResetMode" /> che determina se l'evento viene reimpostato automaticamente o manualmente.</param>
      <param name="name">Nome di un evento di sincronizzazione a livello di sistema.</param>
      <param name="createdNew">Quando questo metodo viene restituito, contiene true se è stato creato un evento locale (ovvero, se il valore di <paramref name="name" /> è null o una stringa vuota) oppure se è stato creato l'evento di sistema denominato specificato; false se l'evento di sistema denominato specificato è già esistente.Questo parametro viene passato non inizializzato.</param>
      <exception cref="T:System.IO.IOException">Si è verificato un errore Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'evento denominato esiste e dispone della sicurezza del controllo di accesso, ma l'utente non possiede i diritti <see cref="F:System.Security.AccessControl.EventWaitHandleRights.FullControl" />.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">Non è possibile creare l'evento denominato, probabilmente perché esiste un handle di attesa di diverso tipo con lo stesso nome.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> è di lunghezza superiore a 260 caratteri.</exception>
    </member>
    <member name="M:System.Threading.EventWaitHandle.OpenExisting(System.String)">
      <summary>Apre l'evento di sincronizzazione denominato specificato, se esistente.</summary>
      <returns>Oggetto che rappresenta l'evento di sistema denominato.</returns>
      <param name="name">Nome dell'evento di sincronizzazione del sistema da aprire.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> è una stringa vuota. In alternativa<paramref name="name" /> è di lunghezza superiore a 260 caratteri.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> è null.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">L'evento di sistema denominato non esiste.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'evento denominato esiste, ma l'utente non dispone dell'accesso di sicurezza necessario per utilizzarlo.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Threading.EventWaitHandle.Reset">
      <summary>Imposta lo stato dell'evento su non segnalato, provocando il blocco dei thread.</summary>
      <returns>true se l'operazione ha esito positivo; in caso contrario, false.</returns>
      <exception cref="T:System.ObjectDisposedException">Il metodo <see cref="M:System.Threading.EventWaitHandle.Close" /> non è stato chiamato precedentemente in questo oggetto <see cref="T:System.Threading.EventWaitHandle" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.EventWaitHandle.Set">
      <summary>Imposta lo stato dell'evento su segnalato, per consentire a uno o più thread in attesa di procedere.</summary>
      <returns>true se l'operazione ha esito positivo; in caso contrario, false.</returns>
      <exception cref="T:System.ObjectDisposedException">Il metodo <see cref="M:System.Threading.EventWaitHandle.Close" /> non è stato chiamato precedentemente in questo oggetto <see cref="T:System.Threading.EventWaitHandle" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.EventWaitHandle.TryOpenExisting(System.String,System.Threading.EventWaitHandle@)">
      <summary>Apre l'evento di sincronizzazione denominato specificato, se esistente, e restituisce un valore che indica se l'operazione è stata completata.</summary>
      <returns>true se l'evento di sincronizzazione denominato è stato aperto correttamente; in caso contrario, false.</returns>
      <param name="name">Nome dell'evento di sincronizzazione del sistema da aprire.</param>
      <param name="result">Quando viene eseguita la restituzione del metodo, contiene un oggetto di <see cref="T:System.Threading.EventWaitHandle" /> che rappresenta l'evento di sincronizzazione denominato se la chiamata ha esito positivo, o null se la chiamata ha esito negativo.Questo parametro viene trattato come non inizializzato.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> è una stringa vuota.In alternativa<paramref name="name" /> è di lunghezza superiore a 260 caratteri.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> è null.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'evento denominato esiste, ma l'utente non dispone dell'accesso di sicurezza desiderato.</exception>
    </member>
    <member name="T:System.Threading.ExecutionContext">
      <summary>Gestisce il contesto di esecuzione per il thread corrente.La classe non può essere ereditata.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.ExecutionContext.Capture">
      <summary>Acquisisce il contesto di esecuzione dal thread corrente.</summary>
      <returns>Oggetto <see cref="T:System.Threading.ExecutionContext" /> che rappresenta il contesto di esecuzione per il thread corrente.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.ExecutionContext.Run(System.Threading.ExecutionContext,System.Threading.ContextCallback,System.Object)">
      <summary>Esegue un metodo in un contesto di esecuzione specifico sul thread corrente.</summary>
      <param name="executionContext">Oggetto <see cref="T:System.Threading.ExecutionContext" /> da impostare.</param>
      <param name="callback">Delegato <see cref="T:System.Threading.ContextCallback" /> che rappresenta il metodo da eseguire nel contesto di esecuzione fornito.</param>
      <param name="state">Oggetto da passare al metodo di callback.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="executionContext" /> è null.- oppure -<paramref name="executionContext" /> non è stato acquisito tramite un'operazione di acquisizione. - oppure -<paramref name="executionContext" /> è stato già utilizzato come argomento per una chiamata <see cref="M:System.Threading.ExecutionContext.Run(System.Threading.ExecutionContext,System.Threading.ContextCallback,System.Object)" />.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="Infrastructure" />
      </PermissionSet>
    </member>
    <member name="T:System.Threading.Interlocked">
      <summary>Fornisce operazioni atomiche per variabili condivise da più thread. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Add(System.Int32@,System.Int32)">
      <summary>Somma due interi a 32 bit e sostituisce il primo intero con la somma, come operazione atomica.</summary>
      <returns>Nuovo valore archiviato in <paramref name="location1" />.</returns>
      <param name="location1">Variabile contenente il primo valore da sommare.La somma dei due valori viene archiviata in <paramref name="location1" />.</param>
      <param name="value">Valore da sommare all'intero in corrispondenza di <paramref name="location1" />.</param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Add(System.Int64@,System.Int64)">
      <summary>Somma due interi a 64 bit e sostituisce il primo intero con la somma, come operazione atomica.</summary>
      <returns>Nuovo valore archiviato in <paramref name="location1" />.</returns>
      <param name="location1">Variabile contenente il primo valore da sommare.La somma dei due valori viene archiviata in <paramref name="location1" />.</param>
      <param name="value">Valore da sommare all'intero in corrispondenza di <paramref name="location1" />.</param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange(System.Double@,System.Double,System.Double)">
      <summary>Confronta due numeri a virgola mobile e precisione doppia per verificarne l'uguaglianza; se sono uguali, sostituisce il primo valore.</summary>
      <returns>Valore originale in <paramref name="location1" />.</returns>
      <param name="location1">Destinazione, il cui valore viene confrontato con <paramref name="comparand" /> e, se possibile, sostituito. </param>
      <param name="value">Valore che sostituisce il valore di destinazione se il confronto rileva l'uguaglianza. </param>
      <param name="comparand">Valore confrontato con il valore in corrispondenza di <paramref name="location1" />. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange(System.Int32@,System.Int32,System.Int32)">
      <summary>Confronta due interi con segno a 32 bit per verificarne l'uguaglianza; se sono uguali, sostituisce il primo valore.</summary>
      <returns>Valore originale in <paramref name="location1" />.</returns>
      <param name="location1">Destinazione, il cui valore viene confrontato con <paramref name="comparand" /> e, se possibile, sostituito. </param>
      <param name="value">Valore che sostituisce il valore di destinazione se il confronto rileva l'uguaglianza. </param>
      <param name="comparand">Valore confrontato con il valore in corrispondenza di <paramref name="location1" />. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange(System.Int64@,System.Int64,System.Int64)">
      <summary>Confronta due interi con segno a 64 bit per verificarne l'uguaglianza; se sono uguali, sostituisce il primo valore.</summary>
      <returns>Valore originale in <paramref name="location1" />.</returns>
      <param name="location1">Destinazione, il cui valore viene confrontato con <paramref name="comparand" /> e, se possibile, sostituito. </param>
      <param name="value">Valore che sostituisce il valore di destinazione se il confronto rileva l'uguaglianza. </param>
      <param name="comparand">Valore confrontato con il valore in corrispondenza di <paramref name="location1" />. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange(System.IntPtr@,System.IntPtr,System.IntPtr)">
      <summary>Confronta due puntatori o handle specifici della piattaforma per verificarne l'uguaglianza; se sono uguali, sostituisce il primo elemento.</summary>
      <returns>Valore originale in <paramref name="location1" />.</returns>
      <param name="location1">Oggetto <see cref="T:System.IntPtr" /> di destinazione, il cui valore viene confrontato con il valore di <paramref name="comparand" /> e, se possibile, sostituito da <paramref name="value" />. </param>
      <param name="value">Oggetto <see cref="T:System.IntPtr" /> che sostituisce il valore di destinazione se il confronto rileva l'uguaglianza. </param>
      <param name="comparand">Oggetto <see cref="T:System.IntPtr" /> confrontato con il valore in corrispondenza di <paramref name="location1" />. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange(System.Object@,System.Object,System.Object)">
      <summary>Confronta due oggetti per verificarne l'uguaglianza dei riferimenti; se sono uguali, sostituisce il primo oggetto.</summary>
      <returns>Valore originale in <paramref name="location1" />.</returns>
      <param name="location1">Oggetto di destinazione confrontato con <paramref name="comparand" /> e, se possibile, sostituito. </param>
      <param name="value">Oggetto che sostituisce l'oggetto di destinazione se il confronto rileva l'uguaglianza. </param>
      <param name="comparand">Oggetto confrontato con l'oggetto in corrispondenza di <paramref name="location1" />. </param>
      <exception cref="T:System.ArgumentNullException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange(System.Single@,System.Single,System.Single)">
      <summary>Confronta due numeri a virgola mobile e precisione singola per verificarne l'uguaglianza; se sono uguali, sostituisce il primo valore.</summary>
      <returns>Valore originale in <paramref name="location1" />.</returns>
      <param name="location1">Destinazione, il cui valore viene confrontato con <paramref name="comparand" /> e, se possibile, sostituito. </param>
      <param name="value">Valore che sostituisce il valore di destinazione se il confronto rileva l'uguaglianza. </param>
      <param name="comparand">Valore confrontato con il valore in corrispondenza di <paramref name="location1" />. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.CompareExchange``1(``0@,``0,``0)">
      <summary>Confronta due istanze del tipo di riferimento <paramref name="T" /> specificato per verificarne l'uguaglianza; se sono uguali, sostituisce la prima istanza.</summary>
      <returns>Valore originale in <paramref name="location1" />.</returns>
      <param name="location1">Destinazione, il cui valore viene confrontato con <paramref name="comparand" /> e, se possibile, sostituito.Rappresenta un parametro di riferimento (ref in C#, ByRef in Visual Basic).</param>
      <param name="value">Valore che sostituisce il valore di destinazione se il confronto rileva l'uguaglianza. </param>
      <param name="comparand">Valore confrontato con il valore in corrispondenza di <paramref name="location1" />. </param>
      <typeparam name="T">Tipo da usare per <paramref name="location1" />, <paramref name="value" /> e <paramref name="comparand" />.Questo tipo deve essere un tipo di riferimento.</typeparam>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
    </member>
    <member name="M:System.Threading.Interlocked.Decrement(System.Int32@)">
      <summary>Diminuisce una variabile specificata e archivia il risultato, come operazione atomica.</summary>
      <returns>Valore diminuito.</returns>
      <param name="location">Variabile il cui valore deve essere diminuito. </param>
      <exception cref="T:System.ArgumentNullException">The address of <paramref name="location" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Decrement(System.Int64@)">
      <summary>Diminuisce la variabile specificata e archivia il risultato, come operazione atomica.</summary>
      <returns>Valore diminuito.</returns>
      <param name="location">Variabile il cui valore deve essere diminuito. </param>
      <exception cref="T:System.ArgumentNullException">The address of <paramref name="location" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange(System.Double@,System.Double)">
      <summary>Imposta un numero a virgola mobile e precisione doppia su un valore specificato e restituisce il valore originale, come operazione atomica.</summary>
      <returns>Valore originale di <paramref name="location1" />.</returns>
      <param name="location1">Variabile da impostare sul valore specificato. </param>
      <param name="value">Valore su cui è impostato il parametro <paramref name="location1" />. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange(System.Int32@,System.Int32)">
      <summary>Imposta un intero con segno a 32 bit su un valore specificato e restituisce il valore originale, come operazione atomica.</summary>
      <returns>Valore originale di <paramref name="location1" />.</returns>
      <param name="location1">Variabile da impostare sul valore specificato. </param>
      <param name="value">Valore su cui è impostato il parametro <paramref name="location1" />. </param>
      <exception cref="T:System.ArgumentNullException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange(System.Int64@,System.Int64)">
      <summary>Imposta un intero con segno a 64 bit su un valore specificato e restituisce il valore originale, come operazione atomica.</summary>
      <returns>Valore originale di <paramref name="location1" />.</returns>
      <param name="location1">Variabile da impostare sul valore specificato. </param>
      <param name="value">Valore su cui è impostato il parametro <paramref name="location1" />. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange(System.IntPtr@,System.IntPtr)">
      <summary>Imposta un puntatore o un handle specifico della piattaforma su un valore specificato e restituisce il valore originale, come operazione atomica.</summary>
      <returns>Valore originale di <paramref name="location1" />.</returns>
      <param name="location1">Variabile da impostare sul valore specificato. </param>
      <param name="value">Valore su cui è impostato il parametro <paramref name="location1" />. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange(System.Object@,System.Object)">
      <summary>Imposta un oggetto su un valore specificato e restituisce un riferimento all'oggetto originale, come operazione atomica.</summary>
      <returns>Valore originale di <paramref name="location1" />.</returns>
      <param name="location1">Variabile da impostare sul valore specificato. </param>
      <param name="value">Valore su cui è impostato il parametro <paramref name="location1" />. </param>
      <exception cref="T:System.ArgumentNullException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange(System.Single@,System.Single)">
      <summary>Imposta un numero a virgola mobile e precisione singola su un valore specificato e restituisce il valore originale, come operazione atomica.</summary>
      <returns>Valore originale di <paramref name="location1" />.</returns>
      <param name="location1">Variabile da impostare sul valore specificato. </param>
      <param name="value">Valore su cui è impostato il parametro <paramref name="location1" />. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Exchange``1(``0@,``0)">
      <summary>Imposta una variabile del tipo <paramref name="T" /> indicato sul valore specificato e restituisce il valore originale, come operazione atomica.</summary>
      <returns>Valore originale di <paramref name="location1" />.</returns>
      <param name="location1">Variabile da impostare sul valore specificato.Rappresenta un parametro di riferimento (ref in C#, ByRef in Visual Basic).</param>
      <param name="value">Valore su cui è impostato il parametro <paramref name="location1" />. </param>
      <typeparam name="T">Tipo da usare per <paramref name="location1" /> e <paramref name="value" />.Questo tipo deve essere un tipo di riferimento.</typeparam>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location1" /> is a null pointer. </exception>
    </member>
    <member name="M:System.Threading.Interlocked.Increment(System.Int32@)">
      <summary>Aumenta una variabile specificata e archivia il risultato, come operazione atomica.</summary>
      <returns>Valore aumentato.</returns>
      <param name="location">Variabile il cui valore deve essere aumentato. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.Increment(System.Int64@)">
      <summary>Aumenta una variabile specificata e archivia il risultato, come operazione atomica.</summary>
      <returns>Valore aumentato.</returns>
      <param name="location">Variabile il cui valore deve essere aumentato. </param>
      <exception cref="T:System.NullReferenceException">The address of <paramref name="location" /> is a null pointer. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Interlocked.MemoryBarrier">
      <summary>Sincronizza l'accesso alla memoria come segue: il processore che esegue il thread corrente non può riordinare le istruzioni in modo tale che gli accessi alla memoria prima della chiamata al metodo <see cref="M:System.Threading.Interlocked.MemoryBarrier" /> vengano eseguiti dopo quelli successivi alla chiamata al metodo <see cref="M:System.Threading.Interlocked.MemoryBarrier" />.</summary>
    </member>
    <member name="M:System.Threading.Interlocked.Read(System.Int64@)">
      <summary>Restituisce un valore a 64 bit, caricato come operazione atomica.</summary>
      <returns>Valore caricato.</returns>
      <param name="location">Valore a 64 bit da caricare.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.LazyInitializer">
      <summary>Fornisce routine di inizializzazione differita.</summary>
    </member>
    <member name="M:System.Threading.LazyInitializer.EnsureInitialized``1(``0@)">
      <summary>Inizializza un tipo di riferimento di destinazione con il relativo costruttore predefinito se non è già stato inizializzato.</summary>
      <returns>Riferimento inizializzato di tipo <paramref name="T" />.</returns>
      <param name="target">Riferimento di tipo <paramref name="T" /> da inizializzare se non è già stato inizializzato.</param>
      <typeparam name="T">Tipo del riferimento da inizializzare.</typeparam>
      <exception cref="T:System.MemberAccessException">Le autorizzazioni per accedere al costruttore di tipo <paramref name="T" /> erano mancanti.</exception>
      <exception cref="T:System.MissingMemberException">Il tipo <paramref name="T" /> non dispone di un costruttore predefinito.</exception>
    </member>
    <member name="M:System.Threading.LazyInitializer.EnsureInitialized``1(``0@,System.Boolean@,System.Object@)">
      <summary>Inizializza un tipo di riferimento o di valore di destinazione con il relativo costruttore predefinito se non è già stato inizializzato.</summary>
      <returns>Valore inizializzato di tipo <paramref name="T" />.</returns>
      <param name="target">Riferimento o valore di tipo <paramref name="T" /> da inizializzare se non è già stato inizializzato.</param>
      <param name="initialized">Riferimento a un valore booleano che determina se la destinazione è già stata inizializzata.</param>
      <param name="syncLock">Riferimento a un oggetto utilizzato come blocco a esclusione reciproca per l'inizializzazione di <paramref name="target" />.Se <paramref name="syncLock" /> è null, verrà creata un'istanza di un nuovo oggetto.</param>
      <typeparam name="T">Tipo del riferimento da inizializzare.</typeparam>
      <exception cref="T:System.MemberAccessException">Le autorizzazioni per accedere al costruttore di tipo <paramref name="T" /> erano mancanti.</exception>
      <exception cref="T:System.MissingMemberException">Il tipo <paramref name="T" /> non dispone di un costruttore predefinito.</exception>
    </member>
    <member name="M:System.Threading.LazyInitializer.EnsureInitialized``1(``0@,System.Boolean@,System.Object@,System.Func{``0})">
      <summary>Inizializza un tipo di riferimento o di valore di destinazione utilizzando una funzione specificata se non è già stato inizializzato.</summary>
      <returns>Valore inizializzato di tipo <paramref name="T" />.</returns>
      <param name="target">Riferimento o valore di tipo <paramref name="T" /> da inizializzare se non è già stato inizializzato.</param>
      <param name="initialized">Riferimento a un valore booleano che determina se la destinazione è già stata inizializzata.</param>
      <param name="syncLock">Riferimento a un oggetto utilizzato come blocco a esclusione reciproca per l'inizializzazione di <paramref name="target" />.Se <paramref name="syncLock" /> è null, verrà creata un'istanza di un nuovo oggetto.</param>
      <param name="valueFactory">Funzione chiamata per inizializzare il riferimento o il valore.</param>
      <typeparam name="T">Tipo del riferimento da inizializzare.</typeparam>
      <exception cref="T:System.MemberAccessException">Le autorizzazioni per accedere al costruttore di tipo <paramref name="T" /> erano mancanti.</exception>
      <exception cref="T:System.MissingMemberException">Il tipo <paramref name="T" /> non dispone di un costruttore predefinito.</exception>
    </member>
    <member name="M:System.Threading.LazyInitializer.EnsureInitialized``1(``0@,System.Func{``0})">
      <summary>Inizializza un tipo di riferimento di destinazione utilizzando una funzione specificata se non è già stato inizializzato.</summary>
      <returns>Valore inizializzato di tipo <paramref name="T" />.</returns>
      <param name="target">Riferimento di tipo <paramref name="T" /> da inizializzare se non è già stato inizializzato.</param>
      <param name="valueFactory">Funzione chiamata per inizializzare il riferimento.</param>
      <typeparam name="T">Tipo del riferimento da inizializzare.</typeparam>
      <exception cref="T:System.MissingMemberException">Il tipo <paramref name="T" /> non dispone di un costruttore predefinito.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="valueFactory" /> restituisce null (Nothing in Visual Basic).</exception>
    </member>
    <member name="T:System.Threading.LockRecursionException">
      <summary>Eccezione generata quando una voce ricorsiva in un blocco non è compatibile con i criteri di ricorsione per tale blocco.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.LockRecursionException.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.LockRecursionException" /> con un messaggio fornito dal sistema in cui viene descritto l'errore.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.LockRecursionException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.LockRecursionException" /> con un messaggio specifico in cui viene descritto l'errore.</summary>
      <param name="message">Messaggio in cui viene descritta l'eccezione.Il chiamante di questo costruttore deve assicurare che la stringa sia stata localizzata in base alle impostazioni cultura correnti del sistema.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.LockRecursionException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.LockRecursionException" /> con un messaggio di errore specificato e un riferimento all'eccezione interna che è la causa dell'eccezione corrente.</summary>
      <param name="message">Messaggio in cui viene descritta l'eccezione.Il chiamante di questo costruttore deve assicurare che la stringa sia stata localizzata in base alle impostazioni cultura correnti del sistema.</param>
      <param name="innerException">Eccezione che ha causato l'eccezione corrente.Se il parametro <paramref name="innerException" /> non è null, l'eccezione corrente viene generata in un blocco catch in cui viene gestita l'eccezione interna.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Threading.LockRecursionPolicy">
      <summary>Specifica se lo stesso thread può accedere a un blocco più volte.</summary>
    </member>
    <member name="F:System.Threading.LockRecursionPolicy.NoRecursion">
      <summary>Se un thread tenta di accedere a un blocco in modo ricorsivo, viene generata un'eccezione.È possibile che alcune classi consentano particolari ricorsioni quando questa impostazione è attivata.</summary>
    </member>
    <member name="F:System.Threading.LockRecursionPolicy.SupportsRecursion">
      <summary>Un thread può accedere a un blocco in modo ricorsivo.Alcune classi possono limitare questa funzionalità.</summary>
    </member>
    <member name="T:System.Threading.ManualResetEvent">
      <summary>Notifica a uno o più thread in attesa che si è verificato un evento.La classe non può essere ereditata.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.ManualResetEvent.#ctor(System.Boolean)">
      <summary>Consente l'inizializzazione di una nuova istanza della classe <see cref="T:System.Threading.ManualResetEvent" /> con un valore Booleano che indica se lo stato iniziale deve essere impostato su segnalato.</summary>
      <param name="initialState">Viene restituito true per impostare lo stato iniziale su segnalato; false per impostare lo stato iniziale su non segnalato. </param>
    </member>
    <member name="T:System.Threading.ManualResetEventSlim">
      <summary>Fornisce una versione più snella di <see cref="T:System.Threading.ManualResetEvent" />.</summary>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.ManualResetEventSlim" /> con uno stato iniziale di non segnalato.</summary>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.#ctor(System.Boolean)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.ManualResetEventSlim" /> con un valore booleano che indica se impostare lo stato iniziale su segnalato.</summary>
      <param name="initialState">true per impostare lo stato iniziale su segnalato; false per impostarlo su non segnalato.</param>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.#ctor(System.Boolean,System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.ManualResetEventSlim" /> con un valore booleano che indica se impostare lo stato iniziale su segnalato e un conteggio rotazioni specificato.</summary>
      <param name="initialState">true per impostare lo stato iniziale su segnalato; false per impostarlo su non segnalato.</param>
      <param name="spinCount">Numero di attese di rotazione che devono verificarsi prima di eseguire il fallback su un'operazione di attesa basata sul kernel.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="spinCount" /> is less than 0 or greater than the maximum allowed value.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Dispose">
      <summary>Rilascia tutte le risorse usate dall'istanza corrente della classe <see cref="T:System.Threading.ManualResetEventSlim" />.</summary>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Dispose(System.Boolean)">
      <summary>Rilascia le risorse non gestite usate dall'oggetto <see cref="T:System.Threading.ManualResetEventSlim" /> e facoltativamente rilascia le risorse gestite.</summary>
      <param name="disposing">True per rilasciare sia le risorse gestite sia quelle non gestite; false per rilasciare solo le risorse non gestite.</param>
    </member>
    <member name="P:System.Threading.ManualResetEventSlim.IsSet">
      <summary>Ottiene un valore che indica se l'evento è impostato.</summary>
      <returns>true se l'evento è impostato; in caso contrario, false.</returns>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Reset">
      <summary>Imposta lo stato dell'evento su non segnalato, provocando il blocco dei thread.</summary>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Set">
      <summary>Imposta lo stato dell'evento su segnalato, per consentire a uno o più thread in attesa dell'evento di procedere.</summary>
    </member>
    <member name="P:System.Threading.ManualResetEventSlim.SpinCount">
      <summary>Ottiene il numero di attese di rotazione che si verificheranno prima di eseguire il fallback su un'operazione di attesa basata sul kernel.</summary>
      <returns>Restituisce il numero di attese di rotazione che si verificheranno prima di eseguire il fallback su un'operazione di attesa basata sul kernel.</returns>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Wait">
      <summary>Blocca il thread corrente finché l'oggetto <see cref="T:System.Threading.ManualResetEventSlim" /> corrente non viene impostato.</summary>
      <exception cref="T:System.InvalidOperationException">The maximum number of waiters has been exceeded.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Wait(System.Int32)">
      <summary>Blocca il thread corrente finché l'oggetto <see cref="T:System.Threading.ManualResetEventSlim" /> corrente non viene impostato, usando un intero con segno a 32 bit per misurare l'intervallo di tempo.</summary>
      <returns>true se l'oggetto <see cref="T:System.Threading.ManualResetEventSlim" /> è stato impostato; in caso contrario, false.</returns>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure <see cref="F:System.Threading.Timeout.Infinite" /> (-1) per un'attesa indefinita.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.InvalidOperationException">The maximum number of waiters has been exceeded.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Wait(System.Int32,System.Threading.CancellationToken)">
      <summary>Blocca il thread corrente finché l'oggetto <see cref="T:System.Threading.ManualResetEventSlim" /> corrente non viene impostato, usando un intero con segno a 32 bit per misurare l'intervallo di tempo e osservando un oggetto <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>true se l'oggetto <see cref="T:System.Threading.ManualResetEventSlim" /> è stato impostato; in caso contrario, false.</returns>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure <see cref="F:System.Threading.Timeout.Infinite" /> (-1) per un'attesa indefinita.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> da osservare.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.InvalidOperationException">The maximum number of waiters has been exceeded.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed or the <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Wait(System.Threading.CancellationToken)">
      <summary>Blocca il thread corrente finché l'oggetto <see cref="T:System.Threading.ManualResetEventSlim" /> corrente non riceve un segnale, osservando un oggetto <see cref="T:System.Threading.CancellationToken" />.</summary>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> da osservare.</param>
      <exception cref="T:System.InvalidOperationException">The maximum number of waiters has been exceeded.</exception>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed or the <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Wait(System.TimeSpan)">
      <summary>Blocca il thread corrente finché l'oggetto <see cref="T:System.Threading.ManualResetEventSlim" /> corrente non viene impostato, usando un oggetto <see cref="T:System.TimeSpan" /> per misurare l'intervallo di tempo.</summary>
      <returns>true se l'oggetto <see cref="T:System.Threading.ManualResetEventSlim" /> è stato impostato; in caso contrario, false.</returns>
      <param name="timeout">Oggetto <see cref="T:System.TimeSpan" /> che rappresenta il numero di millisecondi di attesa oppure <see cref="T:System.TimeSpan" /> che rappresenta -1 millisecondi per un'attesa indefinita.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-The number of milliseconds in <paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.InvalidOperationException">The maximum number of waiters has been exceeded.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.ManualResetEventSlim.Wait(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Blocca il thread corrente finché l'oggetto <see cref="T:System.Threading.ManualResetEventSlim" /> corrente non viene impostato, usando un oggetto <see cref="T:System.TimeSpan" /> per misurare l'intervallo di tempo e osservando un oggetto <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>true se l'oggetto <see cref="T:System.Threading.ManualResetEventSlim" /> è stato impostato; in caso contrario, false.</returns>
      <param name="timeout">Oggetto <see cref="T:System.TimeSpan" /> che rappresenta il numero di millisecondi di attesa oppure <see cref="T:System.TimeSpan" /> che rappresenta -1 millisecondi per un'attesa indefinita.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> da osservare.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-The number of milliseconds in <paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.InvalidOperationException">The maximum number of waiters has been exceeded. </exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed or the <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.ManualResetEventSlim.WaitHandle">
      <summary>Ottiene l'oggetto <see cref="T:System.Threading.WaitHandle" /> sottostante per questo oggetto <see cref="T:System.Threading.ManualResetEventSlim" />.</summary>
      <returns>Oggetto evento <see cref="T:System.Threading.WaitHandle" /> sottostante per questo oggetto <see cref="T:System.Threading.ManualResetEventSlim" />.</returns>
    </member>
    <member name="T:System.Threading.Monitor">
      <summary>Fornisce un meccanismo che sincronizza l'accesso agli oggetti.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.Enter(System.Object)">
      <summary>Acquisisce un blocco esclusivo sull'oggetto specificato.</summary>
      <param name="obj">Oggetto sui cui acquisire il blocco del monitoraggio. </param>
      <exception cref="T:System.ArgumentNullException">Il valore del parametro <paramref name="obj" /> è null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.Enter(System.Object,System.Boolean@)">
      <summary>Acquisisce un blocco esclusivo sull'oggetto specificato e imposta atomicamente un valore che indica se il blocco è stato ottenuto.</summary>
      <param name="obj">Oggetto per il quale attendere. </param>
      <param name="lockTaken">Risultato del tentativo di acquisizione del blocco passato dal riferimento.L'input deve essere false.L'output è true se il blocco viene acquisito; in caso contrario, l'output è false.L'output viene impostato anche se si verifica un'eccezione durante il tentativo di acquisire il blocco.Nota   Se non si verifica alcuna eccezione, l'output di questo metodo è sempre true.</param>
      <exception cref="T:System.ArgumentException">L'input di <paramref name="lockTaken" /> è true.</exception>
      <exception cref="T:System.ArgumentNullException">Il valore del parametro <paramref name="obj" /> è null. </exception>
    </member>
    <member name="M:System.Threading.Monitor.Exit(System.Object)">
      <summary>Viene rilasciato un blocco esclusivo sull'oggetto specificato.</summary>
      <param name="obj">Oggetto sul quale rilasciare il blocco. </param>
      <exception cref="T:System.ArgumentNullException">Il valore del parametro <paramref name="obj" /> è null. </exception>
      <exception cref="T:System.Threading.SynchronizationLockException">Il blocco per l'oggetto specificato non è di proprietà del thread corrente. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.IsEntered(System.Object)">
      <summary>Determina se il thread corrente specificato contiene il blocco sull'oggetto specificato. </summary>
      <returns>true se il thread corrente è responsabile del blocco su <paramref name="obj" />; in caso contrario, false.</returns>
      <param name="obj">Oggetto da testare. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="obj" /> è null. </exception>
    </member>
    <member name="M:System.Threading.Monitor.Pulse(System.Object)">
      <summary>Notifica a un thread della coda di attesa che lo stato dell'oggetto bloccato è stato modificato.</summary>
      <param name="obj">Oggetto atteso da un thread. </param>
      <exception cref="T:System.ArgumentNullException">Il valore del parametro <paramref name="obj" /> è null. </exception>
      <exception cref="T:System.Threading.SynchronizationLockException">Il thread chiamante non è il proprietario del blocco per l'oggetto specificato. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.PulseAll(System.Object)">
      <summary>Notifica a tutti i thread in attesa che lo stato dell'oggetto è stato modificato.</summary>
      <param name="obj">Oggetto che invia l'impulso. </param>
      <exception cref="T:System.ArgumentNullException">Il valore del parametro <paramref name="obj" /> è null. </exception>
      <exception cref="T:System.Threading.SynchronizationLockException">Il thread chiamante non è il proprietario del blocco per l'oggetto specificato. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.TryEnter(System.Object)">
      <summary>Prova ad acquisire un blocco esclusivo sull'oggetto specificato.</summary>
      <returns>true se il thread corrente acquisisce il blocco; in caso contrario, false.</returns>
      <param name="obj">Oggetto sul quale acquisire il blocco. </param>
      <exception cref="T:System.ArgumentNullException">Il valore del parametro <paramref name="obj" /> è null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.TryEnter(System.Object,System.Boolean@)">
      <summary>Prova ad acquisire un blocco esclusivo sull'oggetto specificato e imposta atomicamente un valore che indica se il blocco è stato ottenuto.</summary>
      <param name="obj">Oggetto sul quale acquisire il blocco. </param>
      <param name="lockTaken">Risultato del tentativo di acquisizione del blocco passato dal riferimento.L'input deve essere false.L'output è true se il blocco viene acquisito; in caso contrario, l'output è false.L'output viene impostato anche se si verifica un'eccezione durante il tentativo di acquisire il blocco.</param>
      <exception cref="T:System.ArgumentException">L'input di <paramref name="lockTaken" /> è true.</exception>
      <exception cref="T:System.ArgumentNullException">Il valore del parametro <paramref name="obj" /> è null. </exception>
    </member>
    <member name="M:System.Threading.Monitor.TryEnter(System.Object,System.Int32)">
      <summary>Viene eseguito, per un numero specificato di millisecondi, il tentativo di acquisire un blocco esclusivo sull'oggetto specificato.</summary>
      <returns>true se il thread corrente acquisisce il blocco; in caso contrario, false.</returns>
      <param name="obj">Oggetto sul quale acquisire il blocco. </param>
      <param name="millisecondsTimeout">Tempo di attesa espresso in millisecondi prima che si verifichi il blocco. </param>
      <exception cref="T:System.ArgumentNullException">Il valore del parametro <paramref name="obj" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> è negativo e non è uguale a <see cref="F:System.Threading.Timeout.Infinite" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.TryEnter(System.Object,System.Int32,System.Boolean@)">
      <summary>Prova ad acquisire, per il numero di millisecondi specificato, un blocco esclusivo sull'oggetto specificato e imposta atomicamente un valore che indica se il blocco è stato ottenuto.</summary>
      <param name="obj">Oggetto sul quale acquisire il blocco. </param>
      <param name="millisecondsTimeout">Tempo di attesa espresso in millisecondi prima che si verifichi il blocco. </param>
      <param name="lockTaken">Risultato del tentativo di acquisizione del blocco passato dal riferimento.L'input deve essere false.L'output è true se il blocco viene acquisito; in caso contrario, l'output è false.L'output viene impostato anche se si verifica un'eccezione durante il tentativo di acquisire il blocco.</param>
      <exception cref="T:System.ArgumentException">L'input di <paramref name="lockTaken" /> è true.</exception>
      <exception cref="T:System.ArgumentNullException">Il valore del parametro <paramref name="obj" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> è negativo e non è uguale a <see cref="F:System.Threading.Timeout.Infinite" />. </exception>
    </member>
    <member name="M:System.Threading.Monitor.TryEnter(System.Object,System.TimeSpan)">
      <summary>Viene eseguito, per una quantità di tempo specificata, il tentativo di acquisire un blocco esclusivo sull'oggetto specificato.</summary>
      <returns>true se il thread corrente acquisisce il blocco; in caso contrario, false.</returns>
      <param name="obj">Oggetto sul quale acquisire il blocco. </param>
      <param name="timeout">Oggetto <see cref="T:System.TimeSpan" /> che rappresenta la durata di attesa del blocco.Un valore di –1 millisecondo specifica un'attesa infinita.</param>
      <exception cref="T:System.ArgumentNullException">Il valore del parametro <paramref name="obj" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il valore di <paramref name="timeout" /> in millisecondi è negativo ed è diverso da <see cref="F:System.Threading.Timeout.Infinite" /> (–1 millisecondi) oppure è maggiore di <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.TryEnter(System.Object,System.TimeSpan,System.Boolean@)">
      <summary>Prova ad acquisire, per la quantità di tempo specificata, un blocco esclusivo sull'oggetto specificato e imposta atomicamente un valore che indica se il blocco è stato ottenuto.</summary>
      <param name="obj">Oggetto sul quale acquisire il blocco. </param>
      <param name="timeout">Quantità di tempo che rappresenta la durata di attesa del blocco.Un valore di –1 millisecondo specifica un'attesa infinita.</param>
      <param name="lockTaken">Risultato del tentativo di acquisizione del blocco passato dal riferimento.L'input deve essere false.L'output è true se il blocco viene acquisito; in caso contrario, l'output è false.L'output viene impostato anche se si verifica un'eccezione durante il tentativo di acquisire il blocco.</param>
      <exception cref="T:System.ArgumentException">L'input di <paramref name="lockTaken" /> è true.</exception>
      <exception cref="T:System.ArgumentNullException">Il valore del parametro <paramref name="obj" /> è null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il valore di <paramref name="timeout" /> in millisecondi è negativo ed è diverso da <see cref="F:System.Threading.Timeout.Infinite" /> (–1 millisecondi) oppure è maggiore di <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Threading.Monitor.Wait(System.Object)">
      <summary>Rilascia il blocco su un oggetto e interrompe il thread corrente finché riacquisisce il blocco.</summary>
      <returns>true se la chiamata è stata restituita perché il chiamante ha riacquisito il blocco per l'oggetto specificato.Non viene restituito alcun valore se il blocco non viene riacquisito.</returns>
      <param name="obj">Oggetto per il quale attendere. </param>
      <exception cref="T:System.ArgumentNullException">Il valore del parametro <paramref name="obj" /> è null. </exception>
      <exception cref="T:System.Threading.SynchronizationLockException">Il thread chiamante non è il proprietario del blocco per l'oggetto specificato. </exception>
      <exception cref="T:System.Threading.ThreadInterruptedException">Il thread da cui è stato richiamato Wait viene interrotto in seguito dallo stato di attesa.L'interruzione si verifica quando il metodo <see cref="M:System.Threading.Thread.Interrupt" /> di questo thread viene chiamato da un altro thread.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.Wait(System.Object,System.Int32)">
      <summary>Rilascia il blocco su un oggetto e interrompe il thread corrente finché riacquisisce il blocco.Allo scadere dell'intervallo di timeout specificato, il thread viene inserito nella coda di thread pronti.</summary>
      <returns>true se il blocco è stato riacquisito prima che sia trascorso il tempo specificato; false se il blocco è stato riacquisito dopo che è trascorso il tempo specificato.Il metodo non restituisce alcun valore finché il blocco non viene riacquisito.</returns>
      <param name="obj">Oggetto per il quale attendere. </param>
      <param name="millisecondsTimeout">Numero di millisecondi da attendere prima che il thread venga inserito nella coda di thread pronti. </param>
      <exception cref="T:System.ArgumentNullException">Il valore del parametro <paramref name="obj" /> è null. </exception>
      <exception cref="T:System.Threading.SynchronizationLockException">Il thread chiamante non è il proprietario del blocco per l'oggetto specificato. </exception>
      <exception cref="T:System.Threading.ThreadInterruptedException">Il thread da cui è stato richiamato Wait viene interrotto in seguito dallo stato di attesa.L'interruzione si verifica quando il metodo <see cref="M:System.Threading.Thread.Interrupt" /> di questo thread viene chiamato da un altro thread.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il valore del parametro <paramref name="millisecondsTimeout" /> è negativo e non è uguale a <see cref="F:System.Threading.Timeout.Infinite" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Monitor.Wait(System.Object,System.TimeSpan)">
      <summary>Rilascia il blocco su un oggetto e interrompe il thread corrente finché riacquisisce il blocco.Allo scadere dell'intervallo di timeout specificato, il thread viene inserito nella coda di thread pronti.</summary>
      <returns>true se il blocco è stato riacquisito prima che sia trascorso il tempo specificato; false se il blocco è stato riacquisito dopo che è trascorso il tempo specificato.Il metodo non restituisce alcun valore finché il blocco non viene riacquisito.</returns>
      <param name="obj">Oggetto per il quale attendere. </param>
      <param name="timeout">Oggetto <see cref="T:System.TimeSpan" /> che rappresenta il tempo di attesa prima che il thread venga inserito nella coda di thread pronti. </param>
      <exception cref="T:System.ArgumentNullException">Il valore del parametro <paramref name="obj" /> è null. </exception>
      <exception cref="T:System.Threading.SynchronizationLockException">Il thread chiamante non è il proprietario del blocco per l'oggetto specificato. </exception>
      <exception cref="T:System.Threading.ThreadInterruptedException">Il thread da cui è stato richiamato Wait viene interrotto in seguito dallo stato di attesa.L'interruzione si verifica quando il metodo <see cref="M:System.Threading.Thread.Interrupt" /> di questo thread viene chiamato da un altro thread.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Il valore del parametro <paramref name="timeout" /> in millisecondi è negativo e non rappresenta <see cref="F:System.Threading.Timeout.Infinite" /> (–1 millisecondo) oppure è maggiore di <see cref="F:System.Int32.MaxValue" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Threading.Mutex">
      <summary>Primitiva di sincronizzazione che può essere usata anche per la sincronizzazione interprocesso. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Mutex.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Mutex" /> con le proprietà predefinite.</summary>
    </member>
    <member name="M:System.Threading.Mutex.#ctor(System.Boolean)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Mutex" /> con un valore booleano che indica se il thread chiamante deve avere la proprietà iniziale del mutex.</summary>
      <param name="initiallyOwned">true per concedere al thread chiamante la proprietà iniziale del mutex; in caso contrario, false. </param>
    </member>
    <member name="M:System.Threading.Mutex.#ctor(System.Boolean,System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Mutex" /> con un valore booleano che indica se il thread chiamante deve avere la proprietà iniziale del mutex e con una stringa che rappresenta il nome del mutex.</summary>
      <param name="initiallyOwned">true per concedere al thread chiamante la proprietà iniziale del mutex di sistema denominato, se questo è stato creato come risultato della chiamata; in caso contrario, false. </param>
      <param name="name">Nome di <see cref="T:System.Threading.Mutex" />.Se il valore è null, l'oggetto <see cref="T:System.Threading.Mutex" /> è senza nome.</param>
      <exception cref="T:System.UnauthorizedAccessException">Il mutex denominato esiste e dispone della sicurezza del controllo di accesso, ma l'utente non dispone dei diritti <see cref="F:System.Security.AccessControl.MutexRights.FullControl" />.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore Win32.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">Non è possibile creare il mutex denominato, probabilmente perché esiste un handle di attesa di diverso tipo con lo stesso nome.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> è più lungo di 260 caratteri.</exception>
    </member>
    <member name="M:System.Threading.Mutex.#ctor(System.Boolean,System.String,System.Boolean@)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Mutex" /> con un valore booleano che indica se il thread chiamante deve avere la proprietà iniziale del mutex, con una stringa che rappresenta il nome del mutex e con un valore booleano che, quando il metodo viene restituito, indichi se al thread chiamante era stata concessa la proprietà iniziale del mutex.</summary>
      <param name="initiallyOwned">true per concedere al thread chiamante la proprietà iniziale del mutex di sistema denominato, se questo è stato creato come risultato della chiamata; in caso contrario, false. </param>
      <param name="name">Nome di <see cref="T:System.Threading.Mutex" />.Se il valore è null, l'oggetto <see cref="T:System.Threading.Mutex" /> è senza nome.</param>
      <param name="createdNew">Quando questo metodo viene restituito, contiene un valore booleano che è true se è stato creato un mutex locale (ovvero, se il valore di <paramref name="name" /> è null o una stringa vuota) oppure se è stato creato il mutex di sistema denominato specificato; false se il mutex di sistema denominato specificato è già esistente.Questo parametro viene passato non inizializzato.</param>
      <exception cref="T:System.UnauthorizedAccessException">Il mutex denominato esiste e dispone della sicurezza del controllo di accesso, ma l'utente non dispone dei diritti <see cref="F:System.Security.AccessControl.MutexRights.FullControl" />.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore Win32.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">Non è possibile creare il mutex denominato, probabilmente perché esiste un handle di attesa di diverso tipo con lo stesso nome.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> è più lungo di 260 caratteri.</exception>
    </member>
    <member name="M:System.Threading.Mutex.OpenExisting(System.String)">
      <summary>Apre il mutex denominato specificato, se esistente.</summary>
      <returns>Oggetto che rappresenta il mutex di sistema denominato.</returns>
      <param name="name">Nome del mutex di sistema da aprire.</param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="name" /> è una stringa vuota.-oppure-<paramref name="name" /> è più lungo di 260 caratteri.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> è null.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">Il mutex denominato non esiste.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il mutex denominato esiste, ma l'utente non dispone dell'accesso di sicurezza necessario per utilizzarlo.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Threading.Mutex.ReleaseMutex">
      <summary>Rilascia l'oggetto <see cref="T:System.Threading.Mutex" /> una volta.</summary>
      <exception cref="T:System.ApplicationException">Il thread chiamante non ha la proprietà del mutex. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Mutex.TryOpenExisting(System.String,System.Threading.Mutex@)">
      <summary>Apre il mutex denominato specificato, se esistente, e restituisce un valore che indica se l'operazione è stata completata.</summary>
      <returns>true se il mutex denominato è stato aperto correttamente; in caso contrario, false.</returns>
      <param name="name">Nome del mutex di sistema da aprire.</param>
      <param name="result">Quando questo metodo viene restituito, contiene un oggetto di <see cref="T:System.Threading.Mutex" /> che rappresenta il mutex denominato se la chiamata ha esito positivo o null se la chiamata ha esito negativo.Questo parametro viene trattato come non inizializzato.</param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="name" /> è una stringa vuota.-oppure-<paramref name="name" /> è più lungo di 260 caratteri.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> è null.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il mutex denominato esiste, ma l'utente non dispone dell'accesso di sicurezza necessario per utilizzarlo.</exception>
    </member>
    <member name="T:System.Threading.ReaderWriterLockSlim">
      <summary>Rappresenta un blocco usato per gestire l'accesso a una risorsa, consentendo a più thread l'accesso in lettura o l'accesso esclusivo in scrittura.</summary>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.ReaderWriterLockSlim" /> con i valori predefiniti delle proprietà.</summary>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.#ctor(System.Threading.LockRecursionPolicy)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.ReaderWriterLockSlim" />, specificando i criteri di ricorsione del blocco.</summary>
      <param name="recursionPolicy">Uno dei valori di enumerazione che specifica i criteri di ricorsione del blocco. </param>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.CurrentReadCount">
      <summary>Ottiene il numero complessivo di thread univoci per i quali è stato attivato il blocco in modalità lettura.</summary>
      <returns>Numero di thread univoci per i quali è stato attivato il blocco in modalità lettura.</returns>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.Dispose">
      <summary>Rilascia tutte le risorse usate dall'istanza corrente della classe <see cref="T:System.Threading.ReaderWriterLockSlim" />.</summary>
      <exception cref="T:System.Threading.SynchronizationLockException">
        <see cref="P:System.Threading.ReaderWriterLockSlim.WaitingReadCount" /> is greater than zero. -or-<see cref="P:System.Threading.ReaderWriterLockSlim.WaitingUpgradeCount" /> is greater than zero. -or-<see cref="P:System.Threading.ReaderWriterLockSlim.WaitingWriteCount" /> is greater than zero. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.EnterReadLock">
      <summary>Prova ad attivare il blocco in modalità lettura.</summary>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered read mode. -or-The current thread may not acquire the read lock when it already holds the write lock. -or-The recursion number would exceed the capacity of the counter.This limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.EnterUpgradeableReadLock">
      <summary>Prova ad attivare il blocco in modalità aggiornabile.</summary>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock in any mode. -or-The current thread has entered read mode, so trying to enter upgradeable mode would create the possibility of a deadlock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.EnterWriteLock">
      <summary>Prova ad attivare il blocco in modalità scrittura.</summary>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock in any mode. -or-The current thread has entered read mode, so trying to enter the lock in write mode would create the possibility of a deadlock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.ExitReadLock">
      <summary>Riduce il numero di ricorsioni per la modalità lettura ed esce da questa modalità se il numero risultante è 0 (zero).</summary>
      <exception cref="T:System.Threading.SynchronizationLockException">The current thread has not entered the lock in read mode. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.ExitUpgradeableReadLock">
      <summary>Riduce il numero di ricorsioni per la modalità aggiornabile ed esce da questa modalità se il numero risultante è 0 (zero).</summary>
      <exception cref="T:System.Threading.SynchronizationLockException">The current thread has not entered the lock in upgradeable mode.</exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.ExitWriteLock">
      <summary>Riduce il numero di ricorsioni per la modalità scrittura ed esce da questa modalità se il numero risultante è 0 (zero).</summary>
      <exception cref="T:System.Threading.SynchronizationLockException">The current thread has not entered the lock in write mode.</exception>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.IsReadLockHeld">
      <summary>Ottiene un valore che indica se per il thread corrente è stato attivato il blocco in modalità lettura.</summary>
      <returns>true se per il thread corrente è stata attivata la modalità lettura; in caso contrario, false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.IsUpgradeableReadLockHeld">
      <summary>Ottiene un valore che indica se per il thread corrente è stato attivato il blocco in modalità aggiornabile. </summary>
      <returns>true se per il thread corrente è stata attivata la modalità aggiornabile; in caso contrario, false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.IsWriteLockHeld">
      <summary>Ottiene un valore che indica se per il thread corrente è stato attivato il blocco in modalità scrittura.</summary>
      <returns>true se per il thread corrente è stata attivata la modalità scrittura; in caso contrario, false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy">
      <summary>Ottiene un valore che indica i criteri di ricorsione per l'oggetto <see cref="T:System.Threading.ReaderWriterLockSlim" /> corrente.</summary>
      <returns>Uno dei valori di enumerazione che specifica i criteri di ricorsione del blocco.</returns>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.RecursiveReadCount">
      <summary>Ottiene il numero di volte in cui per il thread corrente è stato attivato il blocco in modalità lettura, come indicazione della ricorsione.</summary>
      <returns>0 (zero) se per il thread corrente non è stata attivata la modalità lettura, 1 se per il thread è stata attivata la modalità lettura ma non in modo ricorsivo o n se per il thread è stato attivato il blocco in modo ricorsivo n - 1 volte.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.RecursiveUpgradeCount">
      <summary>Ottiene il numero di volte in cui per il thread corrente è stato attivato il blocco in modalità aggiornabile, come indicazione della ricorsione.</summary>
      <returns>0 (zero) se per il thread corrente non è stata attivata la modalità aggiornabile, 1 se per il thread è stata attivata la modalità aggiornabile ma non in modo ricorsivo o n se per il thread è stata attivata la modalità aggiornabile in modo ricorsivo n - 1 volte.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.RecursiveWriteCount">
      <summary>Ottiene il numero di volte in cui per il thread corrente è stato attivato il blocco in modalità scrittura, come indicazione della ricorsione.</summary>
      <returns>0 (zero) se per il thread corrente non è stata attivata la modalità scrittura, 1 se per il thread è stata attivata la modalità scrittura ma non in modo ricorsivo o n se per il thread è stata attivata la modalità scrittura in modo ricorsivo n - 1 volte.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.TryEnterReadLock(System.Int32)">
      <summary>Prova ad attivare il blocco in modalità lettura con un timeout intero facoltativo.</summary>
      <returns>true se il thread chiamante è passato in modalità lettura; in caso contrario, false.</returns>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure -1 (<see cref="F:System.Threading.Timeout.Infinite" />) per un'attesa indefinita.</param>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="millisecondsTimeout" /> is negative, but it is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> (-1), which is the only negative value allowed. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.TryEnterReadLock(System.TimeSpan)">
      <summary>Prova ad attivare il blocco in modalità lettura con un timeout facoltativo.</summary>
      <returns>true se il thread chiamante è passato in modalità lettura; in caso contrario, false.</returns>
      <param name="timeout">Intervallo di attesa oppure -1 millisecondi per un'attesa indefinita. </param>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="timeout" /> is negative, but it is not equal to -1 milliseconds, which is the only negative value allowed.-or-The value of <paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" /> milliseconds. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.TryEnterUpgradeableReadLock(System.Int32)">
      <summary>Prova ad attivare il blocco in modalità aggiornabile con un timeout facoltativo.</summary>
      <returns>true se il thread chiamante è passato in modalità aggiornabile; in caso contrario, false.</returns>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure -1 (<see cref="F:System.Threading.Timeout.Infinite" />) per un'attesa indefinita.</param>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock. -or-The current thread initially entered the lock in read mode, and therefore trying to enter upgradeable mode would create the possibility of a deadlock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="millisecondsTimeout" /> is negative, but it is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> (-1), which is the only negative value allowed. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.TryEnterUpgradeableReadLock(System.TimeSpan)">
      <summary>Prova ad attivare il blocco in modalità aggiornabile con un timeout facoltativo.</summary>
      <returns>true se il thread chiamante è passato in modalità aggiornabile; in caso contrario, false.</returns>
      <param name="timeout">Intervallo di attesa oppure -1 millisecondi per un'attesa indefinita.</param>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock. -or-The current thread initially entered the lock in read mode, and therefore trying to enter upgradeable mode would create the possibility of a deadlock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="timeout" /> is negative, but it is not equal to -1 milliseconds, which is the only negative value allowed.-or-The value of <paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" /> milliseconds. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.TryEnterWriteLock(System.Int32)">
      <summary>Prova ad attivare il blocco in modalità scrittura con un timeout facoltativo.</summary>
      <returns>true se il thread chiamante è passato in modalità scrittura; in caso contrario, false.</returns>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure -1 (<see cref="F:System.Threading.Timeout.Infinite" />) per un'attesa indefinita.</param>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock. -or-The current thread initially entered the lock in read mode, and therefore trying to enter write mode would create the possibility of a deadlock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="millisecondsTimeout" /> is negative, but it is not equal to <see cref="F:System.Threading.Timeout.Infinite" /> (-1), which is the only negative value allowed. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="M:System.Threading.ReaderWriterLockSlim.TryEnterWriteLock(System.TimeSpan)">
      <summary>Prova ad attivare il blocco in modalità scrittura con un timeout facoltativo.</summary>
      <returns>true se il thread chiamante è passato in modalità scrittura; in caso contrario, false.</returns>
      <param name="timeout">Intervallo di attesa oppure -1 millisecondi per un'attesa indefinita.</param>
      <exception cref="T:System.Threading.LockRecursionException">The <see cref="P:System.Threading.ReaderWriterLockSlim.RecursionPolicy" /> property is <see cref="F:System.Threading.LockRecursionPolicy.NoRecursion" /> and the current thread has already entered the lock. -or-The current thread initially entered the lock in read mode, and therefore trying to enter write mode would create the possibility of a deadlock. -or-The recursion number would exceed the capacity of the counter.The limit is so large that applications should never encounter it.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="timeout" /> is negative, but it is not equal to -1 milliseconds, which is the only negative value allowed.-or-The value of <paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" /> milliseconds. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.ReaderWriterLockSlim" /> object has been disposed. </exception>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.WaitingReadCount">
      <summary>Ottiene il numero complessivo di thread in attesa di attivazione del blocco in modalità lettura.</summary>
      <returns>Numero complessivo di thread in attesa di attivazione della modalità lettura.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.WaitingUpgradeCount">
      <summary>Ottiene il numero complessivo di thread in attesa di attivazione del blocco in modalità aggiornabile.</summary>
      <returns>Numero complessivo di thread in attesa di attivazione della modalità aggiornabile.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.ReaderWriterLockSlim.WaitingWriteCount">
      <summary>Ottiene il numero complessivo di thread in attesa di attivazione del blocco in modalità scrittura.</summary>
      <returns>Numero complessivo di thread in attesa di attivazione della modalità scrittura.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Threading.Semaphore">
      <summary>Limita il numero di thread che possono accedere a una risorsa o a un pool di risorse contemporaneamente. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Semaphore.#ctor(System.Int32,System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Semaphore" />, specificando il numero di accessi iniziale e il numero massimo di accessi contemporanei. </summary>
      <param name="initialCount">Numero iniziale di richieste per il semaforo che possono essere concesse simultaneamente. </param>
      <param name="maximumCount">Numero massimo di richieste per il semaforo che possono essere concesse simultaneamente. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="initialCount" /> è maggiore di <paramref name="maximumCount" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maximumCount" /> è minore di 1.-oppure-<paramref name="initialCount" /> è minore di 0.</exception>
    </member>
    <member name="M:System.Threading.Semaphore.#ctor(System.Int32,System.Int32,System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Semaphore" />, specificando il numero di accessi iniziale e il numero massimo di accessi contemporanei, nonché indicando facoltativamente il nome di un oggetto semaforo di sistema. </summary>
      <param name="initialCount">Numero iniziale di richieste per il semaforo che possono essere concesse simultaneamente. </param>
      <param name="maximumCount">Numero massimo di richieste per il semaforo che possono essere concesse simultaneamente.</param>
      <param name="name">Nome di un oggetto semaforo di sistema denominato.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="initialCount" /> è maggiore di <paramref name="maximumCount" />.-oppure-<paramref name="name" /> è più lungo di 260 caratteri.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maximumCount" /> è minore di 1.-oppure-<paramref name="initialCount" /> è minore di 0.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il semaforo denominato esiste ed è dotato di sicurezza del controllo di accesso e l'utente non dispone di <see cref="F:System.Security.AccessControl.SemaphoreRights.FullControl" />.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">Non è possibile creare il semaforo denominato, probabilmente a causa di un handle di attesa di tipo diverso con lo stesso nome.</exception>
    </member>
    <member name="M:System.Threading.Semaphore.#ctor(System.Int32,System.Int32,System.String,System.Boolean@)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.Semaphore" />, specificando il numero di accessi iniziale e il numero massimo di accessi contemporanei, indicando facoltativamente il nome di un oggetto semaforo di sistema e specificando una variabile che riceve un valore che indica se è stato creato un nuovo semaforo di sistema.</summary>
      <param name="initialCount">Numero iniziale di richieste per il semaforo che possono essere soddisfatte contemporaneamente. </param>
      <param name="maximumCount">Numero massimo di richieste per il semaforo che possono essere soddisfatte contemporaneamente.</param>
      <param name="name">Nome di un oggetto semaforo di sistema denominato.</param>
      <param name="createdNew">Quando questo metodo viene restituito, contiene true se è stato creato un semaforo locale (ovvero, se il valore di <paramref name="name" /> è null o una stringa vuota) oppure se è stato creato il semaforo di sistema denominato specificato; false se il semaforo di sistema denominato specificato è già esistente.Questo parametro viene passato non inizializzato.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="initialCount" /> è maggiore di <paramref name="maximumCount" />. -oppure-<paramref name="name" /> è più lungo di 260 caratteri.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maximumCount" /> è minore di 1.-oppure-<paramref name="initialCount" /> è minore di 0.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il semaforo denominato esiste ed è dotato di sicurezza del controllo di accesso e l'utente non dispone di <see cref="F:System.Security.AccessControl.SemaphoreRights.FullControl" />.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">Non è possibile creare il semaforo denominato, probabilmente a causa di un handle di attesa di tipo diverso con lo stesso nome.</exception>
    </member>
    <member name="M:System.Threading.Semaphore.OpenExisting(System.String)">
      <summary>Apre il semaforo denominato specificato, se esistente.</summary>
      <returns>Oggetto che rappresenta il semaforo di sistema denominato.</returns>
      <param name="name">Nome del semaforo di sistema da aprire.</param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="name" /> è una stringa vuota.-oppure-<paramref name="name" /> è più lungo di 260 caratteri.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> è null.</exception>
      <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">Il semaforo denominato non esiste.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il semaforo denominato esiste, ma l'utente non dispone dell'accesso di sicurezza necessario per utilizzarlo. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Threading.Semaphore.Release">
      <summary>Esce dal semaforo e restituisce il conteggio precedente.</summary>
      <returns>Conteggio del semaforo prima della chiamata del metodo <see cref="Overload:System.Threading.Semaphore.Release" />. </returns>
      <exception cref="T:System.Threading.SemaphoreFullException">Il conteggio del semaforo ha già raggiunto il valore massimo.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore Win32 relativo a un semaforo denominato.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il semaforo corrente rappresenta un semaforo di sistema denominato, ma l'utente non dispone di <see cref="F:System.Security.AccessControl.SemaphoreRights.Modify" />.-oppure-Il semaforo corrente rappresenta un semaforo di sistema denominato, ma non è stato aperto con <see cref="F:System.Security.AccessControl.SemaphoreRights.Modify" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Semaphore.Release(System.Int32)">
      <summary>Esce dal semaforo il numero di volte specificato e restituisce il conteggio precedente.</summary>
      <returns>Conteggio del semaforo prima della chiamata del metodo <see cref="Overload:System.Threading.Semaphore.Release" />. </returns>
      <param name="releaseCount">Numero di uscite dal semaforo.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="releaseCount" /> è minore di 1.</exception>
      <exception cref="T:System.Threading.SemaphoreFullException">Il conteggio del semaforo ha già raggiunto il valore massimo.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore Win32 relativo a un semaforo denominato.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il semaforo corrente rappresenta un semaforo di sistema denominato, ma l'utente non dispone di diritti <see cref="F:System.Security.AccessControl.SemaphoreRights.Modify" />.-oppure-Il semaforo corrente rappresenta un semaforo di sistema denominato, ma non è stato aperto con i diritti <see cref="F:System.Security.AccessControl.SemaphoreRights.Modify" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.Semaphore.TryOpenExisting(System.String,System.Threading.Semaphore@)">
      <summary>Apre il semaforo denominato specificato, se esistente, e restituisce un valore che indica se l'operazione è riuscita.</summary>
      <returns>true se l'apertura del semaforo denominato è riuscita; in caso contrario, false.</returns>
      <param name="name">Nome del semaforo di sistema da aprire.</param>
      <param name="result">Quando viene eseguita la restituzione del metodo, quest'ultimo contiene un oggetto <see cref="T:System.Threading.Semaphore" /> che rappresenta il semaforo denominato se la chiamata è riuscita o null se la chiamata non è riuscita.Questo parametro viene trattato come non inizializzato.</param>
      <exception cref="T:System.ArgumentException">Il parametro <paramref name="name" /> è una stringa vuota.-oppure-<paramref name="name" /> è più lungo di 260 caratteri.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> è null.</exception>
      <exception cref="T:System.IO.IOException">Si è verificato un errore Win32.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Il semaforo denominato esiste, ma l'utente non dispone dell'accesso di sicurezza necessario per utilizzarlo. </exception>
    </member>
    <member name="T:System.Threading.SemaphoreFullException">
      <summary>Eccezione generata quando il metodo <see cref="Overload:System.Threading.Semaphore.Release" /> viene chiamato su un semaforo il cui conteggio ha già raggiunto il valore massimo. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.SemaphoreFullException.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.SemaphoreFullException" /> con valori predefiniti.</summary>
    </member>
    <member name="M:System.Threading.SemaphoreFullException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.SemaphoreFullException" /> con un messaggio di errore specificato.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione</param>
    </member>
    <member name="M:System.Threading.SemaphoreFullException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.SemaphoreFullException" /> con un messaggio di errore specificato e un riferimento all'eccezione interna che è la causa dell'eccezione corrente.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione</param>
      <param name="innerException">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="innerException" /> non è null, l'eccezione corrente viene generata in un blocco catch in cui viene gestita l'eccezione interna.</param>
    </member>
    <member name="T:System.Threading.SemaphoreSlim">
      <summary>Rappresenta un'alternativa semplificata a <see cref="T:System.Threading.Semaphore" /> che limita il numero di thread che possono accedere simultaneamente a una risorsa o a un pool di risorse.</summary>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.#ctor(System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.SemaphoreSlim" /> specificando il numero iniziale di richieste che possono essere concesse simultaneamente.</summary>
      <param name="initialCount">Numero iniziale di richieste per il semaforo che possono essere concesse simultaneamente.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialCount" /> è minore di 0.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.#ctor(System.Int32,System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.SemaphoreSlim" /> specificando il numero iniziale e massimo di richieste che possono essere concesse simultaneamente.</summary>
      <param name="initialCount">Numero iniziale di richieste per il semaforo che possono essere concesse simultaneamente.</param>
      <param name="maxCount">Numero massimo di richieste per il semaforo che possono essere concesse simultaneamente.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialCount" /> è minore di 0, o <paramref name="initialCount" /> è maggiore di <paramref name="maxCount" /> o <paramref name="maxCount" /> è uguale o minore di 0.</exception>
    </member>
    <member name="P:System.Threading.SemaphoreSlim.AvailableWaitHandle">
      <summary>Restituisce un oggetto <see cref="T:System.Threading.WaitHandle" /> che può essere usato per attendere il semaforo.</summary>
      <returns>Oggetto <see cref="T:System.Threading.WaitHandle" /> che può essere usato per attendere il semaforo.</returns>
      <exception cref="T:System.ObjectDisposedException">L'interfaccia <see cref="T:System.Threading.SemaphoreSlim" /> è stata eliminata.</exception>
    </member>
    <member name="P:System.Threading.SemaphoreSlim.CurrentCount">
      <summary>Ottiene il numero di thread rimanenti che possono accedere all'oggetto <see cref="T:System.Threading.SemaphoreSlim" />. </summary>
      <returns>Numero di thread rimanenti che possono accedere al semaforo.</returns>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Dispose">
      <summary>Rilascia tutte le risorse usate dall'istanza corrente della classe <see cref="T:System.Threading.SemaphoreSlim" />.</summary>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Dispose(System.Boolean)">
      <summary>Rilascia le risorse non gestite usate dall'oggetto <see cref="T:System.Threading.SemaphoreSlim" /> e, facoltativamente, le risorse gestite.</summary>
      <param name="disposing">true per rilasciare sia le risorse gestite sia quelle non gestite; false per rilasciare solo le risorse non gestite.</param>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Release">
      <summary>Rilascia l'oggetto <see cref="T:System.Threading.SemaphoreSlim" /> una volta.</summary>
      <returns>Numero precedente di <see cref="T:System.Threading.SemaphoreSlim" />.</returns>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.Threading.SemaphoreFullException">
        <see cref="T:System.Threading.SemaphoreSlim" /> ha già raggiunto la dimensione massima.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Release(System.Int32)">
      <summary>Rilascia l'oggetto <see cref="T:System.Threading.SemaphoreSlim" /> un numero di volte specificato.</summary>
      <returns>Numero precedente di <see cref="T:System.Threading.SemaphoreSlim" />.</returns>
      <param name="releaseCount">Numero di uscite dal semaforo.</param>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="releaseCount" /> è minore di 1.</exception>
      <exception cref="T:System.Threading.SemaphoreFullException">
        <see cref="T:System.Threading.SemaphoreSlim" /> ha già raggiunto la dimensione massima.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Wait">
      <summary>Blocca il thread corrente finché non può immettere <see cref="T:System.Threading.SemaphoreSlim" />.</summary>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Wait(System.Int32)">
      <summary>Blocca il thread corrente finché non può accedere all'oggetto <see cref="T:System.Threading.SemaphoreSlim" />, usando un intero con segno a 32 bit che specifica il timeout.</summary>
      <returns>true se il thread corrente ha immesso correttamente <see cref="T:System.Threading.SemaphoreSlim" />; in caso contrario, false.</returns>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure <see cref="F:System.Threading.Timeout.Infinite" /> (-1) per un'attesa indefinita.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> è un numero negativo diverso da -1 che rappresenta un timeout indeterminato.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Wait(System.Int32,System.Threading.CancellationToken)">
      <summary>Blocca il thread corrente finché non può accedere all'oggetto <see cref="T:System.Threading.SemaphoreSlim" />, usando un intero con segno a 32 bit che specifica il timeout e osservando un oggetto <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>true se il thread corrente ha immesso correttamente <see cref="T:System.Threading.SemaphoreSlim" />; in caso contrario, false.</returns>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure <see cref="F:System.Threading.Timeout.Infinite" /> (-1) per un'attesa indefinita.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> da osservare.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> è stato annullato.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> è un numero negativo diverso da -1 che rappresenta un timeout indeterminato.</exception>
      <exception cref="T:System.ObjectDisposedException">Il <see cref="T:System.Threading.SemaphoreSlim" /> istanza è stata eliminata, o <see cref="T:System.Threading.CancellationTokenSource" /> che ha creato <paramref name="cancellationToken" /> è stato eliminato.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Wait(System.Threading.CancellationToken)">
      <summary>Blocca il thread corrente finché non può accedere all'oggetto <see cref="T:System.Threading.SemaphoreSlim" /> osservando un oggetto <see cref="T:System.Threading.CancellationToken" />.</summary>
      <param name="cancellationToken">Token <see cref="T:System.Threading.CancellationToken" /> da osservare.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> è stato annullato.</exception>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.-oppure-Il <see cref="T:System.Threading.CancellationTokenSource" /> creato<paramref name=" cancellationToken" /> è già stato eliminato.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Wait(System.TimeSpan)">
      <summary>Blocca il thread corrente finché non può accedere all'oggetto <see cref="T:System.Threading.SemaphoreSlim" />, usando un oggetto <see cref="T:System.TimeSpan" /> per specificare il timeout.</summary>
      <returns>true se il thread corrente ha immesso correttamente <see cref="T:System.Threading.SemaphoreSlim" />; in caso contrario, false.</returns>
      <param name="timeout">Oggetto <see cref="T:System.TimeSpan" /> che rappresenta il numero di millisecondi di attesa oppure <see cref="T:System.TimeSpan" /> che rappresenta -1 millisecondi per un'attesa indefinita.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> è un numero negativo diverso da -1 millisecondi che rappresenta un timeout infinito - o - il timeout è più grande di <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.ObjectDisposedException">L'istanza semaphoreSlim è stata eliminata<paramref name="." /></exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.Wait(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Blocca il thread corrente finché non può accedere all'oggetto <see cref="T:System.Threading.SemaphoreSlim" />, usando un oggetto <see cref="T:System.TimeSpan" /> che specifica il timeout e osservando un oggetto <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>true se il thread corrente ha immesso correttamente <see cref="T:System.Threading.SemaphoreSlim" />; in caso contrario, false.</returns>
      <param name="timeout">Oggetto <see cref="T:System.TimeSpan" /> che rappresenta il numero di millisecondi di attesa oppure <see cref="T:System.TimeSpan" /> che rappresenta -1 millisecondi per un'attesa indefinita.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> da osservare.</param>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> è stato annullato.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> è un numero negativo diverso da -1 millisecondi che rappresenta un timeout infinito - o - il timeout è più grande di <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.ObjectDisposedException">L'istanza semaphoreSlim è stata eliminata<paramref name="." /><paramref name="-or-" />L'oggetto <see cref="T:System.Threading.CancellationTokenSource" /> che ha creato <paramref name="cancellationToken" /> è già stato eliminato.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.WaitAsync">
      <summary>Attende in modo asincrono di immettere <see cref="T:System.Threading.SemaphoreSlim" />. </summary>
      <returns>Attività che verrà completata quando si accede al semaforo.</returns>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.WaitAsync(System.Int32)">
      <summary>Attende in modo asincrono di accedere all'oggetto <see cref="T:System.Threading.SemaphoreSlim" />, usando un intero con segno a 32 bit per misurare l'intervallo di tempo. </summary>
      <returns>Attività che verrà completata con un risultato true se il thread corrente ha immesso correttamente <see cref="T:System.Threading.SemaphoreSlim" />, in caso contrario, con un risultato false.</returns>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure <see cref="F:System.Threading.Timeout.Infinite" /> (-1) per un'attesa indefinita.</param>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> è un numero negativo diverso da -1 che rappresenta un timeout indeterminato.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.WaitAsync(System.Int32,System.Threading.CancellationToken)">
      <summary>Attende in modo asincrono di accedere all'oggetto <see cref="T:System.Threading.SemaphoreSlim" />, usando un intero con segno a 32 bit per misurare l'intervallo di tempo e osservando un oggetto <see cref="T:System.Threading.CancellationToken" />. </summary>
      <returns>Attività che verrà completata con un risultato true se il thread corrente ha immesso correttamente <see cref="T:System.Threading.SemaphoreSlim" />, in caso contrario, con un risultato false. </returns>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure <see cref="F:System.Threading.Timeout.Infinite" /> (-1) per un'attesa indefinita.</param>
      <param name="cancellationToken">Oggetto <see cref="T:System.Threading.CancellationToken" /> da osservare.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> è un numero negativo diverso da -1 che rappresenta un timeout indeterminato. </exception>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata. </exception>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> è stato annullato. </exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.WaitAsync(System.Threading.CancellationToken)">
      <summary>Attende in modo asincrono di accedere all'oggetto <see cref="T:System.Threading.SemaphoreSlim" />, osservando un oggetto <see cref="T:System.Threading.CancellationToken" />. </summary>
      <returns>Attività che verrà completata quando si accede al semaforo. </returns>
      <param name="cancellationToken">Token <see cref="T:System.Threading.CancellationToken" /> da osservare.</param>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> è stato annullato. </exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.WaitAsync(System.TimeSpan)">
      <summary>Attende in modo asincrono di accedere all'oggetto <see cref="T:System.Threading.SemaphoreSlim" />, usando un oggetto <see cref="T:System.TimeSpan" /> per misurare l'intervallo di tempo.</summary>
      <returns>Attività che verrà completata con un risultato true se il thread corrente ha immesso correttamente <see cref="T:System.Threading.SemaphoreSlim" />, in caso contrario, con un risultato false.</returns>
      <param name="timeout">Oggetto <see cref="T:System.TimeSpan" /> che rappresenta il numero di millisecondi di attesa oppure <see cref="T:System.TimeSpan" /> che rappresenta -1 millisecondi per un'attesa indefinita.</param>
      <exception cref="T:System.ObjectDisposedException">L'istanza corrente è già stata eliminata.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> è un numero negativo diverso da -1 che rappresenta un timeout indeterminato. -oppure- timeout è maggiore di <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.SemaphoreSlim.WaitAsync(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Attende in modo asincrono di accedere all'oggetto <see cref="T:System.Threading.SemaphoreSlim" />, usando un oggetto <see cref="T:System.TimeSpan" /> per misurare l'intervallo di tempo e osservando un oggetto <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>Attività che verrà completata con un risultato true se il thread corrente ha immesso correttamente <see cref="T:System.Threading.SemaphoreSlim" />, in caso contrario, con un risultato false.</returns>
      <param name="timeout">Oggetto <see cref="T:System.TimeSpan" /> che rappresenta il numero di millisecondi di attesa oppure <see cref="T:System.TimeSpan" /> che rappresenta -1 millisecondi per un'attesa indefinita.</param>
      <param name="cancellationToken">Token <see cref="T:System.Threading.CancellationToken" /> da osservare.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> è un numero negativo diverso da -1 che rappresenta un timeout indeterminato.-oppure-timeout è maggiore di <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="cancellationToken" /> è stato annullato. </exception>
    </member>
    <member name="T:System.Threading.SendOrPostCallback">
      <summary>Rappresenta un metodo da chiamare quando un messaggio deve essere inviato a un contesto di sincronizzazione.  </summary>
      <param name="state">Oggetto passato al delegato.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Threading.SpinLock">
      <summary>Fornisce un primitiva di blocco a esclusione reciproca in cui un thread che tenta di acquisire il blocco attende in un ciclo eseguendo controlli ripetuti finché il blocco non diventa disponibile.</summary>
    </member>
    <member name="M:System.Threading.SpinLock.#ctor(System.Boolean)">
      <summary>Inizializza una nuova istanza della struttura <see cref="T:System.Threading.SpinLock" /> con l'opzione di rilevamento degli ID dei thread per migliorare il debug.</summary>
      <param name="enableThreadOwnerTracking">Valore che indica se acquisire e utilizzare gli ID dei thread per scopi di debug.</param>
    </member>
    <member name="M:System.Threading.SpinLock.Enter(System.Boolean@)">
      <summary>Acquisisce il blocco in modo affidabile, in modo tale che anche se si verifica un'eccezione all'interno della chiamata al metodo, è possibile esaminare l'oggetto <paramref name="lockTaken" /> in maniera affidabile per determinare se il blocco è stato acquisito.</summary>
      <param name="lockTaken">True se il blocco è stato acquisito. In caso contrario, False.Prima di chiamare questo metodo è necessario inizializzare <paramref name="lockTaken" /> su False.</param>
      <exception cref="T:System.ArgumentException">È necessario inizializzare l'argomento <paramref name="lockTaken" /> su False prima della chiamata a Enter.</exception>
      <exception cref="T:System.Threading.LockRecursionException">Il rilevamento della proprietà dei thread è abilitato e il thread corrente ha già acquisito questo blocco.</exception>
    </member>
    <member name="M:System.Threading.SpinLock.Exit">
      <summary>Rilascia il blocco.</summary>
      <exception cref="T:System.Threading.SynchronizationLockException">Il rilevamento della proprietà dei thread è abilitato e il thread corrente non è il proprietario di questo blocco.</exception>
    </member>
    <member name="M:System.Threading.SpinLock.Exit(System.Boolean)">
      <summary>Rilascia il blocco.</summary>
      <param name="useMemoryBarrier">Valore booleano che indica se generare un limite di memoria per pubblicare immediatamente l'operazione di uscita agli altri thread.</param>
      <exception cref="T:System.Threading.SynchronizationLockException">Il rilevamento della proprietà dei thread è abilitato e il thread corrente non è il proprietario di questo blocco.</exception>
    </member>
    <member name="P:System.Threading.SpinLock.IsHeld">
      <summary>Ottiene un valore che indica se attualmente il blocco è mantenuto da un thread.</summary>
      <returns>true se attualmente il blocco è mantenuto da un thread; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Threading.SpinLock.IsHeldByCurrentThread">
      <summary>Ottiene un valore che indica se il blocco è mantenuto dal thread corrente.</summary>
      <returns>true se il blocco è mantenuto dal thread corrente; in caso contrario, false.</returns>
      <exception cref="T:System.InvalidOperationException">Il rilevamento della proprietà dei thread è disabilitato.</exception>
    </member>
    <member name="P:System.Threading.SpinLock.IsThreadOwnerTrackingEnabled">
      <summary>Ottiene un valore che indica se per questa istanza è abilitato il rilevamento della proprietà dei thread.</summary>
      <returns>true se per questa istanza è abilitato il rilevamento della proprietà dei thread; in caso contrario, false.</returns>
    </member>
    <member name="M:System.Threading.SpinLock.TryEnter(System.Boolean@)">
      <summary>Tenta di acquisire il blocco in modo affidabile, in modo tale che anche se si verifica un'eccezione all'interno della chiamata al metodo, è possibile esaminare l'oggetto <paramref name="lockTaken" /> in maniera affidabile per determinare se il blocco è stato acquisito.</summary>
      <param name="lockTaken">True se il blocco è stato acquisito. In caso contrario, False.Prima di chiamare questo metodo è necessario inizializzare <paramref name="lockTaken" /> su False.</param>
      <exception cref="T:System.ArgumentException">È necessario inizializzare l'argomento <paramref name="lockTaken" /> su False prima della chiamata a TryEnter.</exception>
      <exception cref="T:System.Threading.LockRecursionException">Il rilevamento della proprietà dei thread è abilitato e il thread corrente ha già acquisito questo blocco.</exception>
    </member>
    <member name="M:System.Threading.SpinLock.TryEnter(System.Int32,System.Boolean@)">
      <summary>Tenta di acquisire il blocco in modo affidabile, in modo tale che anche se si verifica un'eccezione all'interno della chiamata al metodo, è possibile esaminare l'oggetto <paramref name="lockTaken" /> in maniera affidabile per determinare se il blocco è stato acquisito.</summary>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure <see cref="F:System.Threading.Timeout.Infinite" /> (-1) per un'attesa indefinita.</param>
      <param name="lockTaken">True se il blocco è stato acquisito. In caso contrario, False.Prima di chiamare questo metodo è necessario inizializzare <paramref name="lockTaken" /> su False.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> è un numero negativo diverso da -1 che rappresenta un timeout indeterminato.</exception>
      <exception cref="T:System.ArgumentException">È necessario inizializzare l'argomento <paramref name="lockTaken" /> su False prima della chiamata a TryEnter.</exception>
      <exception cref="T:System.Threading.LockRecursionException">Il rilevamento della proprietà dei thread è abilitato e il thread corrente ha già acquisito questo blocco.</exception>
    </member>
    <member name="M:System.Threading.SpinLock.TryEnter(System.TimeSpan,System.Boolean@)">
      <summary>Tenta di acquisire il blocco in modo affidabile, in modo tale che anche se si verifica un'eccezione all'interno della chiamata al metodo, è possibile esaminare l'oggetto <paramref name="lockTaken" /> in maniera affidabile per determinare se il blocco è stato acquisito.</summary>
      <param name="timeout">
        <see cref="T:System.TimeSpan" /> che rappresenta il numero di millisecondi di attesa oppure <see cref="T:System.TimeSpan" /> che rappresenta -1 millisecondi per un'attesa indefinita.</param>
      <param name="lockTaken">True se il blocco è stato acquisito. In caso contrario, False.Prima di chiamare questo metodo è necessario inizializzare <paramref name="lockTaken" /> su False.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> è un numero negativo diverso da -1 millisecondi che rappresenta un timeout infinito o il timeout è più grande di <see cref="F:System.Int32.MaxValue" /> millisecondi.</exception>
      <exception cref="T:System.ArgumentException">È necessario inizializzare l'argomento <paramref name="lockTaken" /> su False prima della chiamata a TryEnter.</exception>
      <exception cref="T:System.Threading.LockRecursionException">Il rilevamento della proprietà dei thread è abilitato e il thread corrente ha già acquisito questo blocco.</exception>
    </member>
    <member name="T:System.Threading.SpinWait">
      <summary>Fornisce il supporto per l'attesa basata su rotazione.</summary>
    </member>
    <member name="P:System.Threading.SpinWait.Count">
      <summary>Ottiene il numero di chiamate di <see cref="M:System.Threading.SpinWait.SpinOnce" /> su questa istanza.</summary>
      <returns>Restituisce un intero che rappresenta il numero di volte in cui <see cref="M:System.Threading.SpinWait.SpinOnce" /> è stato chiamato su questa istanza.</returns>
    </member>
    <member name="P:System.Threading.SpinWait.NextSpinWillYield">
      <summary>Ottiene un valore che indica se la chiamata successiva a <see cref="M:System.Threading.SpinWait.SpinOnce" /> comporterà la cessione del processore, attivando un cambio imposto di contesto.</summary>
      <returns>Valore che indica se la chiamata successiva a <see cref="M:System.Threading.SpinWait.SpinOnce" /> comporterà la cessione del processore, attivando un cambio imposto di contesto.</returns>
    </member>
    <member name="M:System.Threading.SpinWait.Reset">
      <summary>Reimposta il contatore delle rotazioni.</summary>
    </member>
    <member name="M:System.Threading.SpinWait.SpinOnce">
      <summary>Esegue una sola rotazione.</summary>
    </member>
    <member name="M:System.Threading.SpinWait.SpinUntil(System.Func{System.Boolean})">
      <summary>Esegue rotazioni finché non è stata soddisfatta la condizione specificata.</summary>
      <param name="condition">Delegato da eseguire ripetutamente finché non restituisce true.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="condition" /> è null.</exception>
    </member>
    <member name="M:System.Threading.SpinWait.SpinUntil(System.Func{System.Boolean},System.Int32)">
      <summary>Esegue rotazioni finché non è stata soddisfatta la condizione specificata o fino allo scadere del timeout specificato.</summary>
      <returns>True se la condizione viene soddisfatta entro lo scadere del timeout. In caso contrario, False.</returns>
      <param name="condition">Delegato da eseguire ripetutamente finché non restituisce true.</param>
      <param name="millisecondsTimeout">Numero di millisecondi di attesa oppure <see cref="F:System.Threading.Timeout.Infinite" /> (-1) per un'attesa indefinita.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="condition" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> è un numero negativo diverso da -1 che rappresenta un timeout indeterminato.</exception>
    </member>
    <member name="M:System.Threading.SpinWait.SpinUntil(System.Func{System.Boolean},System.TimeSpan)">
      <summary>Esegue rotazioni finché non è stata soddisfatta la condizione specificata o fino allo scadere del timeout specificato.</summary>
      <returns>True se la condizione viene soddisfatta entro lo scadere del timeout. In caso contrario, False.</returns>
      <param name="condition">Delegato da eseguire ripetutamente finché non restituisce true.</param>
      <param name="timeout">Oggetto <see cref="T:System.TimeSpan" /> che rappresenta il numero di millisecondi di attesa. In alternativa, per un'attesa indefinita, oggetto TimeSpan che rappresenta -1 millisecondi.</param>
      <exception cref="T:System.ArgumentNullException">L'argomento <paramref name="condition" /> è null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> è un numero negativo diverso da -1 millisecondi che rappresenta un timeout infinito - o - il timeout è più grande di <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="T:System.Threading.SynchronizationContext">
      <summary>Fornisce la funzionalità di base per propagare un contesto di sincronizzazione in vari modelli di sincronizzazione. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.SynchronizationContext.#ctor">
      <summary>Crea una nuova istanza della classe <see cref="T:System.Threading.SynchronizationContext" />.</summary>
    </member>
    <member name="M:System.Threading.SynchronizationContext.CreateCopy">
      <summary>Quando ne viene eseguito l'override in una classe derivata, crea una copia del contesto di sincronizzazione.  </summary>
      <returns>Nuovo oggetto <see cref="T:System.Threading.SynchronizationContext" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Threading.SynchronizationContext.Current">
      <summary>Ottiene il contesto di sincronizzazione per il thread corrente.</summary>
      <returns>Oggetto <see cref="T:System.Threading.SynchronizationContext" /> che rappresenta il contesto di sincronizzazione corrente.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Threading.SynchronizationContext.OperationCompleted">
      <summary>Quando ne viene eseguito l'override in una classe derivata, risponde alla notifica di completamento di un'operazione.</summary>
    </member>
    <member name="M:System.Threading.SynchronizationContext.OperationStarted">
      <summary>Quando ne viene eseguito l'override in una classe derivata, risponde alla notifica di avvio di un'operazione.</summary>
    </member>
    <member name="M:System.Threading.SynchronizationContext.Post(System.Threading.SendOrPostCallback,System.Object)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, invia un messaggio asincrono a un contesto di sincronizzazione.</summary>
      <param name="d">Delegato di <see cref="T:System.Threading.SendOrPostCallback" /> da chiamare.</param>
      <param name="state">Oggetto passato al delegato.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.SynchronizationContext.Send(System.Threading.SendOrPostCallback,System.Object)">
      <summary>Quando ne viene eseguito l'override in una classe derivata, invia un messaggio sincrono a un contesto di sincronizzazione.</summary>
      <param name="d">Delegato di <see cref="T:System.Threading.SendOrPostCallback" /> da chiamare.</param>
      <param name="state">Oggetto passato al delegato. </param>
      <exception cref="T:System.NotSupportedException">The method was called in a Windows Store app.The implementation of <see cref="T:System.Threading.SynchronizationContext" /> for Windows Store apps does not support the <see cref="M:System.Threading.SynchronizationContext.Send(System.Threading.SendOrPostCallback,System.Object)" /> method.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.SynchronizationContext.SetSynchronizationContext(System.Threading.SynchronizationContext)">
      <summary>Imposta il contesto di sincronizzazione corrente.</summary>
      <param name="syncContext">Oggetto <see cref="T:System.Threading.SynchronizationContext" /> da impostare.</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence, ControlPolicy" />
      </PermissionSet>
    </member>
    <member name="T:System.Threading.SynchronizationLockException">
      <summary>Eccezione generata quando un metodo richiede che il chiamante sia il proprietario del blocco su un Monitor specifico, e tale metodo viene richiamato da un chiamante che non è proprietario del blocco.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.SynchronizationLockException.#ctor">
      <summary>Consente l'inizializzazione di una nuova istanza della classe <see cref="T:System.Threading.SynchronizationLockException" /> con le proprietà predefinite.</summary>
    </member>
    <member name="M:System.Threading.SynchronizationLockException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.SynchronizationLockException" /> con un messaggio di errore specificato.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione </param>
    </member>
    <member name="M:System.Threading.SynchronizationLockException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.SynchronizationLockException" /> con un messaggio di errore specificato e un riferimento all'eccezione interna che è la causa dell'eccezione corrente.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione </param>
      <param name="innerException">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="innerException" /> non è null, l'eccezione corrente viene generata in un blocco catch in cui viene gestita l'eccezione interna.</param>
    </member>
    <member name="T:System.Threading.ThreadLocal`1">
      <summary>Consente l'archiviazione dei dati nella memoria locale dei thread.</summary>
      <typeparam name="T">Specifica il tipo di dati archiviati per thread.</typeparam>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.#ctor">
      <summary>Inizializza l'istanza <see cref="T:System.Threading.ThreadLocal`1" />.</summary>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.#ctor(System.Boolean)">
      <summary>Inizializza l'istanza <see cref="T:System.Threading.ThreadLocal`1" />.</summary>
      <param name="trackAllValues">Se tenere traccia di tutti i valori impostati sull'istanza ed esporli mediante la proprietà di <see cref="P:System.Threading.ThreadLocal`1.Values" /> .</param>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.#ctor(System.Func{`0})">
      <summary>Inizializza l'istanza di <see cref="T:System.Threading.ThreadLocal`1" /> con la funzione <paramref name="valueFactory" /> specificata.</summary>
      <param name="valueFactory">Oggetto <see cref="T:System.Func`1" /> richiamato per produrre un valore con inizializzazione differita quando si tenta di recuperare l'oggetto <see cref="P:System.Threading.ThreadLocal`1.Value" /> senza che sia stato inizializzato in precedenza.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="valueFactory" /> è un riferimento null (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.#ctor(System.Func{`0},System.Boolean)">
      <summary>Inizializza l'istanza di <see cref="T:System.Threading.ThreadLocal`1" /> con la funzione <paramref name="valueFactory" /> specificata.</summary>
      <param name="valueFactory">Oggetto <see cref="T:System.Func`1" /> richiamato per produrre un valore con inizializzazione differita quando si tenta di recuperare l'oggetto <see cref="P:System.Threading.ThreadLocal`1.Value" /> senza che sia stato inizializzato in precedenza.</param>
      <param name="trackAllValues">Se tenere traccia di tutti i valori impostati sull'istanza ed esporli mediante la proprietà di <see cref="P:System.Threading.ThreadLocal`1.Values" /> .</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="valueFactory" /> è un riferimento null (Nothing in Visual Basic).</exception>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.Dispose">
      <summary>Rilascia tutte le risorse utilizzate dall'istanza corrente della classe <see cref="T:System.Threading.ThreadLocal`1" />.</summary>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.Dispose(System.Boolean)">
      <summary>Rilascia le risorse utilizzate da questa istanza di <see cref="T:System.Threading.ThreadLocal`1" />.</summary>
      <param name="disposing">Valore booleano che indica se questo metodo viene chiamato a causa di una chiamata a <see cref="M:System.Threading.ThreadLocal`1.Dispose" />.</param>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.Finalize">
      <summary>Rilascia le risorse utilizzate da questa istanza di <see cref="T:System.Threading.ThreadLocal`1" />.</summary>
    </member>
    <member name="P:System.Threading.ThreadLocal`1.IsValueCreated">
      <summary>Ottiene un valore che indica se l'oggetto <see cref="P:System.Threading.ThreadLocal`1.Value" /> è inizializzato sul thread corrente.</summary>
      <returns>true se <see cref="P:System.Threading.ThreadLocal`1.Value" /> viene inizializzato sul thread corrente; in caso contrario, false.</returns>
      <exception cref="T:System.ObjectDisposedException">L'istanza di <see cref="T:System.Threading.ThreadLocal`1" /> è stata eliminata.</exception>
    </member>
    <member name="M:System.Threading.ThreadLocal`1.ToString">
      <summary>Crea e restituisce una rappresentazione di stringa di questa istanza per il thread corrente.</summary>
      <returns>Risultato della chiamata di <see cref="M:System.Object.ToString" /> su <see cref="P:System.Threading.ThreadLocal`1.Value" />.</returns>
      <exception cref="T:System.ObjectDisposedException">L'istanza di <see cref="T:System.Threading.ThreadLocal`1" /> è stata eliminata.</exception>
      <exception cref="T:System.NullReferenceException">L'oggetto <see cref="P:System.Threading.ThreadLocal`1.Value" /> per il thread corrente è un riferimento Null (Nothing in Visual Basic).</exception>
      <exception cref="T:System.InvalidOperationException">La funzione di inizializzazione tenta di fare riferimento in modo ricorsivo a <see cref="P:System.Threading.ThreadLocal`1.Value" />.</exception>
      <exception cref="T:System.MissingMemberException">Non è fornito alcun costruttore predefinito e non è fornito alcun valore di factory.</exception>
    </member>
    <member name="P:System.Threading.ThreadLocal`1.Value">
      <summary>Ottiene o imposta il valore di questa istanza per il thread corrente.</summary>
      <returns>Restituisce un'istanza dell'oggetto della cui inizializzazione è responsabile questo oggetto ThreadLocal.</returns>
      <exception cref="T:System.ObjectDisposedException">L'istanza di <see cref="T:System.Threading.ThreadLocal`1" /> è stata eliminata.</exception>
      <exception cref="T:System.InvalidOperationException">La funzione di inizializzazione tenta di fare riferimento in modo ricorsivo a <see cref="P:System.Threading.ThreadLocal`1.Value" />.</exception>
      <exception cref="T:System.MissingMemberException">Non è fornito alcun costruttore predefinito e non è fornito alcun valore di factory.</exception>
    </member>
    <member name="P:System.Threading.ThreadLocal`1.Values">
      <summary>Ottiene un elenco di tutti i valori attualmente archiviati da tutti i thread che hanno eseguito l'accesso a questa istanza.</summary>
      <returns>Elenco di tutti i valori attualmente archiviati da tutti i thread che hanno eseguito l'accesso a questa istanza.</returns>
      <exception cref="T:System.ObjectDisposedException">L'istanza di <see cref="T:System.Threading.ThreadLocal`1" /> è stata eliminata.</exception>
    </member>
    <member name="T:System.Threading.Volatile">
      <summary>Contiene metodi per l'esecuzione di operazioni relative alla memoria volatile.</summary>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Boolean@)">
      <summary>Legge il valore del campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare dopo questo metodo nel codice, il processore non potrà spostarla in una posizione precedente al metodo stesso.</summary>
      <returns>Valore letto.Questo valore è l'ultimo che è stato scritto da un processore qualsiasi nel computer, indipendentemente dal numero di processori o dallo stato della cache del processore.</returns>
      <param name="location">Campo da leggere.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Byte@)">
      <summary>Legge il valore del campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare dopo questo metodo nel codice, il processore non potrà spostarla in una posizione precedente al metodo stesso.</summary>
      <returns>Valore letto.Questo valore è l'ultimo che è stato scritto da un processore qualsiasi nel computer, indipendentemente dal numero di processori o dallo stato della cache del processore.</returns>
      <param name="location">Campo da leggere.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Double@)">
      <summary>Legge il valore del campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare dopo questo metodo nel codice, il processore non potrà spostarla in una posizione precedente al metodo stesso.</summary>
      <returns>Valore letto.Questo valore è l'ultimo che è stato scritto da un processore qualsiasi nel computer, indipendentemente dal numero di processori o dallo stato della cache del processore.</returns>
      <param name="location">Campo da leggere.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Int16@)">
      <summary>Legge il valore del campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare dopo questo metodo nel codice, il processore non potrà spostarla in una posizione precedente al metodo stesso.</summary>
      <returns>Valore letto.Questo valore è l'ultimo che è stato scritto da un processore qualsiasi nel computer, indipendentemente dal numero di processori o dallo stato della cache del processore.</returns>
      <param name="location">Campo da leggere.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Int32@)">
      <summary>Legge il valore del campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare dopo questo metodo nel codice, il processore non potrà spostarla in una posizione precedente al metodo stesso.</summary>
      <returns>Valore letto.Questo valore è l'ultimo che è stato scritto da un processore qualsiasi nel computer, indipendentemente dal numero di processori o dallo stato della cache del processore.</returns>
      <param name="location">Campo da leggere.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Int64@)">
      <summary>Legge il valore del campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare dopo questo metodo nel codice, il processore non potrà spostarla in una posizione precedente al metodo stesso.</summary>
      <returns>Valore letto.Questo valore è l'ultimo che è stato scritto da un processore qualsiasi nel computer, indipendentemente dal numero di processori o dallo stato della cache del processore.</returns>
      <param name="location">Campo da leggere.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.IntPtr@)">
      <summary>Legge il valore del campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare dopo questo metodo nel codice, il processore non potrà spostarla in una posizione precedente al metodo stesso.</summary>
      <returns>Valore letto.Questo valore è l'ultimo che è stato scritto da un processore qualsiasi nel computer, indipendentemente dal numero di processori o dallo stato della cache del processore.</returns>
      <param name="location">Campo da leggere.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.SByte@)">
      <summary>Legge il valore del campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare dopo questo metodo nel codice, il processore non potrà spostarla in una posizione precedente al metodo stesso.</summary>
      <returns>Valore letto.Questo valore è l'ultimo che è stato scritto da un processore qualsiasi nel computer, indipendentemente dal numero di processori o dallo stato della cache del processore.</returns>
      <param name="location">Campo da leggere.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.Single@)">
      <summary>Legge il valore del campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare dopo questo metodo nel codice, il processore non potrà spostarla in una posizione precedente al metodo stesso.</summary>
      <returns>Valore letto.Questo valore è l'ultimo che è stato scritto da un processore qualsiasi nel computer, indipendentemente dal numero di processori o dallo stato della cache del processore.</returns>
      <param name="location">Campo da leggere.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.UInt16@)">
      <summary>Legge il valore del campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare dopo questo metodo nel codice, il processore non potrà spostarla in una posizione precedente al metodo stesso.</summary>
      <returns>Valore letto.Questo valore è l'ultimo che è stato scritto da un processore qualsiasi nel computer, indipendentemente dal numero di processori o dallo stato della cache del processore.</returns>
      <param name="location">Campo da leggere.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.UInt32@)">
      <summary>Legge il valore del campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare dopo questo metodo nel codice, il processore non potrà spostarla in una posizione precedente al metodo stesso.</summary>
      <returns>Valore letto.Questo valore è l'ultimo che è stato scritto da un processore qualsiasi nel computer, indipendentemente dal numero di processori o dallo stato della cache del processore.</returns>
      <param name="location">Campo da leggere.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.UInt64@)">
      <summary>Legge il valore del campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare dopo questo metodo nel codice, il processore non potrà spostarla in una posizione precedente al metodo stesso.</summary>
      <returns>Valore letto.Questo valore è l'ultimo che è stato scritto da un processore qualsiasi nel computer, indipendentemente dal numero di processori o dallo stato della cache del processore.</returns>
      <param name="location">Campo da leggere.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read(System.UIntPtr@)">
      <summary>Legge il valore del campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare dopo questo metodo nel codice, il processore non potrà spostarla in una posizione precedente al metodo stesso.</summary>
      <returns>Valore letto.Questo valore è l'ultimo che è stato scritto da un processore qualsiasi nel computer, indipendentemente dal numero di processori o dallo stato della cache del processore.</returns>
      <param name="location">Campo da leggere.</param>
    </member>
    <member name="M:System.Threading.Volatile.Read``1(``0@)">
      <summary>Legge il riferimento a un oggetto dal campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare dopo questo metodo nel codice, il processore non potrà spostarla in una posizione precedente al metodo stesso.</summary>
      <returns>Riferimento a <paramref name="T" /> che è stato letto.Questo riferimento è l'ultimo che è stato scritto da un processore qualsiasi nel computer, indipendentemente dal numero di processori o dallo stato della cache del processore.</returns>
      <param name="location">Campo da leggere.</param>
      <typeparam name="T">Tipo di campo da leggere.Deve essere un tipo di riferimento, non un tipo di valore.</typeparam>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Boolean@,System.Boolean)">
      <summary>Scrive il valore specificato nel campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare prima di questo metodo nel codice, il processore non potrà spostarla in una posizione successiva al metodo stesso.</summary>
      <param name="location">Campo in cui viene scritto il valore.</param>
      <param name="value">Valore da scrivere.Il valore viene scritto immediatamente, in modo da essere reso visibile a tutti i processori nel computer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Byte@,System.Byte)">
      <summary>Scrive il valore specificato nel campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare prima di questo metodo nel codice, il processore non potrà spostarla in una posizione successiva al metodo stesso.</summary>
      <param name="location">Campo in cui viene scritto il valore.</param>
      <param name="value">Valore da scrivere.Il valore viene scritto immediatamente, in modo da essere reso visibile a tutti i processori nel computer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Double@,System.Double)">
      <summary>Scrive il valore specificato nel campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare prima di questo metodo nel codice, il processore non potrà spostarla in una posizione successiva al metodo stesso.</summary>
      <param name="location">Campo in cui viene scritto il valore.</param>
      <param name="value">Valore da scrivere.Il valore viene scritto immediatamente, in modo da essere reso visibile a tutti i processori nel computer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Int16@,System.Int16)">
      <summary>Scrive il valore specificato nel campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare prima di questo metodo nel codice, il processore non potrà spostarla in una posizione successiva al metodo stesso.</summary>
      <param name="location">Campo in cui viene scritto il valore.</param>
      <param name="value">Valore da scrivere.Il valore viene scritto immediatamente, in modo da essere reso visibile a tutti i processori nel computer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Int32@,System.Int32)">
      <summary>Scrive il valore specificato nel campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare prima di questo metodo nel codice, il processore non potrà spostarla in una posizione successiva al metodo stesso.</summary>
      <param name="location">Campo in cui viene scritto il valore.</param>
      <param name="value">Valore da scrivere.Il valore viene scritto immediatamente, in modo da essere reso visibile a tutti i processori nel computer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Int64@,System.Int64)">
      <summary>Scrive il valore specificato nel campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di memoria compare prima di questo metodo nel codice, il processore non potrà spostarla in una posizione successiva al metodo stesso.</summary>
      <param name="location">Campo in cui viene scritto il valore.</param>
      <param name="value">Valore da scrivere.Il valore viene scritto immediatamente, in modo da essere reso visibile a tutti i processori nel computer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.IntPtr@,System.IntPtr)">
      <summary>Scrive il valore specificato nel campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare prima di questo metodo nel codice, il processore non potrà spostarla in una posizione successiva al metodo stesso.</summary>
      <param name="location">Campo in cui viene scritto il valore.</param>
      <param name="value">Valore da scrivere.Il valore viene scritto immediatamente, in modo da essere reso visibile a tutti i processori nel computer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.SByte@,System.SByte)">
      <summary>Scrive il valore specificato nel campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare prima di questo metodo nel codice, il processore non potrà spostarla in una posizione successiva al metodo stesso.</summary>
      <param name="location">Campo in cui viene scritto il valore.</param>
      <param name="value">Valore da scrivere.Il valore viene scritto immediatamente, in modo da essere reso visibile a tutti i processori nel computer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.Single@,System.Single)">
      <summary>Scrive il valore specificato nel campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare prima di questo metodo nel codice, il processore non potrà spostarla in una posizione successiva al metodo stesso.</summary>
      <param name="location">Campo in cui viene scritto il valore.</param>
      <param name="value">Valore da scrivere.Il valore viene scritto immediatamente, in modo da essere reso visibile a tutti i processori nel computer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.UInt16@,System.UInt16)">
      <summary>Scrive il valore specificato nel campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare prima di questo metodo nel codice, il processore non potrà spostarla in una posizione successiva al metodo stesso.</summary>
      <param name="location">Campo in cui viene scritto il valore.</param>
      <param name="value">Valore da scrivere.Il valore viene scritto immediatamente, in modo da essere reso visibile a tutti i processori nel computer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.UInt32@,System.UInt32)">
      <summary>Scrive il valore specificato nel campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare prima di questo metodo nel codice, il processore non potrà spostarla in una posizione successiva al metodo stesso.</summary>
      <param name="location">Campo in cui viene scritto il valore.</param>
      <param name="value">Valore da scrivere.Il valore viene scritto immediatamente, in modo da essere reso visibile a tutti i processori nel computer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.UInt64@,System.UInt64)">
      <summary>Scrive il valore specificato nel campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare prima di questo metodo nel codice, il processore non potrà spostarla in una posizione successiva al metodo stesso.</summary>
      <param name="location">Campo in cui viene scritto il valore.</param>
      <param name="value">Valore da scrivere.Il valore viene scritto immediatamente, in modo da essere reso visibile a tutti i processori nel computer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write(System.UIntPtr@,System.UIntPtr)">
      <summary>Scrive il valore specificato nel campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare prima di questo metodo nel codice, il processore non potrà spostarla in una posizione successiva al metodo stesso.</summary>
      <param name="location">Campo in cui viene scritto il valore.</param>
      <param name="value">Valore da scrivere.Il valore viene scritto immediatamente, in modo da essere reso visibile a tutti i processori nel computer.</param>
    </member>
    <member name="M:System.Threading.Volatile.Write``1(``0@,``0)">
      <summary>Scrive il riferimento a un oggetto specificato nel campo specificato.Nei sistemi in cui è richiesto, inserisce una barriera di memoria che impedisce al processore di riordinare le operazioni di memoria nel modo seguente: se un'operazione di lettura o di scrittura compare prima di questo metodo nel codice, il processore non potrà spostarla in una posizione successiva al metodo stesso.</summary>
      <param name="location">Campo in cui viene scritto il riferimento a un oggetto.</param>
      <param name="value">Riferimento a un oggetto da scrivere.Il riferimento viene scritto immediatamente, in modo da essere reso visibile a tutti i processori nel computer.</param>
      <typeparam name="T">Tipo di campo da scrivere.Deve essere un tipo di riferimento, non un tipo di valore.</typeparam>
    </member>
    <member name="T:System.Threading.WaitHandleCannotBeOpenedException">
      <summary>Eccezione generata durante il tentativo di aprire un semaforo o un mutex di sistema inesistente.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Threading.WaitHandleCannotBeOpenedException.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.WaitHandleCannotBeOpenedException" /> con valori predefiniti.</summary>
    </member>
    <member name="M:System.Threading.WaitHandleCannotBeOpenedException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.WaitHandleCannotBeOpenedException" /> con un messaggio di errore specificato.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione</param>
    </member>
    <member name="M:System.Threading.WaitHandleCannotBeOpenedException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Threading.WaitHandleCannotBeOpenedException" /> con un messaggio di errore specificato e un riferimento all'eccezione interna che è la causa dell'eccezione corrente.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione</param>
      <param name="innerException">Eccezione causa dell'eccezione corrente.Se il parametro <paramref name="innerException" /> non è null, l'eccezione corrente viene generata in un blocco catch in cui viene gestita l'eccezione interna.</param>
    </member>
  </members>
</doc>