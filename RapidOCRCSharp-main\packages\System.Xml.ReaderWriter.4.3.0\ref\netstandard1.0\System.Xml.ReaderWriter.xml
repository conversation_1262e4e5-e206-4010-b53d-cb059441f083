﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.ReaderWriter</name>
  </assembly>
  <members>
    <member name="T:System.Xml.ConformanceLevel">
      <summary>Specifies the amount of input or output checking that <see cref="T:System.Xml.XmlReader" /> and <see cref="T:System.Xml.XmlWriter" /> objects perform.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Auto">
      <summary>The <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.Xml.XmlWriter" /> object automatically detects whether document-level or fragment-level checking should be performed, and does the appropriate checking. If you're wrapping another <see cref="T:System.Xml.XmlReader" /> or <see cref="T:System.Xml.XmlWriter" /> object, the outer object doesn't do any additional conformance checking. Conformance checking is left up to the underlying object.See the <see cref="P:System.Xml.XmlReaderSettings.ConformanceLevel" /> and <see cref="P:System.Xml.XmlWriterSettings.ConformanceLevel" /> properties for details on how the compliance level is determined.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Document">
      <summary>The XML data complies with the rules for a well-formed XML 1.0 document, as defined by the W3C.</summary>
    </member>
    <member name="F:System.Xml.ConformanceLevel.Fragment">
      <summary>The XML data is a well-formed XML fragment, as defined by the W3C.</summary>
    </member>
    <member name="T:System.Xml.DtdProcessing">
      <summary>Specifies the options for processing DTDs. The <see cref="T:System.Xml.DtdProcessing" /> enumeration is used by the <see cref="T:System.Xml.XmlReaderSettings" /> class.</summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Ignore">
      <summary>Causes the DOCTYPE element to be ignored. No DTD processing occurs. </summary>
    </member>
    <member name="F:System.Xml.DtdProcessing.Prohibit">
      <summary>Specifies that when a DTD is encountered, an <see cref="T:System.Xml.XmlException" /> is thrown with a message that states that DTDs are prohibited. This is the default behavior.</summary>
    </member>
    <member name="T:System.Xml.IXmlLineInfo">
      <summary>Provides an interface to enable a class to return line and position information.</summary>
    </member>
    <member name="M:System.Xml.IXmlLineInfo.HasLineInfo">
      <summary>Gets a value indicating whether the class can return line information.</summary>
      <returns>true if <see cref="P:System.Xml.IXmlLineInfo.LineNumber" /> and <see cref="P:System.Xml.IXmlLineInfo.LinePosition" /> can be provided; otherwise, false.</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LineNumber">
      <summary>Gets the current line number.</summary>
      <returns>The current line number or 0 if no line information is available (for example, <see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" /> returns false).</returns>
    </member>
    <member name="P:System.Xml.IXmlLineInfo.LinePosition">
      <summary>Gets the current line position.</summary>
      <returns>The current line position or 0 if no line information is available (for example, <see cref="M:System.Xml.IXmlLineInfo.HasLineInfo" /> returns false).</returns>
    </member>
    <member name="T:System.Xml.IXmlNamespaceResolver">
      <summary>Provides read-only access to a set of prefix and namespace mappings.</summary>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>Gets a collection of defined prefix-namespace mappings that are currently in scope.</summary>
      <returns>An <see cref="T:System.Collections.IDictionary" /> that contains the current in-scope namespaces.</returns>
      <param name="scope">An <see cref="T:System.Xml.XmlNamespaceScope" /> value that specifies the type of namespace nodes to return.</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupNamespace(System.String)">
      <summary>Gets the namespace URI mapped to the specified prefix.</summary>
      <returns>The namespace URI that is mapped to the prefix; null if the prefix is not mapped to a namespace URI.</returns>
      <param name="prefix">The prefix whose namespace URI you wish to find.</param>
    </member>
    <member name="M:System.Xml.IXmlNamespaceResolver.LookupPrefix(System.String)">
      <summary>Gets the prefix that is mapped to the specified namespace URI.</summary>
      <returns>The prefix that is mapped to the namespace URI; null if the namespace URI is not mapped to a prefix.</returns>
      <param name="namespaceName">The namespace URI whose prefix you wish to find.</param>
    </member>
    <member name="T:System.Xml.NamespaceHandling">
      <summary>Specifies whether to remove duplicate namespace declarations in the <see cref="T:System.Xml.XmlWriter" />. </summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.Default">
      <summary>Specifies that duplicate namespace declarations will not be removed.</summary>
    </member>
    <member name="F:System.Xml.NamespaceHandling.OmitDuplicates">
      <summary>Specifies that duplicate namespace declarations will be removed. For the duplicate namespace to be removed, the prefix and the namespace must match.</summary>
    </member>
    <member name="T:System.Xml.NameTable">
      <summary>Implements a single-threaded <see cref="T:System.Xml.XmlNameTable" />.</summary>
    </member>
    <member name="M:System.Xml.NameTable.#ctor">
      <summary>Initializes a new instance of the NameTable class.</summary>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>Atomizes the specified string and adds it to the NameTable.</summary>
      <returns>The atomized string or the existing string if one already exists in the NameTable. If <paramref name="len" /> is zero, String.Empty is returned.</returns>
      <param name="key">The character array containing the string to add. </param>
      <param name="start">The zero-based index into the array specifying the first character of the string. </param>
      <param name="len">The number of characters in the string. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />-or- <paramref name="start" /> &gt;= <paramref name="key" />.Length -or- <paramref name="len" /> &gt;= <paramref name="key" />.Length The above conditions do not cause an exception to be thrown if <paramref name="len" /> =0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Add(System.String)">
      <summary>Atomizes the specified string and adds it to the NameTable.</summary>
      <returns>The atomized string or the existing string if it already exists in the NameTable.</returns>
      <param name="key">The string to add. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is null. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>Gets the atomized string containing the same characters as the specified range of characters in the given array.</summary>
      <returns>The atomized string or null if the string has not already been atomized. If <paramref name="len" /> is zero, String.Empty is returned.</returns>
      <param name="key">The character array containing the name to find. </param>
      <param name="start">The zero-based index into the array specifying the first character of the name. </param>
      <param name="len">The number of characters in the name. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="start" />-or- <paramref name="start" /> &gt;= <paramref name="key" />.Length -or- <paramref name="len" /> &gt;= <paramref name="key" />.Length The above conditions do not cause an exception to be thrown if <paramref name="len" /> =0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="len" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.NameTable.Get(System.String)">
      <summary>Gets the atomized string with the specified value.</summary>
      <returns>The atomized string object or null if the string has not already been atomized.</returns>
      <param name="value">The name to find. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
    </member>
    <member name="T:System.Xml.NewLineHandling">
      <summary>Specifies how to handle line breaks.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Entitize">
      <summary>New line characters are entitized. This setting preserves all characters when the output is read by a normalizing <see cref="T:System.Xml.XmlReader" />.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.None">
      <summary>The new line characters are unchanged. The output is the same as the input.</summary>
    </member>
    <member name="F:System.Xml.NewLineHandling.Replace">
      <summary>New line characters are replaced to match the character specified in the <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" />  property.</summary>
    </member>
    <member name="T:System.Xml.ReadState">
      <summary>Specifies the state of the reader.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Closed">
      <summary>The <see cref="M:System.Xml.XmlReader.Close" /> method has been called.</summary>
    </member>
    <member name="F:System.Xml.ReadState.EndOfFile">
      <summary>The end of the file has been reached successfully.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Error">
      <summary>An error occurred that prevents the read operation from continuing.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Initial">
      <summary>The Read method has not been called.</summary>
    </member>
    <member name="F:System.Xml.ReadState.Interactive">
      <summary>The Read method has been called. Additional methods may be called on the reader.</summary>
    </member>
    <member name="T:System.Xml.WriteState">
      <summary>Specifies the state of the <see cref="T:System.Xml.XmlWriter" />.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Attribute">
      <summary>Indicates that an attribute value is being written.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Closed">
      <summary>Indicates that the <see cref="M:System.Xml.XmlWriter.Close" /> method has been called.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Content">
      <summary>Indicates that element content is being written.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Element">
      <summary>Indicates that an element start tag is being written.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Error">
      <summary>An exception has been thrown, which has left the <see cref="T:System.Xml.XmlWriter" /> in an invalid state. You can call the <see cref="M:System.Xml.XmlWriter.Close" /> method to put the <see cref="T:System.Xml.XmlWriter" /> in the <see cref="F:System.Xml.WriteState.Closed" /> state. Any other <see cref="T:System.Xml.XmlWriter" /> method calls results in an <see cref="T:System.InvalidOperationException" />.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Prolog">
      <summary>Indicates that the prolog is being written.</summary>
    </member>
    <member name="F:System.Xml.WriteState.Start">
      <summary>Indicates that a Write method has not yet been called.</summary>
    </member>
    <member name="T:System.Xml.XmlConvert">
      <summary>Encodes and decodes XML names, and provides methods for converting between common language runtime types and XML Schema definition language (XSD) types. When converting data types, the values returned are locale-independent.</summary>
    </member>
    <member name="M:System.Xml.XmlConvert.DecodeName(System.String)">
      <summary>Decodes a name. This method does the reverse of the <see cref="M:System.Xml.XmlConvert.EncodeName(System.String)" /> and <see cref="M:System.Xml.XmlConvert.EncodeLocalName(System.String)" /> methods.</summary>
      <returns>The decoded name.</returns>
      <param name="name">The name to be transformed. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeLocalName(System.String)">
      <summary>Converts the name to a valid XML local name.</summary>
      <returns>The encoded name.</returns>
      <param name="name">The name to be encoded. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeName(System.String)">
      <summary>Converts the name to a valid XML name.</summary>
      <returns>Returns the name with any invalid characters replaced by an escape string.</returns>
      <param name="name">A name to be translated. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.EncodeNmToken(System.String)">
      <summary>Verifies the name is valid according to the XML specification.</summary>
      <returns>The encoded name.</returns>
      <param name="name">The name to be encoded. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToBoolean(System.String)">
      <summary>Converts the <see cref="T:System.String" /> to a <see cref="T:System.Boolean" /> equivalent.</summary>
      <returns>A Boolean value, that is, true or false.</returns>
      <param name="s">The string to convert. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> does not represent a Boolean value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToByte(System.String)">
      <summary>Converts the <see cref="T:System.String" /> to a <see cref="T:System.Byte" /> equivalent.</summary>
      <returns>A Byte equivalent of the string.</returns>
      <param name="s">The string to convert. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Byte.MinValue" /> or greater than <see cref="F:System.Byte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToChar(System.String)">
      <summary>Converts the <see cref="T:System.String" /> to a <see cref="T:System.Char" /> equivalent.</summary>
      <returns>A Char representing the single character.</returns>
      <param name="s">The string containing a single character to convert. </param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="s" /> parameter is null. </exception>
      <exception cref="T:System.FormatException">The <paramref name="s" /> parameter contains more than one character. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTime(System.String,System.Xml.XmlDateTimeSerializationMode)">
      <summary>Converts the <see cref="T:System.String" /> to a <see cref="T:System.DateTime" /> using the <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> specified</summary>
      <returns>A <see cref="T:System.DateTime" /> equivalent of the <see cref="T:System.String" />.</returns>
      <param name="s">The <see cref="T:System.String" /> value to convert.</param>
      <param name="dateTimeOption">One of the <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> values that specify whether the date should be converted to local time or preserved as Coordinated Universal Time (UTC), if it is a UTC date.</param>
      <exception cref="T:System.NullReferenceException">
        <paramref name="s" /> is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="dateTimeOption" /> value is null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is an empty string or is not in a valid format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String)">
      <summary>Converts the supplied <see cref="T:System.String" /> to a <see cref="T:System.DateTimeOffset" /> equivalent.</summary>
      <returns>The <see cref="T:System.DateTimeOffset" /> equivalent of the supplied string.</returns>
      <param name="s">The string to convert.Note   The string must conform to a subset of the W3C Recommendation for the XML dateTime type. For more information see http://www.w3.org/TR/xmlschema-2/#dateTime.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The argument passed to this method is outside the range of allowable values. For information about allowable values, see <see cref="T:System.DateTimeOffset" />.</exception>
      <exception cref="T:System.FormatException">The argument passed to this method does not conform to a subset of the W3C Recommendations for the XML dateTime type. For more information see http://www.w3.org/TR/xmlschema-2/#dateTime.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String)">
      <summary>Converts the supplied <see cref="T:System.String" /> to a <see cref="T:System.DateTimeOffset" /> equivalent.</summary>
      <returns>The <see cref="T:System.DateTimeOffset" /> equivalent of the supplied string.</returns>
      <param name="s">The string to convert.</param>
      <param name="format">The format from which <paramref name="s" /> is converted. The format parameter can be any subset of the W3C Recommendation for the XML dateTime type. (For more information see http://www.w3.org/TR/xmlschema-2/#dateTime.) The string <paramref name="s" /> is validated against this format.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> or <paramref name="format" /> is an empty string or is not in the specified format.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDateTimeOffset(System.String,System.String[])">
      <summary>Converts the supplied <see cref="T:System.String" /> to a <see cref="T:System.DateTimeOffset" /> equivalent.</summary>
      <returns>The <see cref="T:System.DateTimeOffset" /> equivalent of the supplied string.</returns>
      <param name="s">The string to convert.</param>
      <param name="formats">An array of formats from which <paramref name="s" /> can be converted. Each format in <paramref name="formats" /> can be any subset of the W3C Recommendation for the XML dateTime type. (For more information see http://www.w3.org/TR/xmlschema-2/#dateTime.) The string <paramref name="s" /> is validated against one of these formats.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDecimal(System.String)">
      <summary>Converts the <see cref="T:System.String" /> to a <see cref="T:System.Decimal" /> equivalent.</summary>
      <returns>A Decimal equivalent of the string.</returns>
      <param name="s">The string to convert. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Decimal.MinValue" /> or greater than <see cref="F:System.Decimal.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToDouble(System.String)">
      <summary>Converts the <see cref="T:System.String" /> to a <see cref="T:System.Double" /> equivalent.</summary>
      <returns>A Double equivalent of the string.</returns>
      <param name="s">The string to convert. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Double.MinValue" /> or greater than <see cref="F:System.Double.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToGuid(System.String)">
      <summary>Converts the <see cref="T:System.String" /> to a <see cref="T:System.Guid" /> equivalent.</summary>
      <returns>A Guid equivalent of the string.</returns>
      <param name="s">The string to convert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt16(System.String)">
      <summary>Converts the <see cref="T:System.String" /> to a <see cref="T:System.Int16" /> equivalent.</summary>
      <returns>An Int16 equivalent of the string.</returns>
      <param name="s">The string to convert. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int16.MinValue" /> or greater than <see cref="F:System.Int16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt32(System.String)">
      <summary>Converts the <see cref="T:System.String" /> to a <see cref="T:System.Int32" /> equivalent.</summary>
      <returns>An Int32 equivalent of the string.</returns>
      <param name="s">The string to convert. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int32.MinValue" /> or greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToInt64(System.String)">
      <summary>Converts the <see cref="T:System.String" /> to a <see cref="T:System.Int64" /> equivalent.</summary>
      <returns>An Int64 equivalent of the string.</returns>
      <param name="s">The string to convert. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Int64.MinValue" /> or greater than <see cref="F:System.Int64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSByte(System.String)">
      <summary>Converts the <see cref="T:System.String" /> to a <see cref="T:System.SByte" /> equivalent.</summary>
      <returns>An SByte equivalent of the string.</returns>
      <param name="s">The string to convert. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.SByte.MinValue" /> or greater than <see cref="F:System.SByte.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToSingle(System.String)">
      <summary>Converts the <see cref="T:System.String" /> to a <see cref="T:System.Single" /> equivalent.</summary>
      <returns>A Single equivalent of the string.</returns>
      <param name="s">The string to convert. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.Single.MinValue" /> or greater than <see cref="F:System.Single.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Boolean)">
      <summary>Converts the <see cref="T:System.Boolean" /> to a <see cref="T:System.String" />.</summary>
      <returns>A string representation of the Boolean, that is, "true" or "false".</returns>
      <param name="value">The value to convert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Byte)">
      <summary>Converts the <see cref="T:System.Byte" /> to a <see cref="T:System.String" />.</summary>
      <returns>A string representation of the Byte.</returns>
      <param name="value">The value to convert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Char)">
      <summary>Converts the <see cref="T:System.Char" /> to a <see cref="T:System.String" />.</summary>
      <returns>A string representation of the Char.</returns>
      <param name="value">The value to convert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTime,System.Xml.XmlDateTimeSerializationMode)">
      <summary>Converts the <see cref="T:System.DateTime" /> to a <see cref="T:System.String" /> using the <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> specified.</summary>
      <returns>A <see cref="T:System.String" /> equivalent of the <see cref="T:System.DateTime" />.</returns>
      <param name="value">The <see cref="T:System.DateTime" /> value to convert.</param>
      <param name="dateTimeOption">One of the <see cref="T:System.Xml.XmlDateTimeSerializationMode" /> values that specify how to treat the <see cref="T:System.DateTime" /> value.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="dateTimeOption" /> value is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> or <paramref name="dateTimeOption" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset)">
      <summary>Converts the supplied <see cref="T:System.DateTimeOffset" /> to a <see cref="T:System.String" />.</summary>
      <returns>A <see cref="T:System.String" /> representation of the supplied <see cref="T:System.DateTimeOffset" />.</returns>
      <param name="value">The <see cref="T:System.DateTimeOffset" /> to be converted.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.DateTimeOffset,System.String)">
      <summary>Converts the supplied <see cref="T:System.DateTimeOffset" /> to a <see cref="T:System.String" /> in the specified format.</summary>
      <returns>A <see cref="T:System.String" /> representation in the specified format of the supplied <see cref="T:System.DateTimeOffset" />.</returns>
      <param name="value">The <see cref="T:System.DateTimeOffset" /> to be converted.</param>
      <param name="format">The format to which <paramref name="s" /> is converted. The format parameter can be any subset of the W3C Recommendation for the XML dateTime type. (For more information see http://www.w3.org/TR/xmlschema-2/#dateTime.)</param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Decimal)">
      <summary>Converts the <see cref="T:System.Decimal" /> to a <see cref="T:System.String" />.</summary>
      <returns>A string representation of the Decimal.</returns>
      <param name="value">The value to convert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Double)">
      <summary>Converts the <see cref="T:System.Double" /> to a <see cref="T:System.String" />.</summary>
      <returns>A string representation of the Double.</returns>
      <param name="value">The value to convert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Guid)">
      <summary>Converts the <see cref="T:System.Guid" /> to a <see cref="T:System.String" />.</summary>
      <returns>A string representation of the Guid.</returns>
      <param name="value">The value to convert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int16)">
      <summary>Converts the <see cref="T:System.Int16" /> to a <see cref="T:System.String" />.</summary>
      <returns>A string representation of the Int16.</returns>
      <param name="value">The value to convert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int32)">
      <summary>Converts the <see cref="T:System.Int32" /> to a <see cref="T:System.String" />.</summary>
      <returns>A string representation of the Int32.</returns>
      <param name="value">The value to convert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Int64)">
      <summary>Converts the <see cref="T:System.Int64" /> to a <see cref="T:System.String" />.</summary>
      <returns>A string representation of the Int64.</returns>
      <param name="value">The value to convert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.SByte)">
      <summary>Converts the <see cref="T:System.SByte" /> to a <see cref="T:System.String" />.</summary>
      <returns>A string representation of the SByte.</returns>
      <param name="value">The value to convert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.Single)">
      <summary>Converts the <see cref="T:System.Single" /> to a <see cref="T:System.String" />.</summary>
      <returns>A string representation of the Single.</returns>
      <param name="value">The value to convert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.TimeSpan)">
      <summary>Converts the <see cref="T:System.TimeSpan" /> to a <see cref="T:System.String" />.</summary>
      <returns>A string representation of the TimeSpan.</returns>
      <param name="value">The value to convert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt16)">
      <summary>Converts the <see cref="T:System.UInt16" /> to a <see cref="T:System.String" />.</summary>
      <returns>A string representation of the UInt16.</returns>
      <param name="value">The value to convert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt32)">
      <summary>Converts the <see cref="T:System.UInt32" /> to a <see cref="T:System.String" />.</summary>
      <returns>A string representation of the UInt32.</returns>
      <param name="value">The value to convert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToString(System.UInt64)">
      <summary>Converts the <see cref="T:System.UInt64" /> to a <see cref="T:System.String" />.</summary>
      <returns>A string representation of the UInt64.</returns>
      <param name="value">The value to convert. </param>
    </member>
    <member name="M:System.Xml.XmlConvert.ToTimeSpan(System.String)">
      <summary>Converts the <see cref="T:System.String" /> to a <see cref="T:System.TimeSpan" /> equivalent.</summary>
      <returns>A TimeSpan equivalent of the string.</returns>
      <param name="s">The string to convert. The string format must conform to the W3C XML Schema Part 2: Datatypes recommendation for duration.</param>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in correct format to represent a TimeSpan value. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt16(System.String)">
      <summary>Converts the <see cref="T:System.String" /> to a <see cref="T:System.UInt16" /> equivalent.</summary>
      <returns>A UInt16 equivalent of the string.</returns>
      <param name="s">The string to convert. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt16.MinValue" /> or greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt32(System.String)">
      <summary>Converts the <see cref="T:System.String" /> to a <see cref="T:System.UInt32" /> equivalent.</summary>
      <returns>A UInt32 equivalent of the string.</returns>
      <param name="s">The string to convert. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt32.MinValue" /> or greater than <see cref="F:System.UInt32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.ToUInt64(System.String)">
      <summary>Converts the <see cref="T:System.String" /> to a <see cref="T:System.UInt64" /> equivalent.</summary>
      <returns>A UInt64 equivalent of the string.</returns>
      <param name="s">The string to convert. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" /> is null. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="s" /> is not in the correct format. </exception>
      <exception cref="T:System.OverflowException">
        <paramref name="s" /> represents a number less than <see cref="F:System.UInt64.MinValue" /> or greater than <see cref="F:System.UInt64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyName(System.String)">
      <summary>Verifies that the name is a valid name according to the W3C Extended Markup Language recommendation.</summary>
      <returns>The name, if it is a valid XML name.</returns>
      <param name="name">The name to verify. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid XML name. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNCName(System.String)">
      <summary>Verifies that the name is a valid NCName according to the W3C Extended Markup Language recommendation. An NCName is a name that cannot contain a colon.</summary>
      <returns>The name, if it is a valid NCName.</returns>
      <param name="name">The name to verify. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null or String.Empty. </exception>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="name" /> is not a valid non-colon name. </exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyNMTOKEN(System.String)">
      <summary>Verifies that the string is a valid NMTOKEN according to the W3C XML Schema Part2: Datatypes recommendation</summary>
      <returns>The name token, if it is a valid NMTOKEN.</returns>
      <param name="name">The string you wish to verify.</param>
      <exception cref="T:System.Xml.XmlException">The string is not a valid name token.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyPublicId(System.String)">
      <summary>Returns the passed in string instance if all the characters in the string argument are valid public id characters.</summary>
      <returns>Returns the passed-in string if all the characters in the argument are valid public id characters.</returns>
      <param name="publicId">
        <see cref="T:System.String" /> that contains the id to validate.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyWhitespace(System.String)">
      <summary>Returns the passed-in string instance if all the characters in the string argument are valid whitespace characters. </summary>
      <returns>Returns the passed-in string instance if all the characters in the string argument are valid whitespace characters, otherwise null.</returns>
      <param name="content">
        <see cref="T:System.String" /> to verify.</param>
    </member>
    <member name="M:System.Xml.XmlConvert.VerifyXmlChars(System.String)">
      <summary>Returns the passed-in string if all the characters and surrogate pair characters in the string argument are valid XML characters, otherwise an XmlException is thrown with information on the first invalid character encountered. </summary>
      <returns>Returns the passed-in string if all the characters and surrogate-pair characters in the string argument are valid XML characters, otherwise an XmlException is thrown with information on the first invalid character encountered.</returns>
      <param name="content">
        <see cref="T:System.String" /> that contains characters to verify.</param>
    </member>
    <member name="T:System.Xml.XmlDateTimeSerializationMode">
      <summary>Specifies how to treat the time value when converting between string and <see cref="T:System.DateTime" />.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Local">
      <summary>Treat as local time. If the <see cref="T:System.DateTime" /> object represents a Coordinated Universal Time (UTC), it is converted to the local time.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.RoundtripKind">
      <summary>Time zone information should be preserved when converting.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Unspecified">
      <summary>Treat as a local time if a <see cref="T:System.DateTime" /> is being converted to a string.</summary>
    </member>
    <member name="F:System.Xml.XmlDateTimeSerializationMode.Utc">
      <summary>Treat as a UTC. If the <see cref="T:System.DateTime" /> object represents a local time, it is converted to a UTC.</summary>
    </member>
    <member name="T:System.Xml.XmlException">
      <summary>Returns detailed information about the last exception.</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor">
      <summary>Initializes a new instance of the XmlException class.</summary>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String)">
      <summary>Initializes a new instance of the XmlException class with a specified error message.</summary>
      <param name="message">The error description. </param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the XmlException class.</summary>
      <param name="message">The description of the error condition. </param>
      <param name="innerException">The <see cref="T:System.Exception" /> that threw the XmlException, if any. This value can be null. </param>
    </member>
    <member name="M:System.Xml.XmlException.#ctor(System.String,System.Exception,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the XmlException class with the specified message, inner exception, line number, and line position.</summary>
      <param name="message">The error description. </param>
      <param name="innerException">The exception that is the cause of the current exception. This value can be null. </param>
      <param name="lineNumber">The line number indicating where the error occurred. </param>
      <param name="linePosition">The line position indicating where the error occurred. </param>
    </member>
    <member name="P:System.Xml.XmlException.LineNumber">
      <summary>Gets the line number indicating where the error occurred.</summary>
      <returns>The line number indicating where the error occurred.</returns>
    </member>
    <member name="P:System.Xml.XmlException.LinePosition">
      <summary>Gets the line position indicating where the error occurred.</summary>
      <returns>The line position indicating where the error occurred.</returns>
    </member>
    <member name="P:System.Xml.XmlException.Message">
      <summary>Gets a message describing the current exception.</summary>
      <returns>The error message that explains the reason for the exception.</returns>
    </member>
    <member name="T:System.Xml.XmlNamespaceManager">
      <summary>Resolves, adds, and removes namespaces to a collection and provides scope management for these namespaces. </summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.#ctor(System.Xml.XmlNameTable)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XmlNamespaceManager" /> class with the specified <see cref="T:System.Xml.XmlNameTable" />.</summary>
      <param name="nameTable">The <see cref="T:System.Xml.XmlNameTable" /> to use. </param>
      <exception cref="T:System.NullReferenceException">null is passed to the constructor </exception>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.AddNamespace(System.String,System.String)">
      <summary>Adds the given namespace to the collection.</summary>
      <param name="prefix">The prefix to associate with the namespace being added. Use String.Empty to add a default namespace.NoteIf the <see cref="T:System.Xml.XmlNamespaceManager" /> will be used for resolving namespaces in an XML Path Language (XPath) expression, a prefix must be specified. If an XPath expression does not include a prefix, it is assumed that the namespace Uniform Resource Identifier (URI) is the empty namespace. For more information about XPath expressions and the <see cref="T:System.Xml.XmlNamespaceManager" />, refer to the <see cref="M:System.Xml.XmlNode.SelectNodes(System.String)" /> and <see cref="M:System.Xml.XPath.XPathExpression.SetContext(System.Xml.XmlNamespaceManager)" /> methods.</param>
      <param name="uri">The namespace to add. </param>
      <exception cref="T:System.ArgumentException">The value for <paramref name="prefix" /> is "xml" or "xmlns". </exception>
      <exception cref="T:System.ArgumentNullException">The value for <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.DefaultNamespace">
      <summary>Gets the namespace URI for the default namespace.</summary>
      <returns>Returns the namespace URI for the default namespace, or String.Empty if there is no default namespace.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetEnumerator">
      <summary>Returns an enumerator to use to iterate through the namespaces in the <see cref="T:System.Xml.XmlNamespaceManager" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> containing the prefixes stored by the <see cref="T:System.Xml.XmlNamespaceManager" />.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>Gets a collection of namespace names keyed by prefix which can be used to enumerate the namespaces currently in scope.</summary>
      <returns>A collection of namespace and prefix pairs currently in scope.</returns>
      <param name="scope">An enumeration value that specifies the type of namespace nodes to return.</param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.HasNamespace(System.String)">
      <summary>Gets a value indicating whether the supplied prefix has a namespace defined for the current pushed scope.</summary>
      <returns>true if there is a namespace defined; otherwise, false.</returns>
      <param name="prefix">The prefix of the namespace you want to find. </param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupNamespace(System.String)">
      <summary>Gets the namespace URI for the specified prefix.</summary>
      <returns>Returns the namespace URI for <paramref name="prefix" /> or null if there is no mapped namespace. The returned string is atomized.For more information on atomized strings, see the <see cref="T:System.Xml.XmlNameTable" /> class.</returns>
      <param name="prefix">The prefix whose namespace URI you want to resolve. To match the default namespace, pass String.Empty. </param>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.LookupPrefix(System.String)">
      <summary>Finds the prefix declared for the given namespace URI.</summary>
      <returns>The matching prefix. If there is no mapped prefix, the method returns String.Empty. If a null value is supplied, then null is returned.</returns>
      <param name="uri">The namespace to resolve for the prefix. </param>
    </member>
    <member name="P:System.Xml.XmlNamespaceManager.NameTable">
      <summary>Gets the <see cref="T:System.Xml.XmlNameTable" /> associated with this object.</summary>
      <returns>The <see cref="T:System.Xml.XmlNameTable" /> used by this object.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PopScope">
      <summary>Pops a namespace scope off the stack.</summary>
      <returns>true if there are namespace scopes left on the stack; false if there are no more namespaces to pop.</returns>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.PushScope">
      <summary>Pushes a namespace scope onto the stack.</summary>
    </member>
    <member name="M:System.Xml.XmlNamespaceManager.RemoveNamespace(System.String,System.String)">
      <summary>Removes the given namespace for the given prefix.</summary>
      <param name="prefix">The prefix for the namespace </param>
      <param name="uri">The namespace to remove for the given prefix. The namespace removed is from the current namespace scope. Namespaces outside the current scope are ignored. </param>
      <exception cref="T:System.ArgumentNullException">The value of <paramref name="prefix" /> or <paramref name="uri" /> is null. </exception>
    </member>
    <member name="T:System.Xml.XmlNamespaceScope">
      <summary>Defines the namespace scope.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.All">
      <summary>All namespaces defined in the scope of the current node. This includes the xmlns:xml namespace which is always declared implicitly. The order of the namespaces returned is not defined.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.ExcludeXml">
      <summary>All namespaces defined in the scope of the current node, excluding the xmlns:xml namespace, which is always declared implicitly. The order of the namespaces returned is not defined.</summary>
    </member>
    <member name="F:System.Xml.XmlNamespaceScope.Local">
      <summary>All namespaces that are defined locally at the current node.</summary>
    </member>
    <member name="T:System.Xml.XmlNameTable">
      <summary>Table of atomized string objects.</summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XmlNameTable" /> class. </summary>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.Char[],System.Int32,System.Int32)">
      <summary>When overridden in a derived class, atomizes the specified string and adds it to the XmlNameTable.</summary>
      <returns>The new atomized string or the existing one if it already exists. If length is zero, String.Empty is returned.</returns>
      <param name="array">The character array containing the name to add. </param>
      <param name="offset">Zero-based index into the array specifying the first character of the name. </param>
      <param name="length">The number of characters in the name. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />-or- <paramref name="offset" /> &gt;= <paramref name="array" />.Length -or- <paramref name="length" /> &gt; <paramref name="array" />.Length The above conditions do not cause an exception to be thrown if <paramref name="length" /> =0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Add(System.String)">
      <summary>When overridden in a derived class, atomizes the specified string and adds it to the XmlNameTable.</summary>
      <returns>The new atomized string or the existing one if it already exists.</returns>
      <param name="array">The name to add. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.Char[],System.Int32,System.Int32)">
      <summary>When overridden in a derived class, gets the atomized string containing the same characters as the specified range of characters in the given array.</summary>
      <returns>The atomized string or null if the string has not already been atomized. If <paramref name="length" /> is zero, String.Empty is returned.</returns>
      <param name="array">The character array containing the name to look up. </param>
      <param name="offset">The zero-based index into the array specifying the first character of the name. </param>
      <param name="length">The number of characters in the name. </param>
      <exception cref="T:System.IndexOutOfRangeException">0 &gt; <paramref name="offset" />-or- <paramref name="offset" /> &gt;= <paramref name="array" />.Length -or- <paramref name="length" /> &gt; <paramref name="array" />.Length The above conditions do not cause an exception to be thrown if <paramref name="length" /> =0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> &lt; 0. </exception>
    </member>
    <member name="M:System.Xml.XmlNameTable.Get(System.String)">
      <summary>When overridden in a derived class, gets the atomized string containing the same value as the specified string.</summary>
      <returns>The atomized string or null if the string has not already been atomized.</returns>
      <param name="array">The name to look up. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null. </exception>
    </member>
    <member name="T:System.Xml.XmlNodeType">
      <summary>Specifies the type of node.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Attribute">
      <summary>An attribute (for example, id='123' ).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.CDATA">
      <summary>A CDATA section (for example, &lt;![CDATA[my escaped text]]&gt; ).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Comment">
      <summary>A comment (for example, &lt;!-- my comment --&gt; ).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Document">
      <summary>A document object that, as the root of the document tree, provides access to the entire XML document.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentFragment">
      <summary>A document fragment.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.DocumentType">
      <summary>The document type declaration, indicated by the following tag (for example, &lt;!DOCTYPE...&gt; ).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Element">
      <summary>An element (for example, &lt;item&gt; ).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndElement">
      <summary>An end element tag (for example, &lt;/item&gt; ).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EndEntity">
      <summary>Returned when XmlReader gets to the end of the entity replacement as a result of a call to <see cref="M:System.Xml.XmlReader.ResolveEntity" />.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Entity">
      <summary>An entity declaration (for example, &lt;!ENTITY...&gt; ).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.EntityReference">
      <summary>A reference to an entity (for example, &amp;num; ).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.None">
      <summary>This is returned by the <see cref="T:System.Xml.XmlReader" /> if a Read method has not been called.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Notation">
      <summary>A notation in the document type declaration (for example, &lt;!NOTATION...&gt; ).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.ProcessingInstruction">
      <summary>A processing instruction (for example, &lt;?pi test?&gt; ).</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.SignificantWhitespace">
      <summary>White space between markup in a mixed content model or white space within the xml:space="preserve" scope.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Text">
      <summary>The text content of a node.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.Whitespace">
      <summary>White space between markup.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeType.XmlDeclaration">
      <summary>The XML declaration (for example, &lt;?xml version='1.0'?&gt; ).</summary>
    </member>
    <member name="T:System.Xml.XmlParserContext">
      <summary>Provides all the context information required by the <see cref="T:System.Xml.XmlReader" /> to parse an XML fragment.</summary>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace)">
      <summary>Initializes a new instance of the XmlParserContext class with the specified <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, base URI, xml:lang, xml:space, and document type values.</summary>
      <param name="nt">The <see cref="T:System.Xml.XmlNameTable" /> to use to atomize strings. If this is null, the name table used to construct the <paramref name="nsMgr" /> is used instead. For more information about atomized strings, see <see cref="T:System.Xml.XmlNameTable" />. </param>
      <param name="nsMgr">The <see cref="T:System.Xml.XmlNamespaceManager" /> to use for looking up namespace information, or null. </param>
      <param name="docTypeName">The name of the document type declaration. </param>
      <param name="pubId">The public identifier. </param>
      <param name="sysId">The system identifier. </param>
      <param name="internalSubset">The internal DTD subset. The DTD subset is used for entity resolution, not for document validation.</param>
      <param name="baseURI">The base URI for the XML fragment (the location from which the fragment was loaded). </param>
      <param name="xmlLang">The xml:lang scope. </param>
      <param name="xmlSpace">An <see cref="T:System.Xml.XmlSpace" /> value indicating the xml:space scope. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> is not the same XmlNameTable used to construct <paramref name="nsMgr" />. </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.String,System.String,System.String,System.String,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>Initializes a new instance of the XmlParserContext class with the specified <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, base URI, xml:lang, xml:space, encoding, and document type values.</summary>
      <param name="nt">The <see cref="T:System.Xml.XmlNameTable" /> to use to atomize strings. If this is null, the name table used to construct the <paramref name="nsMgr" /> is used instead. For more information about atomized strings, see <see cref="T:System.Xml.XmlNameTable" />. </param>
      <param name="nsMgr">The <see cref="T:System.Xml.XmlNamespaceManager" /> to use for looking up namespace information, or null. </param>
      <param name="docTypeName">The name of the document type declaration. </param>
      <param name="pubId">The public identifier. </param>
      <param name="sysId">The system identifier. </param>
      <param name="internalSubset">The internal DTD subset. The DTD is used for entity resolution, not for document validation.</param>
      <param name="baseURI">The base URI for the XML fragment (the location from which the fragment was loaded). </param>
      <param name="xmlLang">The xml:lang scope. </param>
      <param name="xmlSpace">An <see cref="T:System.Xml.XmlSpace" /> value indicating the xml:space scope. </param>
      <param name="enc">An <see cref="T:System.Text.Encoding" /> object indicating the encoding setting. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> is not the same XmlNameTable used to construct <paramref name="nsMgr" />. </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace)">
      <summary>Initializes a new instance of the XmlParserContext class with the specified <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, xml:lang, and xml:space values.</summary>
      <param name="nt">The <see cref="T:System.Xml.XmlNameTable" /> to use to atomize strings. If this is null, the name table used to construct the <paramref name="nsMgr" /> is used instead. For more information about atomized strings, see <see cref="T:System.Xml.XmlNameTable" />. </param>
      <param name="nsMgr">The <see cref="T:System.Xml.XmlNamespaceManager" /> to use for looking up namespace information, or null. </param>
      <param name="xmlLang">The xml:lang scope. </param>
      <param name="xmlSpace">An <see cref="T:System.Xml.XmlSpace" /> value indicating the xml:space scope. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> is not the same XmlNameTable used to construct <paramref name="nsMgr" />. </exception>
    </member>
    <member name="M:System.Xml.XmlParserContext.#ctor(System.Xml.XmlNameTable,System.Xml.XmlNamespaceManager,System.String,System.Xml.XmlSpace,System.Text.Encoding)">
      <summary>Initializes a new instance of the XmlParserContext class with the specified <see cref="T:System.Xml.XmlNameTable" />, <see cref="T:System.Xml.XmlNamespaceManager" />, xml:lang, xml:space, and encoding.</summary>
      <param name="nt">The <see cref="T:System.Xml.XmlNameTable" /> to use to atomize strings. If this is null, the name table used to construct the <paramref name="nsMgr" /> is used instead. For more information on atomized strings, see <see cref="T:System.Xml.XmlNameTable" />. </param>
      <param name="nsMgr">The <see cref="T:System.Xml.XmlNamespaceManager" /> to use for looking up namespace information, or null. </param>
      <param name="xmlLang">The xml:lang scope. </param>
      <param name="xmlSpace">An <see cref="T:System.Xml.XmlSpace" /> value indicating the xml:space scope. </param>
      <param name="enc">An <see cref="T:System.Text.Encoding" /> object indicating the encoding setting. </param>
      <exception cref="T:System.Xml.XmlException">
        <paramref name="nt" /> is not the same XmlNameTable used to construct <paramref name="nsMgr" />. </exception>
    </member>
    <member name="P:System.Xml.XmlParserContext.BaseURI">
      <summary>Gets or sets the base URI.</summary>
      <returns>The base URI to use to resolve the DTD file.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.DocTypeName">
      <summary>Gets or sets the name of the document type declaration.</summary>
      <returns>The name of the document type declaration.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.Encoding">
      <summary>Gets or sets the encoding type.</summary>
      <returns>An <see cref="T:System.Text.Encoding" /> object indicating the encoding type.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.InternalSubset">
      <summary>Gets or sets the internal DTD subset.</summary>
      <returns>The internal DTD subset. For example, this property returns everything between the square brackets &lt;!DOCTYPE doc [...]&gt;.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NamespaceManager">
      <summary>Gets or sets the <see cref="T:System.Xml.XmlNamespaceManager" />.</summary>
      <returns>The XmlNamespaceManager.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.NameTable">
      <summary>Gets the <see cref="T:System.Xml.XmlNameTable" /> used to atomize strings. For more information on atomized strings, see <see cref="T:System.Xml.XmlNameTable" />.</summary>
      <returns>The XmlNameTable.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.PublicId">
      <summary>Gets or sets the public identifier.</summary>
      <returns>The public identifier.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.SystemId">
      <summary>Gets or sets the system identifier.</summary>
      <returns>The system identifier.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlLang">
      <summary>Gets or sets the current xml:lang scope.</summary>
      <returns>The current xml:lang scope. If there is no xml:lang in scope, String.Empty is returned.</returns>
    </member>
    <member name="P:System.Xml.XmlParserContext.XmlSpace">
      <summary>Gets or sets the current xml:space scope.</summary>
      <returns>An <see cref="T:System.Xml.XmlSpace" /> value indicating the xml:space scope.</returns>
    </member>
    <member name="T:System.Xml.XmlQualifiedName">
      <summary>Represents an XML qualified name.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XmlQualifiedName" /> class.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XmlQualifiedName" /> class with the specified name.</summary>
      <param name="name">The local name to use as the name of the <see cref="T:System.Xml.XmlQualifiedName" /> object. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XmlQualifiedName" /> class with the specified name and namespace.</summary>
      <param name="name">The local name to use as the name of the <see cref="T:System.Xml.XmlQualifiedName" /> object. </param>
      <param name="ns">The namespace for the <see cref="T:System.Xml.XmlQualifiedName" /> object. </param>
    </member>
    <member name="F:System.Xml.XmlQualifiedName.Empty">
      <summary>Provides an empty <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Xml.XmlQualifiedName" /> object is equal to the current <see cref="T:System.Xml.XmlQualifiedName" /> object. </summary>
      <returns>true if the two are the same instance object; otherwise, false.</returns>
      <param name="other">The <see cref="T:System.Xml.XmlQualifiedName" /> to compare. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.GetHashCode">
      <summary>Returns the hash code for the <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>A hash code for this object.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.IsEmpty">
      <summary>Gets a value indicating whether the <see cref="T:System.Xml.XmlQualifiedName" /> is empty.</summary>
      <returns>true if name and namespace are empty strings; otherwise, false.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Name">
      <summary>Gets a string representation of the qualified name of the <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>A string representation of the qualified name or String.Empty if a name is not defined for the object.</returns>
    </member>
    <member name="P:System.Xml.XmlQualifiedName.Namespace">
      <summary>Gets a string representation of the namespace of the <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>A string representation of the namespace or String.Empty if a namespace is not defined for the object.</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Equality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>Compares two <see cref="T:System.Xml.XmlQualifiedName" /> objects.</summary>
      <returns>true if the two objects have the same name and namespace values; otherwise, false.</returns>
      <param name="a">An <see cref="T:System.Xml.XmlQualifiedName" /> to compare. </param>
      <param name="b">An <see cref="T:System.Xml.XmlQualifiedName" /> to compare. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.op_Inequality(System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>Compares two <see cref="T:System.Xml.XmlQualifiedName" /> objects.</summary>
      <returns>true if the name and namespace values for the two objects differ; otherwise, false.</returns>
      <param name="a">An <see cref="T:System.Xml.XmlQualifiedName" /> to compare. </param>
      <param name="b">An <see cref="T:System.Xml.XmlQualifiedName" /> to compare. </param>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString">
      <summary>Returns the string value of the <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>The string value of the <see cref="T:System.Xml.XmlQualifiedName" /> in the format of namespace:localname. If the object does not have a namespace defined, this method returns just the local name.</returns>
    </member>
    <member name="M:System.Xml.XmlQualifiedName.ToString(System.String,System.String)">
      <summary>Returns the string value of the <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <returns>The string value of the <see cref="T:System.Xml.XmlQualifiedName" /> in the format of namespace:localname. If the object does not have a namespace defined, this method returns just the local name.</returns>
      <param name="name">The name of the object. </param>
      <param name="ns">The namespace of the object. </param>
    </member>
    <member name="T:System.Xml.XmlReader">
      <summary>Represents a reader that provides fast, noncached, forward-only access to XML data.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
    </member>
    <member name="M:System.Xml.XmlReader.#ctor">
      <summary>Initializes a new instance of the XmlReader class.</summary>
    </member>
    <member name="P:System.Xml.XmlReader.AttributeCount">
      <summary>When overridden in a derived class, gets the number of attributes on the current node.</summary>
      <returns>The number of attributes on the current node.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.BaseURI">
      <summary>When overridden in a derived class, gets the base URI of the current node.</summary>
      <returns>The base URI of the current node.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadBinaryContent">
      <summary>Gets a value indicating whether the <see cref="T:System.Xml.XmlReader" /> implements the binary content read methods.</summary>
      <returns>true if the binary content read methods are implemented; otherwise false.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanReadValueChunk">
      <summary>Gets a value indicating whether the <see cref="T:System.Xml.XmlReader" /> implements the <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" /> method.</summary>
      <returns>true if the <see cref="T:System.Xml.XmlReader" /> implements the <see cref="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)" /> method; otherwise false.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.CanResolveEntity">
      <summary>Gets a value indicating whether this reader can parse and resolve entities.</summary>
      <returns>true if the reader can parse and resolve entities; otherwise, false.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream)">
      <summary>Creates a new <see cref="T:System.Xml.XmlReader" /> instance using the specified stream with default settings.</summary>
      <returns>An object that is used to read the XML data in the stream.</returns>
      <param name="input">The stream that contains the XML data.The <see cref="T:System.Xml.XmlReader" /> scans the first bytes of the stream looking for a byte order mark or other sign of encoding. When encoding is determined, the encoding is used to continue reading the stream, and processing continues parsing the input as a stream of (Unicode) characters.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="input" /> value is null.</exception>
      <exception cref="T:System.Security.SecurityException">The <see cref="T:System.Xml.XmlReader" /> does not have sufficient permissions to access the location of the XML data.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings)">
      <summary>Creates a new <see cref="T:System.Xml.XmlReader" /> instance with the specified stream and settings.</summary>
      <returns>An object that is used to read the XML data in the stream.</returns>
      <param name="input">The stream that contains the XML data.The <see cref="T:System.Xml.XmlReader" /> scans the first bytes of the stream looking for a byte order mark or other sign of encoding. When encoding is determined, the encoding is used to continue reading the stream, and processing continues parsing the input as a stream of (Unicode) characters.</param>
      <param name="settings">The settings for the new <see cref="T:System.Xml.XmlReader" /> instance. This value can be null.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="input" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.Stream,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>Creates a new <see cref="T:System.Xml.XmlReader" /> instance using the specified stream, settings, and context information for parsing.</summary>
      <returns>An object that is used to read the XML data in the stream.</returns>
      <param name="input">The stream that contains the XML data. The <see cref="T:System.Xml.XmlReader" /> scans the first bytes of the stream looking for a byte order mark or other sign of encoding. When encoding is determined, the encoding is used to continue reading the stream, and processing continues parsing the input as a stream of (Unicode) characters.</param>
      <param name="settings">The settings for the new <see cref="T:System.Xml.XmlReader" /> instance. This value can be null.</param>
      <param name="inputContext">The context information required to parse the XML fragment. The context information can include the <see cref="T:System.Xml.XmlNameTable" /> to use, encoding, namespace scope, the current xml:lang and xml:space scope, base URI, and document type definition. This value can be null.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="input" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader)">
      <summary>Creates a new <see cref="T:System.Xml.XmlReader" /> instance by using the specified text reader.</summary>
      <returns>An object that is used to read the XML data in the stream.</returns>
      <param name="input">The text reader from which to read the XML data. A text reader returns a stream of Unicode characters, so the encoding specified in the XML declaration is not used by the XML reader to decode the data stream.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="input" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings)">
      <summary>Creates a new <see cref="T:System.Xml.XmlReader" /> instance by using the specified text reader and settings.</summary>
      <returns>An object that is used to read the XML data in the stream.</returns>
      <param name="input">The text reader from which to read the XML data. A text reader returns a stream of Unicode characters, so the encoding specified in the XML declaration isn't used by the XML reader to decode the data stream.</param>
      <param name="settings">The settings for the new <see cref="T:System.Xml.XmlReader" />. This value can be null.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="input" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.IO.TextReader,System.Xml.XmlReaderSettings,System.Xml.XmlParserContext)">
      <summary>Creates a new <see cref="T:System.Xml.XmlReader" /> instance by using the specified text reader, settings, and context information for parsing.</summary>
      <returns>An object that is used to read the XML data in the stream.</returns>
      <param name="input">The text reader from which to read the XML data. A text reader returns a stream of Unicode characters, so the encoding specified in the XML declaration isn't used by the XML reader to decode the data stream.</param>
      <param name="settings">The settings for the new <see cref="T:System.Xml.XmlReader" /> instance. This value can be null.</param>
      <param name="inputContext">The context information required to parse the XML fragment. The context information can include the <see cref="T:System.Xml.XmlNameTable" /> to use, encoding, namespace scope, the current xml:lang and xml:space scope, base URI, and document type definition.This value can be null.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="input" /> value is null.</exception>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Xml.XmlReaderSettings.NameTable" />  and <see cref="P:System.Xml.XmlParserContext.NameTable" /> properties both contain values. (Only one of these NameTable properties can be set and used).</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String)">
      <summary>Creates a new <see cref="T:System.Xml.XmlReader" /> instance with specified URI.</summary>
      <returns>An object that is used to read the XML data in the stream.</returns>
      <param name="inputUri">The URI for the file that contains the XML data. The <see cref="T:System.Xml.XmlUrlResolver" /> class is used to convert the path to a canonical data representation.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="inputUri" /> value is null.</exception>
      <exception cref="T:System.Security.SecurityException">The <see cref="T:System.Xml.XmlReader" /> does not have sufficient permissions to access the location of the XML data.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file identified by the URI does not exist.</exception>
      <exception cref="T:System.UriFormatException">In the .NET for Windows Store apps or the Portable Class Library, catch the base class exception, <see cref="T:System.FormatException" />, instead.The URI format is not correct.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.String,System.Xml.XmlReaderSettings)">
      <summary>Creates a new <see cref="T:System.Xml.XmlReader" /> instance by using the specified URI and settings.</summary>
      <returns>An object that is used to read the XML data in the stream.</returns>
      <param name="inputUri">The URI for the file containing the XML data. The <see cref="T:System.Xml.XmlResolver" /> object on the <see cref="T:System.Xml.XmlReaderSettings" /> object is used to convert the path to a canonical data representation. If <see cref="P:System.Xml.XmlReaderSettings.XmlResolver" /> is null, a new <see cref="T:System.Xml.XmlUrlResolver" /> object is used.</param>
      <param name="settings">The settings for the new <see cref="T:System.Xml.XmlReader" /> instance. This value can be null.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="inputUri" /> value is null.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified by the URI cannot be found.</exception>
      <exception cref="T:System.UriFormatException">In the .NET for Windows Store apps or the Portable Class Library, catch the base class exception, <see cref="T:System.FormatException" />, instead.The URI format is not correct.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Create(System.Xml.XmlReader,System.Xml.XmlReaderSettings)">
      <summary>Creates a new <see cref="T:System.Xml.XmlReader" /> instance by using the specified XML reader and settings.</summary>
      <returns>An object that is wrapped around the specified <see cref="T:System.Xml.XmlReader" /> object.</returns>
      <param name="reader">The object that you want to use as the underlying XML reader.</param>
      <param name="settings">The settings for the new <see cref="T:System.Xml.XmlReader" /> instance.The conformance level of the <see cref="T:System.Xml.XmlReaderSettings" /> object must either match the conformance level of the underlying reader, or it must be set to <see cref="F:System.Xml.ConformanceLevel.Auto" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="reader" /> value is null.</exception>
      <exception cref="T:System.InvalidOperationException">If the <see cref="T:System.Xml.XmlReaderSettings" /> object specifies a conformance level that is not consistent with conformance level of the underlying reader.-or-The underlying <see cref="T:System.Xml.XmlReader" /> is in an <see cref="F:System.Xml.ReadState.Error" /> or <see cref="F:System.Xml.ReadState.Closed" /> state.</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Depth">
      <summary>When overridden in a derived class, gets the depth of the current node in the XML document.</summary>
      <returns>The depth of the current node in the XML document.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.Xml.XmlReader" /> class.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Xml.XmlReader" /> and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.EOF">
      <summary>When overridden in a derived class, gets a value indicating whether the reader is positioned at the end of the stream.</summary>
      <returns>true if the reader is positioned at the end of the stream; otherwise, false.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.Int32)">
      <summary>When overridden in a derived class, gets the value of the attribute with the specified index.</summary>
      <returns>The value of the specified attribute. This method does not move the reader.</returns>
      <param name="i">The index of the attribute. The index is zero-based. (The first attribute has index 0.)</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="i" /> is out of range. It must be non-negative and less than the size of the attribute collection.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String)">
      <summary>When overridden in a derived class, gets the value of the attribute with the specified <see cref="P:System.Xml.XmlReader.Name" />.</summary>
      <returns>The value of the specified attribute. If the attribute is not found or the value is String.Empty, null is returned.</returns>
      <param name="name">The qualified name of the attribute.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetAttribute(System.String,System.String)">
      <summary>When overridden in a derived class, gets the value of the attribute with the specified <see cref="P:System.Xml.XmlReader.LocalName" /> and <see cref="P:System.Xml.XmlReader.NamespaceURI" />.</summary>
      <returns>The value of the specified attribute. If the attribute is not found or the value is String.Empty, null is returned. This method does not move the reader.</returns>
      <param name="name">The local name of the attribute.</param>
      <param name="namespaceURI">The namespace URI of the attribute.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.GetValueAsync">
      <summary>Asynchronously gets the value of the current node.</summary>
      <returns>The value of the current node.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlReaderSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlReaderSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasAttributes">
      <summary>Gets a value indicating whether the current node has any attributes.</summary>
      <returns>true if the current node has attributes; otherwise, false.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.HasValue">
      <summary>When overridden in a derived class, gets a value indicating whether the current node can have a <see cref="P:System.Xml.XmlReader.Value" />.</summary>
      <returns>true if the node on which the reader is currently positioned can have a Value; otherwise, false. If false, the node has a value of String.Empty.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsDefault">
      <summary>When overridden in a derived class, gets a value indicating whether the current node is an attribute that was generated from the default value defined in the DTD or schema.</summary>
      <returns>true if the current node is an attribute whose value was generated from the default value defined in the DTD or schema; false if the attribute value was explicitly set.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.IsEmptyElement">
      <summary>When overridden in a derived class, gets a value indicating whether the current node is an empty element (for example, &lt;MyElement/&gt;).</summary>
      <returns>true if the current node is an element (<see cref="P:System.Xml.XmlReader.NodeType" /> equals XmlNodeType.Element) that ends with /&gt;; otherwise, false.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsName(System.String)">
      <summary>Returns a value indicating whether the string argument is a valid XML name.</summary>
      <returns>true if the name is valid; otherwise, false.</returns>
      <param name="str">The name to validate.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="str" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsNameToken(System.String)">
      <summary>Returns a value indicating whether or not the string argument is a valid XML name token.</summary>
      <returns>true if it is a valid name token; otherwise false.</returns>
      <param name="str">The name token to validate.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="str" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement">
      <summary>Calls <see cref="M:System.Xml.XmlReader.MoveToContent" /> and tests if the current content node is a start tag or empty element tag.</summary>
      <returns>true if <see cref="M:System.Xml.XmlReader.MoveToContent" /> finds a start tag or empty element tag; false if a node type other than XmlNodeType.Element was found.</returns>
      <exception cref="T:System.Xml.XmlException">Incorrect XML is encountered in the input stream.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String)">
      <summary>Calls <see cref="M:System.Xml.XmlReader.MoveToContent" /> and tests if the current content node is a start tag or empty element tag and if the <see cref="P:System.Xml.XmlReader.Name" /> property of the element found matches the given argument.</summary>
      <returns>true if the resulting node is an element and the Name property matches the specified string. false if a node type other than XmlNodeType.Element was found or if the element Name property does not match the specified string.</returns>
      <param name="name">The string matched against the Name property of the element found.</param>
      <exception cref="T:System.Xml.XmlException">Incorrect XML is encountered in the input stream.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.IsStartElement(System.String,System.String)">
      <summary>Calls <see cref="M:System.Xml.XmlReader.MoveToContent" /> and tests if the current content node is a start tag or empty element tag and if the <see cref="P:System.Xml.XmlReader.LocalName" /> and <see cref="P:System.Xml.XmlReader.NamespaceURI" /> properties of the element found match the given strings.</summary>
      <returns>true if the resulting node is an element. false if a node type other than XmlNodeType.Element was found or if the LocalName and NamespaceURI properties of the element do not match the specified strings.</returns>
      <param name="localname">The string to match against the LocalName property of the element found.</param>
      <param name="ns">The string to match against the NamespaceURI property of the element found.</param>
      <exception cref="T:System.Xml.XmlException">Incorrect XML is encountered in the input stream.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.Int32)">
      <summary>When overridden in a derived class, gets the value of the attribute with the specified index.</summary>
      <returns>The value of the specified attribute.</returns>
      <param name="i">The index of the attribute.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String)">
      <summary>When overridden in a derived class, gets the value of the attribute with the specified <see cref="P:System.Xml.XmlReader.Name" />.</summary>
      <returns>The value of the specified attribute. If the attribute is not found, null is returned.</returns>
      <param name="name">The qualified name of the attribute.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Item(System.String,System.String)">
      <summary>When overridden in a derived class, gets the value of the attribute with the specified <see cref="P:System.Xml.XmlReader.LocalName" /> and <see cref="P:System.Xml.XmlReader.NamespaceURI" />.</summary>
      <returns>The value of the specified attribute. If the attribute is not found, null is returned.</returns>
      <param name="name">The local name of the attribute.</param>
      <param name="namespaceURI">The namespace URI of the attribute.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.LocalName">
      <summary>When overridden in a derived class, gets the local name of the current node.</summary>
      <returns>The name of the current node with the prefix removed. For example, LocalName is book for the element &lt;bk:book&gt;.For node types that do not have a name (like Text, Comment, and so on), this property returns String.Empty.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.LookupNamespace(System.String)">
      <summary>When overridden in a derived class, resolves a namespace prefix in the current element's scope.</summary>
      <returns>The namespace URI to which the prefix maps or null if no matching prefix is found.</returns>
      <param name="prefix">The prefix whose namespace URI you want to resolve. To match the default namespace, pass an empty string. </param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.Int32)">
      <summary>When overridden in a derived class, moves to the attribute with the specified index.</summary>
      <param name="i">The index of the attribute.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The parameter has a negative value.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String)">
      <summary>When overridden in a derived class, moves to the attribute with the specified <see cref="P:System.Xml.XmlReader.Name" />.</summary>
      <returns>true if the attribute is found; otherwise, false. If false, the reader's position does not change.</returns>
      <param name="name">The qualified name of the attribute.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.ArgumentException">The parameter is an empty string.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToAttribute(System.String,System.String)">
      <summary>When overridden in a derived class, moves to the attribute with the specified <see cref="P:System.Xml.XmlReader.LocalName" /> and <see cref="P:System.Xml.XmlReader.NamespaceURI" />.</summary>
      <returns>true if the attribute is found; otherwise, false. If false, the reader's position does not change.</returns>
      <param name="name">The local name of the attribute.</param>
      <param name="ns">The namespace URI of the attribute.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.ArgumentNullException">Both parameter values are null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContent">
      <summary>Checks whether the current node is a content (non-white space text, CDATA, Element, EndElement, EntityReference, or EndEntity) node. If the node is not a content node, the reader skips ahead to the next content node or end of file. It skips over nodes of the following type: ProcessingInstruction, DocumentType, Comment, Whitespace, or SignificantWhitespace.</summary>
      <returns>The <see cref="P:System.Xml.XmlReader.NodeType" /> of the current node found by the method or XmlNodeType.None if the reader has reached the end of the input stream.</returns>
      <exception cref="T:System.Xml.XmlException">Incorrect XML encountered in the input stream.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToContentAsync">
      <summary>Asynchronously checks whether the current node is a content node. If the node is not a content node, the reader skips ahead to the next content node or end of file.</summary>
      <returns>The <see cref="P:System.Xml.XmlReader.NodeType" /> of the current node found by the method or XmlNodeType.None if the reader has reached the end of the input stream.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlReaderSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlReaderSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToElement">
      <summary>When overridden in a derived class, moves to the element that contains the current attribute node.</summary>
      <returns>true if the reader is positioned on an attribute (the reader moves to the element that owns the attribute); false if the reader is not positioned on an attribute (the position of the reader does not change).</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToFirstAttribute">
      <summary>When overridden in a derived class, moves to the first attribute.</summary>
      <returns>true if an attribute exists (the reader moves to the first attribute); otherwise, false (the position of the reader does not change).</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.MoveToNextAttribute">
      <summary>When overridden in a derived class, moves to the next attribute.</summary>
      <returns>true if there is a next attribute; false if there are no more attributes.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Name">
      <summary>When overridden in a derived class, gets the qualified name of the current node.</summary>
      <returns>The qualified name of the current node. For example, Name is bk:book for the element &lt;bk:book&gt;.The name returned is dependent on the <see cref="P:System.Xml.XmlReader.NodeType" /> of the node. The following node types return the listed values. All other node types return an empty string.Node type Name AttributeThe name of the attribute. DocumentTypeThe document type name. ElementThe tag name. EntityReferenceThe name of the entity referenced. ProcessingInstructionThe target of the processing instruction. XmlDeclarationThe literal string xml. </returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NamespaceURI">
      <summary>When overridden in a derived class, gets the namespace URI (as defined in the W3C Namespace specification) of the node on which the reader is positioned.</summary>
      <returns>The namespace URI of the current node; otherwise an empty string.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NameTable">
      <summary>When overridden in a derived class, gets the <see cref="T:System.Xml.XmlNameTable" /> associated with this implementation.</summary>
      <returns>The XmlNameTable enabling you to get the atomized version of a string within the node.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.NodeType">
      <summary>When overridden in a derived class, gets the type of the current node.</summary>
      <returns>One of the enumeration values that specify the type of the current node.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Prefix">
      <summary>When overridden in a derived class, gets the namespace prefix associated with the current node.</summary>
      <returns>The namespace prefix associated with the current node.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Read">
      <summary>When overridden in a derived class, reads the next node from the stream.</summary>
      <returns>true if the next node was read successfully; otherwise, false.</returns>
      <exception cref="T:System.Xml.XmlException">An error occurred while parsing the XML.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAsync">
      <summary>Asynchronously reads the next node from the stream.</summary>
      <returns>true if the next node was read successfully; false if there are no more nodes to read.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlReaderSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlReaderSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadAttributeValue">
      <summary>When overridden in a derived class, parses the attribute value into one or more Text, EntityReference, or EndEntity nodes.</summary>
      <returns>true if there are nodes to return.false if the reader is not positioned on an attribute node when the initial call is made or if all the attribute values have been read.An empty attribute, such as, misc="", returns true with a single node with a value of String.Empty.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Reads the content as an object of the type specified.</summary>
      <returns>The concatenated text content or attribute value converted to the requested type.</returns>
      <param name="returnType">The type of the value to be returned.Note   With the release of the .NET Framework 3.5, the value of the <paramref name="returnType" /> parameter can now be the <see cref="T:System.DateTimeOffset" /> type.</param>
      <param name="namespaceResolver">An <see cref="T:System.Xml.IXmlNamespaceResolver" /> object that is used to resolve any namespace prefixes related to type conversion. For example, this can be used when converting an <see cref="T:System.Xml.XmlQualifiedName" /> object to an xs:string.This value can be null.</param>
      <exception cref="T:System.FormatException">The content is not in the correct format for the target type.</exception>
      <exception cref="T:System.InvalidCastException">The attempted cast is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="returnType" /> value is null.</exception>
      <exception cref="T:System.InvalidOperationException">The current node is not a supported node type. See the table below for details.</exception>
      <exception cref="T:System.OverflowException">Read Decimal.MaxValue.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Asynchronously reads the content as an object of the type specified.</summary>
      <returns>The concatenated text content or attribute value converted to the requested type.</returns>
      <param name="returnType">The type of the value to be returned.</param>
      <param name="namespaceResolver">An <see cref="T:System.Xml.IXmlNamespaceResolver" /> object that is used to resolve any namespace prefixes related to type conversion.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlReaderSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlReaderSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>Reads the content and returns the Base64 decoded binary bytes.</summary>
      <returns>The number of bytes written to the buffer.</returns>
      <param name="buffer">The buffer into which to copy the resulting text. This value cannot be null.</param>
      <param name="index">The offset into the buffer where to start copying the result.</param>
      <param name="count">The maximum number of bytes to copy into the buffer. The actual number of bytes copied is returned from this method.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="buffer" /> value is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Xml.XmlReader.ReadContentAsBase64(System.Byte[],System.Int32,System.Int32)" /> is not supported on the current node.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The index into the buffer or index + count is larger than the allocated buffer size.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XmlReader" /> implementation does not support this method.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Asynchronously reads the content and returns the Base64 decoded binary bytes.</summary>
      <returns>The number of bytes written to the buffer.</returns>
      <param name="buffer">The buffer into which to copy the resulting text. This value cannot be null.</param>
      <param name="index">The offset into the buffer where to start copying the result.</param>
      <param name="count">The maximum number of bytes to copy into the buffer. The actual number of bytes copied is returned from this method.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlReaderSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlReaderSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>Reads the content and returns the BinHex decoded binary bytes.</summary>
      <returns>The number of bytes written to the buffer.</returns>
      <param name="buffer">The buffer into which to copy the resulting text. This value cannot be null.</param>
      <param name="index">The offset into the buffer where to start copying the result.</param>
      <param name="count">The maximum number of bytes to copy into the buffer. The actual number of bytes copied is returned from this method.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="buffer" /> value is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Xml.XmlReader.ReadContentAsBinHex(System.Byte[],System.Int32,System.Int32)" /> is not supported on the current node.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The index into the buffer or index + count is larger than the allocated buffer size.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XmlReader" /> implementation does not support this method.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Asynchronously reads the content and returns the BinHex decoded binary bytes.</summary>
      <returns>The number of bytes written to the buffer.</returns>
      <param name="buffer">The buffer into which to copy the resulting text. This value cannot be null.</param>
      <param name="index">The offset into the buffer where to start copying the result.</param>
      <param name="count">The maximum number of bytes to copy into the buffer. The actual number of bytes copied is returned from this method.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlReaderSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlReaderSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsBoolean">
      <summary>Reads the text content at the current position as a Boolean.</summary>
      <returns>The text content as a <see cref="T:System.Boolean" /> object.</returns>
      <exception cref="T:System.InvalidCastException">The attempted cast is not valid.</exception>
      <exception cref="T:System.FormatException">The string format is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDateTimeOffset">
      <summary>Reads the text content at the current position as a <see cref="T:System.DateTimeOffset" /> object.</summary>
      <returns>The text content as a <see cref="T:System.DateTimeOffset" /> object.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDecimal">
      <summary>Reads the text content at the current position as a <see cref="T:System.Decimal" /> object.</summary>
      <returns>The text content at the current position as a <see cref="T:System.Decimal" /> object.</returns>
      <exception cref="T:System.InvalidCastException">The attempted cast is not valid.</exception>
      <exception cref="T:System.FormatException">The string format is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsDouble">
      <summary>Reads the text content at the current position as a double-precision floating-point number.</summary>
      <returns>The text content as a double-precision floating-point number.</returns>
      <exception cref="T:System.InvalidCastException">The attempted cast is not valid.</exception>
      <exception cref="T:System.FormatException">The string format is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsFloat">
      <summary>Reads the text content at the current position as a single-precision floating point number.</summary>
      <returns>The text content at the current position as a single-precision floating point number.</returns>
      <exception cref="T:System.InvalidCastException">The attempted cast is not valid.</exception>
      <exception cref="T:System.FormatException">The string format is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsInt">
      <summary>Reads the text content at the current position as a 32-bit signed integer.</summary>
      <returns>The text content as a 32-bit signed integer.</returns>
      <exception cref="T:System.InvalidCastException">The attempted cast is not valid.</exception>
      <exception cref="T:System.FormatException">The string format is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsLong">
      <summary>Reads the text content at the current position as a 64-bit signed integer.</summary>
      <returns>The text content as a 64-bit signed integer.</returns>
      <exception cref="T:System.InvalidCastException">The attempted cast is not valid.</exception>
      <exception cref="T:System.FormatException">The string format is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObject">
      <summary>Reads the text content at the current position as an <see cref="T:System.Object" />.</summary>
      <returns>The text content as the most appropriate common language runtime (CLR) object.</returns>
      <exception cref="T:System.InvalidCastException">The attempted cast is not valid.</exception>
      <exception cref="T:System.FormatException">The string format is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsObjectAsync">
      <summary>Asynchronously reads the text content at the current position as an <see cref="T:System.Object" />.</summary>
      <returns>The text content as the most appropriate common language runtime (CLR) object.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlReaderSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlReaderSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsString">
      <summary>Reads the text content at the current position as a <see cref="T:System.String" /> object.</summary>
      <returns>The text content as a <see cref="T:System.String" /> object.</returns>
      <exception cref="T:System.InvalidCastException">The attempted cast is not valid.</exception>
      <exception cref="T:System.FormatException">The string format is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadContentAsStringAsync">
      <summary>Asynchronously reads the text content at the current position as a <see cref="T:System.String" /> object.</summary>
      <returns>The text content as a <see cref="T:System.String" /> object.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlReaderSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlReaderSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Reads the element content as the requested type.</summary>
      <returns>The element content converted to the requested typed object.</returns>
      <param name="returnType">The type of the value to be returned.Note   With the release of the .NET Framework 3.5, the value of the <paramref name="returnType" /> parameter can now be the <see cref="T:System.DateTimeOffset" /> type.</param>
      <param name="namespaceResolver">An <see cref="T:System.Xml.IXmlNamespaceResolver" /> object that is used to resolve any namespace prefixes related to type conversion.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on an element.</exception>
      <exception cref="T:System.Xml.XmlException">The current element contains child elements.-or-The element content cannot be converted to the requested type.</exception>
      <exception cref="T:System.ArgumentNullException">The method is called with null arguments.</exception>
      <exception cref="T:System.OverflowException">Read Decimal.MaxValue.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAs(System.Type,System.Xml.IXmlNamespaceResolver,System.String,System.String)">
      <summary>Checks that the specified local name and namespace URI matches that of the current element, then reads the element content as the requested type.</summary>
      <returns>The element content converted to the requested typed object.</returns>
      <param name="returnType">The type of the value to be returned.Note   With the release of the .NET Framework 3.5, the value of the <paramref name="returnType" /> parameter can now be the <see cref="T:System.DateTimeOffset" /> type.</param>
      <param name="namespaceResolver">An <see cref="T:System.Xml.IXmlNamespaceResolver" /> object that is used to resolve any namespace prefixes related to type conversion.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceURI">The namespace URI of the element.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on an element.</exception>
      <exception cref="T:System.Xml.XmlException">The current element contains child elements.-or-The element content cannot be converted to the requested type.</exception>
      <exception cref="T:System.ArgumentNullException">The method is called with null arguments.</exception>
      <exception cref="T:System.ArgumentException">The specified local name and namespace URI do not match that of the current element being read.</exception>
      <exception cref="T:System.OverflowException">Read Decimal.MaxValue.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsAsync(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Asynchronously reads the element content as the requested type.</summary>
      <returns>The element content converted to the requested typed object.</returns>
      <param name="returnType">The type of the value to be returned.</param>
      <param name="namespaceResolver">An <see cref="T:System.Xml.IXmlNamespaceResolver" /> object that is used to resolve any namespace prefixes related to type conversion.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlReaderSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlReaderSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>Reads the element and decodes the Base64 content.</summary>
      <returns>The number of bytes written to the buffer.</returns>
      <param name="buffer">The buffer into which to copy the resulting text. This value cannot be null.</param>
      <param name="index">The offset into the buffer where to start copying the result.</param>
      <param name="count">The maximum number of bytes to copy into the buffer. The actual number of bytes copied is returned from this method.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="buffer" /> value is null.</exception>
      <exception cref="T:System.InvalidOperationException">The current node is not an element node.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The index into the buffer or index + count is larger than the allocated buffer size.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XmlReader" /> implementation does not support this method.</exception>
      <exception cref="T:System.Xml.XmlException">The element contains mixed-content.</exception>
      <exception cref="T:System.FormatException">The content cannot be converted to the requested type.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Asynchronously reads the element and decodes the Base64 content.</summary>
      <returns>The number of bytes written to the buffer.</returns>
      <param name="buffer">The buffer into which to copy the resulting text. This value cannot be null.</param>
      <param name="index">The offset into the buffer where to start copying the result.</param>
      <param name="count">The maximum number of bytes to copy into the buffer. The actual number of bytes copied is returned from this method.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlReaderSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlReaderSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>Reads the element and decodes the BinHex content.</summary>
      <returns>The number of bytes written to the buffer.</returns>
      <param name="buffer">The buffer into which to copy the resulting text. This value cannot be null.</param>
      <param name="index">The offset into the buffer where to start copying the result.</param>
      <param name="count">The maximum number of bytes to copy into the buffer. The actual number of bytes copied is returned from this method.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="buffer" /> value is null.</exception>
      <exception cref="T:System.InvalidOperationException">The current node is not an element node.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The index into the buffer or index + count is larger than the allocated buffer size.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XmlReader" /> implementation does not support this method.</exception>
      <exception cref="T:System.Xml.XmlException">The element contains mixed-content.</exception>
      <exception cref="T:System.FormatException">The content cannot be converted to the requested type.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Asynchronously reads the element and decodes the BinHex content.</summary>
      <returns>The number of bytes written to the buffer.</returns>
      <param name="buffer">The buffer into which to copy the resulting text. This value cannot be null.</param>
      <param name="index">The offset into the buffer where to start copying the result.</param>
      <param name="count">The maximum number of bytes to copy into the buffer. The actual number of bytes copied is returned from this method.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlReaderSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlReaderSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean">
      <summary>Reads the current element and returns the contents as a <see cref="T:System.Boolean" /> object.</summary>
      <returns>The element content as a <see cref="T:System.Boolean" /> object.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on an element.</exception>
      <exception cref="T:System.Xml.XmlException">The current element contains child elements.-or-The element content cannot be converted to a <see cref="T:System.Boolean" /> object.</exception>
      <exception cref="T:System.ArgumentNullException">The method is called with null arguments.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsBoolean(System.String,System.String)">
      <summary>Checks that the specified local name and namespace URI matches that of the current element, then reads the current element and returns the contents as a <see cref="T:System.Boolean" /> object.</summary>
      <returns>The element content as a <see cref="T:System.Boolean" /> object.</returns>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceURI">The namespace URI of the element.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on an element.</exception>
      <exception cref="T:System.Xml.XmlException">The current element contains child elements.-or-The element content cannot be converted to the requested type.</exception>
      <exception cref="T:System.ArgumentNullException">The method is called with null arguments.</exception>
      <exception cref="T:System.ArgumentException">The specified local name and namespace URI do not match that of the current element being read.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal">
      <summary>Reads the current element and returns the contents as a <see cref="T:System.Decimal" /> object.</summary>
      <returns>The element content as a <see cref="T:System.Decimal" /> object.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on an element.</exception>
      <exception cref="T:System.Xml.XmlException">The current element contains child elements.-or-The element content cannot be converted to a <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.ArgumentNullException">The method is called with null arguments.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDecimal(System.String,System.String)">
      <summary>Checks that the specified local name and namespace URI matches that of the current element, then reads the current element and returns the contents as a <see cref="T:System.Decimal" /> object.</summary>
      <returns>The element content as a <see cref="T:System.Decimal" /> object.</returns>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceURI">The namespace URI of the element.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on an element.</exception>
      <exception cref="T:System.Xml.XmlException">The current element contains child elements.-or-The element content cannot be converted to a <see cref="T:System.Decimal" />.</exception>
      <exception cref="T:System.ArgumentNullException">The method is called with null arguments.</exception>
      <exception cref="T:System.ArgumentException">The specified local name and namespace URI do not match that of the current element being read.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble">
      <summary>Reads the current element and returns the contents as a double-precision floating-point number.</summary>
      <returns>The element content as a double-precision floating-point number.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on an element.</exception>
      <exception cref="T:System.Xml.XmlException">The current element contains child elements.-or-The element content cannot be converted to a double-precision floating-point number.</exception>
      <exception cref="T:System.ArgumentNullException">The method is called with null arguments.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsDouble(System.String,System.String)">
      <summary>Checks that the specified local name and namespace URI matches that of the current element, then reads the current element and returns the contents as a double-precision floating-point number.</summary>
      <returns>The element content as a double-precision floating-point number.</returns>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceURI">The namespace URI of the element.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on an element.</exception>
      <exception cref="T:System.Xml.XmlException">The current element contains child elements.-or-The element content cannot be converted to the requested type.</exception>
      <exception cref="T:System.ArgumentNullException">The method is called with null arguments.</exception>
      <exception cref="T:System.ArgumentException">The specified local name and namespace URI do not match that of the current element being read.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat">
      <summary>Reads the current element and returns the contents as single-precision floating-point number.</summary>
      <returns>The element content as a single-precision floating point number.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on an element.</exception>
      <exception cref="T:System.Xml.XmlException">The current element contains child elements.-or-The element content cannot be converted to a single-precision floating-point number.</exception>
      <exception cref="T:System.ArgumentNullException">The method is called with null arguments.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsFloat(System.String,System.String)">
      <summary>Checks that the specified local name and namespace URI matches that of the current element, then reads the current element and returns the contents as a single-precision floating-point number.</summary>
      <returns>The element content as a single-precision floating point number.</returns>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceURI">The namespace URI of the element.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on an element.</exception>
      <exception cref="T:System.Xml.XmlException">The current element contains child elements.-or-The element content cannot be converted to a single-precision floating-point number.</exception>
      <exception cref="T:System.ArgumentNullException">The method is called with null arguments.</exception>
      <exception cref="T:System.ArgumentException">The specified local name and namespace URI do not match that of the current element being read.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt">
      <summary>Reads the current element and returns the contents as a 32-bit signed integer.</summary>
      <returns>The element content as a 32-bit signed integer.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on an element.</exception>
      <exception cref="T:System.Xml.XmlException">The current element contains child elements.-or-The element content cannot be converted to a 32-bit signed integer.</exception>
      <exception cref="T:System.ArgumentNullException">The method is called with null arguments.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsInt(System.String,System.String)">
      <summary>Checks that the specified local name and namespace URI matches that of the current element, then reads the current element and returns the contents as a 32-bit signed integer.</summary>
      <returns>The element content as a 32-bit signed integer.</returns>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceURI">The namespace URI of the element.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on an element.</exception>
      <exception cref="T:System.Xml.XmlException">The current element contains child elements.-or-The element content cannot be converted to a 32-bit signed integer.</exception>
      <exception cref="T:System.ArgumentNullException">The method is called with null arguments.</exception>
      <exception cref="T:System.ArgumentException">The specified local name and namespace URI do not match that of the current element being read.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong">
      <summary>Reads the current element and returns the contents as a 64-bit signed integer.</summary>
      <returns>The element content as a 64-bit signed integer.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on an element.</exception>
      <exception cref="T:System.Xml.XmlException">The current element contains child elements.-or-The element content cannot be converted to a 64-bit signed integer.</exception>
      <exception cref="T:System.ArgumentNullException">The method is called with null arguments.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsLong(System.String,System.String)">
      <summary>Checks that the specified local name and namespace URI matches that of the current element, then reads the current element and returns the contents as a 64-bit signed integer.</summary>
      <returns>The element content as a 64-bit signed integer.</returns>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceURI">The namespace URI of the element.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on an element.</exception>
      <exception cref="T:System.Xml.XmlException">The current element contains child elements.-or-The element content cannot be converted to a 64-bit signed integer.</exception>
      <exception cref="T:System.ArgumentNullException">The method is called with null arguments.</exception>
      <exception cref="T:System.ArgumentException">The specified local name and namespace URI do not match that of the current element being read.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject">
      <summary>Reads the current element and returns the contents as an <see cref="T:System.Object" />.</summary>
      <returns>A boxed common language runtime (CLR) object of the most appropriate type. The <see cref="P:System.Xml.XmlReader.ValueType" /> property determines the appropriate CLR type. If the content is typed as a list type, this method returns an array of boxed objects of the appropriate type.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on an element.</exception>
      <exception cref="T:System.Xml.XmlException">The current element contains child elements.-or-The element content cannot be converted to the requested type</exception>
      <exception cref="T:System.ArgumentNullException">The method is called with null arguments.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObject(System.String,System.String)">
      <summary>Checks that the specified local name and namespace URI matches that of the current element, then reads the current element and returns the contents as an <see cref="T:System.Object" />.</summary>
      <returns>A boxed common language runtime (CLR) object of the most appropriate type. The <see cref="P:System.Xml.XmlReader.ValueType" /> property determines the appropriate CLR type. If the content is typed as a list type, this method returns an array of boxed objects of the appropriate type.</returns>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceURI">The namespace URI of the element.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on an element.</exception>
      <exception cref="T:System.Xml.XmlException">The current element contains child elements.-or-The element content cannot be converted to the requested type.</exception>
      <exception cref="T:System.ArgumentNullException">The method is called with null arguments.</exception>
      <exception cref="T:System.ArgumentException">The specified local name and namespace URI do not match that of the current element being read.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsObjectAsync">
      <summary>Asynchronously reads the current element and returns the contents as an <see cref="T:System.Object" />.</summary>
      <returns>A boxed common language runtime (CLR) object of the most appropriate type. The <see cref="P:System.Xml.XmlReader.ValueType" /> property determines the appropriate CLR type. If the content is typed as a list type, this method returns an array of boxed objects of the appropriate type.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlReaderSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlReaderSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString">
      <summary>Reads the current element and returns the contents as a <see cref="T:System.String" /> object.</summary>
      <returns>The element content as a <see cref="T:System.String" /> object.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on an element.</exception>
      <exception cref="T:System.Xml.XmlException">The current element contains child elements.-or-The element content cannot be converted to a <see cref="T:System.String" /> object.</exception>
      <exception cref="T:System.ArgumentNullException">The method is called with null arguments.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsString(System.String,System.String)">
      <summary>Checks that the specified local name and namespace URI matches that of the current element, then reads the current element and returns the contents as a <see cref="T:System.String" /> object.</summary>
      <returns>The element content as a <see cref="T:System.String" /> object.</returns>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceURI">The namespace URI of the element.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XmlReader" /> is not positioned on an element.</exception>
      <exception cref="T:System.Xml.XmlException">The current element contains child elements.-or-The element content cannot be converted to a <see cref="T:System.String" /> object.</exception>
      <exception cref="T:System.ArgumentNullException">The method is called with null arguments.</exception>
      <exception cref="T:System.ArgumentException">The specified local name and namespace URI do not match that of the current element being read.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadElementContentAsStringAsync">
      <summary>Asynchronously reads the current element and returns the contents as a <see cref="T:System.String" /> object.</summary>
      <returns>The element content as a <see cref="T:System.String" /> object.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlReaderSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlReaderSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadEndElement">
      <summary>Checks that the current content node is an end tag and advances the reader to the next node.</summary>
      <exception cref="T:System.Xml.XmlException">The current node is not an end tag or if incorrect XML is encountered in the input stream.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXml">
      <summary>When overridden in a derived class, reads all the content, including markup, as a string.</summary>
      <returns>All the XML content, including markup, in the current node. If the current node has no children, an empty string is returned.If the current node is neither an element nor attribute, an empty string is returned.</returns>
      <exception cref="T:System.Xml.XmlException">The XML was not well-formed, or an error occurred while parsing the XML.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadInnerXmlAsync">
      <summary>Asynchronously reads all the content, including markup, as a string.</summary>
      <returns>All the XML content, including markup, in the current node. If the current node has no children, an empty string is returned.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlReaderSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlReaderSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXml">
      <summary>When overridden in a derived class, reads the content, including markup, representing this node and all its children.</summary>
      <returns>If the reader is positioned on an element or an attribute node, this method returns all the XML content, including markup, of the current node and all its children; otherwise, it returns an empty string.</returns>
      <exception cref="T:System.Xml.XmlException">The XML was not well-formed, or an error occurred while parsing the XML.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadOuterXmlAsync">
      <summary>Asynchronously reads the content, including markup, representing this node and all its children.</summary>
      <returns>If the reader is positioned on an element or an attribute node, this method returns all the XML content, including markup, of the current node and all its children; otherwise, it returns an empty string.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlReaderSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlReaderSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement">
      <summary>Checks that the current node is an element and advances the reader to the next node.</summary>
      <exception cref="T:System.Xml.XmlException">Incorrect XML was encountered in the input stream.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String)">
      <summary>Checks that the current content node is an element with the given <see cref="P:System.Xml.XmlReader.Name" /> and advances the reader to the next node.</summary>
      <param name="name">The qualified name of the element.</param>
      <exception cref="T:System.Xml.XmlException">Incorrect XML was encountered in the input stream. -or- The <see cref="P:System.Xml.XmlReader.Name" /> of the element does not match the given <paramref name="name" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadStartElement(System.String,System.String)">
      <summary>Checks that the current content node is an element with the given <see cref="P:System.Xml.XmlReader.LocalName" /> and <see cref="P:System.Xml.XmlReader.NamespaceURI" /> and advances the reader to the next node.</summary>
      <param name="localname">The local name of the element.</param>
      <param name="ns">The namespace URI of the element.</param>
      <exception cref="T:System.Xml.XmlException">Incorrect XML was encountered in the input stream.-or-The <see cref="P:System.Xml.XmlReader.LocalName" /> and <see cref="P:System.Xml.XmlReader.NamespaceURI" /> properties of the element found do not match the given arguments.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ReadState">
      <summary>When overridden in a derived class, gets the state of the reader.</summary>
      <returns>One of the enumeration values that specifies the state of the reader.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadSubtree">
      <summary>Returns a new XmlReader instance that can be used to read the current node, and all its descendants.</summary>
      <returns>A new XML reader instance set to <see cref="F:System.Xml.ReadState.Initial" />. Calling the <see cref="M:System.Xml.XmlReader.Read" /> method positions the new reader on the node that was current before the call to the <see cref="M:System.Xml.XmlReader.ReadSubtree" /> method.</returns>
      <exception cref="T:System.InvalidOperationException">The XML reader isn't positioned on an element when this method is called.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String)">
      <summary>Advances the <see cref="T:System.Xml.XmlReader" /> to the next descendant element with the specified qualified name.</summary>
      <returns>true if a matching descendant element is found; otherwise false. If a matching child element is not found, the <see cref="T:System.Xml.XmlReader" /> is positioned on the end tag (<see cref="P:System.Xml.XmlReader.NodeType" /> is XmlNodeType.EndElement) of the element.If the <see cref="T:System.Xml.XmlReader" /> is not positioned on an element when <see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String)" /> was called, this method returns false and the position of the <see cref="T:System.Xml.XmlReader" /> is not changed.</returns>
      <param name="name">The qualified name of the element you wish to move to.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.ArgumentException">The parameter is an empty string.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)">
      <summary>Advances the <see cref="T:System.Xml.XmlReader" /> to the next descendant element with the specified local name and namespace URI.</summary>
      <returns>true if a matching descendant element is found; otherwise false. If a matching child element is not found, the <see cref="T:System.Xml.XmlReader" /> is positioned on the end tag (<see cref="P:System.Xml.XmlReader.NodeType" /> is XmlNodeType.EndElement) of the element.If the <see cref="T:System.Xml.XmlReader" /> is not positioned on an element when <see cref="M:System.Xml.XmlReader.ReadToDescendant(System.String,System.String)" /> was called, this method returns false and the position of the <see cref="T:System.Xml.XmlReader" /> is not changed.</returns>
      <param name="localName">The local name of the element you wish to move to.</param>
      <param name="namespaceURI">The namespace URI of the element you wish to move to.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.ArgumentNullException">Both parameter values are null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String)">
      <summary>Reads until an element with the specified qualified name is found.</summary>
      <returns>true if a matching element is found; otherwise false and the <see cref="T:System.Xml.XmlReader" /> is in an end of file state.</returns>
      <param name="name">The qualified name of the element.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.ArgumentException">The parameter is an empty string.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToFollowing(System.String,System.String)">
      <summary>Reads until an element with the specified local name and namespace URI is found.</summary>
      <returns>true if a matching element is found; otherwise false and the <see cref="T:System.Xml.XmlReader" /> is in an end of file state.</returns>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceURI">The namespace URI of the element.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.ArgumentNullException">Both parameter values are null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String)">
      <summary>Advances the XmlReader to the next sibling element with the specified qualified name.</summary>
      <returns>true if a matching sibling element is found; otherwise false. If a matching sibling element is not found, the XmlReader is positioned on the end tag (<see cref="P:System.Xml.XmlReader.NodeType" /> is XmlNodeType.EndElement) of the parent element.</returns>
      <param name="name">The qualified name of the sibling element you wish to move to.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.ArgumentException">The parameter is an empty string.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadToNextSibling(System.String,System.String)">
      <summary>Advances the XmlReader to the next sibling element with the specified local name and namespace URI.</summary>
      <returns>true if a matching sibling element is found; otherwise, false. If a matching sibling element is not found, the XmlReader is positioned on the end tag (<see cref="P:System.Xml.XmlReader.NodeType" /> is XmlNodeType.EndElement) of the parent element.</returns>
      <param name="localName">The local name of the sibling element you wish to move to.</param>
      <param name="namespaceURI">The namespace URI of the sibling element you wish to move to.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.ArgumentNullException">Both parameter values are null.</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunk(System.Char[],System.Int32,System.Int32)">
      <summary>Reads large streams of text embedded in an XML document.</summary>
      <returns>The number of characters read into the buffer. The value zero is returned when there is no more text content.</returns>
      <param name="buffer">The array of characters that serves as the buffer to which the text contents are written. This value cannot be null.</param>
      <param name="index">The offset within the buffer where the <see cref="T:System.Xml.XmlReader" /> can start to copy the results.</param>
      <param name="count">The maximum number of characters to copy into the buffer. The actual number of characters copied is returned from this method.</param>
      <exception cref="T:System.InvalidOperationException">The current node does not have a value (<see cref="P:System.Xml.XmlReader.HasValue" /> is false).</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="buffer" /> value is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The index into the buffer, or index + count is larger than the allocated buffer size.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XmlReader" /> implementation does not support this method.</exception>
      <exception cref="T:System.Xml.XmlException">The XML data is not well-formed.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ReadValueChunkAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Asynchronously reads large streams of text embedded in an XML document.</summary>
      <returns>The number of characters read into the buffer. The value zero is returned when there is no more text content.</returns>
      <param name="buffer">The array of characters that serves as the buffer to which the text contents are written. This value cannot be null.</param>
      <param name="index">The offset within the buffer where the <see cref="T:System.Xml.XmlReader" /> can start to copy the results.</param>
      <param name="count">The maximum number of characters to copy into the buffer. The actual number of characters copied is returned from this method.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlReaderSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlReaderSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.ResolveEntity">
      <summary>When overridden in a derived class, resolves the entity reference for EntityReference nodes.</summary>
      <exception cref="T:System.InvalidOperationException">The reader is not positioned on an EntityReference node; this implementation of the reader cannot resolve entities (<see cref="P:System.Xml.XmlReader.CanResolveEntity" /> returns false).</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Settings">
      <summary>Gets the <see cref="T:System.Xml.XmlReaderSettings" /> object used to create this <see cref="T:System.Xml.XmlReader" /> instance.</summary>
      <returns>The <see cref="T:System.Xml.XmlReaderSettings" /> object used to create this reader instance. If this reader was not created using the <see cref="Overload:System.Xml.XmlReader.Create" /> method, this property returns null.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.Skip">
      <summary>Skips the children of the current node.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlReader.SkipAsync">
      <summary>Asynchronously skips the children of the current node.</summary>
      <returns>The current node.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlReaderSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlReaderSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.Value">
      <summary>When overridden in a derived class, gets the text value of the current node.</summary>
      <returns>The value returned depends on the <see cref="P:System.Xml.XmlReader.NodeType" /> of the node. The following table lists node types that have a value to return. All other node types return String.Empty.Node type Value AttributeThe value of the attribute. CDATAThe content of the CDATA section. CommentThe content of the comment. DocumentTypeThe internal subset. ProcessingInstructionThe entire content, excluding the target. SignificantWhitespaceThe white space between markup in a mixed content model. TextThe content of the text node. WhitespaceThe white space between markup. XmlDeclarationThe content of the declaration. </returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.ValueType">
      <summary>Gets The Common Language Runtime (CLR) type for the current node.</summary>
      <returns>The CLR type that corresponds to the typed value of the node. The default is System.String.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlLang">
      <summary>When overridden in a derived class, gets the current xml:lang scope.</summary>
      <returns>The current xml:lang scope.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlReader.XmlSpace">
      <summary>When overridden in a derived class, gets the current xml:space scope.</summary>
      <returns>One of the <see cref="T:System.Xml.XmlSpace" /> values. If no xml:space scope exists, this property defaults to XmlSpace.None.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlReader" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="T:System.Xml.XmlReaderSettings">
      <summary>Specifies a set of features to support on the <see cref="T:System.Xml.XmlReader" /> object created by the <see cref="Overload:System.Xml.XmlReader.Create" /> method. </summary>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XmlReaderSettings" /> class.</summary>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.Async">
      <summary>Gets or sets whether asynchronous <see cref="T:System.Xml.XmlReader" /> methods can be used on a particular <see cref="T:System.Xml.XmlReader" /> instance.</summary>
      <returns>true if asynchronous methods can be used; otherwise, false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CheckCharacters">
      <summary>Gets or sets a value indicating whether to do character checking.</summary>
      <returns>true to do character checking; otherwise false. The default is true.NoteIf the <see cref="T:System.Xml.XmlReader" /> is processing text data, it always checks that the XML names and text content are valid, regardless of the property setting. Setting <see cref="P:System.Xml.XmlReaderSettings.CheckCharacters" /> to false turns off character checking for character entity references.</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Clone">
      <summary>Creates a copy of the <see cref="T:System.Xml.XmlReaderSettings" /> instance.</summary>
      <returns>The cloned <see cref="T:System.Xml.XmlReaderSettings" /> object.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.CloseInput">
      <summary>Gets or sets a value indicating whether the underlying stream or <see cref="T:System.IO.TextReader" /> should be closed when the reader is closed.</summary>
      <returns>true to close the underlying stream or <see cref="T:System.IO.TextReader" /> when the reader is closed; otherwise false. The default is false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.ConformanceLevel">
      <summary>Gets or sets the level of conformance which the <see cref="T:System.Xml.XmlReader" /> will comply.</summary>
      <returns>One of the enumeration values that specifies the level of conformance that the XML reader will enforce. The default is <see cref="F:System.Xml.ConformanceLevel.Document" />.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.DtdProcessing">
      <summary>Gets or sets a value that determines the processing of DTDs.</summary>
      <returns>One of the enumeration values that determines the processing of DTDs. The default is <see cref="F:System.Xml.DtdProcessing.Prohibit" />.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreComments">
      <summary>Gets or sets a value indicating whether to ignore comments.</summary>
      <returns>true to ignore comments; otherwise false. The default is false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreProcessingInstructions">
      <summary>Gets or sets a value indicating whether to ignore processing instructions.</summary>
      <returns>true to ignore processing instructions; otherwise false. The default is false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.IgnoreWhitespace">
      <summary>Gets or sets a value indicating whether to ignore insignificant white space.</summary>
      <returns>true to ignore white space; otherwise false. The default is false.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LineNumberOffset">
      <summary>Gets or sets line number offset of the <see cref="T:System.Xml.XmlReader" /> object.</summary>
      <returns>The line number offset. The default is 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.LinePositionOffset">
      <summary>Gets or sets line position offset of the <see cref="T:System.Xml.XmlReader" /> object.</summary>
      <returns>The line position offset. The default is 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersFromEntities">
      <summary>Gets or sets a value indicating the maximum allowable number of characters in a document that result from expanding entities.</summary>
      <returns>The maximum allowable number of characters from expanded entities. The default is 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.MaxCharactersInDocument">
      <summary>Gets or sets a value indicating the maximum allowable number of characters in an XML document. A zero (0) value means no limits on the size of the XML document. A non-zero value specifies the maximum size, in characters.</summary>
      <returns>The maximum allowable number of characters in an XML document. The default is 0.</returns>
    </member>
    <member name="P:System.Xml.XmlReaderSettings.NameTable">
      <summary>Gets or sets the <see cref="T:System.Xml.XmlNameTable" /> used for atomized string comparisons.</summary>
      <returns>The <see cref="T:System.Xml.XmlNameTable" /> that stores all the atomized strings used by all <see cref="T:System.Xml.XmlReader" /> instances created using this <see cref="T:System.Xml.XmlReaderSettings" /> object.The default is null. The created <see cref="T:System.Xml.XmlReader" /> instance will use a new empty <see cref="T:System.Xml.NameTable" /> if this value is null.</returns>
    </member>
    <member name="M:System.Xml.XmlReaderSettings.Reset">
      <summary>Resets the members of the settings class to their default values.</summary>
    </member>
    <member name="T:System.Xml.XmlSpace">
      <summary>Specifies the current xml:space scope.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Default">
      <summary>The xml:space scope equals default.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.None">
      <summary>No xml:space scope.</summary>
    </member>
    <member name="F:System.Xml.XmlSpace.Preserve">
      <summary>The xml:space scope equals preserve.</summary>
    </member>
    <member name="T:System.Xml.XmlWriter">
      <summary>Represents a writer that provides a fast, non-cached, forward-only way to generate streams or files that contain XML data.</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XmlWriter" /> class.</summary>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream)">
      <summary>Creates a new <see cref="T:System.Xml.XmlWriter" /> instance using the specified stream.</summary>
      <returns>An <see cref="T:System.Xml.XmlWriter" /> object.</returns>
      <param name="output">The stream to which you want to write. The <see cref="T:System.Xml.XmlWriter" /> writes XML 1.0 text syntax and appends it to the specified stream.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.Stream,System.Xml.XmlWriterSettings)">
      <summary>Creates a new <see cref="T:System.Xml.XmlWriter" /> instance using the stream and <see cref="T:System.Xml.XmlWriterSettings" /> object.</summary>
      <returns>An <see cref="T:System.Xml.XmlWriter" /> object.</returns>
      <param name="output">The stream to which you want to write. The <see cref="T:System.Xml.XmlWriter" /> writes XML 1.0 text syntax and appends it to the specified stream.</param>
      <param name="settings">The <see cref="T:System.Xml.XmlWriterSettings" /> object used to configure the new <see cref="T:System.Xml.XmlWriter" /> instance. If this is null, a <see cref="T:System.Xml.XmlWriterSettings" /> with default settings is used.If the <see cref="T:System.Xml.XmlWriter" /> is being used with the <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> method, you should use the <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> property to obtain an <see cref="T:System.Xml.XmlWriterSettings" /> object with the correct settings. This ensures that the created <see cref="T:System.Xml.XmlWriter" /> object has the correct output settings.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="stream" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter)">
      <summary>Creates a new <see cref="T:System.Xml.XmlWriter" /> instance using the specified <see cref="T:System.IO.TextWriter" />.</summary>
      <returns>An <see cref="T:System.Xml.XmlWriter" /> object.</returns>
      <param name="output">The <see cref="T:System.IO.TextWriter" /> to which you want to write. The <see cref="T:System.Xml.XmlWriter" /> writes XML 1.0 text syntax and appends it to the specified <see cref="T:System.IO.TextWriter" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.IO.TextWriter,System.Xml.XmlWriterSettings)">
      <summary>Creates a new <see cref="T:System.Xml.XmlWriter" /> instance using the <see cref="T:System.IO.TextWriter" /> and <see cref="T:System.Xml.XmlWriterSettings" /> objects.</summary>
      <returns>An <see cref="T:System.Xml.XmlWriter" /> object.</returns>
      <param name="output">The <see cref="T:System.IO.TextWriter" /> to which you want to write. The <see cref="T:System.Xml.XmlWriter" /> writes XML 1.0 text syntax and appends it to the specified <see cref="T:System.IO.TextWriter" />.</param>
      <param name="settings">The <see cref="T:System.Xml.XmlWriterSettings" /> object used to configure the new <see cref="T:System.Xml.XmlWriter" /> instance. If this is null, a <see cref="T:System.Xml.XmlWriterSettings" /> with default settings is used.If the <see cref="T:System.Xml.XmlWriter" /> is being used with the <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> method, you should use the <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> property to obtain an <see cref="T:System.Xml.XmlWriterSettings" /> object with the correct settings. This ensures that the created <see cref="T:System.Xml.XmlWriter" /> object has the correct output settings.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="text" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder)">
      <summary>Creates a new <see cref="T:System.Xml.XmlWriter" /> instance using the specified <see cref="T:System.Text.StringBuilder" />.</summary>
      <returns>An <see cref="T:System.Xml.XmlWriter" /> object.</returns>
      <param name="output">The <see cref="T:System.Text.StringBuilder" /> to which to write to. Content written by the <see cref="T:System.Xml.XmlWriter" /> is appended to the <see cref="T:System.Text.StringBuilder" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Text.StringBuilder,System.Xml.XmlWriterSettings)">
      <summary>Creates a new <see cref="T:System.Xml.XmlWriter" /> instance using the <see cref="T:System.Text.StringBuilder" /> and <see cref="T:System.Xml.XmlWriterSettings" /> objects.</summary>
      <returns>An <see cref="T:System.Xml.XmlWriter" /> object.</returns>
      <param name="output">The <see cref="T:System.Text.StringBuilder" /> to which to write to. Content written by the <see cref="T:System.Xml.XmlWriter" /> is appended to the <see cref="T:System.Text.StringBuilder" />.</param>
      <param name="settings">The <see cref="T:System.Xml.XmlWriterSettings" /> object used to configure the new <see cref="T:System.Xml.XmlWriter" /> instance. If this is null, a <see cref="T:System.Xml.XmlWriterSettings" /> with default settings is used.If the <see cref="T:System.Xml.XmlWriter" /> is being used with the <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> method, you should use the <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> property to obtain an <see cref="T:System.Xml.XmlWriterSettings" /> object with the correct settings. This ensures that the created <see cref="T:System.Xml.XmlWriter" /> object has the correct output settings.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="builder" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter)">
      <summary>Creates a new <see cref="T:System.Xml.XmlWriter" /> instance using the specified <see cref="T:System.Xml.XmlWriter" /> object.</summary>
      <returns>An <see cref="T:System.Xml.XmlWriter" /> object that is wrapped around the specified <see cref="T:System.Xml.XmlWriter" /> object.</returns>
      <param name="output">The <see cref="T:System.Xml.XmlWriter" /> object that you want to use as the underlying writer.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Create(System.Xml.XmlWriter,System.Xml.XmlWriterSettings)">
      <summary>Creates a new <see cref="T:System.Xml.XmlWriter" /> instance using the specified <see cref="T:System.Xml.XmlWriter" /> and <see cref="T:System.Xml.XmlWriterSettings" /> objects.</summary>
      <returns>An <see cref="T:System.Xml.XmlWriter" /> object that is wrapped around the specified <see cref="T:System.Xml.XmlWriter" /> object.</returns>
      <param name="output">The <see cref="T:System.Xml.XmlWriter" /> object that you want to use as the underlying writer.</param>
      <param name="settings">The <see cref="T:System.Xml.XmlWriterSettings" /> object used to configure the new <see cref="T:System.Xml.XmlWriter" /> instance. If this is null, a <see cref="T:System.Xml.XmlWriterSettings" /> with default settings is used.If the <see cref="T:System.Xml.XmlWriter" /> is being used with the <see cref="M:System.Xml.Xsl.XslCompiledTransform.Transform(System.String,System.Xml.XmlWriter)" /> method, you should use the <see cref="P:System.Xml.Xsl.XslCompiledTransform.OutputSettings" /> property to obtain an <see cref="T:System.Xml.XmlWriterSettings" /> object with the correct settings. This ensures that the created <see cref="T:System.Xml.XmlWriter" /> object has the correct output settings.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="writer" /> value is null.</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.Xml.XmlWriter" /> class.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Xml.XmlWriter" /> and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.Flush">
      <summary>When overridden in a derived class, flushes whatever is in the buffer to the underlying streams and also flushes the underlying stream.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.FlushAsync">
      <summary>Asynchronously flushes whatever is in the buffer to the underlying streams and also flushes the underlying stream.</summary>
      <returns>The task that represents the asynchronous Flush operation.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.LookupPrefix(System.String)">
      <summary>When overridden in a derived class, returns the closest prefix defined in the current namespace scope for the namespace URI.</summary>
      <returns>The matching prefix or null if no matching namespace URI is found in the current scope.</returns>
      <param name="ns">The namespace URI whose prefix you want to find.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="ns" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.Settings">
      <summary>Gets the <see cref="T:System.Xml.XmlWriterSettings" /> object used to create this <see cref="T:System.Xml.XmlWriter" /> instance.</summary>
      <returns>The <see cref="T:System.Xml.XmlWriterSettings" /> object used to create this writer instance. If this writer was not created using the <see cref="Overload:System.Xml.XmlWriter.Create" /> method, this property returns null.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributes(System.Xml.XmlReader,System.Boolean)">
      <summary>When overridden in a derived class, writes out all the attributes found at the current position in the <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="reader">The XmlReader from which to copy the attributes.</param>
      <param name="defattr">true to copy the default attributes from the XmlReader; otherwise, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null. </exception>
      <exception cref="T:System.Xml.XmlException">The reader is not positioned on an element, attribute or XmlDeclaration node. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributesAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>Asynchronously writes out all the attributes found at the current position in the <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>The task that represents the asynchronous WriteAttributes operation.</returns>
      <param name="reader">The XmlReader from which to copy the attributes.</param>
      <param name="defattr">true to copy the default attributes from the XmlReader; otherwise, false.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String)">
      <summary>When overridden in a derived class, writes out the attribute with the specified local name and value.</summary>
      <param name="localName">The local name of the attribute.</param>
      <param name="value">The value of the attribute.</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String)">
      <summary>When overridden in a derived class, writes an attribute with the specified local name, namespace URI, and value.</summary>
      <param name="localName">The local name of the attribute.</param>
      <param name="ns">The namespace URI to associate with the attribute.</param>
      <param name="value">The value of the attribute.</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeString(System.String,System.String,System.String,System.String)">
      <summary>When overridden in a derived class, writes out the attribute with the specified prefix, local name, namespace URI, and value.</summary>
      <param name="prefix">The namespace prefix of the attribute.</param>
      <param name="localName">The local name of the attribute.</param>
      <param name="ns">The namespace URI of the attribute.</param>
      <param name="value">The value of the attribute.</param>
      <exception cref="T:System.InvalidOperationException">The state of writer is not WriteState.Element or writer is closed. </exception>
      <exception cref="T:System.ArgumentException">The xml:space or xml:lang attribute value is invalid. </exception>
      <exception cref="T:System.Xml.XmlException">The <paramref name="localName" /> or <paramref name="ns" /> is null. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteAttributeStringAsync(System.String,System.String,System.String,System.String)">
      <summary>Asynchronously writes out the attribute with the specified prefix, local name, namespace URI, and value.</summary>
      <returns>The task that represents the asynchronous WriteAttributeString operation.</returns>
      <param name="prefix">The namespace prefix of the attribute.</param>
      <param name="localName">The local name of the attribute.</param>
      <param name="ns">The namespace URI of the attribute.</param>
      <param name="value">The value of the attribute.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>When overridden in a derived class, encodes the specified binary bytes as Base64 and writes out the resulting text.</summary>
      <param name="buffer">Byte array to encode.</param>
      <param name="index">The position in the buffer indicating the start of the bytes to write.</param>
      <param name="count">The number of bytes to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Asynchronously encodes the specified binary bytes as Base64 and writes out the resulting text.</summary>
      <returns>The task that represents the asynchronous WriteBase64 operation.</returns>
      <param name="buffer">Byte array to encode.</param>
      <param name="index">The position in the buffer indicating the start of the bytes to write.</param>
      <param name="count">The number of bytes to write.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHex(System.Byte[],System.Int32,System.Int32)">
      <summary>When overridden in a derived class, encodes the specified binary bytes as BinHex and writes out the resulting text.</summary>
      <param name="buffer">Byte array to encode.</param>
      <param name="index">The position in the buffer indicating the start of the bytes to write.</param>
      <param name="count">The number of bytes to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteBinHexAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Asynchronously encodes the specified binary bytes as BinHex and writes out the resulting text.</summary>
      <returns>The task that represents the asynchronous WriteBinHex operation.</returns>
      <param name="buffer">Byte array to encode.</param>
      <param name="index">The position in the buffer indicating the start of the bytes to write.</param>
      <param name="count">The number of bytes to write.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCData(System.String)">
      <summary>When overridden in a derived class, writes out a &lt;![CDATA[...]]&gt; block containing the specified text.</summary>
      <param name="text">The text to place inside the CDATA block.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCDataAsync(System.String)">
      <summary>Asynchronously writes out a &lt;![CDATA[...]]&gt; block containing the specified text.</summary>
      <returns>The task that represents the asynchronous WriteCData operation.</returns>
      <param name="text">The text to place inside the CDATA block.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntity(System.Char)">
      <summary>When overridden in a derived class, forces the generation of a character entity for the specified Unicode character value.</summary>
      <param name="ch">The Unicode character for which to generate a character entity.</param>
      <exception cref="T:System.ArgumentException">The character is in the surrogate pair character range, 0xd800 - 0xdfff.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharEntityAsync(System.Char)">
      <summary>Asynchronously forces the generation of a character entity for the specified Unicode character value.</summary>
      <returns>The task that represents the asynchronous WriteCharEntity operation.</returns>
      <param name="ch">The Unicode character for which to generate a character entity.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteChars(System.Char[],System.Int32,System.Int32)">
      <summary>When overridden in a derived class, writes text one buffer at a time.</summary>
      <param name="buffer">Character array containing the text to write.</param>
      <param name="index">The position in the buffer indicating the start of the text to write.</param>
      <param name="count">The number of characters to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero.-or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />; the call results in surrogate pair characters being split or an invalid surrogate pair being written.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="buffer" /> parameter value is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCharsAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Asynchronously writes text one buffer at a time.</summary>
      <returns>The task that represents the asynchronous WriteChars operation.</returns>
      <param name="buffer">Character array containing the text to write.</param>
      <param name="index">The position in the buffer indicating the start of the text to write.</param>
      <param name="count">The number of characters to write.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteComment(System.String)">
      <summary>When overridden in a derived class, writes out a comment &lt;!--...--&gt; containing the specified text.</summary>
      <param name="text">Text to place inside the comment.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well-formed XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteCommentAsync(System.String)">
      <summary>Asynchronously writes out a comment &lt;!--...--&gt; containing the specified text.</summary>
      <returns>The task that represents the asynchronous WriteComment operation.</returns>
      <param name="text">Text to place inside the comment.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocType(System.String,System.String,System.String,System.String)">
      <summary>When overridden in a derived class, writes the DOCTYPE declaration with the specified name and optional attributes.</summary>
      <param name="name">The name of the DOCTYPE. This must be non-empty.</param>
      <param name="pubid">If non-null it also writes PUBLIC "pubid" "sysid" where <paramref name="pubid" /> and <paramref name="sysid" /> are replaced with the value of the given arguments.</param>
      <param name="sysid">If <paramref name="pubid" /> is null and <paramref name="sysid" /> is non-null it writes SYSTEM "sysid" where <paramref name="sysid" /> is replaced with the value of this argument.</param>
      <param name="subset">If non-null it writes [subset] where subset is replaced with the value of this argument.</param>
      <exception cref="T:System.InvalidOperationException">This method was called outside the prolog (after the root element). </exception>
      <exception cref="T:System.ArgumentException">The value for <paramref name="name" /> would result in invalid XML.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteDocTypeAsync(System.String,System.String,System.String,System.String)">
      <summary>Asynchronously writes the DOCTYPE declaration with the specified name and optional attributes.</summary>
      <returns>The task that represents the asynchronous WriteDocType operation.</returns>
      <param name="name">The name of the DOCTYPE. This must be non-empty.</param>
      <param name="pubid">If non-null it also writes PUBLIC "pubid" "sysid" where <paramref name="pubid" /> and <paramref name="sysid" /> are replaced with the value of the given arguments.</param>
      <param name="sysid">If <paramref name="pubid" /> is null and <paramref name="sysid" /> is non-null it writes SYSTEM "sysid" where <paramref name="sysid" /> is replaced with the value of this argument.</param>
      <param name="subset">If non-null it writes [subset] where subset is replaced with the value of this argument.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String)">
      <summary>Writes an element with the specified local name and value.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="value">The value of the element.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding. For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names. The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer. Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values). However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String)">
      <summary>Writes an element with the specified local name, namespace URI, and value.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="ns">The namespace URI to associate with the element.</param>
      <param name="value">The value of the element.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding. For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names. The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer. Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values). However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementString(System.String,System.String,System.String,System.String)">
      <summary>Writes an element with the specified prefix, local name, namespace URI, and value.</summary>
      <param name="prefix">The prefix of the element.</param>
      <param name="localName">The local name of the element.</param>
      <param name="ns">The namespace URI of the element.</param>
      <param name="value">The value of the element.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="localName" /> value is null or an empty string.-or-The parameter values are not valid.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding. For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names. The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer. Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values). However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteElementStringAsync(System.String,System.String,System.String,System.String)">
      <summary>Asynchronously writes an element with the specified prefix, local name, namespace URI, and value.</summary>
      <returns>The task that represents the asynchronous WriteElementString operation.</returns>
      <param name="prefix">The prefix of the element.</param>
      <param name="localName">The local name of the element.</param>
      <param name="ns">The namespace URI of the element.</param>
      <param name="value">The value of the element.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttribute">
      <summary>When overridden in a derived class, closes the previous <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" /> call.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndAttributeAsync">
      <summary>Asynchronously closes the previous <see cref="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)" /> call.</summary>
      <returns>The task that represents the asynchronous WriteEndAttribute operation.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocument">
      <summary>When overridden in a derived class, closes any open elements or attributes and puts the writer back in the Start state.</summary>
      <exception cref="T:System.ArgumentException">The XML document is invalid.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndDocumentAsync">
      <summary>Asynchronously closes any open elements or attributes and puts the writer back in the Start state.</summary>
      <returns>The task that represents the asynchronous WriteEndDocument operation.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElement">
      <summary>When overridden in a derived class, closes one element and pops the corresponding namespace scope.</summary>
      <exception cref="T:System.InvalidOperationException">This results in an invalid XML document.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEndElementAsync">
      <summary>Asynchronously closes one element and pops the corresponding namespace scope.</summary>
      <returns>The task that represents the asynchronous WriteEndElement operation.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRef(System.String)">
      <summary>When overridden in a derived class, writes out an entity reference as &amp;name;.</summary>
      <param name="name">The name of the entity reference.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteEntityRefAsync(System.String)">
      <summary>Asynchronously writes out an entity reference as &amp;name;.</summary>
      <returns>The task that represents the asynchronous WriteEntityRef operation.</returns>
      <param name="name">The name of the entity reference.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElement">
      <summary>When overridden in a derived class, closes one element and pops the corresponding namespace scope.</summary>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteFullEndElementAsync">
      <summary>Asynchronously closes one element and pops the corresponding namespace scope.</summary>
      <returns>The task that represents the asynchronous WriteFullEndElement operation.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteName(System.String)">
      <summary>When overridden in a derived class, writes out the specified name, ensuring it is a valid name according to the W3C XML 1.0 recommendation (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name).</summary>
      <param name="name">The name to write.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid XML name; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNameAsync(System.String)">
      <summary>Asynchronously writes out the specified name, ensuring it is a valid name according to the W3C XML 1.0 recommendation (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name).</summary>
      <returns>The task that represents the asynchronous WriteName operation.</returns>
      <param name="name">The name to write.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmToken(System.String)">
      <summary>When overridden in a derived class, writes out the specified name, ensuring it is a valid NmToken according to the W3C XML 1.0 recommendation (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name).</summary>
      <param name="name">The name to write.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid NmToken; or <paramref name="name" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNmTokenAsync(System.String)">
      <summary>Asynchronously writes out the specified name, ensuring it is a valid NmToken according to the W3C XML 1.0 recommendation (http://www.w3.org/TR/1998/REC-xml-19980210#NT-Name).</summary>
      <returns>The task that represents the asynchronous WriteNmToken operation.</returns>
      <param name="name">The name to write.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNode(System.Xml.XmlReader,System.Boolean)">
      <summary>When overridden in a derived class, copies everything from the reader to the writer and moves the reader to the start of the next sibling.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> to read from.</param>
      <param name="defattr">true to copy the default attributes from the XmlReader; otherwise, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="reader" /> contains invalid characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteNodeAsync(System.Xml.XmlReader,System.Boolean)">
      <summary>Asynchronously copies everything from the reader to the writer and moves the reader to the start of the next sibling.</summary>
      <returns>The task that represents the asynchronous WriteNode operation.</returns>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> to read from.</param>
      <param name="defattr">true to copy the default attributes from the XmlReader; otherwise, false.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstruction(System.String,System.String)">
      <summary>When overridden in a derived class, writes out a processing instruction with a space between the name and text as follows: &lt;?name text?&gt;.</summary>
      <param name="name">The name of the processing instruction.</param>
      <param name="text">The text to include in the processing instruction.</param>
      <exception cref="T:System.ArgumentException">The text would result in a non-well formed XML document.<paramref name="name" /> is either null or String.Empty.This method is being used to create an XML declaration after <see cref="M:System.Xml.XmlWriter.WriteStartDocument" /> has already been called. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteProcessingInstructionAsync(System.String,System.String)">
      <summary>Asynchronously writes out a processing instruction with a space between the name and text as follows: &lt;?name text?&gt;.</summary>
      <returns>The task that represents the asynchronous WriteProcessingInstruction operation.</returns>
      <param name="name">The name of the processing instruction.</param>
      <param name="text">The text to include in the processing instruction.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedName(System.String,System.String)">
      <summary>When overridden in a derived class, writes out the namespace-qualified name. This method looks up the prefix that is in scope for the given namespace.</summary>
      <param name="localName">The local name to write.</param>
      <param name="ns">The namespace URI for the name.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="localName" /> is either null or String.Empty.<paramref name="localName" /> is not a valid name. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteQualifiedNameAsync(System.String,System.String)">
      <summary>Asynchronously writes out the namespace-qualified name. This method looks up the prefix that is in scope for the given namespace.</summary>
      <returns>The task that represents the asynchronous WriteQualifiedName operation.</returns>
      <param name="localName">The local name to write.</param>
      <param name="ns">The namespace URI for the name.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.Char[],System.Int32,System.Int32)">
      <summary>When overridden in a derived class, writes raw markup manually from a character buffer.</summary>
      <param name="buffer">Character array containing the text to write.</param>
      <param name="index">The position within the buffer indicating the start of the text to write.</param>
      <param name="count">The number of characters to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. -or-The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRaw(System.String)">
      <summary>When overridden in a derived class, writes raw markup manually from a string.</summary>
      <param name="data">String containing the text to write.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="data" /> is either null or String.Empty.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Asynchronously writes raw markup manually from a character buffer.</summary>
      <returns>The task that represents the asynchronous WriteRaw operation.</returns>
      <param name="buffer">Character array containing the text to write.</param>
      <param name="index">The position within the buffer indicating the start of the text to write.</param>
      <param name="count">The number of characters to write.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteRawAsync(System.String)">
      <summary>Asynchronously writes raw markup manually from a string.</summary>
      <returns>The task that represents the asynchronous WriteRaw operation.</returns>
      <param name="data">String containing the text to write.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String)">
      <summary>Writes the start of an attribute with the specified local name.</summary>
      <param name="localName">The local name of the attribute.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding. For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names. The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer. Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values). However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String)">
      <summary>Writes the start of an attribute with the specified local name and namespace URI.</summary>
      <param name="localName">The local name of the attribute.</param>
      <param name="ns">The namespace URI of the attribute.</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding. For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names. The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer. Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values). However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttribute(System.String,System.String,System.String)">
      <summary>When overridden in a derived class, writes the start of an attribute with the specified prefix, local name, and namespace URI.</summary>
      <param name="prefix">The namespace prefix of the attribute.</param>
      <param name="localName">The local name of the attribute.</param>
      <param name="ns">The namespace URI for the attribute.</param>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding. For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names. The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer. Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values). However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartAttributeAsync(System.String,System.String,System.String)">
      <summary>Asynchronously writes the start of an attribute with the specified prefix, local name, and namespace URI.</summary>
      <returns>The task that represents the asynchronous WriteStartAttribute operation.</returns>
      <param name="prefix">The namespace prefix of the attribute.</param>
      <param name="localName">The local name of the attribute.</param>
      <param name="ns">The namespace URI for the attribute.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument">
      <summary>When overridden in a derived class, writes the XML declaration with the version "1.0".</summary>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocument(System.Boolean)">
      <summary>When overridden in a derived class, writes the XML declaration with the version "1.0" and the standalone attribute.</summary>
      <param name="standalone">If true, it writes "standalone=yes"; if false, it writes "standalone=no".</param>
      <exception cref="T:System.InvalidOperationException">This is not the first write method called after the constructor. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync">
      <summary>Asynchronously writes the XML declaration with the version "1.0".</summary>
      <returns>The task that represents the asynchronous WriteStartDocument operation.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartDocumentAsync(System.Boolean)">
      <summary>Asynchronously writes the XML declaration with the version "1.0" and the standalone attribute.</summary>
      <returns>The task that represents the asynchronous WriteStartDocument operation.</returns>
      <param name="standalone">If true, it writes "standalone=yes"; if false, it writes "standalone=no".</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String)">
      <summary>When overridden in a derived class, writes out a start tag with the specified local name.</summary>
      <param name="localName">The local name of the element.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding. For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names. The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer. Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values). However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections. </exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String)">
      <summary>When overridden in a derived class, writes the specified start tag and associates it with the given namespace.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="ns">The namespace URI to associate with the element. If this namespace is already in scope and has an associated prefix, the writer automatically writes that prefix also.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding. For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names. The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer. Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values). However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElement(System.String,System.String,System.String)">
      <summary>When overridden in a derived class, writes the specified start tag and associates it with the given namespace and prefix.</summary>
      <param name="prefix">The namespace prefix of the element.</param>
      <param name="localName">The local name of the element.</param>
      <param name="ns">The namespace URI to associate with the element.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">There is a character in the buffer that is a valid XML character but is not valid for the output encoding. For example, if the output encoding is ASCII, you should only use characters from the range of 0 to 127 for element and attribute names. The invalid character might be in the argument of this method or in an argument of previous methods that were writing to the buffer. Such characters are escaped by character entity references when possible (for example, in text nodes or attribute values). However, the character entity reference is not allowed in element and attribute names, comments, processing instructions, or CDATA sections.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStartElementAsync(System.String,System.String,System.String)">
      <summary>Asynchronously writes the specified start tag and associates it with the given namespace and prefix.</summary>
      <returns>The task that represents the asynchronous WriteStartElement operation.</returns>
      <param name="prefix">The namespace prefix of the element.</param>
      <param name="localName">The local name of the element.</param>
      <param name="ns">The namespace URI to associate with the element.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.WriteState">
      <summary>When overridden in a derived class, gets the state of the writer.</summary>
      <returns>One of the <see cref="T:System.Xml.WriteState" /> values.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteString(System.String)">
      <summary>When overridden in a derived class, writes the given text content.</summary>
      <param name="text">The text to write.</param>
      <exception cref="T:System.ArgumentException">The text string contains an invalid surrogate pair.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteStringAsync(System.String)">
      <summary>Asynchronously writes the given text content.</summary>
      <returns>The task that represents the asynchronous WriteString operation.</returns>
      <param name="text">The text to write.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntity(System.Char,System.Char)">
      <summary>When overridden in a derived class, generates and writes the surrogate character entity for the surrogate character pair.</summary>
      <param name="lowChar">The low surrogate. This must be a value between 0xDC00 and 0xDFFF.</param>
      <param name="highChar">The high surrogate. This must be a value between 0xD800 and 0xDBFF.</param>
      <exception cref="T:System.ArgumentException">An invalid surrogate character pair was passed.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteSurrogateCharEntityAsync(System.Char,System.Char)">
      <summary>Asynchronously generates and writes the surrogate character entity for the surrogate character pair.</summary>
      <returns>The task that represents the asynchronous WriteSurrogateCharEntity operation.</returns>
      <param name="lowChar">The low surrogate. This must be a value between 0xDC00 and 0xDFFF.</param>
      <param name="highChar">The high surrogate. This must be a value between 0xD800 and 0xDBFF.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Boolean)">
      <summary>Writes a <see cref="T:System.Boolean" /> value.</summary>
      <param name="value">The <see cref="T:System.Boolean" /> value to write.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.DateTimeOffset)">
      <summary>Writes a <see cref="T:System.DateTimeOffset" /> value.</summary>
      <param name="value">The <see cref="T:System.DateTimeOffset" /> value to write.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Decimal)">
      <summary>Writes a <see cref="T:System.Decimal" /> value.</summary>
      <param name="value">The <see cref="T:System.Decimal" /> value to write.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Double)">
      <summary>Writes a <see cref="T:System.Double" /> value.</summary>
      <param name="value">The <see cref="T:System.Double" /> value to write.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int32)">
      <summary>Writes a <see cref="T:System.Int32" /> value.</summary>
      <param name="value">The <see cref="T:System.Int32" /> value to write.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Int64)">
      <summary>Writes a <see cref="T:System.Int64" /> value.</summary>
      <param name="value">The <see cref="T:System.Int64" /> value to write.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Object)">
      <summary>Writes the object value.</summary>
      <param name="value">The object value to write.Note   With the release of the .NET Framework 3.5, this method accepts <see cref="T:System.DateTimeOffset" /> as a parameter.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is closed or in error state.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.Single)">
      <summary>Writes a single-precision floating-point number.</summary>
      <param name="value">The single-precision floating-point number to write.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteValue(System.String)">
      <summary>Writes a <see cref="T:System.String" /> value.</summary>
      <param name="value">The <see cref="T:System.String" /> value to write.</param>
      <exception cref="T:System.ArgumentException">An invalid value was specified.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespace(System.String)">
      <summary>When overridden in a derived class, writes out the given white space.</summary>
      <param name="ws">The string of white space characters.</param>
      <exception cref="T:System.ArgumentException">The string contains non-white space characters.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlWriter.WriteWhitespaceAsync(System.String)">
      <summary>Asynchronously writes out the given white space.</summary>
      <returns>The task that represents the asynchronous WriteWhitespace operation.</returns>
      <param name="ws">The string of white space characters.</param>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to true. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlLang">
      <summary>When overridden in a derived class, gets the current xml:lang scope.</summary>
      <returns>The current xml:lang scope.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="P:System.Xml.XmlWriter.XmlSpace">
      <summary>When overridden in a derived class, gets an <see cref="T:System.Xml.XmlSpace" /> representing the current xml:space scope.</summary>
      <returns>An XmlSpace representing the current xml:space scope.Value Meaning NoneThis is the default if no xml:space scope exists.DefaultThe current scope is xml:space="default".PreserveThe current scope is xml:space="preserve".</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="T:System.Xml.XmlWriterSettings">
      <summary>Specifies a set of features to support on the <see cref="T:System.Xml.XmlWriter" /> object created by the <see cref="Overload:System.Xml.XmlWriter.Create" /> method.</summary>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XmlWriterSettings" /> class.</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Async">
      <summary>Gets or sets a value that indicates whether asynchronous <see cref="T:System.Xml.XmlWriter" /> methods can be used on a particular <see cref="T:System.Xml.XmlWriter" /> instance.</summary>
      <returns>true if asynchronous methods can be used; otherwise, false.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CheckCharacters">
      <summary>Gets or sets a value that indicates whether the XML writer should check to ensure that all characters in the document conform to the "2.2 Characters" section of the W3C XML 1.0 Recommendation.</summary>
      <returns>true to do character checking; otherwise, false. The default is true.</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Clone">
      <summary>Creates a copy of the <see cref="T:System.Xml.XmlWriterSettings" /> instance.</summary>
      <returns>The cloned <see cref="T:System.Xml.XmlWriterSettings" /> object.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.CloseOutput">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.Xml.XmlWriter" /> should also close the underlying stream or <see cref="T:System.IO.TextWriter" /> when the <see cref="M:System.Xml.XmlWriter.Close" /> method is called.</summary>
      <returns>true to also close the underlying stream or <see cref="T:System.IO.TextWriter" />; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.ConformanceLevel">
      <summary>Gets or sets the level of conformance that the XML writer checks the XML output for.</summary>
      <returns>One of the enumeration values that specifies the level of conformance (document, fragment, or automatic detection). The default is <see cref="F:System.Xml.ConformanceLevel.Document" />.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Encoding">
      <summary>Gets or sets the type of text encoding to use.</summary>
      <returns>The text encoding to use. The default is Encoding.UTF8.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.Indent">
      <summary>Gets or sets a value indicating whether to indent elements.</summary>
      <returns>true to write individual elements on new lines and indent; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.IndentChars">
      <summary>Gets or sets the character string to use when indenting. This setting is used when the <see cref="P:System.Xml.XmlWriterSettings.Indent" /> property is set to true.</summary>
      <returns>The character string to use when indenting. This can be set to any string value. However, to ensure valid XML, you should specify only valid white space characters, such as space characters, tabs, carriage returns, or line feeds. The default is two spaces.</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.IndentChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NamespaceHandling">
      <summary>Gets or sets a value that indicates whether the <see cref="T:System.Xml.XmlWriter" /> should remove duplicate namespace declarations when writing XML content. The default behavior is for the writer to output all namespace declarations that are present in the writer's namespace resolver.</summary>
      <returns>The <see cref="T:System.Xml.NamespaceHandling" /> enumeration used to specify whether to remove duplicate namespace declarations in the <see cref="T:System.Xml.XmlWriter" />.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineChars">
      <summary>Gets or sets the character string to use for line breaks.</summary>
      <returns>The character string to use for line breaks. This can be set to any string value. However, to ensure valid XML, you should specify only valid white space characters, such as space characters, tabs, carriage returns, or line feeds. The default is \r\n (carriage return, new line).</returns>
      <exception cref="T:System.ArgumentNullException">The value assigned to the <see cref="P:System.Xml.XmlWriterSettings.NewLineChars" /> is null.</exception>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineHandling">
      <summary>Gets or sets a value indicating whether to normalize line breaks in the output.</summary>
      <returns>One of the <see cref="T:System.Xml.NewLineHandling" /> values. The default is <see cref="F:System.Xml.NewLineHandling.Replace" />.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.NewLineOnAttributes">
      <summary>Gets or sets a value indicating whether to write attributes on a new line.</summary>
      <returns>true to write attributes on individual lines; otherwise, false. The default is false.NoteThis setting has no effect when the <see cref="P:System.Xml.XmlWriterSettings.Indent" /> property value is false.When <see cref="P:System.Xml.XmlWriterSettings.NewLineOnAttributes" /> is set to true, each attribute is pre-pended with a new line and one extra level of indentation.</returns>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.OmitXmlDeclaration">
      <summary>Gets or sets a value indicating whether to omit an XML declaration.</summary>
      <returns>true to omit the XML declaration; otherwise, false. The default is false, an XML declaration is written.</returns>
    </member>
    <member name="M:System.Xml.XmlWriterSettings.Reset">
      <summary>Resets the members of the settings class to their default values.</summary>
    </member>
    <member name="P:System.Xml.XmlWriterSettings.WriteEndDocumentOnClose">
      <summary>Gets or sets a value that indicates whether the <see cref="T:System.Xml.XmlWriter" /> will add closing tags to all unclosed element tags when the <see cref="M:System.Xml.XmlWriter.Close" /> method is called.</summary>
      <returns>true if all unclosed element tags will be closed out; otherwise, false. The default value is true. </returns>
    </member>
    <member name="T:System.Xml.Schema.XmlSchema">
      <summary>An in-memory representation of an XML Schema, as specified in the World Wide Web Consortium (W3C) XML Schema Part 1: Structures and XML Schema Part 2: Datatypes specifications.</summary>
    </member>
    <member name="T:System.Xml.Schema.XmlSchemaForm">
      <summary>Indicates if attributes or elements need to be qualified with a namespace prefix.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.None">
      <summary>Element and attribute form is not specified in the schema.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Qualified">
      <summary>Elements and attributes must be qualified with a namespace prefix.</summary>
    </member>
    <member name="F:System.Xml.Schema.XmlSchemaForm.Unqualified">
      <summary>Elements and attributes are not required to be qualified with a namespace prefix.</summary>
    </member>
    <member name="T:System.Xml.Serialization.IXmlSerializable">
      <summary>Provides custom formatting for XML serialization and deserialization.</summary>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.GetSchema">
      <summary>This method is reserved and should not be used. When implementing the IXmlSerializable interface, you should return null (Nothing in Visual Basic) from this method, and instead, if specifying a custom schema is required, apply the <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" /> to the class.</summary>
      <returns>An <see cref="T:System.Xml.Schema.XmlSchema" /> that describes the XML representation of the object that is produced by the <see cref="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)" /> method and consumed by the <see cref="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)" /> method.</returns>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.ReadXml(System.Xml.XmlReader)">
      <summary>Generates an object from its XML representation.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> stream from which the object is deserialized. </param>
    </member>
    <member name="M:System.Xml.Serialization.IXmlSerializable.WriteXml(System.Xml.XmlWriter)">
      <summary>Converts an object into its XML representation.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlWriter" /> stream to which the object is serialized. </param>
    </member>
    <member name="T:System.Xml.Serialization.XmlSchemaProviderAttribute">
      <summary>When applied to a type, stores the name of a static method of the type that returns an XML schema and a <see cref="T:System.Xml.XmlQualifiedName" /> (or <see cref="T:System.Xml.Schema.XmlSchemaType" /> for anonymous types) that controls the serialization of the type.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaProviderAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSchemaProviderAttribute" /> class, taking the name of the static method that supplies the type's XML schema.</summary>
      <param name="methodName">The name of the static method that must be implemented.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.IsAny">
      <summary>Gets or sets a value that determines whether the target class is a wildcard, or that the schema for the class has contains only an xs:any element.</summary>
      <returns>true, if the class is a wildcard, or if the schema contains only the xs:any element; otherwise, false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaProviderAttribute.MethodName">
      <summary>Gets the name of the static method that supplies the type's XML schema and the name of its XML Schema data type.</summary>
      <returns>The name of the method that is invoked by the XML infrastructure to return an XML schema.</returns>
    </member>
  </members>
</doc>